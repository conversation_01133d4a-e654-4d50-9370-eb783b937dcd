<!DOCTYPE html>
<html>
<head>
    <title>Minimal Search Test</title>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <link rel="stylesheet" href="css/select2.min.css" />
    <script src="select2.min.js"></script>
</head>
<body>
    <h2>Minimal Search Test</h2>
    
    <div class="control-group">
        <label class="control-label">Shipper Code: <span class="text-error">*</span> </label>
        <div class="controls">
            <select name="sname" id="sname" onchange="fun(this);to(this);" class="select2-search" required>
                <option onSelect="fun()" value="" selected >-- Please Select --</option>
                <option value="1">ABC-Company Name-Mumbai</option>
                <option value="2">XYZ-Another Company-Delhi</option>
                <option value="3">DEF-Third Company-Bangalore</option>
                <option value="4">GHI-Fourth Company-Chennai</option>
                <option value="5">JKL-Fifth Company-Kolkata</option>
            </select>
        </div>
    </div>

    <script>
    // Dummy functions to prevent errors
    function fun() { console.log('fun called'); }
    function to() { console.log('to called'); }

    $(document).ready(function() {
        console.log('=== Minimal Test Started ===');
        
        function initializeSearchForSelect(selectId, selectName) {
            var $select = $(selectId);
            
            if ($select.length === 0) {
                console.log('Select element not found:', selectId);
                return;
            }

            console.log('Initializing search for:', selectName, 'Element:', $select);

            // Store original options (excluding placeholder)
            var originalOptions = [];
            $select.find('option').each(function(index) {
                if (index > 0 && $(this).val()) {
                    originalOptions.push({
                        value: $(this).val(),
                        text: $(this).text(),
                        selected: $(this).is(':selected')
                    });
                }
            });
            $select.data('original-options', originalOptions);

            console.log('Original options stored:', originalOptions);

            // Add "Search" option as the second option
            var firstOption = $select.find('option:first');
            firstOption.after('<option value="__SEARCH__">🔍 Search...</option>');
            
            console.log('Search option added. Total options now:', $select.find('option').length);

            // Initialize Select2
            $select.select2({
                placeholder: "-- Please Select --",
                allowClear: true,
                width: '100%'
            });

            console.log('Select2 initialized');

            // Handle selection change
            $select.on('change', function() {
                var selectedValue = $(this).val();

                console.log('=== CHANGE EVENT TRIGGERED ===');
                console.log('Selected value for ' + selectName + ':', selectedValue);

                if (selectedValue === '__SEARCH__') {
                    console.log('=== OPENING SEARCH MODAL ===');
                    showSearchModal($select, originalOptions, selectName);

                    setTimeout(function() {
                        console.log('Resetting select to empty');
                        $select.val('').trigger('change');
                    }, 100);
                }
            });

            console.log('Enhanced search initialized for ' + selectName);
        }

        function showSearchModal($select, originalOptions, selectName) {
            console.log('=== showSearchModal called ===');
            
            var modalHtml = `
                <div id="searchModal" style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.5);
                    z-index: 9999;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                ">
                    <div style="
                        background: white;
                        padding: 20px;
                        border-radius: 8px;
                        width: 90%;
                        max-width: 500px;
                        max-height: 80vh;
                        overflow-y: auto;
                        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                    ">
                        <h3 style="margin-top: 0; color: #333;">Search in ${selectName}</h3>
                        <input type="text" id="searchInput" placeholder="Type to search anywhere in text..." style="
                            width: 100%;
                            padding: 10px;
                            border: 2px solid #ddd;
                            border-radius: 4px;
                            font-size: 16px;
                            margin-bottom: 15px;
                            box-sizing: border-box;
                        ">
                        <div id="searchResults" style="
                            max-height: 300px;
                            overflow-y: auto;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            margin-bottom: 15px;
                        "></div>
                        <div style="text-align: right;">
                            <button onclick="closeSearchModal()" style="
                                padding: 8px 16px;
                                background: #6c757d;
                                color: white;
                                border: none;
                                border-radius: 4px;
                                cursor: pointer;
                                margin-right: 10px;
                            ">Cancel</button>
                        </div>
                    </div>
                </div>
            `;

            $('#searchModal').remove();
            console.log('Removed existing modal');

            $('body').append(modalHtml);
            console.log('Modal added to body');
            console.log('Modal exists:', $('#searchModal').length > 0);

            $('#searchInput').focus();
            console.log('Focus set on search input');

            $('#searchInput').on('input', function() {
                var searchTerm = $(this).val().toLowerCase();
                var resultsHtml = '';

                if (searchTerm.length === 0) {
                    resultsHtml = '<div style="padding: 10px; color: #666;">Type to search...</div>';
                } else {
                    var matchCount = 0;
                    $.each(originalOptions, function(index, option) {
                        if (option.value && option.text.toLowerCase().indexOf(searchTerm) > -1) {
                            resultsHtml += `
                                <div class="search-result-item" data-value="${option.value}" style="
                                    padding: 10px;
                                    border-bottom: 1px solid #eee;
                                    cursor: pointer;
                                    transition: background-color 0.2s;
                                " onmouseover="this.style.backgroundColor='#f8f9fa'"
                                   onmouseout="this.style.backgroundColor='white'">
                                    ${option.text}
                                </div>
                            `;
                            matchCount++;
                        }
                    });

                    if (matchCount === 0) {
                        resultsHtml = '<div style="padding: 10px; color: #666;">No matches found</div>';
                    }
                }

                $('#searchResults').html(resultsHtml);

                $('.search-result-item').on('click', function() {
                    var selectedValue = $(this).data('value');
                    var selectedText = $(this).text();
                    $select.val(selectedValue).trigger('change');
                    console.log('Selected:', selectedText, 'with value:', selectedValue);
                    closeSearchModal();
                });
            });

            $('#searchInput').trigger('input');
        }

        function closeSearchModal() {
            $('#searchModal').remove();
            console.log('Modal closed');
        }

        window.closeSearchModal = closeSearchModal;

        $(document).on('click', '#searchModal', function(e) {
            if (e.target.id === 'searchModal') {
                closeSearchModal();
            }
        });

        $(document).on('keydown', function(e) {
            if (e.key === 'Escape') {
                closeSearchModal();
            }
        });

        // Initialize
        initializeSearchForSelect('#sname', 'Shipper Code');
        
        console.log('=== Initialization Complete ===');
    });
    </script>
</body>
</html>
