<?php 
//start session
session_start();
require 'connection.php';
require_once('database.php');

$action = $_GET['action'];

switch($action) {
	case 'add-hub1':
		bukTohub();
	break;
	case 'delivered':
		markDelivered();
	break;
	case 'add-hub':
		addHub();
	break;
	case 'hub-ack1':
		hubAck();
	break;
	
	
	
}//switch

function bukTohub(){

		$curntloca1 = $_POST['mloca']; 
	$curdate1 = $_POST['curdate'];
	$curtime1 = $_POST['curtime'];
        $fromloc1= $_POST['cid']; 
	 $status= $_POST['status'];
	$userid= $_POST['cid'];
	
	if (is_array($_POST['bh']))
{
foreach($_POST['bh'] as $area) {
         //   echo $area;
	 $sql1 ="INSERT INTO `currentloc` (`consig_no`, `userid`, `sourcesid`, `current_loc`, `current_date`, `current_time`, `currentstatus`, `book_date`) VALUES ('$area', '$userid', '$fromloc1', '$curntloca1', '$curdate1 ','$curtime1 ','$status', NOW())";
	dbQuery($sql1);
	//mysqli_query($con,$sql);

	 $sql2="UPDATE tbl_courier SET status = '$status' WHERE cons_no = '$area'";
	 dbQuery($sql2);
	 
	}
}
	 header('Location: bukhub.php');
		
}// close buktohub

function hubAck(){


       $toloc= $_POST['mloca'];                              
        //$fromloc1= $_POST['cid']; 
	    $useradd= $_POST['uaddress'];
        $userid= $_POST['cid'];
        $curdate1 = $_POST['curdate'];                 
	   $curtime1 = $_POST['curtime'];
	   $status= $_POST['status']; 
	// $rem = $_POST['remark'];

 if (is_array($_POST['remark']) && is_array($_POST['bh']))
	{
		$r=$_POST['remark'];
		$bh=$_POST['bh'];
		$ac=count($_POST['remark']);
		//echo $ac;
		$j=0;
		for($i=0;$i<$ac;$i++)
		{
			if($r[$i]!="")
			{       
                            
			$sql2 = "INSERT INTO `currentloc` (`consig_no`, `userid`, `sourcesid`, `current_loc`, `current_date`, `current_time`, `currentstatus`, `remark`, `book_date`) VALUES ('$bh[$j]', '$userid', '$useradd', '$toloc', '$curdate1', '$curtime1', '$status', '$r[$i]', NOW())";
			  
	dbQuery($sql2);
	$sql2="UPDATE tbl_courier SET status = '$status' WHERE cons_no = '$bh[$j]'";
	 dbQuery($sql2);
	
	         	$j++;
	     
			}
		}
		
	}
/*
 if (is_array($_POST['remark']) && is_array($_POST['bh']))
	{
		$r=$_POST['remark'];
		$bh=$_POST['bh'];
		$ac=count($_POST['remark']);
		//echo $ac;
		$j=0;
		for($i=0;$i<$ac;$i++)
		{
			if($r[$i]!="")
			{
              $sql2 ="INSERT INTO `currentloc` (`consig_no`, `userid`, `sourcesid`, `current_loc`, `current_date`, `current_time`, `remark`, `book_date`) VALUES ('$bh[$j]', '$userid', '$fromloc1', '$toloc', '$curdate1', '$curtime1', '$r[$i]', NOW())";
         //$sql2="update `currentloc` set `current_loc`='$toloc', `current_date`='$curdate1', `current_time`='$curtime1',`remark`='$r[$i]',`book_date`='NOW()' where `consig_no`='$bh[$j]'";
	dbQuery($sql2);
	$j++;
			}
		}
	} 
*/

	header('Location: huback.php');
	
}// close hubtohub

function addHub(){
require 'connection.php';

	$huboffname = $_POST['huboffname']; $hubuname = $_POST['hubuname'];$hubupass = $_POST['hubupass'];$hubno = $_POST['hubno'];$hubemail = $_POST['hubemail']; $hubaddress = $_POST['hubaddress'];   $OfficeName= $_POST['curntloca'];
 echo $usid = $_POST['cid']; $pname = $_POST['pname'];$mobilno = $_POST['mobilno'];
   
   $cpass=$_POST['cpass'];//$OfficeName = $_POST['curntloca'];
	foreach($_POST['curntloca'] as $name)
	{
		$nnme[]=$name;
	}
	$nmme=implode("",$nnme);
		
$sql="INSERT INTO `hubreg` (`userid`, `hoffname`, `uname`, `pass`, `cont`, `hmail`, `haddress`, `offhub`, `regdate`,`perid`,`contact_person`,`mobilno`) VALUES ('$usid','$huboffname', '$hubuname ', '$hubupass ', '$hubno ', '$hubemail ', '$hubaddress ', '$nmme', NOW(),'','$pname','$mobilno');";
	mysqli_query($con,$sql);
$data=mysqli_query($con,"select max(hubid) from hubreg");
	while($rec=mysqli_fetch_row($data))
	{
		$uidd=$rec[0];
	}

	$sqll="INSERT INTO `login` (`username`, `password`, `cpassword`, `type`, `rid`) VALUES ('$hubuname', '$hubupass', '$cpass', 'admin', '$uidd')";
	mysqli_query($con,$sqll);
	
if(isset($_POST['submit']))
 {
$message='Hello '.$_POST['hubuname'].',<br>
Welcome to RCPL Express..<br>
Your Registration Details as follows:
Your Office Name: '.$_POST['huboffname'].'<br>
Your User Name: '.$_POST['hubuname'].'<br>
Your Password: '.$_POST['hubupass'].' ';

$my_email = "<EMAIL>";
$headers = "MIME-Version: 1.0" . "\r\n";
$headers .= "Message-ID: <".gettimeofday()." TheSystem@Runanubandh>\r\n";
$headers .= "X-Mailer: PHP v Balaji\r\n";
date_default_timezone_set( 'Asia/Calcutta' );
$headers .= "BCC: <EMAIL>\r\n";
//$headers .= "BCC: <EMAIL>,<EMAIL> ,".$_POST['hubemail']."\r\n";
$headers .= "Content-type: text/html\r\n"; 

$subject = "RCPL Express HUB Office Registration Successfully..";
mail($my_email,$subject,$message,$headers);

}

	header('Location:addhuboffice.php'); 
	
	
}//addHub

function updateStatus() {
	
	$OfficeName = $_POST['OfficeName'];
	$status = $_POST['status'];
	$comments = $_POST['comments'];
	$cid = (int)$_POST['cid'];
	$cons_no = $_POST['cons_no'];
	//$OfficeName = $_POST['OfficeName'];
	
	$sql = "INSERT INTO tbl_courier_track (cid, cons_no, current_city, status, comments, bk_time)
			VALUES ($cid, '$cons_no', '$OfficeName', '$status', '$comments', NOW())";
	dbQuery($sql);
	
	$sql_1 = "UPDATE tbl_courier 
				SET status = '$status' 
				WHERE cid = $cid
				AND cons_no = '$cons_no'";
	dbQuery($sql_1);	
	
$target_path = "attach/";
$target_path = $target_path.$_FILES['photo']['name']; 
//print_r($_FILES['photo']);exit;
if(move_uploaded_file($_FILES['photo']['tmp_name'], $target_path))
{
 echo "<h4>The file ". basename($_FILES['photo']['name']). " has been uploaded Successfully.</h4>";
}
else
{
	echo "Sorry there was an error uploading the file, please try again!";
} 
	
header('Location: update-success.php');

}//addNewOffice

?>
