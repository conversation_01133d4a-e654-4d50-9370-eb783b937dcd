<?php
error_reporting(~E_ALL);
session_start();
require 'connection.php';
require_once('database.php');
require_once('library.php');
isUser();

 date_default_timezone_set('Asia/Kolkata');
$todate = date('d/m/Y h:i:s', time());


if(isset($_POST['Submit']))
 {
	 $cname=$_POST['cname'];    $userid=$_POST['cid']; $frm=$_POST['uoffice'];$clerkname=$_POST['uaddress']; 
	 $fuel=$_POST['fuelcharge'];
	
 $date=$_POST['date2'];  $date2=$_POST['date4']; // $bill=$_POST['bilno'];
	// 
       
if($date2=='')
{
 
$Cdate2=date('Y-m-d',time());
}
else
{
 $Cdate2=date('Y-m-d',strtotime($date2));
}
 $Cdate=date('Y-m-d',strtotime($date));

if($cname!="")
{
	$sql="SELECT * FROM custreg INNER JOIN tbl_courier ON `custreg`.`custname`=`tbl_courier`.`ship_name` WHERE `custreg`.`custid`= '$cname' and tbl_courier.book_date between '$Cdate' and '$Cdate2'";
//$sql="SELECT * FROM tbl_courier WHERE `tbl_courier`.`ship_name` ='$cname' and tbl_courier.book_date between '$Cdate' and '$Cdate2'";
}

$result = mysqli_query($con,$sql);
$cntr=0;

while($row = mysqli_fetch_array($result)) 
 {
	 $cntr=$cntr+1;  
$inno=$row["invice_no"];     $cname1=$row["custname"];     $ccity=$row["custcity"];      $ccstin=$row["csttin"];
$indate=$row["book_date"];     $cadd=$row["custadd"];     $cstats=$row["custsts"];      $cpan=$row["custpan"];
$inno1=$row["invice_no"];     $cmob=$row["custphone"];     $czip=$row["custzip"];
$cctax=$row["custstax"];     $cmail=$row["custmail"];     $cvattin=$row["vattin"];



$totweight=$row['weight']+$row['chweight']+$row['volumw'];
$custcode=$custcode.'<tr><td >'.$cntr.'</td><td>0</td><td>0</td><td>0</td><td>0</td><td>'.$towe[]=$totweight.'</td><td>'.$toamt[]=$row['freight'].'</td></tr>';
 
 $tot=$row['servicecharg']+$row['oda_mis'];
$mischar=$mischar.'<tr><td >'.$cntr.'</td><td>'.$row['cons_no'].'</td><td>'.$row['type'].'</td><td>'.$trans="0".'</td><td>'.$row['servicecharg'].'</td><td>'.$riskch="0".'</td><td>'.$row['oda_mis'].'</td><td>'.$tot.'</td></tr>';

$wemode=$wemode.'<tr><td>'.$row['cons_no'].'</td><td>'.$row['book_date'].'</td><td>0</td><td>'.$row['type'].'</td><td>0</td><td>0</td><td>'.$row['inno'].'</td><td>'.$row['indate'].'</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr>';
  
 
 
 $rto=$rto.'<tr><td >'.$cntr.'</td><td>'.$row['cons_no'].'</td><td>'.$row['book_date'].'</td><td>'.$riskcharg="0".'</td><td>'.$row['desti'].'</td><td>'.$row['weight'].'</td><td>'.$row['freight'].'</td></tr>';
  
$tot1=$row['servicecharg'];
$gec=$gec.'<tr><td >'.$cntr.'</td><td>'.$row['cons_no'].'</td><td>'.$row['servicecharg'].'</td><td>'.$riskcharg="0".'</td><td>0</td><td>0</td><td>0</td><td>0</td><td>'.$tot1.'</td></tr>';

 }
 $pagetot=$pagetot.'<tr><td colspan="5">Page Total </td><td>'.$totwe=array_sum($towe).'</td><td>'.$totamt=array_sum($toamt).'</td></tr>';
 
 $pagesubtott=$pagesubtott.'<tr><td colspan="5">Sub Total </td><td>'.$tottwe=array_sum($towe).'</td><td>'.$tottamt=array_sum($toamt).'</td></tr>';
 
 $wemodetot=$wemodetot.'<tr><td colspan="4"><b>Total</b></td><td>0</td><td>0</td><td colspan="3"></td><td>0</td><td>0</td><td colspan="3"></td></tr>';
 
 echo $subtot=array_sum($sch);
 echo $subtot1=array_sum($mis);
 echo $subtot11=array_sum($tot);
$tr1=$tr1.'<tr><td colspan="7" align="right">Sub Total </td><td>'.$subtot=array_sum($tot).'</td></tr>';


//$fuelrs=$fuel;
$totf= $subtot+$sertax+$fuel;
$tr2=$tr2.'<tr><td colspan="7" align="right">Service Tax (14%) </td><td>'.$sertax.'</td></tr>';
$tr3=$tr3.'<tr><td colspan="7" align="right">Fuel Charges </td><td>'.$fuel.'</td></tr>';

$tr4=$tr4.'<tr><td colspan="4" align="center">Total </td><td>'.$totw=array_sum($we).'</td><td colspan="1"></td><td>N/A</td><td>'.$totf.'</td></tr>';
 
 
$result1 = mysqli_query($con, $sql);
$row1 = mysqli_fetch_array($result1, MYSQLI_BOTH);
          
$ship_name=$row1['ship_name'];     $custpan=$row1['custpan']; 
$s_add=$row1['s_add'];             $custin=$row1['custin']; 
$phone=$row1['phone'];             $custstax=$row1['custstax'];
$custemil=$row1['smail']; 
}

$subtot=0;

$sertax= $subtot*14 /100;

$gtot=0;
$gtot=$fuel;

mysqli_close($con);
?> 
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>
	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
<script language="JavaScript">
</script>
</head>

    <center>  <?php $_SESSION['b']=b; ?>
    <tr ><?php if(isset($_POST['submit']))  
  {
   echo '<img src="img/bill.png" />';
  }
  else
  {
  echo'<img src="img/bill.png" />';
  
  } ?></tr>
  </center>

   <div class="container">
   <div class="row">	
				    <div class="span12">
                     <div class="flexslider">
  <table border="1" width="100%" >
							<tbody>
						<tr>
							<td width="50%"> 
							<table border="0" width="100%">
							        <tr><td align="left" colspan="2"> To  &nbsp; <b><?php echo $cname1;?></b></td></tr>
									<tr><td align="left" colspan="2"> M/S. &nbsp;<b><?php echo $cname1;?></b></td></tr>
									<tr><td align="left" colspan="2"><b><?php echo $cadd;?></b></td></tr>
									<tr><td align="left" colspan="2"><b><?php echo $ccity;?>, <b><?php echo $cstats;?> - <b><?php echo $czip;?> </b></td></tr>
									<tr><td align="left" colspan="2">Mobile No : &nbsp;<b><?php echo $cmob;?></b></td></tr>
									<tr><td align="left" colspan="2">Email Id : &nbsp; <b><?php echo $cmail;?></b></td></tr>
									<tr><td align="left" colspan="2">Service Tax No : &nbsp; <b><?php echo $cctax;?></b></td></tr>
									<tr><td align="left" colspan="2">PAN No :  &nbsp;  <b><?php echo $cpan;?></b></td></tr>
									<tr><td align="left"colspan="2">VAT Tin No : &nbsp;<b><?php echo $cvattin;?></b></td></tr>
									<tr><td align="left"colspan="2" >CST Tin No :&nbsp; <b><?php echo $ccstin;?></b></td></tr>
							</table>
							
							</td>
							<td width="50%">  
							<table border="1" width="100%">
									<tr><td align="left">Invoice Period </td><td align="right"><?php echo $Cdate ;?>to<?php echo $Cdate2 ;?></td></tr>
									<tr><td align="left">Invoice No</td><td align="right"><?php echo $inno ;?></td></tr>
									<tr><td align="left">Invoice Date</td><td align="right"><?php echo $indate ;?></td></tr>
									<tr><td align="left">Service Charges</td><td align="right"><?php echo 0;?></td></tr>
									<tr><td align="left">Fuel Surcharge</td><td align="right"><?php echo $fuel;?></td></tr>
									<tr><td align="left">Sub Total</td><td align="right"><?php echo $subtot ;?></td></tr>
									<tr><td align="left">Service Tax@14% </td><td align="right"><?php echo $sertax ;?></td></tr>
									<tr><td align="left">Edu-Cess@</td><td align="right"><?php echo 0;?></td></tr>
									<tr><td align="left">SHEC@</td><td align="right"><?php echo 0 ;?></td></tr>
									<tr><td align="left">Grand Total</td><td align="right"><b><?php echo $gtot;?></b></td></tr>
							</table>  </td>
						</tr>
						<tr><td align="left" colspan="2"> Amount in words :&nbsp; - <b>
				<?php echo convert_number_to_words($gtot);?> Only </b></td> </tr>
						</tbody>
							</table>
							<table border="1" width="100%" >
							<tr> <td align="center" colspan="6"> ReliablePlus Cargo </td></tr>
						<tr> <td  align="center" colspan="6"> Payment Advice (Please detach and return with your payment) </td></tr>
						<tr> <td colspan="2">Invoice No : &nbsp;<?php echo $inno ;?>   </td><td colspan="2"> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Date : <?php echo $indate ;?></td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<td colspan="2">Client Code: &nbsp;&nbsp<b><?php echo $t;?></b> </td></tr>
<tr><td colspan="3"> <b><?php echo $frm;?></b></td> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<td colspan="3"><b><?php echo $cname1;?></b></td></tr>
<tr><td>Name of the Bank </td><td>Cheque / DD Number</td><td>Cheque / DD Date</td><td>Invoice Amount (RS.)</td><td>TDS (Rs.)</td><td>Net Amount (Rs.)</td></tr>
<tr><td><b><?php echo $t;?></b> </td><td><b><?php echo $t;?></b> </td><td><b><?php echo $t;?></b> </td><td><b><?php echo $t;?></b> </td><td><b><?php echo $t;?></b> </td><td><b><?php echo $t;?></b> </td></tr>
<tr><td colspan="6">Amount in words : &nbsp;&nbsp;<b><?php echo $t;?></b> </td></tr>
<tr><td colspan="6" align="center">Please make crossed Cheque or DD in favour of "ReliablePlus Cargo"</td></tr>
<tr><td colspan="2">Name :&nbsp; <b><?php echo $t;?></b> </td><td colspan="2">Signature : &nbsp;<b><?php echo $t;?></b> </td><td colspan="2">Date : &nbsp;<b><?php echo $todate;?></b>  </td></tr>
<tr><td colspan="6" align="center">--------------------------------------------------------------------------------------------------------</td></tr>
<tr><td colspan="6" align="center">ReliablePlus Cargo </td></tr>
<tr><td colspan="6" align="center">Bill Acknowledgement </td></tr>
<tr><td colspan="1" >Client Code: &nbsp;<b><?php echo $t;?></b>  </td><td colspan="2" >Invoice No: &nbsp;<b><?php echo $inno;?></b> </td><td colspan="2" >Invoice Date:&nbsp;<b><?php echo $indate;?></b></td><td colspan="1" >Net Amount(Rs.)&nbsp;<b><?php echo $t;?></b>  </td></tr>
<tr><td colspan="3" >Client Name:&nbsp;<b><?php echo $t;?></b>  </td><td colspan="3" > Due Date: &nbsp;&nbsp;<b><?php echo $t;?></b>    </td></tr>
<tr><td colspan="3" >Name of the Receiver&nbsp;<b><?php echo $cname1;?></b> &nbsp;&nbsp;   <br> Receiver Date:&nbsp;<b><?php echo $todate;?></b> 	</td>&nbsp;<td align="center"  colspan="3">&nbsp;&nbsp;Sign & Seal <br><b><?php echo $clerkname; ?>,&nbsp;  <?php echo $frm; ?></b> </td></tr>
</table>
<h6 align="right"> Page 1</h6></div></div></div>

			<div class="row">	
				    <div class="span12">
						<div class="flexslider">
	
				     
						<table border="1" width="100%"> <tbody><table width="100%" height="141" border="1">
  <tr>
  <td>&nbsp;Customer Code :&nbsp;<b><?php echo $cname;?></b> </td> <td>&nbsp;&nbsp; Invoice No :&nbsp;<b><?php echo $inno;?></b> &nbsp;&nbsp;</td> <td> Invoice Date :&nbsp;<b><?php echo $indate;?></b>&nbsp;&nbsp; </td> <td colspan="3"> Customer Name : &nbsp;<b><?php echo $cname1;?></b> </td> <td>Branch :&nbsp;<b><?php echo $frm;?></b></td>
  </tr>
  
  <tr>
    <td height="33">SLNO</td>
    <td >MFNO</td>
    <td>MF DATE</td>
    <td >Cn No City Exp</td>
    <td>Total Normal</td>
    <td>Total Weight</td>
    <td> Amount (Rs.)</td>
  </tr>
  <tr>
  <?php echo $custcode ;?>
  </tr>
  <tr>
   <?php echo $pagetot ;?>
  </tr>
  <tr>
    <?php echo $pagesubtott ;?>
  </tr>
</table>
</tr>
<p>&nbsp;</p><tr>
<table width="100%" height="143" border="1">
  <tr>
    <th colspan="8" scope="col">CONSIGNMENT DETAILS(VAS &amp; MIS CHARGES)</th>
  </tr>
  <tr>
    <td >Sr.No</td>
    <td >Consignment No</td>
    <td >Product </td>
    <td >Transhipment</td>
    <td >Services Charge</td>
    <td >Risk Surcharge</td>
    <td >Misc. Charge</td>
    <td>Total</td>
  </tr>
  <tr> <?php echo $mischar;?>
  </tr>
   <tr>
    <td colspan="8"><div align="left">* OTH ( Other Product )</div></td>
  </tr>
</table></tr>
<p>&nbsp;</p><tr>
<table width="100%" border="1">
  <tr>
    <th colspan="13" scope="col">CONSIGNMENT DETAILS(WEIGHT / MODE &amp; DESTINATION MISMATCH)</th>
  </tr>
  <tr>
    <td height="53"><strong>CN no</strong></td>
    <td><strong>BKG. Date</strong></td>
    <td ><strong>Dest Billed</strong></td>
    <td><strong>Product</strong></td>
    <td><strong>Bill wt</strong></td>
    <td><strong>Bill Amnt</strong></td>
    <td ><strong>Inv. No</strong></td>
    <td ><strong>Inv.Date</strong></td>
    <td><strong>Act. Dest</strong></td>
    <td><strong>Act Wt</strong></td>
    <td><strong>Act Amnt</strong></td>
    <td><strong>Diff. Amt.Bill</strong></td>
    <td><strong>Changes Narration</strong></td>
  </tr>
  <tr>
    <?php echo $wemode;?>
  </tr>
  <tr>
   <?php echo $wemodetot;?>
  </tr>
</table></tr>
<p>&nbsp;</p><tr>
<table width="100%" border="1">
  <tr>
    <th colspan="7" scope="col">Reverse RTO Charges Details</th>
  </tr>
  <tr>
    <td height="40">Sr.No</td>
    <td >Consignment</td>
    <td>Date</td>
    <td >Origin</td>
    <td>Destination</td>
    <td >Weight</td>
    <td>Amount(Rs.)</td>
  </tr>
  <tr>
    <?php echo $rto;?>
  </tr>
</table></tr>
<p>&nbsp;</p><tr>
<table width="100%" border="1">
  <tr>
    <th colspan="9">GEC Consignment Charges Details</th>
  </tr>
  <tr>
    <td>Sr.No</td>
    <td>C/N No</td>
    <td>Service Charge</td>
    <td>Risk Charge</td>
    <td>ESS Charge</td>
    <td>Warai Charge</td>
    <td>Extra Charges for Box</td>
    <td>Other Charges</td>
    <td>Total Amount</td>
  </tr>
  <tr>
    <?php echo $gec;?>
  </tr>
</table></tr>
<p>&nbsp;</p>
<tr>
<table width="100%" border="1">
  <tr>
    <th colspan="3" scope="col">Expire Consignment Charges Details</th>
  </tr>
  <tr>
    <td >Rate per C/N</td>
    <td colspan="1">Total Amount</td>
  </tr>
  <tr>
    <td colspan="3">&nbsp;</td>
  </tr>
</table></tr> <h6 align="right"> Page 2</h6></tbody></table>
					 
					 
						</div><!--end flexslider <ul class="slides"></ul>-->
                    </div></div>						
   
  
<p align="left">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>



<p align="left">  <input type="button" id="printpagebutton" onClick="printpage();" value="Print"><a id="backbutton" href="invoice.php"><input type="button" id="backbutton" onClick="closeWin();" value="Close"> </a></p>

</div>

<script type="text/javascript">
    function printpage() {
        //Get the print button and put it into a variable
        var printButton = document.getElementById("printpagebutton");
        var back=document.getElementById("backbutton");
        //Set the print button visibility to 'hidden' 
        printButton.style.visibility = 'hidden';
        back.style.visibility = 'hidden';
        //Print the page content
        window.print();
        //Set the print button to 'visible' again 
        //[Delete this line if you want it to stay hidden after printing]
        printButton.style.visibility = 'visible';
         back.style.visibility = 'visible';
    }
</script>
<script type="text/javascript">

function closeWin() {
    back.close();
}
    window.onbeforeunload = function() {
        
    }
</script>
<?php
function convert_number_to_words($number) {

    $hyphen      = '-';
    $conjunction = ' and ';
    $separator   = ', ';
    $negative    = 'negative ';
    $decimal     = ' point ';
    $dictionary  = array(
        0                   => 'Zero',
        1                   => 'One',
        2                   => 'Two',
        3                   => 'Three',
        4                   => 'Four',
        5                   => 'Five',
        6                   => 'Six',
        7                   => 'Seven',
        8                   => 'Eight',
        9                   => 'Nine',
        10                  => 'Ten',
        11                  => 'Eleven',
        12                  => 'Twelve',
        13                  => 'Thirteen',
        14                  => 'Fourteen',
        15                  => 'Fifteen',
        16                  => 'Sixteen',
        17                  => 'Seventeen',
        18                  => 'Eighteen',
        19                  => 'Nineteen',
        20                  => 'Twenty',
        30                  => 'Thirty',
        40                  => 'Fourth',
        50                  => 'Fifty',
        60                  => 'Sixty',
        70                  => 'Seventy',
        80                  => 'Eighty',
        90                  => 'Ninety',
        100                 => 'Hundred',
        1000                => 'Thousand',
        1000000             => 'Million',
        1000000000          => 'Billion',
        1000000000000       => 'Trillion',
        1000000000000000    => 'Quadrillion',
        1000000000000000000 => 'Quintillion'
    );

    if (!is_numeric($number)) {
        return false;
    }

    if (($number >= 0 && (int) $number < 0) || (int) $number < 0 - PHP_INT_MAX) {
        // overflow
        trigger_error(
            'convert_number_to_words only accepts numbers between -' . PHP_INT_MAX . ' and ' . PHP_INT_MAX,
            E_USER_WARNING
        );
        return false;
    }

    if ($number < 0) {
        return $negative . convert_number_to_words(abs($number));
    }

    $string = $fraction = null;

    if (strpos($number, '.') !== false) {
        list($number, $fraction) = explode('.', $number);
    }

    switch (true) {
        case $number < 21:
            $string = $dictionary[$number];
            break;
        case $number < 100:
            $tens   = ((int) ($number / 10)) * 10;
            $units  = $number % 10;
            $string = $dictionary[$tens];
            if ($units) {
                $string .= $hyphen . $dictionary[$units];
            }
            break;
        case $number < 1000:
            $hundreds  = $number / 100;
            $remainder = $number % 100;
            $string = $dictionary[$hundreds] . ' ' . $dictionary[100];
            if ($remainder) {
                $string .= $conjunction . convert_number_to_words($remainder);
            }
            break;
        default:
            $baseUnit = pow(1000, floor(log($number, 1000)));
            $numBaseUnits = (int) ($number / $baseUnit);
            $remainder = $number % $baseUnit;
            $string = convert_number_to_words($numBaseUnits) . ' ' . $dictionary[$baseUnit];
            if ($remainder) {
                $string .= $remainder < 100 ? $conjunction : $separator;
                $string .= convert_number_to_words($remainder);
            }
            break;
    }

    if (null !== $fraction && is_numeric($fraction)) {
        $string .= $decimal;
        $words = array();
        foreach (str_split((string) $fraction) as $number) {
            $words[] = $dictionary[$number];
        }
        $string .= implode(' ', $words);
    }

    return $string;
}

?>
</html>
