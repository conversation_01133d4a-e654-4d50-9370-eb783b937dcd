<?php
session_start();
require_once('database.php');
require_once('library.php');
require 'connection.php';

isUser();

$query = "SELECT MAX(cid) AS cid FROM tbl_courier_officers";  
    if($result = mysqli_query($con,$query))
    {
  while ($row = mysqli_fetch_assoc($result))
  {
        $count = $row['cid'];
        //$count = $count+1;

      $code_no = str_pad($count, 7, "0", STR_PAD_LEFT);
  }
	}

?>
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>

	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
	
</head>
<?php
include("header.php");
?>
     <div class="container">

			<div class="row">

				<div class="span3">
				
				</div><!--end span8-->
                
                
                <div class="span6">
					<div class="account-list-outer">

						<div class="titleHeader clearfix">
							<h3>Office Employee is Register Successfully...! </h3>
                                                       <h4>Your Employee Registration ID is : <?php  echo $count;?></h4>
						</div><!--end titleHeader-->
				
					</div><!--end -->
				</div><!--end span6-->


				
			</div><!--end row-->


		</div><!--end conatiner-->       
  

<script language="javascript">
function validate()
  {
 if (form.Consignment.value == "" )
		 {
			alert("Consignment No is required.");
			form.track.focus( );
			return false;
		}
	}
</script>	
			
<?php
include("footer.php");
?>	