<?php
session_start();
include("connection.php");

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Debug: Log that file is being accessed
error_log("🚀 download_pod_file.php accessed at " . date('Y-m-d H:i:s'));

// Check for test mode
$test_mode = isset($_GET['test']) && $_GET['test'] === 'true';
$test_month = isset($_GET['month']) ? $_GET['month'] : '2025-07';

if ($test_mode) {
    error_log("🧪 TEST MODE: Bypassing token verification");
    $month = $test_month;
    $month_parts = explode('-', $month);
    $year = $month_parts[0];
    $month_num = $month_parts[1];
    $month_names = array(
        '01' => 'January', '02' => 'February', '03' => 'March', '04' => 'April',
        '05' => 'May', '06' => 'June', '07' => 'July', '08' => 'August',
        '09' => 'September', '10' => 'October', '11' => 'November', '12' => 'December'
    );
    $month_name = $month_names[$month_num] . ' ' . $year;

    // Skip to data fetching
    goto data_fetching;
}

// Get token and filter from URL
$token = isset($_GET['token']) ? $_GET['token'] : '';
$data_filter = isset($_GET['filter']) ? $_GET['filter'] : 'delivered';

if (empty($token)) {
    error_log("❌ No token provided");
    die('Invalid download token');
}

// Validate data filter
if (!in_array($data_filter, ['delivered', 'all'])) {
    $data_filter = 'delivered'; // Default to delivered
}

error_log("🔑 Token received: " . $token);

// Verify token and get download details
$sql = "SELECT * FROM pod_downloads WHERE download_token = '$token' AND expires_at > NOW()";
error_log("🔍 Token verification query: " . $sql);

$result = mysqli_query($con, $sql);

if (!$result) {
    error_log("❌ Token query failed: " . mysqli_error($con));
    die('Database error during token verification');
}

if (mysqli_num_rows($result) == 0) {
    error_log("❌ No valid token found or token expired");
    // Check if token exists but expired
    $expired_check = "SELECT * FROM pod_downloads WHERE download_token = '$token'";
    $expired_result = mysqli_query($con, $expired_check);
    if ($expired_result && mysqli_num_rows($expired_result) > 0) {
        $expired_data = mysqli_fetch_assoc($expired_result);
        error_log("⏰ Token exists but expired. Expires at: " . $expired_data['expires_at']);
        die('Download token has expired');
    } else {
        error_log("🔍 Token not found in database");
        die('Invalid download token');
    }
}

$download_record = mysqli_fetch_assoc($result);
$month = $download_record['month'];
$month_name = $download_record['month_name'];

error_log("✅ Token verified. Month: $month, Month Name: $month_name");

data_fetching:
// Parse month (format: YYYY-MM)
$month_parts = explode('-', $month);
$year = $month_parts[0];
$month_num = $month_parts[1];

// Debug: Check database connection
if (!$con) {
    error_log('❌ Database connection failed: ' . mysqli_connect_error());
    die('Database connection failed');
}

// Debug: Check if table exists
$table_check = "SHOW TABLES LIKE 'tbl_courier'";
$table_result = mysqli_query($con, $table_check);
if (mysqli_num_rows($table_result) == 0) {
    error_log('❌ Table tbl_courier does not exist');
    die('Table tbl_courier does not exist');
}

error_log('✅ Database connected and table exists');

// Query using actual tbl_courier field names with proper date formatting
$sql = "SELECT
            c.cons_no as ConsignmentNo,
            DATE_FORMAT(c.book_date, '%d-%m-%Y') as book_date,
            c.partno as PartNo,
            c.noofpackage as noofpackages,
            c.qty as Qnty,
            c.weight as Weight,
            c.status,
            DATE_FORMAT(c.status_date, '%d-%m-%Y') as delivery_date,
            c.rev_name as receiver_name,
            c.ship_name as sender_name,
            c.s_add as sender_address,
            c.phone as sender_phone,
            c.r_add as receiver_address,
            c.r_phone as receiver_phone,
            c.remark as delivery_remarks,
            c.invice_no as invoice_no,
            c.invi_value as invoice_value,
            c.book_mode,
            c.freight,
            c.gtotamt as grand_total,
            c.e_waybill,
            c.vehicle,
            c.clerkname as staff_name,
            c.clerkcon as staff_contact
        FROM tbl_courier c
        WHERE YEAR(c.book_date) = '$year'
        AND MONTH(c.book_date) = '$month_num'";

// Add status filter based on data_filter parameter
if ($data_filter === 'delivered') {
    $sql .= " AND (c.status = '5' OR c.status = 'Delivered' OR c.status = 'delivered' OR c.status = 'DELIVERED'
                  OR c.status = '6' OR c.status = 'Complete' OR c.status = 'Completed'
                  OR LOWER(c.status) LIKE '%deliver%' OR LOWER(c.status) LIKE '%complete%')";
}

$sql .= " ORDER BY c.book_date DESC";

// Debug: Log the actual query being executed
error_log("🔍 POD Query for $month_name: " . $sql);

$result = mysqli_query($con, $sql);

if (!$result) {
    error_log('❌ Database query failed: ' . mysqli_error($con));
    die('Database error: ' . mysqli_error($con));
}

$records = array();
$row_count = 0;
while ($row = mysqli_fetch_assoc($result)) {
    $records[] = $row;
    $row_count++;

    // Debug: Log first few records
    if ($row_count <= 3) {
        error_log("📋 Sample Record $row_count: " . json_encode($row));
    }
}

// Enhanced debug logging
error_log('📊 POD Download - Month: ' . $month_name . ', Records found: ' . count($records));
error_log('📊 Query executed: ' . $sql);
error_log('📊 Year: ' . $year . ', Month: ' . $month_num);

// Additional debugging - compare with total records for the month
$total_check_sql = "SELECT COUNT(*) as total_all,
                           COUNT(CASE WHEN (c.status = '5' OR c.status = 'Delivered' OR c.status = 'delivered' OR c.status = 'DELIVERED'
                                           OR c.status = '6' OR c.status = 'Complete' OR c.status = 'Completed'
                                           OR LOWER(c.status) LIKE '%deliver%' OR LOWER(c.status) LIKE '%complete%') THEN 1 END) as total_delivered
                    FROM tbl_courier c
                    WHERE YEAR(c.book_date) = '$year' AND MONTH(c.book_date) = '$month_num'";

$total_result = mysqli_query($con, $total_check_sql);
if ($total_result) {
    $total_data = mysqli_fetch_assoc($total_result);
    error_log('📊 Total records in month: ' . $total_data['total_all'] . ', Delivered: ' . $total_data['total_delivered']);
    error_log('📊 Filter applied: ' . $data_filter . ', Records fetched: ' . count($records));

    if ($data_filter === 'delivered') {
        error_log('📊 Expected delivered records: ' . $total_data['total_delivered'] . ', Actually fetched: ' . count($records));
    } else {
        error_log('📊 Expected all records: ' . $total_data['total_all'] . ', Actually fetched: ' . count($records));
    }
}

if (empty($records)) {
    // Additional debugging - check what data exists
    $debug_check = "SELECT COUNT(*) as total,
                           GROUP_CONCAT(DISTINCT status) as statuses,
                           MIN(book_date) as min_date,
                           MAX(book_date) as max_date
                    FROM tbl_courier
                    WHERE YEAR(book_date) = '$year' AND MONTH(book_date) = '$month_num'";

    $debug_result = mysqli_query($con, $debug_check);
    if ($debug_result) {
        $debug_data = mysqli_fetch_assoc($debug_result);
        error_log('🔍 Debug Check - Total: ' . $debug_data['total'] .
                  ', Statuses: ' . $debug_data['statuses'] .
                  ', Date Range: ' . $debug_data['min_date'] . ' to ' . $debug_data['max_date']);
    }

    error_log('❌ No delivered shipments found for ' . $month_name);
    die('No delivered shipments found for ' . $month_name . '. Check error logs for debug information.');
}

// Generate filenames with timestamp (like demo.php)
$timestamp = date('Y-m-d_H-i-s');
$csv_filename = "POD_Data_{$month_name}_{$timestamp}.csv";
$zip_filename = "POD_Data_{$month_name}_{$timestamp}.zip";

// Clean filenames (remove spaces and special characters)
$csv_filename = preg_replace('/[^a-zA-Z0-9._-]/', '_', $csv_filename);
$zip_filename = preg_replace('/[^a-zA-Z0-9._-]/', '_', $zip_filename);

// Create CSV file (like demo.php)
$csv_file = fopen($csv_filename, 'w');
if (!$csv_file) {
    error_log('❌ Failed to create CSV file');
    die('Failed to create CSV file');
}

// Write headers (like demo.php but with our custom headers)
$headers = array(
    'Consignment No',
    'Book Date (DD-MM-YYYY)',
    'Part No',
    'No of Packages',
    'Quantity',
    'Weight (KG)',
    'Status',
    'Delivery Date (DD-MM-YYYY)',
    'Receiver Name',
    'Sender Name',
    'Sender Address',
    'Sender Phone',
    'Receiver Address',
    'Receiver Phone',
    'Delivery Remarks',
    'Invoice No',
    'Invoice Value',
    'Booking Mode',
    'Freight Amount',
    'Grand Total',
    'E-Way Bill',
    'Vehicle No',
    'Staff Name',
    'Staff Contact'
);
fputcsv($csv_file, $headers);

// Write data rows (like demo.php)
foreach ($records as $row) {
    $csv_row = array(
        $row['ConsignmentNo'] ?? '',
        $row['book_date'] ?? '',
        $row['PartNo'] ?? '',
        $row['noofpackages'] ?? '',
        $row['Qnty'] ?? '',
        $row['Weight'] ?? '',
        $row['status'] ?? '',
        $row['delivery_date'] ?? '',
        $row['receiver_name'] ?? '',
        $row['sender_name'] ?? '',
        $row['sender_address'] ?? '',
        $row['sender_phone'] ?? '',
        $row['receiver_address'] ?? '',
        $row['receiver_phone'] ?? '',
        $row['delivery_remarks'] ?? '',
        $row['invoice_no'] ?? '',
        $row['invoice_value'] ?? '',
        $row['book_mode'] ?? '',
        $row['freight'] ?? '',
        $row['grand_total'] ?? '',
        $row['e_waybill'] ?? '',
        $row['vehicle'] ?? '',
        $row['staff_name'] ?? '',
        $row['staff_contact'] ?? ''
    );
    fputcsv($csv_file, $csv_row);
}
fclose($csv_file);

// Check if ZIP extension is available (like demo.php)
if (class_exists('ZipArchive')) {
    // Create ZIP archive (like demo.php)
    $zip = new ZipArchive();
    if ($zip->open($zip_filename, ZipArchive::CREATE) === TRUE) {
        $zip->addFile($csv_filename);
        $zip->close();

        // Send ZIP to browser (like demo.php)
        header('Content-Type: application/zip');
        header('Content-Disposition: attachment; filename="' . basename($zip_filename) . '"');
        header('Content-Length: ' . filesize($zip_filename));
        readfile($zip_filename);

        // Clean up temporary files (like demo.php)
        unlink($csv_filename);
        unlink($zip_filename);

        // Mark as downloaded
        $update_sql = "UPDATE pod_downloads SET downloaded = 1 WHERE download_token = '$token'";
        mysqli_query($con, $update_sql);

        error_log("✅ ZIP file created and downloaded: $zip_filename");
        exit;
    } else {
        error_log("❌ Failed to create ZIP file");
        $error = "Failed to create ZIP file";
    }
} else {
    // Fallback: Send CSV directly if ZIP not available (like demo.php but with better UI)
    $error = "ZIP extension not available. Please enable ZIP extension in PHP or download CSV directly.";
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px; border-radius: 5px;'>";
    echo "<h3>⚠️ ZIP Extension Not Available</h3>";
    echo "<p>The ZIP extension is not enabled in PHP.</p>";
    echo "<p><strong>To enable ZIP:</strong></p>";
    echo "<ol>";
    echo "<li>Open XAMPP Control Panel</li>";
    echo "<li>Click 'Config' next to Apache</li>";
    echo "<li>Select 'PHP (php.ini)'</li>";
    echo "<li>Find ';extension=zip' and remove the semicolon</li>";
    echo "<li>Save and restart Apache</li>";
    echo "</ol>";
    echo "<p><a href='check_zip.php' target='_blank' style='background: #17a2b8; color: white; padding: 8px 16px; text-decoration: none; border-radius: 3px; margin-right: 10px;'>Check ZIP Status</a>";
    echo "<a href='$csv_filename' download style='background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 3px;'>Download CSV Instead</a></p>";
    echo "</div>";

    // Mark as downloaded even if ZIP failed
    $update_sql = "UPDATE pod_downloads SET downloaded = 1 WHERE download_token = '$token'";
    mysqli_query($con, $update_sql);
    
    exit;
}
?>