<?php
session_start();
include("connection.php");
require_once('database.php');
require_once('library.php');
isUser();

$sql = "SELECT COUNT(status) FROM tbl_courier
		WHERE status = 'Delivered'";
$result=mysql_query($sql) or die(mysql_error());
while($row = mysql_fetch_array($result)){
	
$deliver=$row['COUNT(status)'];
	
 }
$sql1 = "SELECT COUNT(status) FROM tbl_courier
		WHERE status = 'Onhold'";
$result1=mysql_query($sql1) or die(mysql_error());
while($row1 = mysql_fetch_array($result1)){
	
$onhold=$row1['COUNT(status)'];
	
 }
 
 $sql2 = "SELECT COUNT(status) FROM tbl_courier
		WHERE status = 'In Transit'";
$result2=mysql_query($sql2) or die(mysql_error());
while($row2 = mysql_fetch_array($result2)){
	
$transit=$row2['COUNT(status)'];
	
 }

 $sql3 = "SELECT COUNT(status) FROM tbl_courier
		WHERE status = 'Landed'";
$result3=mysql_query($sql3) or die(mysql_error());
while($row3 = mysql_fetch_array($result3)){
	
$land=$row3['COUNT(status)'];
	
 }
 
 /*$sql4 = "SELECT COUNT(status) FROM tbl_courier
		WHERE status = 'Delayed'";
$result4=mysql_query($sql4) or die(mysql_error());
while($row4= mysql_fetch_array($result4)){
	
	echo $deliver=$row4['COUNT(status)'];
	
 }*/
?>
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>

	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
	
</head>
<?php
include("header.php");
?>
		<div class="container">
			<div class="row">
     			<div class="span2">
				</div><!--end span2-->
                
                <div class="span8" align="center">
                                  
<h2> Welcome To </h2>  <h1> ReliablePlus Cargo </h1>
				
				</div><!--end span8-->
			</div><!--end row-->
		</div><!--end conatiner-->

<?php
include("footer.php");
?>
		