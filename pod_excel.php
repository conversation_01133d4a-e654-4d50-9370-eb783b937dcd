<?php
set_time_limit(300);
ini_set('memory_limit', '512M');
session_start();
include("connection.php");
require_once('database.php');
require_once('library.php');
ob_start();

$date = $_GET['strdate'];
$date2 = $_GET['date2'];
$id = isset($_GET['id']) ? $_GET['id'] : '';

$Cdate = date('Y-m-d', strtotime($date));
$Cdate1 = date('Y-m-d', strtotime($date2));

$tr = "";
$count = 0;

// Add POD filter based on report type selection
$podFilter = "";
if ($id == "uploaded") {
    $podFilter = " AND tc.pod_imag_path IS NOT NULL AND tc.pod_imag_path != ''";
} elseif ($id == "pending") {
    $podFilter = " AND (tc.pod_imag_path IS NULL OR tc.pod_imag_path = '')";
}

// Optimized query: join all needed tables and select all required fields
$sql = "SELECT
    tc.userid, tc.pod_imag_path, tc.cons_no, tc.weight, tc.gtotamt, tc.rev_name, tc.r_add, tc.ship_name, tc.rate, tc.oda_mis, tc.status_date,
    tc.book_mode, tc.noofpackage, tc.e_waybill, tc.eway_start_date, tc.eway_end_date, tc.status, tc.eway_expdate, tc.gst, tc.partno, tc.remark, tc.freight,
    tc.invi_value, tc.qty, tc.assured_dly_date, tc.book1_date, a.city_name as city, tc.type, tc.invice_no, tc.chweight, tc.mode, s.statusname, tc.vehicle,
    co.Manager_name, co.empcode,
    cr.custname, cr.custphone, cr.custmail, cr.custadd, cr.custcity,
    dest.city_name as destination_city
FROM tbl_courier tc
INNER JOIN status s ON tc.status = s.statusid
JOIN tbl_city_code a ON tc.s_add = a.Id
JOIN tbl_courier_officers co ON co.cid = tc.userid
LEFT JOIN custreg cr ON cr.custname = tc.ship_name
LEFT JOIN tbl_city_code dest ON tc.r_add = dest.Id
WHERE (tc.book_date BETWEEN '$Cdate' AND '$Cdate1') $podFilter
ORDER BY tc.cons_no DESC";

$result = mysqli_query($con, $sql);

while ($row = mysqli_fetch_array($result)) {
    $shipper_name = $row['custname'] ?? '';
    $shipper_phone = $row['custphone'] ?? '';
    $shipper_mail = $row['custmail'] ?? '';
    $shipper_add = $row['custadd'] ?? '';
    $shipper_city = $row['custcity'] ?? '';

    $statusdate = ($row['status'] == '6' || $row['status'] == '50' || $row['status'] == '57') ? $row['status_date'] : '';
    $delivereddate = ($row['status'] == '5') ? $row['status_date'] : '';
    $pod_status = (!empty($row['pod_imag_path'])) ? 'Uploaded' : 'Pending';

    $destination = $row['destination_city'] ?? '';
    $Manager_name = $row['Manager_name'] ?? '';
    $empcode = $row['empcode'] ?? '';

    $invi_value = $row['invi_value'] ?? 0;
    $withoutgstvalue = ($invi_value > 0) ? round($invi_value / 1.18, 2) : 0;

    $count++;
    $total = floatval($row['rate']) + floatval($row['oda_mis']) + floatval($row['freight']) + floatval($row['gst']);

    $tr .= "<tr>
        <td>".$count."</td>
        <td>".$pod_status."</td>
        <td>".$Manager_name."</td>
        <td>".$empcode."</td>
        <td>".$shipper_name."</td>
        <td>".$shipper_phone."</td>
        <td>".$shipper_add."</td>
        <td>".$shipper_mail."</td>
        <td>".$shipper_city."</td>
        <td>".$row['status_date']."</td>
        <td>".$row['cons_no']."</td>
        <td>".$row['book1_date']."</td>
        <td>".$row['invice_no']."</td>
        <td>".$row['rev_name']."</td>
        <td>".$destination."</td>
        <td>".$row['noofpackage']."</td>
        <td>".$row['qty']."</td>
        <td>".$row['partno']."</td>
        <td>".$withoutgstvalue."</td>
        <td>".$invi_value."</td>
        <td>".$statusdate."</td>
        <td>".$row['type']."</td>
        <td>".$row['statusname']."</td>
        <td>".$row['remark']."</td>
        <td>".$delivereddate."</td>
        <td>".$row['mode']."</td>
        <td>".$row['weight']."</td>
        <td>".$row['chweight']."</td>
        <td>".$row['e_waybill']."</td>
        <td>".$row['eway_start_date']."</td>
        <td>".$row['eway_end_date']."</td>
        <td>".$row['vehicle']."</td>
        <td>".$row['rate']."</td>
        <td>".$row['oda_mis']."</td>
        <td>".$row['freight']."</td>
        <td>".$row['gst']."</td>
        <td>".$total."</td>
    </tr>";
}

header("Content-Type: application/vnd.ms-excel; charset=utf-8");
header("Content-Disposition: attachment; filename=pod_Report.xls");
header("Expires: 0");
header("Cache-Control: must-revalidate");
header("Pragma: public");

echo '<table border="1" style="width:100%">
    <thead>
        <tr>
            <th>Sr No.</th>
            <th>Status</th>
            <th>Employee Name</th>
            <th>Emp ID</th>
            <th>Shipper Name</th>
            <th>Shipper Phone</th>
            <th>Shipper Address</th>
            <th>Shipper Email</th>
            <th>Shipper City</th>
            <th>Scanning Date</th>
            <th>LR No</th>
            <th>BKG Date</th>
            <th>Invoice No</th>
            <th>Customer Name</th>
            <th>Destination</th>
            <th>Cases</th>
            <th>Qty</th>
            <th>Part No.</th>
            <th>Without GST Value</th>
            <th>Invoice Value</th>
            <th>Godown Receipt Date</th>
            <th>Type</th>
            <th>Remarks</th>
            <th>My Remarks</th>
            <th>Delivery Date</th>
            <th>Mode</th>
            <th>A/Weight</th>
            <th>C/Weight</th>
            <th>E WayBill No.</th>
            <th>E Way Start Date</th>
            <th>E Way End Date</th>
            <th>Vehicle No.</th>
            <th>Rate</th>
            <th>ODA</th>
            <th>Freight</th>
            <th>GST</th>
            <th>Total</th>
        </tr>
    </thead>
    <tbody>
        '.$tr.'
    </tbody>
</table>';
?>