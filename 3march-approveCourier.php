<?php
session_start();
require_once('database.php');
require_once('library.php');
isUser();

$num_rec_per_page=10;
if (isset($_GET["page"])) { $page  = $_GET["page"]; } else { $page=1; }; 
$start_from = ($page-1) * $num_rec_per_page; 

$a=$_SESSION['username'];
 $sql="select * from login where username='$a'";
$result = mysqli_query($con, $sql);
$row = mysqli_fetch_array($result, MYSQLI_BOTH);
   $id=$row['rid'];
   $userid=$_SESSION['desgn'];





	if(isset($_POST['search'])){
	    $valueToSearch= $_POST['consno'];
	    $sql="SELECT cid, cons_no, ship_name, rev_name, book1_date, statusname,st
		FROM tbl_courier inner join status on tbl_courier.status=status.statusid  
		WHERE  `cons_no` LIKE '%$valueToSearch%' OR `st` LIKE '%$valueToSearch%'";
	    //$sql="select cons_no,ship_name,rev_name,book1_date,statusname from tbl_courier inner join status on tbl_courier.status=status.statusid where CONCAT(cons_no,ship_name,rev_name,book1_date,statusname)LIKE '%.$valueToSearch.%'  ";
	   $result = mysqli_query($con,$sql);
	    
	
	

}else{
       $sql = "SELECT cid, cons_no, ship_name, rev_name, book1_date, statusname,st
		FROM tbl_courier  inner join status on tbl_courier.status=status.statusid
		
		ORDER BY cid DESC 
		LIMIT $start_from, $num_rec_per_page";

$result = mysqli_query($con,$sql);	

	}

?>

<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>
	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
<script language="JavaScript">
var checkflag = "false";

function check(field) {
if (checkflag == "false")
 {
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll")
	{
	field[i].checked=true;	
	}
	}
	checkflag = "true";
}
else
{
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll")
	{
	field[i].checked=false;
	}
	}
	checkflag = "false";
}

}
function confirmDel(field,msg)
{
	count=0;
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll" && field[i].checked==true)
	{
	count++;
	}
	}
	
	if(count == 0)
	{
		alert("Select any one record to delete!");
		return false;
	}
	else
	{
		return confirm(msg);
	}
}
</script>
</head>
<?php include("header.php"); ?>
		<div class="container">
			<div class="row">
               <div class="span11">
					<div class="account-list-outer">

						<div class="titleHeader clearfix">
							<h3>Approve Shipment </h3>
						</div><!--end titleHeader-->
	<form method="post" action="approveCourier.php" class="form-horizontal">
 <div class="control-group success">
							    <label class="control-label" for="custzip">Docket No.: </label>
							    
							    <div class="controls">
							      <input type="text" name="consno"  id="consno" placeholder="Enter Docket No.">
							       
							      	<input name="search" class="btn btn-primary" type="submit" value="Search">
							   
							   </div>
							</div>
								
							</form>
								
						<table class="table">
						<thead>
							<tr>
								<th><h5>Consignment No </h5></th>
								<th><h5>Shipper Name</h5></th>
								<th><h5>Receiver Name</h5></th>
								<th><h5>Book Date</h5></th> &nbsp;  &nbsp;  &nbsp;  &nbsp;  &nbsp;  &nbsp; 
								<th><h5>Status</h5></th> &nbsp;  &nbsp;  &nbsp;  &nbsp; 
								<th><h5>Approve Status</h5></th>&nbsp;  &nbsp;
								<th><h5>Action</h5></th>
							</tr>
						</thead>
						<?php
							 while($data = mysqli_fetch_array($result)){
							 extract($data);
							 $date2=$data['book1_date'];
							 $date1=date('d-m-Y h:i:s', strtotime($date2));
						 ?>
						<tbody>
							<tr>
								<td class="desc">
									<?php echo $cons_no; ?>
								</td>
								<input type="hidden" name="consno" id="consno" value="$cons_no">
								<td>
									<?php echo $ship_name; ?>
								</td>
								<td>
								<?php echo $rev_name; ?>
								</td>
								<td>
									<?php echo $book1_date; ?> &nbsp; &nbsp; &nbsp;  &nbsp;  &nbsp; 
								</td>
									<td>
									<?php echo $statusname; ?> &nbsp;  &nbsp;  &nbsp;  &nbsp; 
								</td>
								
								<td>
									<?php echo $st; ?> &nbsp;  &nbsp;  &nbsp;  &nbsp; 
								</td>
                                <td>
                                    <?php if($userid=='1'){
                                        
                                     ?>
                                    <a href="approveupdate.php?cid=<?php echo $cid; ?>">
									<button class="btn btn-small btn-primary" data-title="To Edit" data-placement="top" rel="tooltip" value="Edit" ><i class="">APPROVE</i></button></a>
									<?php } ?>
									<a href="editshipment.php?cid=<?php echo $cid; ?>"><button class="btn btn-small btn-primary" data-title="To Edit" data-placement="top" rel="tooltip" value="Edit" ><i class="">EDIT</i></button></a>
									<!--<button class="btn btn-small btn-danger" data-title="Remove" data-placement="top" rel="tooltip"><i class="icon-trash"></i></button>-->
								</td>
							</tr>
						</tbody>
						<?php	}//while ?>
					</table>
					</div><!--end -->
				</div><!--end span-->
			</div><!--end row-->
		</div><!--end conatiner--> 
		<div class="pagination">
      <ul>
        <li><a href="#">Prev</a></li>
        <li><a href="#">1</a></li>
        <li><a href="#">2</a></li>
        <li><a href="#">3</a></li>
        <li><a href="#">4</a></li>
        <li><a href="#">Next</a></li>
      </ul>
    </div><?php
											
								$msg=$_GET['msg'];
								if($msg=="yes1")
								{
						echo "<script> alert('Docket Approved Successfully');</script>";
								}
								else if($msg=="no1"){
						echo "<script> alert('Docket Not Approved');</script>";
								}
								?>   
	<div class="pagination" align="center">
      <ul>
       <?php if($currentPage != $firstPage) { ?>
<li class="page-item">
<a class="page-link" href="?page=<?php echo $firstPage ?>" tabindex="-1" aria-label="Previous">
<span aria-hidden="true">Previous</span>
</a>
</li>
<?php } ?>
<?php if($currentPage >= 2) { ?>
<li class="page-item"><a class="page-link" href="?page=<?php echo $previousPage ?>"><?php echo $previousPage ?></a></li>
<?php } ?>
<li class="page-item active"><a class="page-link" href="?page=<?php echo $currentPage ?>"><?php echo $currentPage ?></a></li>
<?php if($currentPage != $lastPage) { ?>
<li class="page-item"><a class="page-link" href="?page=<?php echo $nextPage ?>"><?php echo $nextPage ?></a></li>
<li class="page-item">
<a class="page-link" href="?page=<?php echo $lastPage ?>" aria-label="Next">
<span aria-hidden="true">Next</span>
</a>
</li>
<?php } ?>
      </ul>
    </div>