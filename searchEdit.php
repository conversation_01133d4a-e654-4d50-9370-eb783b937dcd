<?php
session_start();
require_once('database.php');
require_once('library.php');
isUser();
?>

<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>

	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
<script language="javascript">
function validate()
  {
 if (document.form1.track.value == "" )
		 {
			alert("Consignment No is required.");
			document.form1.track.focus( );
			return false;
		}
	}
</script>
	
</head>

<?php include("header.php"); ?>

		<div class="container">
             <div class="row">
            	 <div class="span2">
				 </div>
             <div class="span9">
				<form action="new_track_submit.php" method="post" name="form1" id="form1" onSubmit="return validate()"></form>
					<div class="account-list-outer" align="center">
						<div class="titleHeader clearfix">
							<h3>Enter Docket Number </h3>
						</div><!--end titleHeader-->
						
						<h4> Key in the Shipment Number to MODIFY the data. This is helpful if you have made spelling errors while adding the shipment.</h4>
						 
						     <!-- <form action="trackConsi.php" method="post">-->
							 <form action="newtracker1.php" method="post">
							   <div class="control-group" align="center">
							      <h3 >Enter Docket number : <input type="text" name="Consignment" placeholder="Enter Docket number"/><span class="text-error">*</span> </h3><br>
							       <button type="submit" name="Submit" class="btn btn-primary">Search </button>	
							   </div><!--end control-group-->
								
							</form>
					</div><!--end -->
			 </div><!--end span-->
			</div><!--end row-->
		</div><!--end conatiner-->

<?php include("footer.php"); ?>