<?php
session_start();
include("connection.php");
require_once('database.php');
require_once('library.php');
isUser();

  $date=$_POST['date2'];

  $date2=$_POST['date4'];

$custid=$_POST['rep'];
//$statusid="All";

//$prospect=$_GET['prospect'];





 $Cdate=date('Y-m-d',strtotime($date));


 //$sql="SELECT * FROM tbl_courier inner join status on tbl_courier.status=status.statusid WHERE tbl_courier.shipper_code ='$custid' and tbl_courier.book1_date between '$date' and '$date2'   ";


  $sql = "SELECT cons_no,weight,gtotamt,rev_name,ship_name,rate,noofpackage,gst,partno,freight,invi_value,qty,assured_dly_date,book1_date,a.city_name as city ,tbl_city_code.city_name,type,invice_no,chweight,mode,statusname FROM (tbl_courier inner join status on tbl_courier.status=status.statusid) join tbl_city_code on tbl_city_code.Id= tbl_courier.r_add join tbl_city_code a on tbl_courier.s_add=a.Id WHERE tbl_courier.shipper_code ='$custid' and tbl_courier.book1_date between '$date' and '$date2' ORDER BY cons_no DESC ";
//$sql = "SELECT cons_no, ship_name, rev_name,STATUS , r_add,TYPE , weight, invice_no,MODE , book_mode FROM tbl_courier WHERE STATUS = 5 ORDER BY cons_no DESC LIMIT $start_from, $num_rec_per_page";
$result = mysqli_query($con,$sql);	
$count=0;
$result = dbQuery($sql);	
$tr=$tr. "<tr><td>".$count."</td><td>".$row['book1_date']."</td><td>".$row['book1_date']."</td><td><a href='details.php?id=".$row['cons_no']."'>".$row['cons_no']."</a></td><td>".$row['ship_name']."</td><td>".$row['city']."</td><td>".$row['invice_no']."</td><td>".$row['invi_value']."</td><td>".$row['rev_name']."</td><td>".$row['city_name']."</td><td>".$row['noofpackage']."</td><td>".$row['qty']."</td><td>".$row['weight']."</td><td>".$row['chweight']."</td><td>".$row['partno']."</td><td>".$row['statusname']."</td><td>".$row['type']."</td><td>".$row['mode']."</td><td>".$row['assured_dly_date']."</td><td>".$row['freight']."</td><td>".$row['gst']."</td><td>".$row['gtotamt']."</td></tr>";

	
?>
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>
	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	
<style type = "text/css">
.row{margin-left:0px!important;}
table{border:none!important;}
table tr, table tr td, table tr td table tr, table tr td table tr td{border-collapse: collapse;}
@media print {
.row{margin-left:0px!important;}
.remove {margin-left: 0px;}}

.remove {margin-left: 10px; color: red;}
.remove:hover{cursor: pointer;}
body{margin:0 auto; 
    width:980px;
}
</style>


</head>
    
<body>
 <div style="margin-top:20px;">
   <div>
       <center>
          <form action="invoicebillIn.php" method="post" name="myform">     
<table style="margin:0 auto; width:100%; border:1px solid #000; border-collapse:collapse;">

        
<!--header--->
<tr><td colspan="3"> Copy Heading</td></tr>    

<tr>
<td width="20%"><center><img src="img/logo.png" width="60%" /></center></td>   
<td width="60%" style="text-align:center;">
     <h5><b><font size="4">Vivanta Logistics Private. Limited</font></b> </h5>
  <b>CIN: U74999PN2017PTC172759</b></br>
  <b>Registered Address</b>: Bungalow No.-7,samata Hsg.Soc,behind MSEB Colony,Bhosale Nagar,</br> Pune-411007
   Customer Care No.-18003131944
</td>
<td width="20%"></td>
</tr>

<tr><td colspan="3" width="100%"> <center>Billing Details</center></br>
<p>Name</p>
<p>Address</p>
<p>GSTIN</p>
</td></tr>


<tr><td colspan="3"> 
<table style="margin:0 auto; width:100%; border-collapse:collapse;" border="1">
<thead>
    <tr>
<th>Sr. No.</th>
           <th>Date</th>
           <th>Docket No.</th>
           <th>Destin.</th>
           <th><span id="Weight" class="remove remove-col">Weight</span></th>
           <th><span id="Rate" class="remove remove-col">Rate</span></th>
           <th><span id="Freight" class="remove remove-col">Freight</span> </th>
           <th><span id="invalue" class=" remove remove-col">Invoice Value</span></th>
           <th><span id="invoiceno" class="remove remove-col">Invoice No.</span></th>
           <th><span id="othercharges" class=" remove remove-col">Other Charges</span> </th>
           <th><span id="delichg" class="remove remove-col">Delivery Charges</span></th>
           <th><span id="topaychg" class=" remove remove-col">ToPay Charges</span></th>
           <th><span id="rov" class="remove remove-col">Rov</span></th>
           <th><span id="fov" class=" remove remove-col">FOV</span> </th>
           <th><span id="oda" class="remove remove-col">ODA</span></th>
           <th><span id="igst" class="remove remove-col">IGST 18%</span></th>
           <th><span id="sgst" class="remove remove-col">SGST</span></th>
           <th><span id="cgst" class="remove remove-col">CGST</span></th>
           <th><span id="total" class="remove remove-col">Total</span></th>
</tr>   
</thead>     
<tbody>
    <tr>
          <td><?php echo $count;?></td>
          <td><?php echo $book1_date; ?></td>
          <td><?php echo $cons_no; ?></td>
		  <td><?php echo $city; ?></td>
		  <td><?php echo $chweight; ?></td>
		  <td><?php echo $rate; ?></td>
		  <td><?php echo $freight; ?></td>
		  <td><?php echo $invi_value; ?></td>
		  <td><?php echo $invice_no; ?>	</td>
		  <td><?php echo $other_charges; ?></td>				
		  <td><?php echo $delivery_charge; ?></td>					
		  <td><?php echo $topay_charge; ?></td>
		  <td><?php echo $aftcharge; ?>	</td>
		  <td><?php echo $cons_no; ?></td>
		   <td><?php echo $cons_no; ?></td>
		    <td><?php echo $cons_no; ?></td>
	       <td><?php echo $esscharge; ?></td> 	
			<td><?php echo $cons_no; ?>	</td>
			<td><?php echo $cons_no; ?></td>
</tr>   
</tbody>
</table>
</td></tr> 


<tr><td colspan="3"> 
Notes:-</br>
  1.   Please Pay as per due date given in this Logistics Services Invoice.:-</br>
  2.  Please pay by cheque only in favour of "Vivanta Logistics Private Limited"</br>
  3.  Permanent Account Number(PAN):-**********</br>
  4.  GSTN:-27**********1Z5</br>
  5.  Corporate Identity Number:-U74999PN2017PTC172759</br>
  6.  Invoice Queries,please mail <NAME_EMAIL></br>
  7.  Request you to please pay on time to avoid disruption in service/late payment fees.</br>
  8.  All disputes are subject to pune jirisdiction.</br>
  9.  TDS to be deducted as per provision of section 194C</br>
  10.  Please email TDS <NAME_EMAIL></br>
</td></tr>

<tr><td colspan="3"> 
Bank Details:-
  1.   Please Pay as per due date given in this Logistics Services Invoice.:-
  2.  Please pay by cheque only in favour of "Vivanta Logistics Private Limited"
  3.  Permanent Account Number(PAN):-**********
  4.  GSTN:-27**********1Z5
  5.  Corporate Identity Number:-U74999PN2017PTC172759
  6.  Invoice Queries,please mail <NAME_EMAIL>
  7.  Request you to please pay on time to avoid disruption in service/late payment fees.
  8.  All disputes are subject to pune jirisdiction.
  9.  TDS to be deducted as per provision of section 194C
  10.  Please email TDS <NAME_EMAIL>
</td></tr>

  <!--end header---> 



</table>


<div class="control-group">
							    <div class="controls">
									<?php  if(isset($_GET['cust'])){ ?>
									<input name="update" class="btn btn-primary" type="submit" value="Update" onClick="return validation()"> <?php }else {?>
									<input name="submit" class="btn btn-primary" type="submit" value="Submit" onClick="return validation()"> <?php }?>
								
									<input type="button" id="printpagebutton" onClick="printpage();" value="Print" class="btn btn-primary">
									<a id="backbutton" href="invoice1.php">&nbsp;
									<input type="button" class="btn btn-primary" id="backbutton" onClick="closeWin();" value="Close"> </a>
							    </div>
							</div><!--end control-group-->

</form>
</center>
</div>
</div>
<link href="./bootstrap/css/bootstrap-datetimepicker.min.css" rel="stylesheet" media="screen">		
<script type="text/javascript" src="./jquery/jquery-1.8.3.min.js" charset="UTF-8"></script>
<script type="text/javascript" src="./bootstrap/js/bootstrap.min.js"></script>
<script type="text/javascript" src="./jquery/bootstrap-datetimepicker.js" charset="UTF-8"></script>
<script type="text/javascript" src="./jquery/locales/bootstrap-datetimepicker.pl.js" charset="UTF-8"></script>
<script type="text/javascript">
   	$('.form_date').datetimepicker({
        language:  'pl',
        weekStart: 1,
        todayBtn:  1,
		autoclose: 1,
		todayHighlight: 1,
		startView: 2,
		minView: 2,
		forceParse: 0
    });
	
</script>

<script>
// we're binding a lot of different click event-handlers to this element
// there's no point looking it up every time we do so:
var body = $('body');

// binding the click event for the add-row button:
body.on('click', 'button.add-row', function() {
  // getting the relevant <table>:
  var table = $(this).closest('div.table-content'),
    // and the <tbody> and <thead> elements:
    tbody = table.find('tbody'),
    thead = table.find('thead');

  // if the <tbody> has children:
  if (tbody.children().length > 0) {
    // we find the last <tr> child element, clone it, and append
    // it to the <tbody>:
    tbody.find('tr:last-child').clone().appendTo(tbody);
  } else {
    // otherwise, we create the basic/minimum <tr> element:
    var trBasic = $('<tr />', {
        'html': '<td><span class="remove remove-row">x</span></td><td><input type="text" class="form-control" /></td>'
      }),
      // we find the number of columns that should exist, by
      // looking at the last <tr> element of the <thead>,
      // and finding out how many children (<th>) elements it has:
      columns = thead.find('tr:last-child').children().length;

    // a for loop to iterate over the difference between the number
    // of children in the created trBasic element and the current
    // number of child elements of the last <tr> of the <thead>:
    for (var i = 0, stopWhen = columns - trBasic.children.length; i < stopWhen; i++) {
      // creating a <td> element:
      $('<td />', {
        // setting its text:
        'text': 'static element'
          // appending that created <td> to the trBasic:
      }).appendTo(trBasic);
    }
    // appending the trBasic to the <tbody>:
    tbody.append(trBasic);
  }
});

body.on('click', 'span.remove-row', function() {
  $(this).closest('tr').remove();
});

body.on('click', 'span.remove-col', function() {
  // getting the closest <th> ancestor:
  var cell = $(this).closest('th'),
    // getting its index with jQuery's index(), though
    // cell.prop('cellIndex') would also work just as well,
    // and adding 1 (JavaScript is zero-based, CSS is one-based):
    index = cell.index() + 1;
  // finding the closest <table> ancester of the <th> containing the
  // clicked <span>:
  cell.closest('table')
    // finding all <td> and <th> elements:
    .find('th, td')
    // filtering that collection, keeping only those that match
    // the same CSS-based, using :nth-child(), index as the <th>
    // containing the clicked <span>:
    .filter(':nth-child(' + index + ')')
    // removing those cells:
    .remove();
});

body.on('click', 'button.add-col', function() {
  // finding the table (because we're using it to find both
  // the <thead> and <tbody>:
  var table = $(this).closest('div.table-content').find('table'),
    thead = table.find('thead'),
    // finding the last <tr> of the <thead>:
    lastTheadRow = thead.find('tr:last-child'),
    tbody = table.find('tbody');

  // creating a new <th>, setting its innerHTML to the string:
  $('<th>', {
    'html': '<input type="text" class="form-control pull-left" value="Property" /> <span class="pull-left remove remove-col">x</span>'
      // appending that created <th> to the last <tr> of the <thead>:
  }).appendTo(lastTheadRow);
  // creating a <td>:
  $('<td>', {
    // setting its text:
    'text': 'static element'
      // inserting the created <td> after every <td> element
      // that is a :last-child of its parent:
  }).insertAfter('td:last-child');
});
</script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.0/jquery.min.js"></script>
<script type="text/javascript">
    function printpage() {
        //Get the print button and put it into a variable
        var printButton = document.getElementById("printpagebutton");
        var back=document.getElementById("backbutton");
        /*var docket=document.getElementById("docket");
        var destn=document.getElementById("destn");
        var Weight=document.getElementById("Weight");
        var Rate=document.getElementById("Rate");
        var Freight=document.getElementById("Freight");
        var invalue=document.getElementById("invalue");
        var invoiceno=document.getElementById("invoiceno");
        var othercharges=document.getElementById("othercharges");
        var delichg=document.getElementById("delichg");
        var topaychg=document.getElementById("topaychg");
        var rov=document.getElementById("rov");
        var fov=document.getElementById("fov");
        var oda=document.getElementById("oda");
         var igst=document.getElementById("igst");
        var sgst=document.getElementById("sgst");
         var cgst=document.getElementById("cgst");
        var total=document.getElementById("total");*/
          window.print();
        //Set the print button visibility to 'hidden' 
        printButton.style.visibility = 'hidden';
        back.style.visibility = 'hidden';
        /*docket.style.visibility = 'hidden';
        destn.style.visibility = 'hidden';
        Weight.style.visibility = 'hidden';
        Freight.style.visibility = 'hidden';
        Rate.style.visibility = 'hidden';
        invalue.style.visibility = 'hidden';
        invoiceno.style.visibility = 'hidden';
        othercharges.style.visibility = 'hidden';
        delichg.style.visibility = 'hidden';
        topaychg.style.visibility = 'hidden';
        rov.style.visibility = 'hidden';
        fov.style.visibility = 'hidden';
        oda.style.visibility = 'hidden';
        sgst.style.visibility = 'hidden';
        igst.style.visibility = 'hidden';
        cgst.style.visibility = 'hidden';
        total.style.visibility = 'hidden';*/
        //Print the page content
      
        //Set the print button to 'visible' again 
        //[Delete this line if you want it to stay hidden after printing]
         printButton.style.visibility = 'visible';
         back.style.visibility = 'visible';
    }
</script>
<script type="text/javascript">

function closeWin() {
    back.close();
}
    window.onbeforeunload = function() {
        
    }
</script>
<?php

 
function convert_number_to_words($number) {

    $hyphen      = '-';
    $conjunction = ' and ';
    $separator   = ', ';
    $negative    = 'negative ';
    $decimal     = ' point ';
    $dictionary  = array(
        0                   => 'Zero',
        1                   => 'One',
        2                   => 'Two',
        3                   => 'Three',
        4                   => 'Four',
        5                   => 'Five',
        6                   => 'Six',
        7                   => 'Seven',
        8                   => 'Eight',
        9                   => 'Nine',
        10                  => 'Ten',
        11                  => 'Eleven',
        12                  => 'Twelve',
        13                  => 'Thirteen',
        14                  => 'Fourteen',
        15                  => 'Fifteen',
        16                  => 'Sixteen',
        17                  => 'Seventeen',
        18                  => 'Eighteen',
        19                  => 'Nineteen',
        20                  => 'Twenty',
        30                  => 'Thirty',
        40                  => 'Fourth',
        50                  => 'Fifty',
        60                  => 'Sixty',
        70                  => 'Seventy',
        80                  => 'Eighty',
        90                  => 'Ninety',
        100                 => 'Hundred',
        1000                => 'Thousand',
        1000000             => 'Million',
        1000000000          => 'Billion',
        1000000000000       => 'Trillion',
        1000000000000000    => 'Quadrillion',
        1000000000000000000 => 'Quintillion'
    );

    if (!is_numeric($number)) {
        return false;
    }

    if (($number >= 0 && (int) $number < 0) || (int) $number < 0 - PHP_INT_MAX) {
        // overflow
        trigger_error(
            'convert_number_to_words only accepts numbers between -' . PHP_INT_MAX . ' and ' . PHP_INT_MAX,
            E_USER_WARNING
        );
        return false;
    }

    if ($number < 0) {
        return $negative . convert_number_to_words(abs($number));
    }

    $string = $fraction = null;

    if (strpos($number, '.') !== false) {
        list($number, $fraction) = explode('.', $number);
    }

    switch (true) {
        case $number < 21:
            $string = $dictionary[$number];
            break;
        case $number < 100:
            $tens   = ((int) ($number / 10)) * 10;
            $units  = $number % 10;
            $string = $dictionary[$tens];
            if ($units) {
                $string .= $hyphen . $dictionary[$units];
            }
            break;
        case $number < 1000:
            $hundreds  = $number / 100;
            $remainder = $number % 100;
            $string = $dictionary[$hundreds] . ' ' . $dictionary[100];
            if ($remainder) {
                $string .= $conjunction . convert_number_to_words($remainder);
            }
            break;
        default:
            $baseUnit = pow(1000, floor(log($number, 1000)));
            $numBaseUnits = (int) ($number / $baseUnit);
            $remainder = $number % $baseUnit;
            $string = convert_number_to_words($numBaseUnits) . ' ' . $dictionary[$baseUnit];
            if ($remainder) {
                $string .= $remainder < 100 ? $conjunction : $separator;
                $string .= convert_number_to_words($remainder);
            }
            break;
    }

    if (null !== $fraction && is_numeric($fraction)) {
        $string .= $decimal;
        $words = array();
        foreach (str_split((string) $fraction) as $number) {
            $words[] = $dictionary[$number];
        }
        $string .= implode(' ', $words);
    }

    return $string;
}

?>



<script type="text/javascript">

$(document).ready(function() {
alert();
$("#remove_column").click( function() {

//Remove all columns except first column

$('#my_table td:not(:nth-child(1))').remove();

//Remove all columns except first column along with Header

$('#my_table th:not(:nth-child(1)), #my_table td:not(:nth-child(1))').remove();

});

});

</script>

</body>    
</html>