
<?php
session_start();
include("connection.php");
require_once('database.php');
require_once('library.php');
isUser();

// Set content type for JSON response
header('Content-Type: application/json');

// Enable CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Function to send JSON response
function sendResponse($status, $message, $data = null) {
    $response = array(
        'status' => $status,
        'message' => $message
    );
    if ($data !== null) {
        $response['data'] = $data;
    }
    echo json_encode($response);
    exit;
}

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse('error', 'Only POST method allowed');
}

// Verify CSRF token
if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
    sendResponse('error', 'Invalid CSRF token');
}

// Get and validate input
$month = isset($_POST['month']) ? trim($_POST['month']) : '';
$email = isset($_POST['email']) ? trim($_POST['email']) : '';

if (empty($month)) {
    sendResponse('error', 'Month is required');
}

if (empty($email)) {
    sendResponse('error', 'Email is required');
}

// Validate email format
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    sendResponse('error', 'Invalid email format');
}

// Parse month (format: YYYY-MM)
$monthParts = explode('-', $month);
if (count($monthParts) !== 2) {
    sendResponse('error', 'Invalid month format');
}

$year = intval($monthParts[0]);
$monthNum = intval($monthParts[1]);

if ($year < 2020 || $year > date('Y') + 1) {
    sendResponse('error', 'Invalid year');
}

if ($monthNum < 1 || $monthNum > 12) {
    sendResponse('error', 'Invalid month');
}
// Debug: Check what status values exist for July 2025
$debug_sql = "SELECT DISTINCT status 
              FROM tbl_courier 
              WHERE YEAR(book_date) = '$year' 
              AND MONTH(book_date) = '$month'";
$debug_result = mysqli_query($con, $debug_sql);
$status_values = [];
while ($row = mysqli_fetch_assoc($debug_result)) {
    $status_values[] = $row['status'];
}
error_log("Actual status values for $month_name: " . implode(", ", $status_values));
try {
    // Query to get POD data for the selected month with proper date formatting
    $sql = "SELECT
                c.cons_no as ConsignmentNo,
                DATE_FORMAT(c.book_date, '%d-%m-%Y') as book_date,
                c.partno as PartNo,
                c.noofpackage as noofpackages,
                c.qty as Qnty,
                c.weight as Weight,
                c.status,
                DATE_FORMAT(c.status_date, '%d-%m-%Y') as delivery_date,
                c.rev_name as receiver_name,
                c.ship_name as sender_name,
                r.name as receiver_name_full
            FROM tbl_courier c
            LEFT JOIN tbl_sender s ON c.sender_id = s.id
            LEFT JOIN tbl_receiver r ON c.receiver_id = r.id
            WHERE YEAR(c.book_date) = ?
            AND MONTH(c.book_date) = ?
            AND (c.status = 'Delivered' OR c.status = 'delivered' OR c.status = 'DELIVERED'
                 OR c.status = '6' OR c.status = 'Complete' OR c.status = 'Completed'
                 OR LOWER(c.status) LIKE '%deliver%' OR LOWER(c.status) LIKE '%complete%')
            ORDER BY c.book_date DESC";
    
    $stmt = mysqli_prepare($con, $sql);
    if (!$stmt) {
        throw new Exception('Database prepare failed: ' . mysqli_error($con));
    }
    
    mysqli_stmt_bind_param($stmt, "ii", $year, $monthNum);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if (!$result) {
        throw new Exception('Database query failed: ' . mysqli_error($con));
    }
    
    $podData = array();
    $recordCount = 0;
    
    while ($row = mysqli_fetch_assoc($result)) {
        $podData[] = $row;
        $recordCount++;
    }
    
    mysqli_stmt_close($stmt);
    
    if ($recordCount === 0) {
        sendResponse('error', 'No delivered shipments found for the selected month');
    }
    
    // Generate Excel content
    $excelContent = generateExcel($podData);

    // Create filename
    $monthNames = array(
        1 => 'January', 2 => 'February', 3 => 'March', 4 => 'April',
        5 => 'May', 6 => 'June', 7 => 'July', 8 => 'August',
        9 => 'September', 10 => 'October', 11 => 'November', 12 => 'December'
    );

    $filename = 'POD_' . $monthNames[$monthNum] . '_' . $year . '.zip';
    
    // Save file temporarily (you might want to save to a specific directory)
    $tempDir = 'temp_downloads/';
    if (!is_dir($tempDir)) {
        mkdir($tempDir, 0755, true);
    }
    
    $filePath = $tempDir . $filename;
    file_put_contents($filePath, $excelContent);
    
    // Create secure download URL
    $downloadUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/download_file.php?file=' . urlencode($filename);

    // Return success with download URL
    sendResponse('success', 'POD data generated successfully', array(
        'download_url' => $downloadUrl,
        'filename' => $filename,
        'record_count' => $recordCount
    ));
    
} catch (Exception $e) {
    error_log('POD Download Error: ' . $e->getMessage());
    sendResponse('error', 'An error occurred while processing your request: ' . $e->getMessage());
}

function generateCSV($data) {
    $csv = '';
    
    // CSV Headers with date format clarification
    $headers = array(
        'Consignment No',
        'Book Date (DD-MM-YYYY)',
        'Part No',
        'No of Packages',
        'Quantity',
        'Weight',
        'Status',
        'Delivery Date (DD-MM-YYYY)',
        'Sender Name',
        'Sender Address',
        'Sender Phone',
        'Receiver Name',
        'Receiver Address',
        'Receiver Phone',
        'Delivery Remarks'
    );
    
    $csv .= implode(',', $headers) . "\n";
    
    // CSV Data (dates already formatted in SQL query)
    foreach ($data as $row) {
        $csvRow = array(
            '"' . ($row['ConsignmentNo'] ?? '') . '"',
            '"' . ($row['book_date'] ?? '') . '"',
            '"' . ($row['PartNo'] ?? '') . '"',
            '"' . ($row['noofpackages'] ?? '') . '"',
            '"' . ($row['Qnty'] ?? '') . '"',
            '"' . ($row['Weight'] ?? '') . '"',
            '"' . ($row['status'] ?? '') . '"',
            '"' . ($row['delivery_date'] ?? '') . '"',
            '"' . ($row['sender_name'] ?? '') . '"',
            '"' . str_replace('"', '""', $row['sender_address'] ?? '') . '"',
            '"' . ($row['sender_phone'] ?? '') . '"',
            '"' . ($row['receiver_name_full'] ?? $row['receiver_name'] ?? '') . '"',
            '"' . str_replace('"', '""', $row['receiver_address'] ?? '') . '"',
            '"' . ($row['receiver_phone'] ?? '') . '"',
            '"' . str_replace('"', '""', $row['delivery_remarks'] ?? '') . '"'
        );
        
        $csv .= implode(',', $csvRow) . "\n";
    }
    
    return $csv;
}

function generateExcel($data) {
    // Excel Headers with date format clarification
    $headers = array(
        'Consignment No',
        'Book Date (DD-MM-YYYY)',
        'Part No',
        'No of Packages',
        'Quantity',
        'Weight',
        'Status',
        'Delivery Date (DD-MM-YYYY)',
        'Sender Name',
        'Sender Address',
        'Sender Phone',
        'Receiver Name',
        'Receiver Address',
        'Receiver Phone',
        'Delivery Remarks'
    );

    // Create Excel XML content
    $excel_content = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    $excel_content .= '<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet">' . "\n";
    $excel_content .= '<Worksheet ss:Name="POD Data">' . "\n";
    $excel_content .= '<Table>' . "\n";

    // Add headers
    $excel_content .= '<Row>' . "\n";
    foreach ($headers as $header) {
        $excel_content .= '<Cell><Data ss:Type="String">' . htmlspecialchars($header) . '</Data></Cell>' . "\n";
    }
    $excel_content .= '</Row>' . "\n";

    // Add data rows
    foreach ($data as $row) {
        $excel_content .= '<Row>' . "\n";

        $rowData = array(
            $row['ConsignmentNo'] ?? '',
            $row['book_date'] ?? '',
            $row['PartNo'] ?? '',
            $row['noofpackages'] ?? '',
            $row['Qnty'] ?? '',
            $row['Weight'] ?? '',
            $row['status'] ?? '',
            $row['delivery_date'] ?? '',
            $row['sender_name'] ?? '',
            $row['sender_address'] ?? '',
            $row['sender_phone'] ?? '',
            $row['receiver_name_full'] ?? $row['receiver_name'] ?? '',
            $row['receiver_address'] ?? '',
            $row['receiver_phone'] ?? '',
            $row['delivery_remarks'] ?? ''
        );

        foreach ($rowData as $cell) {
            $cell_value = htmlspecialchars($cell);
            // Detect if it's a number
            if (is_numeric($cell_value) && $cell_value != '') {
                $excel_content .= '<Cell><Data ss:Type="Number">' . $cell_value . '</Data></Cell>' . "\n";
            } else {
                $excel_content .= '<Cell><Data ss:Type="String">' . $cell_value . '</Data></Cell>' . "\n";
            }
        }
        $excel_content .= '</Row>' . "\n";
    }

    $excel_content .= '</Table>' . "\n";
    $excel_content .= '</Worksheet>' . "\n";
    $excel_content .= '</Workbook>';

    return $excel_content;
}
?>




