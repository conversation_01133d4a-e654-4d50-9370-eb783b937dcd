<?php
//start session
session_start();
require 'connection.php';
require_once('database.php');

$action = $_GET['action'];

switch($action) {
	 case 'regDriver':
		driver();
	 break;
	 case 'regBoy':
		boyreg();
	 break;
	
        case 'add-hub':
		 addHub();
	break;
	case 'hub-ack':
		hubAck();
	break;
	
	
	
}//switch

function boyreg(){
require 'connection.php';
        echo $uid= $_POST['cid'];
	$boyname1 = $_POST['boyname'];
	$uname=$_POST['uname'];
	$boymob1 = $_POST['boymob'];
	$boyaddress1 = $_POST['boyaddress'];
	$states = $_POST['states']; 
	$city = $_POST['city'];
	$zip = $_POST['zip'];
	$ememobno = $_POST['ememobno'];
	$drilin1=$_POST['drilin1'];
	$pass=$_POST['pass'];
	$cpass=$_POST['cpass'];
	$vehicleno = $_POST['vehicleno'];
	$vehiclename = $_POST['vehiclename'];
	
	 //$sql1 ="INSERT INTO `courier_db`.`delivboyreg` (`userid`,`name`, `mob`, `address`, `state`, `city`, `zip`, `emergmob`, `bikeno`, `bikename`, bookdate) VALUES ('$boyname1', '$boymob1', '$boyaddress1', '$states', '$city', '$zip', '$ememobno', '$vehicleno', '$vehiclename', NOW())";
	 $sql1="INSERT INTO `delivboyreg` (`userid`,`name`, `mob`, `address`, `state`, `city`, `zip`, `emergmob`, `dlno`, `bikeno`, `bikename`, `bookdate`) VALUES ('$uid','$boyname1', '$boymob1', '$boyaddress1', '$states', '$city', '$zip', '$ememobno', '$drilin1', '$vehicleno', '$vehiclename', NOW())";
	mysqli_query($con,$sql1);
	//mysqli_query($con,$sql);

 $data=mysqli_query($con,"select Max(delivboyid) from delivboyreg");
 while($rec=mysqli_fetch_row($data))
 {
	 $idd=$rec[0];
 }

 $sqlt="INSERT INTO `login` ( `username`, `password`, `cpassword`, `type`, `rid`) VALUES ('$uname', '$pass', '$cpass', 'dbboy', '$idd')";
 mysqli_query($con,$sqlt);

	 header('Location: deliverBoyReg.php');
	
}// close boyreg

function driver(){
require 'connection.php';
    echo $uid= $_POST['cid'];
	$drivername1 = $_POST['drivername']; 
	$drivermob1 = $_POST['drivermob'];
	$driveraddress1 = $_POST['driveraddress'];
	$states1 = $_POST['states']; 
	$city1 = $_POST['city'];
	$driverzip1 = $_POST['driverzip'];
    $drilin= $_POST['drilin'];    
	$ememobno1 = $_POST['ememobno']; 
	$vehicleno1 = $_POST['vehicleno'];
	$vehiclename1 = $_POST['vehiclename'];
	
	$sql1 ="INSERT INTO `driverreg` (`userid`,`name`, `mob`, `add`, `state`, `city`, `zip`, `emermob`,`dlno`,  `vehicleno`, `vehiclename`,bookdate) VALUES ('$uid','$drivername1','$drivermob1','$driveraddress1', '$states1', '$city1', '$driverzip1', '$ememobno1','$drilin', '$vehicleno1', '$vehiclename1', NOW())";
	mysqli_query($con,$sql1);
	//mysqli_query($con,$sql);


	header('Location: deliverDriverReg.php');
	
}// close boyreg


function addHub(){
require 'connection.php';

	$huboffname = $_POST['huboffname']; $hubuname = $_POST['hubuname'];$hubupass = $_POST['hubupass'];$hubno = $_POST['hubno'];$hubemail = $_POST['hubemail']; $hubaddress = $_POST['hubaddress']; $OfficeName = $_POST['curntloca'];
		
	$sqlb = "INSERT INTO hubreg (`hoffname`, `uname`, `pass`, `cont`, `hmail`, `haddress`, `offhub`, `regdate`) VALUES('$huboffname','$hubuname','$hubupass', $hubno,'$hubemail','$hubaddress', '$OfficeName', NOW() )";
	mysqli_query($con,$sqlb);
	
	header('Location:addhuboffice.php'); 
	
	
}//addHub

function updateStatus() {
require 'connection.php';

	$OfficeName = $_POST['OfficeName'];
	$status = $_POST['status'];
	$comments = $_POST['comments'];
	$cid = (int)$_POST['cid'];
	$cons_no = $_POST['cons_no'];
	//$OfficeName = $_POST['OfficeName'];
	
	$sql = "INSERT INTO tbl_courier_track (cid, cons_no, current_city, status, comments, bk_time)
			VALUES ($cid, '$cons_no', '$OfficeName', '$status', '$comments', NOW())";
	mysqli_query($con,$sql);

	$sql_1 = "UPDATE tbl_courier
				SET status = '$status'
				WHERE cid = $cid
				AND cons_no = '$cons_no'";
	mysqli_query($con,$sql_1);
	
$target_path = "attach/";
$target_path = $target_path.$_FILES['photo']['name']; 
//print_r($_FILES['photo']);exit;
if(move_uploaded_file($_FILES['photo']['tmp_name'], $target_path))
{
 echo "<h4>The file ". basename($_FILES['photo']['name']). " has been uploaded Successfully.</h4>";
}
else
{
	echo "Sorry there was an error uploading the file, please try again!";
} 
	
header('Location: update-success.php');

}//addNewOffice

?>