<?php 
	
	
	require "dbconnect.php";


	 $date = $_POST['Date'];
	 $userid =$_POST['userid'];
     $sql="SELECT * FROM `tbl_courier` inner join status on tbl_courier.status=status.statusid WHERE tbl_courier.userid='$userid' and tbl_courier.book_date ='$date'";

$r = mysqli_query($conn,$sql);

$result = array();
while($row = mysqli_fetch_array($r)){
  array_push($result,array(
     'cons_no'=>$row['cons_no'],
      'status'=>$row['statusname'],
      

    ));
}
echo json_encode(array('result'=>$result));

mysqli_close($conn);

?>