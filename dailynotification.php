<?php
session_start();
require_once('database.php');
require_once('library.php');
require 'connection.php';
isUser();
$date = date('Y-m-d');
$num_rec_per_page=10;
if (isset($_GET["page"])) { $page  = $_GET["page"]; } else { $page=1; }; 
$start_from = ($page-1) * $num_rec_per_page; 

$sql = "SELECT cons_no,weight,gtotamt,rev_name,r_add,ship_name,userid,book_mode,status_date,noofpackage,status,e_waybill,eway_expdate,gst,partno,remark,freight,book_mode,invi_value,qty,
assured_dly_date,book1_date,a.city_name as city ,type,invice_no,chweight,mode,rate,oda_mis,statusname FROM (tbl_courier inner join status on 
tbl_courier.status=status.statusid)
join  tbl_city_code a on tbl_courier.s_add=a.Id 
where status_date='$date'ORDER BY status_update_datetime DESC";

$result = mysqli_query($con,$sql);	
$count=0;
while($row=mysqli_fetch_array($result))
{
    
   if($row['status']=='6' || $row['status']=='50' || $row['status']=='57'){
      $statusdate=$row['status_date'];
 }else{
     $statusdate="";
 }
 
  if($row['status']=='5'){
     $delivereddate=$row['status_date'];
 }
 
 if($row['r_add']==""){
         $destination="";
     }else{
  $sql1="SELECT city_name,r_add from tbl_city_code inner join tbl_courier on tbl_city_code.Id= tbl_courier.r_add 
  WHERE tbl_courier.r_add = '".$row['r_add']."' " ;

$result1 = mysqli_query($con,$sql1);
$row1 = mysqli_fetch_array($result1);
$destination=$row1['city_name'];
     }
     
$sql2="SELECT Manager_name,empcode from tbl_courier_officers inner join tbl_courier on tbl_courier_officers.cid=tbl_courier.userid
  WHERE tbl_courier.userid = '".$row['userid']."' " ;

$result2 = mysqli_query($con,$sql2);
$row2 = mysqli_fetch_array($result2);
$Manager_name=$row2['Manager_name'];  
$empcode=$row2['empcode'];  
    
    
	$count++;
	
$tr=$tr."<tr><td>".$count."</td><td>".$Manager_name."</td><td>".$empcode."</td><td>".$row['status_date']."</td><td>".$row['cons_no']."</td><td>".$row['book1_date']."</td><td>".$row['invice_no']."</td>
<td>".$row['rev_name']."</td><td>".$destination."</td><td>".$row['noofpackage']."</td><td>".$row['qty']."</td><td>".$row['partno']."</td><td>".$withoutgstvalue."</td><td>".$row['invi_value']."</td><td>".$statusdate."</td>
<td>".$row['book_mode']."</td><td>".$row['statusname']."</td><td>".$row['remark']."</td><td>".$delivereddate."</td><td>".$row['mode']."</td><td>".$row['weight']."</td><td>".$row['chweight']."</td><td>".$row['e_waybill']."</td><td>".$row['rate']."</td>
<td>".$row['oda_mis']."</td><td>".$row['freight']."</td><td></td><td></td></tr>";
}
$sql = "SELECT * FROM tbl_courier_officers inner join tbl_courier on tbl_courier_officers.cid=tbl_courier.userid inner join status on tbl_courier.status=status.statusid
where status_date='$date' LIMIT $start_from, $num_rec_per_page";
$result = mysqli_query($con,$sql);		

?>
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>

	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
	
</head>
<script language="JavaScript">
var checkflag = "false";

function check(field) {
if (checkflag == "false")
 {
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll")
	{
	field[i].checked=true;	
	}
	}
	checkflag = "true";
}
else
{
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll")
	{
	field[i].checked=false;
	}
	}
	checkflag = "false";
}

}
function confirmDel(field,msg)
{
	count=0;
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll" && field[i].checked==true)
	{
	count++;
	}
	}
	
	if(count == 0)
	{
		alert("Select any one record to delete!");
		return false;
	}
	else
	{
		return confirm(msg);
	}
}
</script>

<?php include("header.php"); ?>



		<div class="container">

			<div class="row">

				              
                
                <div class="span11">
					<div class="account-list-outer">

						<div class="titleHeader clearfix">
							<h3>Daily Updated Docket Details   </h3>
						</div><!--end titleHeader-->
						<div class="controls">
								     <input class="btn btn-primary"  type="button" value="Export" onclick="abc();" >
								     
							     </div>
	<div class="one-third last" style="height:450px;overflow:scroll;width=8000px;">
						<table class="table1"  style="width:100%">
						<thead>
						
							<tr>
							     	
							    <th><h6>Sr No. </h6></th>
							      <th><h6>Employee Name </h6></th>
							      <th><h6>Emp=ID</h6></th>
							       <th><h6>Scanning Date</h6></th>
							  <th><h6>LR No </h6></th>
							     <th><h6>BKG Date</h6></th>
							     <th><h6>Invoice No </h6></th>
							     <th><h6>Customer Name </h6></th>
						         <th><h6>Destinantion</h6></th>
						         	<th><h6>Cases </h6></th>
									<th><h6>Qty </h6></th>
										<th><h6>Part No. </h6></th>
								
								 
								  <th><h6>Without GST Value </h6></th>
								    <th><h6>Invoice Value </h6></th>
								  	 
									
								   <th><h6>Godown Receipt Date </h6></th>
								<th><h6>Type</h6></th>
								<th><h6>Status</h6></th>
								<th><h6>My Remarks</h6></th>
								  <th><h6>Delivery date </h6></th>
								    <th><h6>Mode</h6></th>
							    <th><h6>A/Weight </h6></th>
								<th><h6>C/Weight </h6></th>
									<th><h6>E WayBill No. </h6></th>
									<th><h6>Rate </h6></th>
										<th><h6>ODA </h6></th>
											<th><h6>Fright </h6></th>
											<th><h6>GST </h6></th>
								
								<th><h6>Total </h6></th>
							</tr>
							
						</thead> 
						<tbody>
							<tr><?php echo $tr; ?>	</tr>
						</tbody>
					</table>
				

				
					</div></div><!--end -->
				</div><!--end span-->


				
			</div><!--end row-->



			<!--end row-->


			<!--end row-->


		</div><!--end conatiner-->
<?php 
$sql = "SELECT * FROM tbl_courier_officers inner join tbl_courier on tbl_courier_officers.cid=tbl_courier.userid inner join status on tbl_courier.status=status.statusid
where status_date='$date'"; 
$rs_result = mysqli_query($con,$sql); //run the query
$total_records = mysqli_num_rows($rs_result);  //count number of records
$total_pages = ceil($total_records / $num_rec_per_page); 

echo "<a href='dailynotification.php?page=1'>".'|<'."</a> "; // Goto 1st page  

for ($i=1; $i<=$total_pages; $i++) { 
            echo "<a href='dailynotification.php?page=".$i."'>".$i."</a> "; 
}; 

echo "<script> 
function abc()
{

location.href='dailyexport_excel.php';

}</script>"; 
include("footer.php"); ?>