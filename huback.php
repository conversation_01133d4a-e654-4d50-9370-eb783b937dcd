<?php
session_start();
require_once('database.php');
require_once('library.php');
require 'connection.php';
isUser();

$a=$_SESSION['username'];
$sql="select * from login where username='$a'";
$result = mysqli_query($con, $sql);
$row1 = mysqli_fetch_array($result, MYSQLI_BOTH);
 $d = ($row1 && isset($row1['rid'])) ? $row1['rid'] : 0;

 $sql="select * from tbl_courier_officers where cid='$d'";
$result = mysqli_query($con, $sql);
$row1 = mysqli_fetch_array($result, MYSQLI_BOTH);
$uoffice = ($row1 && isset($row1['office'])) ? $row1['office'] : '';
$uaddress = ($row1 && isset($row1['address'])) ? $row1['address'] : '';
$id = ($row1 && isset($row1['cid'])) ? $row1['cid'] : 0;
 
$num_rec_per_page=20;
if (isset($_GET["page"])) { $page  = $_GET["page"]; } else { $page=1; }; 
$start_from = ($page-1) * $num_rec_per_page; 

if($a!="admin"){
$sql = "SELECT * FROM tbl_courier WHERE status = '2' and userid='$id' ORDER BY cid DESC LIMIT $start_from, $num_rec_per_page";
}
else
{
$sql = "SELECT * FROM tbl_courier WHERE status = '2' ORDER BY cid DESC LIMIT $start_from, $num_rec_per_page";
}
 
//$result = dbQuery($sql);
$cnt=0;
// Initialize variables
$tr = '';
$doc = array();
$wei = array();

$result=mysqli_query($con,$sql);
while($row=mysqli_fetch_array($result))
{
$cnt=$cnt+1;
 //$tr=$tr.'<tr class="table"><td>'.$cnt.'</td><td> '.$row['current_date'].'</td><td> '.$row['current_time'].'</td><td> '.$row['consig_no'].'</td><td><input type="checkbox" name="bh[]" value="'.$row['consig_no'].'"> </td></tr>';

// Add values to arrays for totals
$doc[] = $row['qty'];
$wei[] = $row['weight'];
// Build table row with individual values
$tr=$tr.'<tr class="table"><td>'.$cnt.'</td><td> '.$row['cons_no'].'</td><td> '.$row['r_zip'].'</td><td> '.$row['qty'].'</td><td> '.$row['weight'].'</td><td> '.$row['invice_no'].'</td><td> '.$row['type'].'</td><td> '.$row['book_mode'].'</td><td> '.$row['oda_mis'].'</td><td><input type="test" name="remark[]" value="" class="span2"> </td><td><input type="checkbox" name="bh[]" value="'.$row['cons_no'].'"> </td></tr>';
}
$docket=array_sum($doc);
 $wei_total=array_sum($wei);
  
 /*
$first = true;
echo "You checked boxes:";
foreach($_POST['bh'] as $cb)
{
    if (!$first)
    echo ",";
 echo " $cb";
   $first = false;
}
exit();*/
 
 if(isset($_GET['id']) && $_GET['del']=='yes')

{
 $get_id = isset($_GET['id']) ? $_GET['id'] : 0;
 $sql1="select * from `tbl_offices` WHERE `id`=".$get_id." ORDER BY off_name ASC";

}

// Initialize variables
$company = $_POST['company'] ?? '';
$loc = $_POST['loc'] ?? '';
$stat = $_POST['stat'] ?? '';
$stsusa = $_POST['stsusa'] ?? '';

$sql1="select off_name as name , id from `tbl_offices` ORDER BY name ASC";
 $result2=mysqli_query($con,$sql1);
while($row2=mysqli_fetch_array($result2))
{
	if($company==$row2['id'])
	{
	$loc=$loc."<option value='".$row2['id']."' selected>".$row2['name']."</option>";
	}
	else{
	$loc=$loc."<option value='".$row2['id']."' >".$row2['name']."</option>";
	}
}
 $sql1="select statusname , statusid from `status` ORDER BY statusid ASC";
 $result2=mysqli_query($con,$sql1);
while($stsrow=mysqli_fetch_array($result2))
{
	if($stat==$stsrow['statusid'])
	{
 $stsusa=$stsusa."<option value='".$stsrow['statusid']."' selected>".$stsrow['statusname']."</option>";
	}
	else{
	$stsusa=$stsusa."<option value='".$stsrow['statusid']."' >".$stsrow['statusname']."</option>";
	}
}
  
?>

<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>
	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
<script language="JavaScript">
var checkflag = "false";
 //---- Checks consignment is selected or not script start-----
function check(field) {
if (checkflag == "false")
 {
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll")
	{
	field[i].checked=true;	
	}
	}
	checkflag = "true";
}
else
{
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll")
	{
	field[i].checked=false;
	}
	}
	checkflag = "false";
}

} //---- Checks consignment is selected or not script end-----
//---- Delete the consignment script start-----
function confirmDel(field,msg)
{
	count=0;
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll" && field[i].checked==true)
	{
	count++;
	}
	}
	
	if(count == 0)
	{
		alert("Select any one record to delete!");
		return false;
	}
	else
	{
		return confirm(msg);
	}
}
//---- Delete the consignment script end-----
</script>
</head>
<?php include("header.php"); ?>
		<div class="container">
			<div class="row">
               <div class="span11">
					<div class="account-list-outer">

						<div class="titleHeader clearfix">
							<h3> Unloading Sheet</h3> 
                                                  <h5 align="center"> Source :<b><?php echo $uoffice;?> </b>/ Destination :<b><?php echo $uoffice;?></b></h5>
						</div>	
							<!--<div class="control-group">
                            <label>Branch Location :</label>
							<div class="controls">
	                            <select name="curntloca" id="curntloca">
												<option value="">-- Please select --</option>
												<?php echo $loc; ?>
								</select>
							</div>
					    </div>
						</div> end titleHeader-->
					 
                  <form  action="processH.php?action=hub-ack1" method="POST" name="hubtohub">
					<table class="table">
						<thead>
							<tr>
								<th>Sr.No</th>
								<th>C/N No</th>
								<th>Zipcode</th>
								<th>Qnty</th>
								<th>Weight</th>
								<th>Invoice No</th>
								<th>Type</th>
								<th>Mode</th>
								<th>ODA</th>
								<th>Remark</th>
								<th><input type="checkbox" name="check_all" id="check_all" onClick="checkAll(this)">All / Select</th>
								
							</tr>
						</thead>
						
						<tbody><?php echo $tr; ?> 
						   
						</tbody>
						
					</table>
				<div class="row" >
     			    <div class="span5" >
					     <div class="control-group">
                            <label>Total Consignment</label>
							<div class="controls">
	                           <input type="text" name="totqty" value="<?php echo $docket;?>" readonly>
							</div>
					    </div>
					</div>	
					<div class="span5" >	
					   <div class="control-group">
					   <label>Total Weight</label>
						<div class="controls"> 
						     <input type="text" name="totamnt" value="<?php echo $wei_total;?>" readonly>
							 <input type="hidden" name="uoffice" value="<?php echo $uoffice;?>">
						</div>
				       </div>
				    </div>	
				</div>
				<div class="row" >
     			    <div class="span3">	
					    <div class="control-group">
                            <div class="controls">
							 <label class="control-label"> <input type="radio" name="huback" id="huback" value="HUB" onChange="filterbytype('HUB_Office');"> HUB Office</label>
							 </div>
					    </div>
					</div>	
					<div class="span3">	
					    <div class="control-group">
                            <div class="controls">
					        <label class="control-label"> <input type="radio" name="huback" id="huback" value="Branch" onChange="filterbytype('Branch_Office');"> Branch Office </label>
						 </div>
					    </div>
					</div>	
					<div class="span2">	
					    <div class="control-group">
                            <div class="controls">
					           <label class="control-label">  <input type="radio" name="huback" id="huback" value="De" onChange="filterbytype('Deli_Vehicle');"> Delivery Vehicle </label>
						    </div>
					    </div>
					</div>	
					<div class="span2">	
					    <div class="control-group">
                            <div class="controls">
								<label class="control-label"> <input type="radio" name="huback" id="huback" value="#" onChange="filterbytype('Deli_Boy');"> Delivery Boy </label>
					           </div>
					    </div>
					</div>	
				</div> 
				<div class="control-group">
						<div class="controls"> 
							<input type="hidden" name="uaddress" value="<?php echo $uaddress;?>">
							<input type="hidden" name="cid" value="<?php echo $id;?>">		
					    </div>
				</div>
			<div class="row" >
     			<div class="span5" >
					     <div class="control-group">
                            <label>Move to Location :</label>
							<div class="controls">
	                            <select name="mloca" id="mloca" value="">
												<option value="">-- Please select --</option>
												<!--<?php echo $loc; ?>-->
								</select>
							</div>
					    </div>
						<div class="control-group">
                            <label>Date :</label>
							    <div class="controls">
									 <div class="controls input-append date form_date" data-date="" data-date-format="dd MM yyyy" data-link-field="dtp_input2" data-link-format="yyyy-mm-dd">
										<input name="curdate" id="curdate" type="text" value="" readonly>
										<span class="add-on"><i class="icon-remove"></i></span>
										<span class="add-on"><i class="icon-th"></i></span>
									 </div>
										<input type="hidden" id="dtp_input2" value="" />
							    </div>
					    </div>
						
				</div>	
				<div class="span5" >
                        <div class="control-group">
                            <label>Status :</label>
							<div class="controls">
	                            <select name="status" id="status" value="">
												<option value="">-- Please select --</option>
												<?php echo $stsusa; ?>
								</select>
							</div>
					    </div>
					   <div class="control-group">
                            <label>Time :</label>
							    <div class="controls">
									 <div class="controls input-append date form_time" data-date="" data-date-format="hh:ii" data-link-field="dtp_input3" data-link-format="hh:ii">
										<input name="curtime" id="curtime" type="text" value="" readonly>
										<span class="add-on"><i class="icon-remove"></i></span>
										<span class="add-on"><i class="icon-th"></i></span>
									 </div>
										<input type="hidden" id="dtp_input3" value="" />
							    </div>
					    </div>	 
				</div>
				
			</div>	
			<div>
				
				</div>
			<div class="row" align="center">	
			    <div class="span8">
				    <div class="control-group">
						<div class="controls"> 
							<input name="Submit" class="btn btn-primary" type="submit" value="Save" onClick="return valid()" >&nbsp;&nbsp;&nbsp;<button id="backbutton" type="reset" class="btn ">Clear</button>&nbsp;&nbsp;<input type="button" class='btn btn-primary' id="printpagebutton" onClick="printpage();" value="Print">
					    </div>
					</div>
				</div>
			</div>			
						</form>
					</div><!--end -->
				</div><!--end span-->
			</div><!--end row-->
		</div><!--end conatiner-->


<script type="text/javascript">
    function printpage() {
        //Get the print button and put it into a variable
        var printButton = document.getElementById("printpagebutton");
        var back=document.getElementById("backbutton");
        //Set the print button visibility to 'hidden' 
        printButton.style.visibility = 'hidden';
        back.style.visibility = 'hidden';
        //Print the page content
        window.print();
        //Set the print button to 'visible' again 
        //[Delete this line if you want it to stay hidden after printing]
        printButton.style.visibility = 'visible';
         back.style.visibility = 'visible';
    }
</script>
<script type="text/javascript">

function closeWin() {
    back.close();
}
    window.onbeforeunload = function() {
        
    }
</script>



	
<script>
function checkAll(bx) {

  var cbs = document.getElementsByTagName('input');
  for(var i=0; i < cbs.length; i++) {
    if(cbs[i].type == 'checkbox') {
      cbs[i].checked = bx.checked;
    }
  }
}
</script>	
<script  type="text/javascript">
function valid()
{
var curntloca1=document.forms["hubtohub"]["mloca"].value;
if (curntloca1==null || curntloca1=="")
  {
  alert("Please Select HUB Location");
  return false;
  }
  
  var curdate1=document.forms["hubtohub"]["curdate"].value;
if (curdate1==null || curdate1=="")
  {
   alert("Please Select Date ");
  return false;
  
  }
  
  var curtime1=document.forms["hubtohub"]["curtime"].value;
if (curtime1==null || curtime1=="")
  {
  alert("Please Select Time ");
  return false;
  }
   var status1=document.forms["hubtohub"]["status"].value;
if (status1==null || status1=="")
  {
  alert("Please Select status ");
  return false;
  } 
  
}
</script>
<link href="./bootstrap/css/bootstrap-datetimepicker.min.css" rel="stylesheet" media="screen">
<script type="text/javascript" src="./jquery/jquery-1.8.3.min.js" charset="UTF-8"></script>
<script type="text/javascript" src="./bootstrap/js/bootstrap.min.js"></script>
<script type="text/javascript" src="./jquery/bootstrap-datetimepicker.js" charset="UTF-8"></script>
<script type="text/javascript" src="./jquery/locales/bootstrap-datetimepicker.pl.js" charset="UTF-8"></script>
<script type="text/javascript">
	$('.form_date').datetimepicker({
        language:  'pl',
        weekStart: 1,
        todayBtn:  1,
		autoclose: 1,
		todayHighlight: 1,
		startView: 2,
		minView: 2,
		forceParse: 0
    });
	$('.form_time').datetimepicker({
        language:  'pl',
        weekStart: 1,
        todayBtn:  1,
		autoclose: 1,
		todayHighlight: 1,
		startView: 1,
		minView: 0,
		maxView: 1,
		forceParse: 0
    });
</script>

<script src="jquery/lib/sweet-alert.min.js"></script>
<script src="jquery/jquery.min.js"></script>
<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
<script  type="text/javascript">
function filterbytype(type)
{
//alert("type="+type);
$('#huback').val(type);

$('#mloca').find('option').remove().end().append('<option value="">--- Select '+type+'---</option>').val('');
	// Ajax post
	jQuery.ajax({
	type: "POST",
	url: "ajaxfiltertype.php?type="+type,
	dataType: 'json',
	data: {
		
	},
	//cache: false,	
	success: function(data) { 
	
	   		if (data)
			{  
				$.each(data, function(index, data) 
				{
				$('#mloca').append( $('<option></option>').val(data.id).html(data.name) );	

			} );
			
			} 
		} 
		
	});

	

}

$(document).ready(function() {
    $('#selecctall').click(function(event) {  //on click 
        if(this.checked) { // check select status
            $('.checkbox1').each(function() { //loop through each checkbox
                this.checked = true;  //select all checkboxes with class "checkbox1"  
                $("#sel").html("Deselect All");             
            });
        }else{
            $('.checkbox1').each(function() { //loop through each checkbox
                this.checked = false; //deselect all checkboxes with class "checkbox1"  
                 $("#sel").html("Select All");                         
            });         
        }
    });
    
});

</script>		
<?php 
$sql = "SELECT * FROM tbl_courier WHERE status = '2' ORDER BY cid DESC "; 
$rs_result = mysqli_query($con, $sql); //run the query
$total_records = mysqli_num_rows($rs_result);  //count number of records
$total_pages = ceil($total_records / $num_rec_per_page); 

echo "<a href='huback.php?page=1'>".'|<'."</a> "; // Goto 1st page  

for ($i=1; $i<=$total_pages; $i++) { 
            echo "<a href='huback.php?page=".$i."'>".$i."</a> "; 
}; ?><?php
include("footer.php"); ?>