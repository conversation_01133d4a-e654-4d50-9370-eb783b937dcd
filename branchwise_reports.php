<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
require 'connection.php';
require_once('database.php');
require_once('library.php');
isUser();   

$company = $_POST['company'] ?? $_GET['company'] ?? ''; 
$id = $_POST['id'] ?? $_GET['id'] ?? '';
$date1 = $_POST['date1'] ?? $_GET['date1'] ?? '';
$date2 = $_POST['date2'] ?? $_GET['date2'] ?? '';
?>

<?php
$branchDropdown = '';

$statusQuery = "SELECT DISTINCT statusname FROM status WHERE LOWER(statusname) LIKE 'stock at %'";
$statusResult = mysqli_query($con, $statusQuery);

$selectedBranch = $_POST['branch'] ?? '';

while ($row = mysqli_fetch_assoc($statusResult)) {
    $statusname = htmlspecialchars($row['statusname']);
    if ($selectedBranch === $statusname) {
        $branchDropdown .= "<option value=\"$statusname\" selected>$statusname</option>";
    } else {
        $branchDropdown .= "<option value=\"$statusname\">$statusname</option>";
    }
}
?>


<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>

	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">

	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
<script src="js/jquery-1.7.2.min.js"></script>

</head>

<?php include("header.php"); ?>

   <div class="container">
			<div class="row">
     			<div class="span2">
				<div class="control-group ">

				</div>
				</div><!--end span3-->

                <div class="span8">
					<div class="account-list-outer">
						<div class="control-group ">
						   <div class="controls">
							 <label class="control-label"> Branch Report : <span class="text-error">*</span></label>
				                 <select name="branch" id="branch">
									    <option value="">-- Select Branch --</option>
										<?php echo $branchDropdown; ?>
								 </select>

          					</div>
						</div>
						<div class="control-group ">
						   <div class="controls">
							<label for="date1"> Start Date : <span class="text-error">*</span></label>
								<div class="controls input-append date form_date" data-date="" data-date-format="dd MM yyyy" data-link-field="dtp_input1" data-link-format="yyyy-mm-dd">
									 <input  type="text" value=""id="date1" name="date1" readonly>
										<span class="add-on"><i class="icon-remove"></i></span>
										 <span class="add-on"><i class="icon-th"></i></span>
								</div>
									 <input type="hidden" id="dtp_input1" value="" />
						    </div>
						</div>
						<div class="control-group ">
						   <div class="controls">
							<label class="control-label" for="date2"> End Date : <span class="text-error">*</span></label>
								<div  class="controls input-append date form_date" data-date="" data-date-format="dd MM yyyy" data-link-field="dtp_input2" data-link-format="yyyy-mm-dd">
									<input  type="text" value="" id="date2" name="date2" readonly>
										<span class="add-on"><i class="icon-remove"></i></span>
										 <span class="add-on"><i class="icon-th"></i></span>
								</div>
									 <input type="hidden" id="dtp_input2" value="" />
						   </div>
						</div>

							<div class="control-group ">
							     <div class="controls">
								     <input class="btn btn-primary"  type="button" value="Submit" onClick="getBranchRep();">
								     <input class="btn btn-primary"  type="button" value="Export" onclick="exportBranch();" >
									     <input class="btn btn-warning" type="reset" value="Reset">
							     </div>
							</div>

							<style>
				.btn-warning{
  border:1px solid #f16325;
  background: #f16325;
  background: -moz-linear-gradient(top, #f4885a 0%, #f16325 100%);
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#f4885a), color-stop(100%,#f16325));
  background: -webkit-linear-gradient(top, #f4885a 0%,#f16325 100%);
  background: -o-linear-gradient(top, #f4885a 0%,#f16325 100%);
  background: -ms-linear-gradient(top, #f4885a 0%,#f16325 100%);
  background: linear-gradient(to bottom, #f4885a 0%,#f16325 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}</style>

					<div style="overflow-x:auto!important;
   height:350px; ">	<div id="cmp"></div></div>


					</div><!--end -->
				</div><!--end span6-->
			</div><!--end row-->
		</div><!--end conatiner-->
<!-- <link href="./bootstrap/css/bootstrap.min.css" rel="stylesheet" media="screen">-->
    <link href="./bootstrap/css/bootstrap-datetimepicker.min.css" rel="stylesheet" media="screen">
<script type="text/javascript" src="./jquery/jquery-1.8.3.min.js" charset="UTF-8"></script>
<script type="text/javascript" src="./bootstrap/js/bootstrap.min.js"></script>
<script type="text/javascript" src="./jquery/bootstrap-datetimepicker.js" charset="UTF-8"></script>
<!--<script type="text/javascript" src="./jquery/locales/bootstrap-datetimepicker.pl.js" charset="UTF-8"></script>-->
<script type="text/javascript">
   	$('.form_date').datetimepicker({
        language:  'pl',
        weekStart: 1,
        todayBtn:  1,
		autoclose: 1,
		todayHighlight: 1,
		startView: 2,
		minView: 2,
		forceParse: 0
    });

</script>


<?php
echo "<script>
function exportBranch()
{
    var branch=document.getElementById('branch').value;
    var date=document.getElementById('date1').value;
    var date2=document.getElementById('date2').value;

    if(branch=='')
    {
        alert('Please Select Branch');
        return false;
    }
    else if(date=='')
    {
        alert('Please Fill Start Date');
        return false;
    }
    else if(date2=='')
    {
        alert('Please Fill End Date');
        return false;
    }
    else
    {
        // All validations passed, proceed with export
        exportToExcel();
    }
}
</script>";
?>

<script>
function getBranchRep()
{
	//alert("hello");
var date=document.getElementById("date1").value;
var date2=document.getElementById("date2").value;
var branch=document.getElementById("branch").value;
alert(branch+" Do not Refresh Wait...");
loadPage(1, 10); // Load first page with 10 records by default
}

function loadPage(page, limit)
{
var date=document.getElementById("date1").value;
var date2=document.getElementById("date2").value;
var branch=document.getElementById("branch").value;

if (window.XMLHttpRequest)
  {// code for IE7+, Firefox, Chrome, Opera, Safari
  xmlhttp=new XMLHttpRequest();
  }
else
  {// code for IE6, IE5
  xmlhttp=new ActiveXObject("Microsoft.XMLHTTP");
  }
xmlhttp.onreadystatechange=function()
  {
  if (xmlhttp.readyState==4 && xmlhttp.status==200)
    {
 //alert (xmlhttp.responseText);
   document.getElementById("cmp").innerHTML=xmlhttp.responseText;
    }
  }
  xmlhttp.open("GET","getBranchDetails.php?date="+date+"&date2="+date2+"&branch="+branch+"&page="+page+"&limit="+limit);
xmlhttp.send();
}

function exportToExcel()
{
    var date=document.getElementById("date1").value;
    var date2=document.getElementById("date2").value;
    var branch=document.getElementById("branch").value;

    // Additional validation before export
    if(branch=='' || date=='' || date2=='')
    {
        alert('Please fill all required fields before exporting');
        return false;
    }

    // Validate end date is not before start date
    if (new Date(date2) < new Date(date)) {
        alert('End Date cannot be before Start Date.');
        return false;
    }

    // Proceed with export
    window.location.href='branchwise_excel.php?branch='+encodeURIComponent(branch)+'&date1='+encodeURIComponent(date)+'&date2='+encodeURIComponent(date2);
}

</script>



<?php


include("footer.php"); ?>

</body>
</html>
