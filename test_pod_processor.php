
<?php
// Test the pod_download_processor.php for syntax and basic functionality
echo "<h2>Testing POD Download Processor</h2>";

// Check syntax first
echo "<h3>1. Syntax Check</h3>";
$output = [];
$return_var = 0;
exec("php -l pod_download_processor.php 2>&1", $output, $return_var);

if ($return_var === 0) {
    echo "<span style='color: green;'>✅ Syntax OK</span><br>";
} else {
    echo "<span style='color: red;'>❌ Syntax Error:</span><br>";
    echo "<pre style='background: #f8f8f8; padding: 10px; border: 1px solid #ddd;'>";
    echo implode("\n", $output);
    echo "</pre>";
    exit;
}

// Test POST simulation
echo "<h3>2. POST Simulation Test</h3>";

// Start session and set required session variables
session_start();
$_SESSION['username'] = 'test_user';
$_SESSION['desgn'] = 1;

// Simulate POST data
$_POST['month'] = '2025-07';
$_POST['email'] = '<EMAIL>';
$_POST['data_filter'] = 'all';
$_POST['csrf_token'] = 'test_token';

// Set session token for CSRF
$_SESSION['csrf_token'] = 'test_token';

echo "Simulating POST data:<br>";
echo "- Month: " . $_POST['month'] . "<br>";
echo "- Email: " . $_POST['email'] . "<br>";
echo "- Filter: " . $_POST['data_filter'] . "<br><br>";

// Capture output
if (ob_get_level()) {
    ob_end_clean();
}
ob_start();

try {
    // Include the processor (this will execute it)
    include 'pod_download_processor.php';
} catch (Exception $e) {
    echo "<span style='color: red;'>❌ Exception: " . $e->getMessage() . "</span><br>";
} catch (Error $e) {
    echo "<span style='color: red;'>❌ Error: " . $e->getMessage() . "</span><br>";
}

$output = ob_get_clean();

echo "<h3>3. Output Analysis</h3>";
echo "<strong>Raw Output:</strong><br>";
echo "<pre style='background: #f8f8f8; padding: 10px; border: 1px solid #ddd; max-height: 300px; overflow-y: auto;'>";
echo htmlspecialchars($output);
echo "</pre>";

// Try to parse as JSON
echo "<br><strong>JSON Parse Test:</strong><br>";
$json_data = json_decode($output, true);
if ($json_data !== null) {
    echo "<span style='color: green;'>✅ Valid JSON</span><br>";
    echo "<strong>Parsed Data:</strong><br>";
    echo "<pre>";
    print_r($json_data);
    echo "</pre>";
} else {
    echo "<span style='color: red;'>❌ Invalid JSON</span><br>";
    echo "JSON Error: " . json_last_error_msg() . "<br>";
}

echo "<hr>";
echo "<h3>4. Quick Fixes</h3>";
echo "<p>If there are issues:</p>";
echo "<ul>";
echo "<li>Check database connection in connection.php</li>";
echo "<li>Ensure tbl_courier table exists</li>";
echo "<li>Check PHP error logs</li>";
echo "<li>Verify session variables are set</li>";
echo "</ul>";
?>

{
  "status": "success",
  "message": "Download link has been sent to your email",
  "download_url": "http://localhost/project/download_pod_file.php?token=..."
}



