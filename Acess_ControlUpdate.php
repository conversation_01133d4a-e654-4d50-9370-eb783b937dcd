<?php
session_start();
require 'connection.php';
require_once('database.php');
require_once('library.php');
isUser();

$id=$_GET['id'];
$list="SELECT * FROM Access_Control inner join tbl_courier_officers on tbl_courier_officers.cid=Access_Control.Emp_ID   group by Emp_ID";
$listresult = mysqli_query($con,$list);
$ls = "";
while($row= mysqli_fetch_array($listresult))
{
 $ls=$ls."<tr><td>".$row['cid']."</td><td>".$row['Manager_name']."</td><td><a href='menuDel.php?id=".$row['cid']."'>Delete</a>&nbsp;<a href='Acess_ControlUpdate.php?id=".$row['cid']."'>Update</a></td></tr>";   
} 

$sql="select * from tbl_courier_officers where cid not in (select Emp_ID from Access_Control )";
$result = mysqli_query($con,$sql);  
$team="";        
while($row= mysqli_fetch_array($result))
{
 $team=$team."<option value='".$row['cid']."'>".$row['Manager_name']."</option>";   
} 
mysqli_free_result($result);   
mysqli_next_result($con); 
$cnt=0;
$sql1="select * from Sub_Menu order by  Menu_Id";
$List1= mysqli_query($con,$sql1);  
//$Expenselist="";        
//$empname="";
$sqlSel="SELECT * FROM `Access_Control` WHERE Emp_ID='".$id."'";
$res=mysqli_query($con,$sqlSel);
$subid=array();
$i=0;
while($row1=mysqli_fetch_array($res))
{
$subid[$i]=$row1['Sub_ID'];
$i=$i+1;
}
 count($subid);
$drop1 = "";
$drop2 = "";
$drop3 = "";
$drop4 = "";
$drop5 = "";
$drop6 = "";
$drop7 = "";
while($row= mysqli_fetch_array($List1))
{
$cnt=$cnt+1;

if($row['Menu_Id']==1)
{
	if (in_array($row['Id'], $subid)) {
  	$drop1=$drop1."<tr><td>".$cnt."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']." checked></td></tr>";   
  	}
  	else{
  	$drop1=$drop1."<tr><td>".$cnt."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']." ></td></tr>";    
  	}
}
if($row['Menu_Id']==2)
{
	if (in_array($row['Id'], $subid)) {
  	$drop2=$drop2."<tr><td>".$cnt."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']." checked></td></tr>";   
  	}
  	else{
  	 $drop2=$drop2."<tr><td>".$cnt."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']."></td></tr>";   
  	}
}
  if($row['Menu_Id']==3)
{
	if (in_array($row['Id'], $subid)) {
  $drop3=$drop3."<tr><td>".$cnt."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']." checked></td></tr>";   
  	}
  	else{
  	$drop3=$drop3."<tr><td>".$cnt."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']."></td></tr>";   
  	}
  }
   if($row['Menu_Id']==4)
{
		if (in_array($row['Id'], $subid)) {	
  		$drop4=$drop4."<tr><td>".$cnt."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']." checked></td></tr>";   
  		}
  		else{
  		$drop4=$drop4."<tr><td>".$cnt."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']." ></td></tr>";   		
  		}
}
   if($row['Menu_Id']==5)
{
	if (in_array($row['Id'], $subid)) {	
  	$drop4=$drop4."<tr><td>".$cnt."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']." checked></td></tr>";   
  	}
  	else{
  	$drop4=$drop4."<tr><td>".$cnt."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']." ></td></tr>";   		
  	}
}
 
   if($row['Menu_Id']==6)
{
	if (in_array($row['Id'], $subid)) {	
  	$drop6=$drop6."<tr><td>".$cnt."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']." checked></td></tr>";   
  	}
  	else{
 	 $drop6=$drop6."<tr><td>".$cnt."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']."></td></tr>";    
  	}
  }
   if($row['Menu_Id']==7)
{
	if (in_array($row['Menu_Id'], $subid)) {	
  	$drop7=$drop7."<tr><td>".$cnt."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']." checked></td></tr>";   
  	}
  	else{
   	$drop7=$drop7."<tr><td>".$cnt."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']."></td></tr>"; 
  	}
}
 if($row['Menu_Id']==8)
{
	if (in_array($row['Id'], $subid)) {	
  	$drop8=$drop8."<tr><td>".$cnt."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']." checked></td></tr>";   
  	}
  	else{
  	$drop8=$drop8."<tr><td>".$cnt."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']."></td></tr>";   
  	}
  }
  if($row['Menu_Id']==17)
{
	if (in_array($row['Id'], $subid)) {	
  	$drop9=$drop9."<tr><td>".$cnt."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']." checked></td></tr>";   
  		}
  	else{
  	$drop9=$drop9."<tr><td>".$cnt."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']."></td></tr>";   
  	}
  }
  if($row['Menu_Id']==16)
{
	if (in_array($row['Id'], $subid)) {	
  	$drop10=$drop10."<tr><td>".$cnt."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']." checked></td></tr>";   
  	}
  	else{
   	$drop10=$drop10."<tr><td>".$cnt."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']."></td></tr>";   
  	}
}
} 
?>

<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>

	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	<title>Vivanta Logistics</title>
	<meta name="description" content="">
	<meta name="author" content="Ahmed Saeed">
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
	
<script type="text/JavaScript">

function MM_findObj(n, d) { //v4.01
  var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {
    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}
  if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];
  for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);
  if(!x && d.getElementById) x=d.getElementById(n); return x;
}

function MM_validateForm() { //v4.0
  var i,p,q,nm,test,num,min,max,errors='',args=MM_validateForm.arguments;
  for (i=0; i<(args.length-2); i+=3) { test=args[i+2]; val=MM_findObj(args[i]);
    if (val) { nm=val.name; if ((val=val.value)!="") {
      if (test.indexOf('isEmail')!=-1) { p=val.indexOf('@');
        if (p<1 || p==(val.length-1)) errors+='- '+nm+' must contain an e-mail address.\n';
      } else if (test!='R') { num = parseFloat(val);
        if (isNaN(val)) errors+='- '+nm+' must contain a number.\n';
        if (test.indexOf('inRange') != -1) { p=test.indexOf(':');
          min=test.substring(8,p); max=test.substring(p+1);
          if (num<min || max<num) errors+='- '+nm+' must contain a number between '+min+' and '+max+'.\n';
    } } } else if (test.charAt(0) == 'R') errors += '- '+nm+' is required.\n'; }
  } if (errors) alert('The following error(s) occurred:\n'+errors);
  document.MM_returnValue = (errors == '');
}
function fun2()
{
var a=document.getElementById("rccode").value;
//alert(a);
obj=new XMLHttpRequest();
obj.open("GET","ajaxgetrcDetails.php?a="+a,true);
obj.send();
obj.onreadystatechange=funca1
}
function fun()
{
var a=document.getElementById("sname").value;
//alert(a);
obj=new XMLHttpRequest();
obj.open("GET","ajaxgetCustDetails.php?a="+a,true);
obj.send();
obj.onreadystatechange=funca
}
function funca()
{
   if(obj.readyState==4)
     {
	vala=obj.responseText;
	//alert(vala);
	var res = vala.split("*");
		//alert(res[0]);
		document.getElementById("Shippername").value=res[1];
		document.getElementById("Shipperphone").value=res[2];
		document.getElementById("Shipperemail").value=res[3];
		document.getElementById("Shipperaddress").value=res[4];
		document.getElementById("custzip").value=res[5];
	//	document.getElementById("custin").value=res[8];
	//	document.getElementById("cstin").value=res[9];
		document.getElementById("custgst").value=res[10];
		document.getElementById("custpan").value=res[11];
		
		}	
}

function funca1()
{
   if(obj.readyState==4)
     {
	vala=obj.responseText;
	//alert(vala);
	var res = vala.split("*");
		//alert(res[0]);
		document.getElementById("Receivername").value=res[3];
		document.getElementById("Receiveraddress").value=res[6];
		document.getElementById("Receiverphone").value=res[4];
		document.getElementById("Receiveremail").value=res[5];
		document.getElementById("rzip").value=res[7];
		document.getElementById("gstno").value=res[10];
		document.getElementById("rcpan").value=res[11];
		//document.getElementById("custstax").value=res[10];
		//document.getElementById("custpan").value=res[11];
		
		}	
}
function fun1()
{ alert("hiii"); }
</script>

</head>
<?php
include("header.php");
?>
<?php 
		$cnt=0;
$sql1="select * from Sub_Menu order by  Menu_Id";
$List1= mysqli_query($con,$sql1);  
//$Expenselist="";        
//$empname="";
while($row= mysqli_fetch_array($List1))
{
$cnt=$cnt+1;

// $emplist=$emplist."<tr><td>".$row['empname']."</td></tr>";
if($row['Menu_Id']==1)
{
  $drop1=$drop1."<tr><td>".$cnt."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']."></td></tr>";   
  }
if($row['Menu_Id']==2)
{
  $drop2=$drop2."<tr><td>".$cnt."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']."></td></tr>";   
  }
  if($row['Menu_Id']==3)
{
  $drop3=$drop3."<tr><td>".$cnt."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']."></td></tr>";   
  }
   if($row['Menu_Id']==4)
{
  $drop4=$drop4."<tr><td>".$cnt."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']."></td></tr>";   
  }
   if($row['Menu_Id']==5)
{
  $drop5=$drop5."<tr><td>".$cnt."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']."></td></tr>";   
  }
   if($row['Menu_Id']==6)
{
  $drop6=$drop6."<tr><td>".$cnt."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']."></td></tr>";   
  }
   if($row['Menu_Id']==7)
{
  $drop7=$drop7."<tr><td>".$cnt."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']."></td></tr>";   
  }
   if($row['Menu_Id']==8)
{
  $drop8=$drop8."<tr><td>".$cnt."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']."></td></tr>";   
  }
if($row['Menu_Id']==9)
{
$cntr=$cntr+1;
  $drop9=$drop9."<tr><td>".$cntr."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']."></td></tr>";   
  }
  if($row['Menu_Id']==10)
{
$cntr=$cntr+1;
  $drop10=$drop10."<tr><td>".$cntr."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']."></td></tr>";   
  }
   if($row['Menu_Id']==11)
{
$cntr=$cntr+1;
  $drop11=$drop11."<tr><td>".$cntr."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']."></td></tr>";   
  }
   if($row['Menu_Id']==12)
{
$cntr=$cntr+1;
  $drop12=$drop12."<tr><td>".$cntr."</td><td>".$row['Sub_Menu_Name']."</td><td><input type='checkbox' id=".$row['Id']." name='SM[]' value=".$row['Id']."></td></tr>";   
  }
}

?>

<script>
function checkAll(bx) {

  var cbs = document.getElementsByTagName('input');
  for(var i=0; i < cbs.length; i++) {
    if(cbs[i].type == 'checkbox') {
      cbs[i].checked = bx.checked;
    }
  }
}
</script>

</head>			<!--	............................	-->
<script >
function validate(form)
{
    var userName = form.t_name.value;
    var password = form.t_detail.value;
    var errors = [];
 
    if (!checkLength(userName)) {
        errors.push("You must enter a Type Name.");
    }
 
    if (!checkLength(password)) {
        errors.push("You must enter a Type Details.");
    }
 
    if (errors.length > 0) {
        reportErrors(errors);
        return false;
    }
 
    return true;
}

function checkLength(text, min, max){
    min = min || 1;
    max = max || 10000;
 
    if (text.length < min || text.length > max) {
        return false;
    }
    return true;
}
 
function reportErrors(errors){
    var msg = "There were some problems...\n";
    var numError;
    for (var i = 0; i<errors.length; i++) {
        numError = i + 1;
        msg += "\n" + numError + ". " + errors[i];
    } 
    alert(msg);
}

</script>

       
	</br>		 <div class="span8" >    
				

       
       
	  
	      <?php	echo '<form action="Acess_ControlUpdateIn.php?id='.$_GET['id'].'" method="POST">'; ?>
			<table class="table" border="1">
       <tr>
 	<th >User Name&nbsp;&nbsp;&nbsp;&nbsp;</th>
    	<td>
    	<?php 
    	$empSel="select Manager_name from tbl_courier_officers where cid='".$_GET['id']."'";
    	$resultEmp=mysqli_query($con,$empSel);
    	while($row4=mysqli_fetch_array($resultEmp))
    	{
    	echo $row4['Manager_name'];
    	}
    	?>
   	</td>
					
				
   	
	   </tr>	
	   </table>
	     <table class="table"  border="1">
	  	   <tr>
	  	         <th>Sr.No. </th>	   
        <th>Sub Menu List </th>	   
	   <th>Select/Deselect All&nbsp;&nbsp;<input type="checkbox" onClick="checkAll(this)" /> </th></tr>
       

     
	   
       
	  
       
	   <?php echo $drop1; ?>
	   	   <?php echo $drop2; ?>
	 
	 	   <?php echo $drop3; ?>
	   
	   <?php echo $drop4; ?>
	 
	   <?php echo $drop5; ?>

	   <?php echo $drop6; ?>

	   <?php echo $drop7; ?> 
	   
	  
	

	   <tr><td colspan="3" align="center">
       <input type="submit" class="btn btn-primary"  value="Save" class="button small color" /></td></tr> 
	</table>
	
    		</form>  
		</div>
		
		
		
			<div class="span4">
			    	<h4>Menu Set To</h4>
						<table class="table"  border="1">
					
						<tr>
						<th >User Id</th><th>&nbsp;&nbsp;User Role</th>
						<th></th>
						</tr>
					
 
<?php
						 echo $ls;
                                            
			
						?>

						</table>
					</div>
	  <!-- Expense List-->
			  </div>
			 
			</div>
	
<script>
function validation(){
	var custn= document.forms["bookshipment"]["sname1"].selectedIndex;
	if(custn==null || custn=="")
	{
		alert("Please Select Customer Name");
		return false;
	}
	var bookpm= document.forms["bookshipment"]["Bookingmode"].selectedIndex;
	if(bookpm==null || bookpm=="")
	{
		alert("Please Select Payment Mode");
		return false;
	}
	var insuper= document.forms["bookshipment"]["insuran"].selectedIndex;
	if(insuper==null || insuper=="")
	{
		alert("Please Select Insurance Percentage");
		return false;
	}
	var shipname= document.forms["bookshipment"]["Shiname"].value;
	if(shipname==null || shipname=="")
	{
		alert("Please Enter Shipper Name");
		return false;
	}
	var cont= document.forms["bookshipment"]["Shipperphone"].value;
	if(cont==null || cont=="")
	{
		alert("Please enter Contact number");
		return false;
	}
	var shipadd= document.forms["bookshipment"]["Shipperaddress"].value;
	if(shipadd==null || shipadd=="")
	{
		alert("Please Enter Shipper Address");
		return false;
	}
	var recn= document.forms["bookshipment"]["Receivername"].value;
	if(recn==null || recn=="")
	{
		alert("Please Enter Receiver Name");
		return false;
	}
	var recont= document.forms["bookshipment"]["Receiverphone"].value;
	if(recont==null || recont=="")
	{
		alert("Please Enter Receiver Contact No");
		return false;
	}
	var recadd= document.forms["bookshipment"]["Receiveraddress"].value;
	if(recadd==null || recadd=="")
	{
		alert("Please enter Receiver Address");
		return false;
	}
	var toadd= document.forms["bookshipment"]["asdfg1"].value;
	if(toadd==null || toadd=="")
	{
		alert("Please enter Destination");
		return false;
	}
	var rate11= document.forms["bookshipment"]["rate"].value;
	if(rate11==null || rate11=="")
	{
		alert("Please Enter Rate");
		return false;
	}
	var unit11= document.forms["bookshipment"]["unit1"].value;
	if(unit11==null || unit11=="")
	{
		alert("Please enter unit (ftl/perkg)");
		return false;
	}
	var consignl= document.forms["bookshipment"]["ConsignmentNo"].value;
	if(consignl==null || consignl=="")
	{
		alert("Please Enter Consignment No");
		return false;
	}
	var invice_no1= document.forms["bookshipment"]["Invoiceno"].value;
	if(invice_no1==null || invice_no1=="")
	{
		alert("Please ente Invoice Number");
		return false;
	}
	var invicev= document.forms["bookshipment"]["Shiptype"].value;
	if(invicev==null || invicev=="")
	{
		alert("Please Enter Type of Shipment");
		return false;
	}
/*	var actwe12= document.forms["bookshipment"]["Weight"].value;
	if(actwe12==null || actwe12=="")
	{
		alert("Please enter Actual weight");
		return false;
	}
	var chab= document.forms["bookshipment"]["cweight"].value;
	if(chab==null || chab=="")
	{
		alert("Please enter Chargeable Weight");
		return false;
	}
	var qty1= document.forms["bookshipment"]["Qnty"].value;
	if(qty1==null || qty1=="")
	{
		alert("Please Enter Quantity");
		return false;
	}*/
	var mode1= document.forms["bookshipment"]["Mode"].selectedIndex;
	if(mode1==null || mode1=="")
	{
		alert("Please Select Mode");
		return false;
	}
	var delivery11= document.forms["bookshipment"]["delivery"].value;
	if(delivery11==null || delivery11=="")
	{
		alert("Please Select Delivery type");
		return false;
	}
	/*var actwe112= document.forms["bookshipment"]["Weight"].value;
	if(actwe112==null || actwe112=="")
	{
		alert("Please enter Actual weight");
		return false;
	}*/
	
	
}
</script>




<link href="./bootstrap/css/bootstrap-datetimepicker.min.css" rel="stylesheet" media="screen">
	<script type="text/javascript" src="./jquery/jquery-1.8.3.min.js" charset="UTF-8"></script>
<script type="text/javascript" src="./bootstrap/js/bootstrap.min.js"></script>
<script type="text/javascript" src="./jquery/bootstrap-datetimepicker.js" charset="UTF-8"></script>
<script type="text/javascript" src="./jquery/locales/bootstrap-datetimepicker.pl.js" charset="UTF-8"></script>
<script type="text/javascript">
	$('.form_date').datetimepicker({
        language:  'pl',
        weekStart: 1,
        todayBtn:  1,
		autoclose: 1,
		todayHighlight: 1,
		startView: 2,
		minView: 2,
		forceParse: 0
    });
	$('.form_time').datetimepicker({
        language:  'pl',
        weekStart: 1,
        todayBtn:  1,
		autoclose: 1,
		todayHighlight: 1,
		startView: 1,
		minView: 0,
		maxView: 1,
		forceParse: 0
    });
</script>

<script language="JavaScript" type="text/javascript" src="http://ajax.googleapis.com/ajax/libs/jquery/1.11.1/jquery.min.js"></script>

<script>
function getValues(){
	var numVal1 = Number(document.getElementById("hei").value);
	var numVal2 = Number(document.getElementById("wei").value);
	var numVal3 = Number(document.getElementById("len").value);
	//var numVal5 = Number(document.getElementById("freight").value);
	
	var totalValue = (numVal1 * numVal2 * numVal3 )/6000;
	document.getElementById("tot").value = totalValue;
}

function hideqty(id){
	var rat1=document.getElementById("rate").value;
		//alert(rat1);
	if(id=="ftl"){
	//alert("ftl");
		$("#Weight1").hide();
		$("#cweight1").hide();
		$("#volwem1").hide();
		$("#Qnty1").hide();
		
		$("#totfre").val(rat1);
	}
}
</script>
	
<script type="text/javascript" src="http://code.jquery.com/jquery.min.js"></script>
<script type="text/javascript">
$(document).ready(function(){
    $('input[type="radio"]').click(function(){
        if($(this).attr("value")=="account"){
            $(".box").not(".account").hide();
            $(".account").show();
        }
        if($(this).attr("value")=="cash"){
            $(".box").not(".cash").hide();
            $(".cash").show();
        }
      });
});
</script>



<script type="text/javascript">
function consignmen()
{
	var a=document.getElementById("ConsignmentNo").value;
//alert(a);
obja=new XMLHttpRequest();
obja.open("GET","constt.php?a="+a,true);
obja.send();
obja.onreadystatechange=func
}
function func()
{
	//alert("hi");
if(obja.readyState==4)
{
	valar=obja.responseText;
	if(valar!="")
	{
		alert(valar)
	}
}
}


// <!-- <![CDATA[

// Project: Dynamic Date Selector (DtTvB) - 2006-03-16
// Script featured on JavaScript Kit- http://www.javascriptkit.com
// Code begin...
// Set the initial date.
var ds_i_date = new Date();
ds_c_month = ds_i_date.getMonth() + 1;
ds_c_year = ds_i_date.getFullYear();

// Get Element By Id
function ds_getel(id) {
	return document.getElementById(id);
}

// Get the left and the top of the element.
function ds_getleft(el) {
	var tmp = el.offsetLeft;
	el = el.offsetParent
	while(el) {
		tmp += el.offsetLeft;
		el = el.offsetParent;
	}
	return tmp;
}
function ds_gettop(el) {
	var tmp = el.offsetTop;
	el = el.offsetParent
	while(el) {
		tmp += el.offsetTop;
		el = el.offsetParent;
	}
	return tmp;
}

// Output Element
var ds_oe = ds_getel('ds_calclass');
// Container
var ds_ce = ds_getel('ds_conclass');

// Output Buffering
var ds_ob = ''; 
function ds_ob_clean() {
	ds_ob = '';
}
function ds_ob_flush() {
	ds_oe.innerHTML = ds_ob;
	ds_ob_clean();
}
function ds_echo(t) {
	ds_ob += t;
}

var ds_element; // Text Element...

var ds_monthnames = [
'January', 'February', 'March', 'April', 'May', 'June',
'July', 'August', 'September', 'October', 'November', 'December'
]; // You can translate it for your language.

var ds_daynames = [
'Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'
]; // You can translate it for your language.

// Calendar template
function ds_template_main_above(t) {
	return '<table cellpadding="3" cellspacing="1" class="ds_tbl">'
	     + '<tr>'
		 + '<td class="ds_head" style="cursor: pointer" onclick="ds_py();">&lt;&lt;</td>'
		 + '<td class="ds_head" style="cursor: pointer" onclick="ds_pm();">&lt;</td>'
		 + '<td class="ds_head" style="cursor: pointer" onclick="ds_hi();" colspan="3">[Close]</td>'
		 + '<td class="ds_head" style="cursor: pointer" onclick="ds_nm();">&gt;</td>'
		 + '<td class="ds_head" style="cursor: pointer" onclick="ds_ny();">&gt;&gt;</td>'
		 + '</tr>'
	     + '<tr>'
		 + '<td colspan="7" class="ds_head">' + t + '</td>'
		 + '</tr>'
		 + '<tr>';
}

function ds_template_day_row(t) {
	return '<td class="ds_subhead">' + t + '</td>';
	// Define width in CSS, XHTML 1.0 Strict doesn't have width property for it.
}

function ds_template_new_week() {
	return '</tr><tr>';
}

function ds_template_blank_cell(colspan) {
	return '<td colspan="' + colspan + '"></td>'
}

function ds_template_day(d, m, y) {
	return '<td class="ds_cell" onclick="ds_onclick(' + d + ',' + m + ',' + y + ')">' + d + '</td>';
	// Define width the day row.
}

function ds_template_main_below() {
	return '</tr>'
	     + '</table>';
}

// This one draws calendar...
function ds_draw_calendar(m, y) {
	// First clean the output buffer.
	ds_ob_clean();
	// Here we go, do the header
	ds_echo (ds_template_main_above(ds_monthnames[m - 1] + ' ' + y));
	for (i = 0; i < 7; i ++) {
		ds_echo (ds_template_day_row(ds_daynames[i]));
	}
	// Make a date object.
	var ds_dc_date = new Date();
	ds_dc_date.setMonth(m - 1);
	ds_dc_date.setFullYear(y);
	ds_dc_date.setDate(1);
	if (m == 1 || m == 3 || m == 5 || m == 7 || m == 8 || m == 10 || m == 12) {
		days = 31;
	} else if (m == 4 || m == 6 || m == 9 || m == 11) {
		days = 30;
	} else {
		days = (y % 4 == 0) ? 29 : 28;
	}
	var first_day = ds_dc_date.getDay();
	var first_loop = 1;
	// Start the first week
	ds_echo (ds_template_new_week());
	// If sunday is not the first day of the month, make a blank cell...
	if (first_day != 0) {
		ds_echo (ds_template_blank_cell(first_day));
	}
	var j = first_day;
	for (i = 0; i < days; i ++) {
		// Today is sunday, make a new week.
		// If this sunday is the first day of the month,
		// we've made a new row for you already.
		if (j == 0 && !first_loop) {
			// New week!!
			ds_echo (ds_template_new_week());
		}
		// Make a row of that day!
		ds_echo (ds_template_day(i + 1, m, y));
		// This is not first loop anymore...
		first_loop = 0;
		// What is the next day?
		j ++;
		j %= 7;

	}
	// Do the footer
	ds_echo (ds_template_main_below());
	// And let's display..
	ds_ob_flush();
	// Scroll it into view.
	ds_ce.scrollIntoView();
}

// A function to show the calendar.
// When user click on the date, it will set the content of t.
function ds_sh(t) {
	// Set the element to set...
	ds_element = t;
	// Make a new date, and set the current month and year.
	var ds_sh_date = new Date();
	ds_c_month = ds_sh_date.getMonth() + 1;
	ds_c_year = ds_sh_date.getFullYear();
	// Draw the calendar
	ds_draw_calendar(ds_c_month, ds_c_year);
	// To change the position properly, we must show it first.
	ds_ce.style.display = '';
	// Move the calendar container!
	the_left = ds_getleft(t);
	the_top = ds_gettop(t) + t.offsetHeight;
	ds_ce.style.left = the_left + 'px';
	ds_ce.style.top = the_top + 'px';
	// Scroll it into view.
	ds_ce.scrollIntoView();
}

// Hide the calendar.
function ds_hi() {
	ds_ce.style.display = 'none';
}

// Moves to the next month...
function ds_nm() {
	// Increase the current month.
	ds_c_month ++;
	// We have passed December, let's go to the next year.
	// Increase the current year, and set the current month to January.
	if (ds_c_month > 12) {
		ds_c_month = 1; 
		ds_c_year++;
	}
	// Redraw the calendar.
	ds_draw_calendar(ds_c_month, ds_c_year);
}

// Moves to the previous month...
function ds_pm() {
	ds_c_month = ds_c_month - 1; // Can't use dash-dash here, it will make the page invalid.
	// We have passed January, let's go back to the previous year.
	// Decrease the current year, and set the current month to December.
	if (ds_c_month < 1) {
		ds_c_month = 12; 
		ds_c_year = ds_c_year - 1; // Ca
		
		</script>
		<?php
include("footer.php");
?>