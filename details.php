<?php
session_start();
require_once('database.php');
require_once('library.php');
require 'connection.php';
isUser();

$cons= $_GET['id'];
//echo $cons;

//$sql="SELECT * FROM currentloc  WHERE `consig_no` = '$cons'";

$sql="SELECT `office`,current_date,current_time,`off_name` FROM (`currentloc` inner join `tbl_offices` on  `currentloc`.`current_loc`= `tbl_offices`.`id`)inner join `tbl_courier_officers` on `currentloc`.`sourcesid`= `tbl_courier_officers`.`cid` where  `currentloc`.`consig_no`='$cons'";

$result = mysqli_query($con,$sql);  
$tr="";     
$count=0;

while($row= mysqli_fetch_array($result))
{
	
$tr=$tr."<tr><td>".++$count."</td><td>".$row['office']."</td><td>".$row['off_name']."</td><td>".$row['current_date']."</td><td>".$row['current_time']."</td></tr>";
}


$sql = "SELECT * FROM tbl_courier WHERE cons_no = '$cons'" ;
	//	echo $sql;
$result = dbQuery($sql);
$no = dbNumRows($result);
if($no == 1){
while($data = dbFetchAssoc($result)) {
extract($data);


?>
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>
	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
	
</head>
<?php
include("header.php");
?>
      
		<div class="container">
			<div class="row">
				<div class="span2">
				</div><!--end span8-->

                <div class="span8">
					<div class="account-list-outer">

						<div class="titleHeader clearfix">
							<h3>Consignment Details </h3>
						</div><!--end titleHeader-->
	<table class="table" border="2"> 
            <tbody>     <tr> 
							    <td> 
        <table class="table" border="1" >
			<tbody> <th align="center"> Shipper Details </th> <th>&nbsp;&nbsp;&nbsp; </th>
			    <tr>
					<td >Shipper Name : <?php echo $ship_name; ?></td>
					<td >TIN No :  <?php echo $custin; ?> </td>
				</tr>
				<tr>
					<td>Shipper Phone :<?php echo $phone; ?> </td>
					<td>Service TAX No :  <?php echo $custstax; ?>   </td>
				</tr>
				<tr>
					<td>Shipper Address :<?php echo $s_add; ?></td>
					<td>PAN No : <?php echo $custpan; ?>  </td>
				</tr>
				<tr>
					<td>Email Id :</td>
					<td> <?php echo $smail; ?>  </td>
				</tr>
								
			</tbody>
		</table>
                             </td > </tr>
					   <tr>
							 <td >

        <table class="table" border="1" >
			<tbody> <th> Receiver Details </th><th>&nbsp;&nbsp;&nbsp;&nbsp; </th>
				<tr>
					<td width="55%">Receiver Name :<?php echo $rev_name; ?> </td>
					<td width="45%">Receiver Phone :  <?php echo $r_phone; ?> </td>
				</tr>
				<tr>
					<td>Receiver Address :<?php echo $r_add; ?></td>
					<td>Receiver TIN No :  <?php echo $rtin; ?>  </td>
				</tr>
				<tr>
					<td>Receiver E-mail Id :</td>
					<td> <?php echo $rmail; ?>  </td>
				</tr>
				
			</tbody>
		</table>
                             </td >
									</tr> 
	<tr>
      <td >
        <table class="table" border="1">
			<tbody><th> Courier Details </th><th> &nbsp;&nbsp;</th>
	<tr> 
      <td >Consignment No :&nbsp;  <?php echo $cons_no; ?>&nbsp;</td> 
      <td >Booking Mode : &nbsp; <?php echo $book_mode; ?>&nbsp;</td> 
    </tr> 
    <tr>
      <td >Type of Shipment :&nbsp; <?php echo $type; ?></td>
      <td> Mode :&nbsp;<?php echo $mode; ?></td>
    </tr>
    <tr>
      <td >Actual Weight :&nbsp; <?php echo $weight; ?></td>
      <td>Chargable Weight :&nbsp; <?php echo $chweight; ?>&nbsp;Kg</td>
    </tr>
    <tr>
      <td >Invoice no :&nbsp;  <?php echo $invice_no; ?></td>
      <td>Invoice Value :&nbsp; <?php echo $invi_value; ?>&nbsp;</td>
    </tr>
    <tr>
      <td >Qunty :&nbsp;<?php echo $qty; ?> </td>
      <td>Total Freight :Rs.&nbsp; <?php echo $freight; ?></td>
    </tr>
    <tr>
      <td >COD/DOD :<?php echo $dod_cod; ?></td>
      <td>ODA/Mis :<?php echo $oda_mis; ?></td>
    </tr> 
    <tr> 
      <td >Pickup Date :<?php echo $pick_date; ?></td> 
      <td>Pickup Time :<?php echo $pick_time; ?></td> 
    </tr> 
    <tr> 
      <td>Docket Charges :<?php echo $dock_charg; ?></td> 
      <td>Services Tax (%) :<?php echo $st; ?></td> 
    </tr>  
    <tr> 
      <td >&nbsp;Status :</td> 
      <td>&nbsp;<?php echo $status; ?></td> 
    </tr> 
    	</tbody>
		</table>
		</td>
    </tr>
	
    </tbody>
		</table> 	

 <table border="1" class="table">
		<thead>
			<tr>
				<th >Sr.No</th>
				<th >Sources</th>
				<th >Destination</th>
				<th >Date</th>
				<th >Time</th>
			</tr>
		</thead>
		<tbody>     
				<?php echo $tr;?>
		</tbody>  	
				                    
	</table>



</div><!--end -->
				</div><!--end span6-->
			</div><!--end row-->
		</div><!--end conatiner-->
		
		
<?php }
include("footer.php");
}
else {
//echo 'In else....';
?>
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>
	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
	
</head>
<?php
include("header.php");
?>
      
		<div class="container">
			<div class="row">
				<div class="span2">
				</div><!--end span8-->

                <div class="span8">
					<div class="account-list-outer">

						<div class="titleHeader clearfix">
							<h3>Consignment Details </h3>
						</div><!--end titleHeader-->
					 <center> <h2>Consignment Number <font color="#FF0F0F"><?php echo $cons; ?></font> not found. Please verify the Number.<br>
                          <a href="datewiseReport.php">Go Back</a> to Try Again.</h2> </center>	 
	
                    </div><!--end -->
				</div><!--end span6-->
			</div><!--end row-->
		</div><!--end conatiner-->
		
<?php
include("footer.php");
}
?>