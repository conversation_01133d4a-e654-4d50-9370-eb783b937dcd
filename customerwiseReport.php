<?php
session_start();
require 'connection.php';
require_once('database.php');
require_once('library.php');
isUser();

// Initialize variables
$company = $_POST['company'] ?? $_GET['company'] ?? '';
$drop1 = '';
$custid = $_POST['custid'] ?? $_GET['custid'] ?? '';
$date1 = $_POST['date1'] ?? $_GET['date1'] ?? '';
$date2 = $_POST['date2'] ?? $_GET['date2'] ?? '';

$sql="select * from custreg order by custname ASC";
$result2=mysqli_query($con,$sql);
while($row2=mysqli_fetch_array($result2))
{
	if($company==$row2['custid'])
	{
	$drop1=$drop1."<option value='".$row2['custid']."' selected>".$row2['custname']."</option>";
	}
	else{
	 $drop1=$drop1."<option value='".$row2['custid']."' >".$row2['custname']."</option>";
	}
}
  $sql="SELECT * FROM tbl_courier inner join status on tbl_courier.status=status.statusid WHERE tbl_courier.shipper_code ='$custid' and tbl_courier.book_date between '$date1' and '$date2'   ";	
 

$sql_r="SELECT * FROM `receiver_reg` order by receivername ASC";
$result2=mysqli_query($con,$sql_r);
$drop_r="";
while($row2=mysqli_fetch_array($result2))
{
	if($company==$row2['id'])
	{
	$drop_r=$drop_r."<option value='".$row2['id']."' selected>".$row2['receivername']."</option>";
	}
	else{
 	 $drop_r=$drop_r."<option value='".$row2['id']."' >".$row2['receivername']."</option>";
	}
}
  $sql="SELECT * FROM tbl_courier inner join status on tbl_courier.status=status.statusid WHERE tbl_courier.shipper_code ='$custid' and tbl_courier.book_date between '$date1' and '$date2'   ";	
?>


<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>

	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons 
	================================================== -->
<script src="js/jquery-1.7.2.min.js"></script>
   
</head>
<?php include("header.php"); ?>

   <div class="container">
			<div class="row">
     			<div class="span2">
				<div class="control-group ">
				
				</div>
				</div><!--end span3-->
                	 <form method="post" action="reporta.php" >
                <div class="span8">
					<div class="account-list-outer">
						<div class="control-group ">
						   <div class="controls">
							 <label class="control-label">Customer  <span class="text-error">*</span></label>
						
				                 <select name="rep" id="rep">
										<option value="">------- Please Select-------</option>
										<?php echo $drop1; ?>
										
								 </select>
						
          					</div>	
          				<!--	<div class="control-group ">
						   <div class="controls">
							 <label class="control-label">Receiver Report : <span class="text-error">*</span></label>
						
				                 <select name="rep" id="rep2">
										<option value="">------- Please Select-------</option>
										<?php echo $drop_r; ?>
										
								 </select>
						
          					</div>	 
						</div>
						</div>-->
						<div class="control-group ">
						   <div class="controls">
							<label for="rep"> Start Date : <span class="text-error">*</span></label>
								<div class="controls input-append date form_date" data-date="" data-date-format="dd MM yyyy" data-link-field="dtp_input2" data-link-format="yyyy-mm-dd">
									 <input  type="text" value=""id="date2" name="date2" readonly>	
										<span class="add-on"><i class="icon-remove"></i></span>
										 <span class="add-on"><i class="icon-th"></i></span>
								</div>
									 <input type="hidden" id="dtp_input2" value="" />
						    </div>
						</div>
						<div class="control-group ">
						   <div class="controls">
							<label class="control-label" for="rep"> End Date : <span class="text-error">*</span></label>
								<div  class="controls input-append date form_date" data-date="" data-date-format="dd MM yyyy" data-link-field="dtp_input2" data-link-format="yyyy-mm-dd">
									<input  type="text" value="" id="date4" name="date4" readonly>	
										<span class="add-on"><i class="icon-remove"></i></span>
										 <span class="add-on"><i class="icon-th"></i></span>
								</div>
									 <input type="hidden" id="dtp_input2" value="" />
						   </div>
						</div>
					
							<div class="control-group ">
							     <div class="controls">
								     <input class="btn btn-primary"  type="submit" value="Submit" >
							     </div>
							</div>	
							</form>
						
							
				
							
							<div id="cmp"></div>
					</div><!--end -->
				</div><!--end span6-->
			</div><!--end row-->
		</div><!--end conatiner-->
<!-- <link href="./bootstrap/css/bootstrap.min.css" rel="stylesheet" media="screen">-->
    <link href="./bootstrap/css/bootstrap-datetimepicker.min.css" rel="stylesheet" media="screen">		
<script type="text/javascript" src="./jquery/jquery-1.8.3.min.js" charset="UTF-8"></script>
<script type="text/javascript" src="./bootstrap/js/bootstrap.min.js"></script>
<script type="text/javascript" src="./jquery/bootstrap-datetimepicker.js" charset="UTF-8"></script>
<!--<script type="text/javascript" src="./jquery/locales/bootstrap-datetimepicker.pl.js" charset="UTF-8"></script>-->
<script type="text/javascript">
   	$('.form_date').datetimepicker({
        language:  'pl',
        weekStart: 1,
        todayBtn:  1,
		autoclose: 1,
		todayHighlight: 1,
		startView: 2,
		minView: 2,
		forceParse: 0
    });
	
</script>

 
<?php
echo "<script> 
function abc()
{

var rep=document.getElementById('rep').value;
if(rep)
{
    
}
else
{
   var rep=document.getElementById('rep2').value;
}
var date=document.getElementById('date2').value;
var date2=document.getElementById('date4').value;
if(rep=='')
{
alert('Please Select Status Report Name');
}
else if(date=='')
{
alert('Please Fill Start Date');
}
else if(date2=='')
{
alert('Please Fill End Date');
}
}
</script>";
?>

<script>
function getRep()
{
	//alert("hello");
var date=document.getElementById("date2").value;
var date2=document.getElementById("date4").value;
var rep=document.getElementById("rep").value;
//alert(rep);
if(rep)
{
    
}
else
{
   var rep=document.getElementById('rep2').value;
}
if (window.XMLHttpRequest)
  {// code for IE7+, Firefox, Chrome, Opera, Safari
  xmlhttp=new XMLHttpRequest();
  }
else
  {// code for IE6, IE5
  xmlhttp=new 
   
  veXObject("Microsoft.XMLHTTP");
  }
xmlhttp.onreadystatechange=function()
  {
  if (xmlhttp.readyState==4 && xmlhttp.status==200)
    {
   // alert (xmlhttp.responseText);
   document.getElementById("cmp").innerHTML=xmlhttp.responseText;
    }
  }
  xmlhttp.open("GET","getdetails2.php?date="+date+"&date2="+date2+"&rep="+rep);
xmlhttp.send();

}




</script>
<!--<script>
function printDiv(divID) {
       
            //Get the HTML of div
            var divElements = document.getElementById(divID).innerHTML;
            //Get the HTML of whole page
            var oldPage = document.body.innerHTML;

            //Reset the page's HTML with div's HTML only
            document.body.innerHTML = 
             echo "<html><head><title></title></head><body>" + 
              divElements + "</body>";

            //Print Page
            window.print();

            //Restore orignal HTML
            document.body.innerHTML = oldPage;

          
        }

</script>-->
		
		
<?php 


include("footer.php"); ?>