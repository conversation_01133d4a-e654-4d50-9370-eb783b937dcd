
<?php 
session_start();
require 'connection.php';
require_once('database.php');
require_once('library.php');
ob_start();

// Handle different date parameter scenarios
$date_condition = "";
if (isset($_GET['date'])) {
    // Single date from dashboard
    $selected_date = $_GET['date'];
    $date_condition = "AND DATE(status_date) = '$selected_date'";
} elseif (isset($_GET['start_date']) && isset($_GET['end_date']) && !empty($_GET['start_date']) && !empty($_GET['end_date'])) {
    // Date range from form with format conversion
    $start_date_input = trim($_GET['start_date']);
    $end_date_input = trim($_GET['end_date']);

    // Handle different date formats
    $start_date = '';
    $end_date = '';

    // Try to parse different date formats
    if (preg_match('/^(\d{1,2})-(\d{1,2})-(\d{4})$/', $start_date_input, $matches1)) {
        // DD-MM-YYYY format
        $start_date = $matches1[3] . '-' . sprintf('%02d', $matches1[2]) . '-' . sprintf('%02d', $matches1[1]);
    } elseif (preg_match('/^(\d{4})-(\d{1,2})-(\d{1,2})$/', $start_date_input)) {
        // YYYY-MM-DD format (already correct)
        $start_date = $start_date_input;
    } else {
        // Try strtotime as fallback
        $timestamp = strtotime($start_date_input);
        if ($timestamp !== false) {
            $start_date = date('Y-m-d', $timestamp);
        }
    }

    if (preg_match('/^(\d{1,2})-(\d{1,2})-(\d{4})$/', $end_date_input, $matches2)) {
        // DD-MM-YYYY format
        $end_date = $matches2[3] . '-' . sprintf('%02d', $matches2[2]) . '-' . sprintf('%02d', $matches2[1]);
    } elseif (preg_match('/^(\d{4})-(\d{1,2})-(\d{1,2})$/', $end_date_input)) {
        // YYYY-MM-DD format (already correct)
        $end_date = $end_date_input;
    } else {
        // Try strtotime as fallback
        $timestamp = strtotime($end_date_input);
        if ($timestamp !== false) {
            $end_date = date('Y-m-d', $timestamp);
        }
    }

    // Only apply filter if both dates are valid
    if (!empty($start_date) && !empty($end_date)) {
        $start_date = mysqli_real_escape_string($con, $start_date);
        $end_date = mysqli_real_escape_string($con, $end_date);
        $date_condition = "AND DATE(status_date) BETWEEN '$start_date' AND '$end_date'";
    }
} elseif (isset($_GET['date1']) && isset($_GET['date2']) && !empty($_GET['date1']) && !empty($_GET['date2'])) {
    // Legacy date range parameters
    $date1 = $_GET['date1'];
    $date2 = $_GET['date2'];
    $Cdate = date('Y-m-d', strtotime($date1));
    $Cdate1 = date('Y-m-d', strtotime($date2));
    $date_condition = "AND DATE(status_date) BETWEEN '$Cdate' AND '$Cdate1'";
} else {
    // No date filter - show all records
    $date_condition = "";
}

// Build the WHERE clause (same logic as main page)
$whereClause = "WHERE status = '5'";
if (!empty($date_condition)) {
    $whereClause .= " " . $date_condition;
}

// Build the SQL query with appropriate date filtering (same as main page)
$sql = "SELECT cons_no,weight,gtotamt,rev_name,r_add,ship_name,book_mode,userid,status_date,noofpackage,status,e_waybill,eway_expdate,eway_start_date,eway_end_date,gst,partno,remark,freight,book_mode,invi_value,qty,
assured_dly_date,book1_date,a.city_name as city ,type,invice_no,rate,oda_mis,chweight,mode,statusname,vehicle FROM (tbl_courier inner join status on tbl_courier.status=status.statusid)
join  tbl_city_code a on tbl_courier.s_add=a.Id
$whereClause ORDER BY cons_no DESC";

$result = mysqli_query($con, $sql);

// Debug: Check if query executed and has results
if (!$result) {
    die("Export Query failed: " . mysqli_error($con));
}

$num_rows = mysqli_num_rows($result);
$count = 0;
$tr = '';

// Process the results

while ($row = mysqli_fetch_array($result)) {
    $count++;

    // Add error handling for each row
    try {

    // Shipper details
    $shipper_name = $shipper_phone = $shipper_mail = $shipper_add = $shipper_city = '';
    $sql_s = "SELECT * FROM custreg WHERE custname = '" . mysqli_real_escape_string($con, $row['ship_name']) . "'";
    $result_s = mysqli_query($con, $sql_s);
    if ($row_s = mysqli_fetch_array($result_s)) {
        $shipper_name = $row_s['custname'];
        $shipper_phone = $row_s['custphone'];
        $shipper_mail = $row_s['custmail'];
        $shipper_add = $row_s['custadd'];

        $city_id = $row_s['custcity'];
        $sql_city = "SELECT cityname FROM city WHERE ctid = '$city_id'";
        $result_city = mysqli_query($con, $sql_city);
        if ($row_city = mysqli_fetch_array($result_city)) {
            $shipper_city = $row_city['cityname'];
        } else {
            $shipper_city = 'Unknown';
        }
    } else {
        $shipper_name = $row['ship_name'];
        $shipper_city = 'Unknown';
    }

    // Manager Info
    $Manager_name = $empcode = '';
    $sql2 = "SELECT Manager_name, empcode FROM tbl_courier_officers WHERE cid = '" . $row['userid'] . "'";
    $result2 = mysqli_query($con, $sql2);
    if ($row2 = mysqli_fetch_array($result2)) {
        $Manager_name = $row2['Manager_name'];
        $empcode = $row2['empcode'];
    } else {
        $Manager_name = 'Unknown';
        $empcode = 'Unknown';
    }

    // Destination
    $destination = '';
    if (!empty($row['r_add'])) {
        $sql1 = "SELECT city_name FROM tbl_city_code WHERE Id = '" . $row['r_add'] . "'";
        $result1 = mysqli_query($con, $sql1);
        if ($row1 = mysqli_fetch_array($result1)) {
            $destination = $row1['city_name'];
        }
    }

    // Status dates (same logic as main page)
    $statusdate = in_array($row['status'], ['6', '50', '57']) ? $row['status_date'] : '';

    // Delivered date - only for delivered records (status = 5)
    $delivereddate = ($row['status'] == '5') ? $row['status_date'] : '';

    // GST calculations
    $gst = isset($row['gst']) ? $row['gst'] : 0;
    $invi_value = isset($row['invi_value']) ? $row['invi_value'] : 0;
    $withoutgstvalue = $gst > 0 ? round($invi_value / (1 + ($gst / 100)), 2) : $invi_value;

    // Get vehicle information
    $vehicle = isset($row['vehicle']) ? $row['vehicle'] : '';

    $tr .= "<tr>
        <td>{$count}</td>
        <td>{$Manager_name}</td>
        <td>{$empcode}</td>
        <td>{$shipper_name}</td>
        <td>{$shipper_city}</td>
        <td>{$row['status_date']}</td>
        <td>{$row['cons_no']}</td>
        <td>{$row['book1_date']}</td>
        <td>{$row['invice_no']}</td>
        <td>{$row['rev_name']}</td>
        <td>{$destination}</td>
        <td>{$row['noofpackage']}</td>
        <td>{$row['qty']}</td>
        <td>{$row['partno']}</td>
        <td>{$withoutgstvalue}</td>
        <td>{$invi_value}</td>
        <td>{$statusdate}</td>
        <td>{$row['book_mode']}</td>
        <td>{$row['statusname']}</td>
        <td>{$row['remark']}</td>
        <td>{$delivereddate}</td>
        <td>{$row['mode']}</td>
        <td>{$row['weight']}</td>
        <td>{$row['chweight']}</td>
        <td>{$row['e_waybill']}</td>
        <td>" . (isset($row['eway_start_date']) ? $row['eway_start_date'] : '') . "</td>
        <td>" . (isset($row['eway_end_date']) ? $row['eway_end_date'] : '') . "</td>
        <td>{$vehicle}</td>
        <td>{$row['rate']}</td>
        <td>{$row['oda_mis']}</td>
        <td>{$row['freight']}</td>
        <td>{$gst}</td>
        <td>{$row['gtotamt']}</td>
    </tr>";

    } catch (Exception $e) {
        // If there's an error with this row, add an error row
        $tr .= "<tr><td colspan='33' style='background: #ffebee; color: red;'>Error processing record {$count}: " . $e->getMessage() . "</td></tr>";
    }
}

// Excel headers
header("Content-Type: application/vnd.ms-excel; charset=utf-8");
header("Content-Disposition: attachment; filename=Delivery_Report.xls");
header("Pragma: no-cache");
header("Expires: 0");

// Output Excel table
echo '<table border="1" style="width:100%">
    <thead>
        <tr>
            <th>Sr No.</th>
            <th>Employee Name</th>
            <th>Emp-ID</th>
            <th>Shipper Name</th>
            <th>Shipper City</th>
            <th>Scanning Date</th>
            <th>LR No</th>
            <th>BKG Date</th>
            <th>Invoice No</th>
            <th>Customer Name</th>
            <th>Destination</th>
            <th>Cases</th>
            <th>Qty</th>
            <th>Part No.</th>
            <th>Without GST Value</th>
            <th>Invoice Value</th>
            <th>Godown Receipt Date</th>
            <th>Type</th>
            <th>Status</th>
            <th>Remarks</th>
            <th>Delivery Date</th>
            <th>Mode</th>
            <th>A/Weight</th>
            <th>C/Weight</th>
            <th>E-WayBill No.</th>
            <th>E-Way Start Date</th>
            <th>E-Way End Date</th>
            <th>Vehicle No.</th>
            <th>Rate</th>
            <th>ODA</th>
            <th>Freight</th>
            <th>GST</th>
            <th>Total</th>
        </tr>
    </thead>
    <tbody>' . $tr . '</tbody>
</table>';
?>
