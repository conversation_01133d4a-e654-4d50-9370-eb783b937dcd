<?php
require 'conn.php';
 $DefaultId = 0;
 
 $ImageData = $_POST['image_path'];
 
 $ImageName = $_POST['image_name'];
 $imagname="$ImageName.jpg";

 $GetOldIdSQL ="SELECT cid FROM  tbl_courier ORDER BY cid ASC";
 
 $Query = mysqli_query($con,$GetOldIdSQL);
 
 while($row = mysqli_fetch_array($Query)){
 
 $DefaultId = $row['cid'];
 }
 
 $year = date("Y");
 $path = "images/".$year;
  if(!file_exists($path))
  {
 mkdir($path);
}
$month = date("m");
 $ImagePath = "images/".$year."/".$month."/";

 if(!file_exists($ImagePath))
  {
 mkdir($ImagePath);
      
  }
  $ImagePath = $ImagePath .$ImageName."";
// $ImagePath = "images/$ImageName.jpeg";
 
 echo $ServerURL = "https://vivantalogistics.in/vivanta/PHP-API/$ImagePath"; 
echo $InsertSQL = "UPDATE `tbl_courier` SET pod_img='".$imagname."',pod_imag_path='$ServerURL' where cons_no='$ImageName' ";

 //$InsertSQL = "insert into UploadImageToServer (image_path,image_name) values ('$ServerURL','$ImageName')";
 
 if(mysqli_query($con, $InsertSQL)){

 file_put_contents($ImagePath,base64_decode($ImageData));

 echo "Your Image Has Been Uploaded.";
 
 

 }else{
 echo "Not Uploaded";
 }

/*//$Sql_Query = "UPDATE `tbl_courier` SET userid='$id',book_date='$date1',userid='$id',pod_img='".$_FILES['photo']['name']."' where cons_no='$qrcode' ";
$Sql_Query = "UPDATE `tbl_courier` SET status='$status',status_date = NOW(),userid='$id', pod_img='".$_FILES['image']['name']."',remark='$remark',status_update_datetime ='$date' where cons_no='A1003550' ";


 
if(mysqli_query($con,$Sql_Query))
{
 
echo $ImagePath;
  }
else{
 
 echo 'Try Again';
 
 }
 mysqli_close($con);*/
 
?>