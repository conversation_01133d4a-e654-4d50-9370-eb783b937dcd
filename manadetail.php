<?php
session_start();
require 'connection.php';

require_once('database.php');
require_once('library.php');
isUser();
$num_rec_per_page=10;
if (isset($_GET["page"])) { $page  = $_GET["page"]; } else { $page=1; }; 
$start_from = ($page-1) * $num_rec_per_page; 
$sql = "SELECT * FROM tbl_courier_officers inner join tbl_offices on tbl_courier_officers.office=tbl_offices.id  LIMIT $start_from, $num_rec_per_page";
$result = mysqli_query($con,$sql);		

?>
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>

	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
	
</head>
<script language="JavaScript">
var checkflag = "false";

function check(field) {
if (checkflag == "false")
 {
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll")
	{
	field[i].checked=true;	
	}
	}
	checkflag = "true";
}
else
{
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll")
	{
	field[i].checked=false;
	}
	}
	checkflag = "false";
}

}
function confirmDel(field,msg)
{
	count=0;
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll" && field[i].checked==true)
	{
	count++;
	}
	}
	
	if(count == 0)
	{
		alert("Select any one record to delete!");
		return false;
	}
	else
	{
		return confirm(msg);
	}
}
</script>

<?php include("header.php"); ?>



		<div class="container">

			<div class="row">

				              
                
                <div class="span11">
					<div class="account-list-outer">

						<div class="titleHeader clearfix">
							<h3>View All Manager Details   </h3>
						</div><!--end titleHeader-->

						<table class="table">
						<thead>
							<tr>
							<!--<th><h5>Sr.No </h5></th>-->
								<th><h5>Manager Name </h5></th>
								<th><h5>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Phone Number</h5></th> 
								<th><h5>Email&nbsp;&nbsp;&nbsp;&nbsp; </h5></th>		
								<th><h5>Office Name&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </h5></th>
								<th><h5>Office Address </h5></th>
								
							</tr>
						</thead>
	<?php
	
	while($data = mysqli_fetch_array($result)){
	extract($data);	
	?>
						<tbody>
							<tr>
								<!--<td><!--Sr.No
									
								</td>-->
								<td ><!--Manager Name-->
									<?php echo $Manager_name; ?>
								</td>
								<td><!--Contact Number-->
									<?php echo $ph_no; ?>
								</td>
								<td><!--Email-->
								<?php echo $email; ?>
								</td>
								<td ><!--Office Name-->
									<?php echo $off_name; ?>
								</td>
								<td><!--Office Address-->
									<?php echo $address; ?>
								</td>
								                                
							</tr>
							
						</tbody>
						<?php
	}//while
	?>
					</table>
				

				
					</div><!--end -->
				</div><!--end span-->


				
			</div><!--end row-->



			<!--end row-->


			<!--end row-->


		</div><!--end conatiner-->
<?php 
$sql = "SELECT * FROM tbl_courier_officers"; 
$rs_result = mysqli_query($con,$sql); //run the query
$total_records = mysqli_num_rows($rs_result);  //count number of records
$total_pages = ceil($total_records / $num_rec_per_page); 

echo "<a href='manadetail.php?page=1'>".'|<'."</a> "; // Goto 1st page  

for ($i=1; $i<=$total_pages; $i++) { 
            echo "<a href='manadetail.php?page=".$i."'>".$i."</a> "; 
}; 

include("footer.php"); ?>