<?php 
session_start();
require_once('library.php');
require 'connection.php';
$rand = get_rand_id(8);
//echo $rand;

$a=$_SESSION['username'];
$sql="select * from login where username='$a'";
$result = mysqli_query($con, $sql);
$row1 = mysqli_fetch_array($result, MYSQLI_BOTH);
$userid = ($row1 && isset($row1['rid'])) ? $row1['rid'] : 0;

// Initialize variables
$cityname = $_POST['cityname'] ?? '';
$citydrop = $_POST['citydrop'] ?? '';

	 $statesql="SELECT * FROM tbl_city_code";
 $cityresult=mysqli_query($con,$statesql);
 while($staterow=mysqli_fetch_array($cityresult))
{
	if($cityname==$staterow['Id'])
	{
	 $citydrop=$citydrop."<option value='".$staterow['Id']."' selected>".$staterow['city_name']."-".$staterow['city_code']."</option>";
	}
	else{
	  $citydrop=$citydrop."<option value='".$staterow['Id']."' >".$staterow['city_name']."-".$staterow['city_code']."</option>";
	}
}


$query = "SELECT MAX(custid) AS custid FROM custreg ";  
    if($result = mysqli_query($con,$query))
    {
  while ($row = mysqli_fetch_assoc($result))
  {
        $count = $row['custid'];
        $count = $count+1;

      $code_no = str_pad($count, 7, "0", STR_PAD_LEFT);
  }
	}

	$he = $_POST['cstin'] ?? 0;
	$we = $_POST['custstax'] ?? 0;
	$len = $_POST['custpan'] ?? 0;

	// echo $he;
	// echo $we;
	// echo $len;

	 $tot=($len*$we*$he)/6000;


	// Initialize variables
	$company = $_POST['company'] ?? '';
	$drop1 = $_POST['drop1'] ?? '';

	$sql = "SELECT * FROM `custreg` ORDER BY custname ASC";

$result2=mysqli_query($con,$sql);
while($row2=mysqli_fetch_array($result2))
{
	if($company==$row2['custid'])
	{
	$drop1=$drop1."<option value='".$row2['custid']."' selected>".$row2['custcode'].$row2['custname']."</option>";
	}
	else{
	 $drop1=$drop1."<option value='".$row2['custid']."' >".$row2['custcode'].$row2['custname']."</option>";
	}
}
	/*$custid=$_POST['sname'];
	$sql = "SELECT * FROM `custrate` where crid='$custid' ORDER BY crdesti ASC";
	$result=mysqli_query($con,$sql);
while($row=mysqli_fetch_array($result))
{
	
	echo $culoc=$row['crsource'];
	echo $soloc=$row['crdesti'];
$tr=$tr."<tr><td></td><td><input type='text' name='currloc' id='currloc' value='".$row['crsource']."'></td><td><input type='text' name='destiloc' id='destiloc' value='".$row['crdesti']."'></td></tr>";
}*/
	
?>
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>

	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
<script type="text/javascript" src="jquery-1.8.0.min.js"></script>
<script type="text/javascript" src="jquery-1.8.3.js"></script>
<script src="typeahead.min.js"></script>
<script type="text/javascript">
$(function(){ 
$(".search").keyup(function() 
{ 

var searchid = $(this).val();
var dataString = 'search='+ searchid;
if(searchid!='')
{
	$.ajax({
	type: "POST",
	url: "search.php",
	data: dataString,
	cache: false,
	success: function(html)
	{
	$("#result").html(html).show();
	}
	});
}return false;    
});

jQuery("#result").on("click",function(e){ 
	var $clicked = $(e.target);
	var $name = $clicked.find('.name').html();
	 var decoded = $("<div/>").html($name).text();
	$('#searchid').val(decoded);
	funaa(decoded);
});
jQuery(document).on("click", function(e) {
	var $clicked = $(e.target);
	if (! $clicked.hasClass("search")){
	jQuery("#result").fadeOut(); 
	}
});
$('#searchid').click(function(){
	jQuery("#result").fadeIn();
});
});
function funaa(a)
{ 
//alert(a);
	if(a!="")
	{   
obja=new XMLHttpRequest();
obja.open("GET","custregi.php?a="+a,true);
obja.send();
obja.onreadystatechange=func

	}
}
function func()
{
	//alert("hi");
if(obja.readyState==4)
{
	valar=obja.responseText;
	
	if(valar!="")
	{  
		var res = valar.split("*");
		alert(res);
		document.getElementById("custname").value=res[1];
		document.getElementById("custphone").value=res[2];
		document.getElementById("custemail").value=res[3];
		document.getElementById("custaddress").value=res[4];
		document.getElementById("custzip").value=res[5];
		document.getElementById("cusvatin").value=res[8];
		//document.getElementById("csttin").value=res[9];
	    document.getElementById("cstgst").value=res[10];
		document.getElementById("custpan").value=res[11];
	
	}
	
}
}
</script>

<style type="text/css">
	.content{
		width:10px;
		margin:1 auto;
	}
  .show
	{
		padding:5px; 
		border-bottom:1px #999 dashed;
		font-size:15px; 
		height:25px;
	}
	.show:hover
	{
		background:#4c66a4;
		color:#FFF;
		cursor:pointer;
	}
</style>	
</head>
<?php
include("header.php");
?>
  <div class="container">
  <?php echo ''; ?><br>	
   <form action="custUpdateIn.php"  method="POST" class="form-horizontal" onSubmit="return validate()" name="frmShipment">
			<div class="titleHeader clearfix">
					<h3>Update Shipper</h3> 
			</div>
			<legend>&nbsp;&nbsp;&nbsp;&nbsp;Shipper Information :</legend>
			<div class="row">
     			<div class="span6">
				        
						 <!--<div class="control-group ">
							   <label class="control-label">Search Coustomer Name: <span class="text-error" >*</span></label>
							    <div class="controls" class="content">
							      <input type="text" class="search" name="searchid" id="searchid" placeholder="Search for Customer" >
								    <div id="result"></div>
								 </div>
						</div> end control-group-->   
                        <div class="control-group ">
							    <label class="control-label">Select Shipper Name:  <span class="text-error" >*</span></label> 
							        <div class="controls">
									    <select name="sname" id="sname" onChange="to(this);" >
									     <option  value="">-- Please Select --</option>
												<?php echo $drop1; ?>
								        </select>
	                                </div>
							     <br>	
						</div>
						<div class="control-group ">
							    <label class="control-label">Shipper Name :</label>
							    <div class="controls">
							      <input type="text" name="custname" id="custname" placeholder="Shipper Name">
							     <!-- <span class="help-inline"><i class="icon-ok"></i> Avaliable input!</span>-->
							    </div>
						</div><!--end control-group-->
						
						<div class="control-group">
							    <label class="control-label" for="custphone">Mobile No :</label>
							    <div class="controls">
							      <input type="text" name="custphone" id="custphone"placeholder="Mobile No">
							    </div>
						</div><!--end control-group-->
						
						
						
						 
						  <div class="control-group">
							    <label class="control-label" for="csttin">GST No:</label>
							    <div class="controls">
							      <input type="text" name="custgst" id="custgst" placeholder="GST  No" >
							    </div>
							</div><!--end control-group-->    
				
				</div>
				<div class="span6">
				          <div class="control-group">
							    <label class="control-label">E-Mail :</label>
							    <div class="controls">
							      <input type="email" name="custemail" id="custemail" placeholder="<EMAIL>">
							    </div>
						</div><!--end control-group-->					 
						 
				         <div class="control-group ">
							    <label class="control-label" for="custaddress"> Address : </label>
							    <div class="controls">
							      <input type="text" name="custaddress" id="custaddress" placeholder="Address">
							     <!--< <span class="help-inline">-->
							    </div>
						</div><!--end control-group   <i class="icon-remove"></i> Invalid input!</span>-->
						
						 <div class="control-group success">
							    <label class="control-label" for="custzip">Zip Code: </label>
							    <div class="controls">
							      <input type="text" name="custzip" id="custzip" placeholder="Zip Code">
							      <!--<span class="help-inline"><i class="icon-ok"></i> Avaliable input!</span>-->
							    </div>
						</div><!--end control-group-->
						
							<div class="control-group ">
							    <label class="control-label" for="custpan">PAN No :</label>
							    <div class="controls">
							      <input type="text" name="custpan" id="custpan" placeholder="PAN No">
								 							     <!--< <span class="help-inline">-->
							    </div>
							</div><!--end control-group   <i class="icon-remove"></i> Invalid input!</span>-->
						
						
						
				
				</div>
					
			</div>
			
		<div class="row">
			<legend>&nbsp;&nbsp;&nbsp;&nbsp;Destination Rate Information :</legend>	 
             	<table  id="tb" border="1" class="table table-hover table-nomargin table-bordered">
      				<tr> 
      				<th>Sr no.</th>
      				<th>From</th>
      				<th>Destination</th>
					<th>Mode</th>
				
					<th>Docket Charges</th>
					<th>Other</th>
					<th>Unit </th>
					<th>Rate</th>
					<th>Weight</th>
					</tr>
<tr>
<td><input type="hidden" name="srn1" id="srn1" value="1" class="input-small" >1</td>
<td><select name="sources1" id="sources1" required  class="input-small"><option value="" selected="selected">-- Select--</option>
							     	<?php echo $citydrop; ?></select></td>
<td>
    <select name="destin1" id="destin1" required  class="input-small"><option value="" selected="selected">-- Select--</option>
							     	<?php echo $citydrop; ?></select>

</td>
<td>
<select name="mode1" id="mode1" class="input-small">
<option selected="selected" value="">Select</option><option value="Air">Air </option><option value="Road"> Road</option></select>
</td>
<td><input type="text" name="docketcharge1" id="docketcharge1" value="" class="input-small"></td>
<td><input type="text" name="other1" id="other1" value="" class="input-small"></td>
<td>
<select name="ftl1" id="ftl1" class="input-small" >
<option selected="selected" value="">Select</option><option value="ftl">FTL</option><option value="per/kg">Per/Kg</option></select>
<input type="hidden" name="ftl1" id="ftl1" value="" class="input-small">
<input type="hidden" name="ftl1" id="ftl1" value="ftl" class="input-small">
</td>
<td>
<input type="text" name="rate1" id="rate1" value="" class="input-small" >
</td>
<td>
<input type="text" name="weight" id="weight" value="" class="input-small" >
</td>
</tr>																			
</table>
<input type="hidden" name="ct" id="ct" value="1" >

							
		</div>		<!-- end row-->		<br>		
        	<div class="row">
			                 <div class="control-group">
							    <div class="controls">
						
<input class="btn btn-primary" value="Add Another" type="button" id="add" onClick="add()">

<input class="btn btn-primary" value="Delete Row" type="button" id="del" onClick="del1()">
					
								 <input type="submit" class="btn btn-primary" value="Update" name="update" onClick="return validateForm()">
								 <input type="submit" class="btn btn-primary" value="Delete " name="delete" onClick="return validateForm()">
									<input type="hidden" name="userid" id="userid" value="<?php echo $userid; ?>">
									<input type="hidden" name="count" id="count" value="0">
									<button type="reset" class="btn ">Clear</button>
							    </div>
							</div><!--end control-group-->
			
			</div> 
		 
 </form>
 </div>	
		
<script>

$(document).ready(function ()
  {
   $("#sname").change(function () {  
  
    $.ajax({                                      
   url: 'ajaxCustDetailUpdate.php?name1='+$('#sname').val(), 
     
    // url: 'ajaxFlatReg.php?cid='+$('#fno').val()+'&flatno='+$('#FlatName').val(),  
       
            //the script to call to get data          
      data: "",                        //you can insert url argumnets here to pass to api.php
                                       //for example "id=5&parent=6"
      dataType: 'json',                //data format      
      success: function(data)          //on recieve of reply
      {
     // alert();
      $.each(data, function(index, data) {
    //  alert();
       
       
       $('#custname').val(data.custname); 
       $('#custphone').val(data.custphone);  
       $('#cusvatin').val(data.cusvatin);

       $('#csttin').val(data.csttin); 
      $('#custemail').val(data.custemail);
      $('#custaddress').val(data.custaddress);
      
      $('#custzip').val(data.custzip);
      $('#custgst').val(data.custgst);
      $('#custpan').val(data.custpan);
           
       });
       
       }
       });
       
    });
  }); 
  
  

</script>	
				
<script>

function to() { 

   $("#sname").change(function () {  
   var count=0;
var sname1=$("#sname").val();
//alert(sname1);
//alert(count);
     $('#tb tr:not(:first)').remove();
    $.ajax({                                      
      url: 'ajaxgetCustRateDetail.php?custid='+$('#sname').val(),                  //the script to call to get data          
           // url: 'ajaxgetdesti.php',                  //the script to call to get data          

      data: "",                        //you can insert url argumnets here to pass to api.php
                                       //for example "id=5&parent=6"
      dataType: 'json',                //data format      
      success: function(data)          //on recieve of reply
      {
    
            $.each(data, function(index, data) {
           count=count+1;
            $('#sources'+rowCount).append( $('<option></option>').val(data.id).html(data.name) );
          //alert(count);
     
     
$("#tb").append('<tr><td>'+rowCount+'</td><td><select name="sources'+rowCount+'" id="sources'+rowCount+'" class="input-small"></select></td><td><select name="destin'+rowCount+'" id="destin'+rowCount+'" class="input-small"></select></td><td><select name="mode'+rowCount+'" id="mode'+rowCount+'" class="input-small" ><option name="mode'+rowCount+'" id="mode'+rowCount+'" value="" selected>Select</option><option name="mode'+rowCount+'" id="mode'+rowCount+'" value="Air" >Air</option><option name="mode'+rowCount+'" id="mode'+rowCount+'" value="Road" >Road</option></select> </td><td><input name="docketcharge'+rowCount+'" id="docketcharge'+rowCount+'" class="input-small"></td><td><input name="other'+rowCount+'" id="other'+rowCount+'" class="input-small"></td><td><select name="ftl'+rowCount+'" id="ftl'+rowCount+'" class="input-small"><option name="ftl'+rowCount+'" id="ftl'+rowCount+'" class="input-small"  value="" selected>Select</option><option name="ftl'+rowCount+'" id="ftl'+rowCount+'" value="ftl" class="input-small">FTL</option><option name="ftl'+rowCount+'" id="ftl'+rowCount+'" value="per/kg" class="input-small" >Per/Kg</option></select> </td><td><input name="rate'+rowCount+'" id="rate'+rowCount+'" class="input-small"></td><td><input name="weight'+rowCount+'" id="weight'+rowCount+'" class="input-small"></td></tr>');

});
$('#count').val(count);
}
 });
});
    }
	
</script>
					
<script  type="text/javascript">
function validateForm()
{
var snamex=document.forms["frmShipment"]["sname1"].value;
if snamex==null || snamex=="")
  {
  alert("Please Select Customer Name ");
  return false;
  }
    var custphone=document.forms["frmShipment"]["custphone"].value;
if (!custphone>10 || custphone=10)
  {
  alert("Mobile No must be 10 digit ");
  return false;
  }
   var x2=document.forms["frmShipment"]["custemail"].value;
 var atposition=x2.indexOf("@");  
var dotposition=x2.lastIndexOf(".");  
if (atposition<1 || dotposition<atposition+2 || dotposition+2>=x.length){  
  alert("Please enter a valid e-mail address \n atpostion:"+atposition+"\n dotposition:"+dotposition);  
  return false;  
  }
   var x4=document.forms["frmShipment"]["custzip"].value;
if (x4==6 || !x4>6)
  {
  alert("Zip code must be 6 Digit only ");
  return false;
  }
  var x7=document.forms["frmShipment"]["custin"].value;
if (isNaN(x7)){  
  document.getElementById("vati").innerHTML="Enter Numeric value only";  
  return false;  
}else{  
  return true;  
  }  
  
  var x8=document.forms["frmShipment"]["custstax"].value;
if (isNaN(x8)){  
  document.getElementById("st").innerHTML="Enter Numeric value only";  
  return false;  
}else{  
  return true;  
  }  
  
 }
</script>

<script> 
  function del1()
{
rowCount=0;
 rowCount=$('#tb tr').length;
 if((rowCount-1)!=1)
 {
  	$('#hid_count1').val(rowCount-2);
 	if(rowCount!=2)
 	{
	var table = document.getElementById("tb");
 	table.deleteRow(rowCount -1);
  
	}
}
}

$(document).ready(function(){ 
         $("#add").click(function(){
        
  var rowCount=$('#tb tr').length;
   $.ajax({                                      
      url: 'ajaxgetdesti.php',                  //the script to call to get data          
      data: "",                        //you can insert url argumnets here to pass to api.php
                                       //for example "id=5&parent=6"
      dataType: 'json',                //data format      
      success: function(data)          //on recieve of reply
      {
      $.each(data, function(index, data) {
        $('#sources'+rowCount).append( $('<option></option>').val(data.id).html(data.name) );
               $('#destin'+rowCount).append( $('<option></option>').val(data.id).html(data.name) );

        //alert(data.plotno);
       });
       }
       });

$("#tb").append('<tr><td>'+rowCount+'</td><td><select name="sources'+rowCount+'" id="sources'+rowCount+'" class="input-small"><option value="" selected="selected">-- Select--</option></select></td><td><select name="destin'+rowCount+'" id="destin'+rowCount+'" class="input-small"><option value="" selected="selected">-- Select--</option></select></td><td><select name="mode'+rowCount+'" id="mode'+rowCount+'" class="input-small" ><option name="mode'+rowCount+'" id="mode'+rowCount+'" value="" selected>Select</option><option name="mode'+rowCount+'" id="mode'+rowCount+'" value="Air" >Air</option><option name="mode'+rowCount+'" id="mode'+rowCount+'" value="Road" >Road</option></select> </td><td><input name="docketcharge'+rowCount+'" id="docketcharge'+rowCount+'" class="input-small"></td><td><input name="other'+rowCount+'" id="other'+rowCount+'" class="input-small"></td><td><select name="ftl'+rowCount+'" id="ftl'+rowCount+'" class="input-small"><option name="ftl'+rowCount+'" id="ftl'+rowCount+'" class="input-small"  value="" selected>Select</option><option name="ftl'+rowCount+'" id="ftl'+rowCount+'" value="ftl" class="input-small">FTL</option><option name="ftl'+rowCount+'" id="ftl'+rowCount+'" value="per/kg" class="input-small" >Per/Kg</option></select> </td><td><input name="rate'+rowCount+'" id="rate'+rowCount+'" class="input-small"><td><input name="weight'+rowCount+'" id="weight'+rowCount+'" class="input-small"></td></tr>');




     $("#ct").val(rowCount);
    });
});
</script>
<script> 
  function del1()
{
rowCount=0;
 rowCount=$('#tb tr').length;
 if((rowCount-1)!=1)
 {
  	$('#hid_count1').val(rowCount-2);
 	if(rowCount!=2)
 	{
	var table = document.getElementById("tb");
 	table.deleteRow(rowCount -1);
  
	}
}
}
</script>
</script>
   <?php
											
								$msg = $_GET['msg'] ?? '';
								if($msg=="yes1")
								{
						echo "<script> alert('Customer Updated Successfully');</script>";
								}
								else if($msg=="no1"){
						echo "<script> alert('Customer Not Updated Successfully');</script>";
								}
								?>    <!--   --> 

<?php
include("footer.php");
?>