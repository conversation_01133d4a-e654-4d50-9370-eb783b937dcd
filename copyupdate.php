<?php
session_start();
require_once('database.php');
require_once('library.php');
isUser();

$num_rec_per_page=20;
if (isset($_GET["page"])) { $page  = $_GET["page"]; } else { $page=1; }; 
$start_from = ($page-1) * $num_rec_per_page; 

$a=$_SESSION['username'];
 $sql="select * from login where username='$a'";
$result = mysqli_query($con, $sql);
$row = mysqli_fetch_array($result, MYSQLI_BOTH);
 $id=$row['rid'];



		
		if(isset($_POST['search'])){
	    $valueToSearch= $_POST['consno'];
	    $sql="SELECT cid, cons_no, ship_name, rev_name, book1_date, statusname
		FROM tbl_courier inner join status on tbl_courier.status=status.statusid  
		WHERE  st = 'Approve' and `cons_no` LIKE '%$valueToSearch%'";
	    //$sql="select cons_no,ship_name,rev_name,book1_date,statusname from tbl_courier inner join status on tbl_courier.status=status.statusid where CONCAT(cons_no,ship_name,rev_name,book1_date,statusname)LIKE '%.$valueToSearch.%'  ";
	   $result = dbQuery($sql);
	    
	}	
	else{
	   	$sql = "SELECT cid, cons_no, ship_name, rev_name, book1_date, statusname
		FROM tbl_courier inner join status on tbl_courier.status=status.statusid  
		WHERE  status != '5'and st='Approve'
		ORDER BY cid DESC 
		LIMIT $start_from, $num_rec_per_page";
		$result = dbQuery($sql);
	}
	//function filterTable($query);
//	{
//	    $connect = mysqli_connect();
///	    $filter_Result = mysqli_query($connect,$query);
	    //return $filter_Result;
//	}
	

//	$sql = "SELECT cid, cons_no, ship_name, rev_name, book1_date, statusname
//		FROM tbl_courier inner join status on tbl_courier.status=status.statusid //
	//	WHERE status != '5'
	//	ORDER BY cid DESC 
	//	LIMIT $start_from, $num_rec_per_page";


$result = dbQuery($sql);		

?>

<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>
	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
<script language="JavaScript">
var checkflag = "false";

function check(field) {
if (checkflag == "false")
 {
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll")
	{
	field[i].checked=true;	
	}
	}
	checkflag = "true";
}
else
{
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll")
	{
	field[i].checked=false;
	}
	}
	checkflag = "false";
}

}
function confirmDel(field,msg)
{
	count=0;
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll" && field[i].checked==true)
	{
	count++;
	}
	}
	
	if(count == 0)
	{
		alert("Select any one record to delete!");
		return false;
	}
	else
	{
		return confirm(msg);
	}
}
</script>
</head>
<?php include("header.php"); ?>
		<div class="container">
			<div class="row">
               <div class="span11">
					<div class="account-list-outer">

						<div class="titleHeader clearfix">
							<h3>Update Shipment </h3>
						</div><!--end titleHeader-->
						<form method="post" action="updship.php" class="form-horizontal">
 <div class="control-group success">
							    <label class="control-label" for="custzip">Docket No.: </label>
							    
							    <div class="controls">
							      <input type="text" name="consno"  id="consno" placeholder="Enter Docket No.">
							       
							      	<input name="search" class="btn btn-primary" type="submit" value="Search">
							   
							   </div>
							</div>
								
							</form>
								
									
						<table class="table">
						<thead>
							<tr>
								<th><h5>Consignment No </h5></th>
								<th><h5>Shipper Name</h5></th>
								<th><h5>Receiver Name</h5></th>
								<th><h5>Book Date</h5></th>&nbsp; &nbsp; &nbsp; &nbsp; 
								<th><h5>Status</h5></th>&nbsp; &nbsp; &nbsp; &nbsp; 
								<th><h5>Action</h5></th>&nbsp; &nbsp; &nbsp; &nbsp; 
							</tr>
						</thead>
						<?php
							 while($data = dbFetchAssoc($result)){
							 extract($data);	
						 ?>
						<tbody>
							<tr>
								<td class="desc">
									<?php echo $cons_no; ?>
								</td>
								<td>
									<?php echo $ship_name; ?>
								</td>
								<td>
								<?php echo $rev_name; ?>
								</td>
								<td>
									<?php echo $book1_date; ?> &nbsp; &nbsp; &nbsp; &nbsp; 
								</td>
								<td>
									<?php echo $statusname; ?>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 
								</td>
                                <td><a href="serchUpdate.php?cid=<?php echo $cid; ?>"></i>
									<button class="btn btn-small btn-primary" data-title="To Edit" data-placement="top" rel="tooltip" value="Edit" ><i class=""><img src="images/edit_icon.gif"/></button>
									<!--<button class="btn btn-small btn-danger" data-title="Remove" data-placement="top" rel="tooltip"><i class="icon-trash"></i></button>-->
								</td>
							</tr>
						</tbody>
						<?php	}//while ?>
					</table>
					</div><!--end -->
				</div><!--end span-->
			</div><!--end row-->
		</div><!--end conatiner-->
<?php 
$sql = "SELECT * FROM tbl_courier WHERE status != 'Delivered' ORDER BY cid DESC "; 
$rs_result = mysql_query($sql); //run the query
$total_records = mysql_num_rows($rs_result);  //count number of records
$total_pages = ceil($total_records / $num_rec_per_page); 

echo "<a href='updship.php?page=1'>".'|<'."</a> "; // Goto 1st page  

for ($i=1; $i<=$total_pages; $i++) { 
            echo "<a href='updship.php?page=".$i."'>".$i."</a> "; 
}; 
include("footer.php"); ?>