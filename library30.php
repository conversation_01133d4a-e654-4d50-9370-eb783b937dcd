<?php 
require_once('database.php');
require 'connection.php';
function assign_rand_value($num)
{
// accepts 1 - 36
  switch($num)
  {
    case "1":
     $rand_value = "a";
    break;
    case "2":
     $rand_value = "b";
    break;
    case "3":
     $rand_value = "c";
    break;
    case "4":
     $rand_value = "d";
    break;
    case "5":
     $rand_value = "e";
    break;
    case "6":
     $rand_value = "f";
    break;
    case "7":
     $rand_value = "g";
    break;
    case "8":
     $rand_value = "h";
    break;
    case "9":
     $rand_value = "i";
    break;
    case "10":
     $rand_value = "j";
    break;
    case "11":
     $rand_value = "k";
    break;
    case "12":
     $rand_value = "l";
    break;
    case "13":
     $rand_value = "m";
    break;
    case "14":
     $rand_value = "n";
    break;
    case "15":
     $rand_value = "o";
    break;
    case "16":
     $rand_value = "p";
    break;
    case "17":
     $rand_value = "q";
    break;
    case "18":
     $rand_value = "r";
    break;
    case "19":
     $rand_value = "s";
    break;
    case "20":
     $rand_value = "t";
    break;
    case "21":
     $rand_value = "u";
    break;
    case "22":
     $rand_value = "v";
    break;
    case "23":
     $rand_value = "w";
    break;
    case "24":
     $rand_value = "x";
    break;
    case "25":
     $rand_value = "y";
    break;
    case "26":
     $rand_value = "z";
    break;
    case "27":
     $rand_value = "0";
    break;
    case "28":
     $rand_value = "1";
    break;
    case "29":
     $rand_value = "2";
    break;
    case "30":
     $rand_value = "3";
    break;
    case "31":
     $rand_value = "4";
    break;
    case "32":
     $rand_value = "5";
    break;
    case "33":
     $rand_value = "6";
    break;
    case "34":
     $rand_value = "7";
    break;
    case "35":
     $rand_value = "8";
    break;
    case "36":
     $rand_value = "9";
    break;
  }
return $rand_value;
}

function get_rand_id($length)
{
  if($length>0) 
  { 
  $rand_id="";
   for($i=1; $i<=$length; $i++)
   {
   mt_srand((double)microtime() * 1000000);
   $num = mt_rand(1,36);
   $rand_id .= assign_rand_value($num);
   }
  }
return $rand_id;
} 
$un=$_GET['txtusername'];
$pwd=$_GET['txtpassword'];	
//--------------------- Hard Coded user entry--------------
function checkUser($un,$pwd,$con) {
  $sql1="select * from login inner join tbl_courier_officers on login.rid=tbl_courier_officers.cid where login.username='$un' and login.password='$pwd' ";
    	$result1 = mysqli_query($con,$sql1);
	$no1 = mysqli_fetch_array($result1);
	$_SESSION['empname']=$no1['Manager_name'];
    
	$sql="select * from login where username='$un' and password='$pwd'";
	//echo $sql;
	$result = mysqli_query($con,$sql);
	$no = mysqli_fetch_array($result);
	$_SESSION['desgn']=$no['rid'];
	$_SESSION['emptype']=$no['type'];
	//if($un == 'admin' && $pwd = 'admin123') {
	if($no['type']=='admin'){
		$_SESSION['username'] = 'admin';
		$_SESSION['userType'] = 'admin';
		header('Location: welcome.php');
		//echo 'Iam here.......';
	}else if($no['type']==	$_SESSION['emptype']){
		$_SESSION['username'] = "$un";
		$_SESSION['userType'] = 'emp';
			header('Location: addCourier.php');
		}else if($no['type']=='dbboy'){
		$_SESSION['username'] = "$un";
		$_SESSION['userType'] = 'dbboy';
			header('Location: welcome.php');
		}
		else {
			return "Your Credintials are not a Valid. Please try Again.";
		}		
	}//else
		//checkUser

function isUser(){
	if(!isset($_SESSION['desgn'])){
	    $msg="no";
		//echo 'sprry , wrong username';
	//	header("Location: index.php?err=* The User Name or password you entered is incorrect.<br>");
		header("location: index.php?msg=".$msg);
	}
	
}
?>