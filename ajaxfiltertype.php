<?php 

session_start();
require_once('database.php');
require_once('library.php');
require 'connection.php';
isUser();


//$utype=isUser();;
//$UserId=$_SESSION['UserId'];

$type=$_GET['type'];

if($type=='HUB_Office')
{
$sql="SELECT hoffname as name, hubid as id FROM `hubreg` ORDER BY name ";
}
 elseif ($type=='Branch_Office')
{
$sql="select off_name as name , id from tbl_offices order by name ";
}
 elseif ($type=='Deli_Vehicle')
{
$sql="select name , driverid as id from driverreg order by name ";
}
 elseif ($type=='Deli_Boy')
{
$sql="select `delivboyid` as id , name  from  `delivboyreg` order by name ";
}
$result = mysqli_query($con,$sql); 
        
 $data = array();
 while($row = mysqli_fetch_array($result)){
	 
$row_data = array( 
   
   'id' => $row['id'],
   
   'name' => $row['name'],
   
   );
   
  array_push($data, $row_data);
  
 }
 
echo json_encode($data);
 
?>