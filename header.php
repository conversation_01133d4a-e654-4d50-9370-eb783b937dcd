<?php 
// if (session_status() == PHP_SESSION_NONE) {
//     session_start();
// }


require 'connection.php';
require_once('database.php');

require_once('library.php');
       $a=$_SESSION['username'];
     $usertype=$_SESSION['userType'];
    $desn=$_SESSION['desgn'];
     $empname=$_SESSION['empname'];



$sql="select DISTINCT(Sub_Menu.Id),Sub_Menu.Sub_Menu_Name,Sub_Menu.Sub_Menu_Link,Access_Control.Emp_ID,Menu.Id from (Access_Control inner join Sub_Menu on Sub_Menu.Id=Access_Control.Sub_ID) left outer join Menu on Menu.Id=Sub_Menu.Menu_Id where Access_Control.Emp_ID='$desn'";
 // $sql="select Sub_Menu.Sub_Menu_Name,Sub_Menu.Sub_Menu_Link,Access_Control.Emp_ID,Menu.Id from ((Access_Control inner join Sub_Menu on Sub_Menu.Id=Access_Control.Sub_ID) left outer join Menu on Menu.Id=Sub_Menu.Menu_Id)left outer join tbl_courier_officers on tbl_courier_officers.cid=Access_Control.Emp_ID  where Access_Control.Emp_ID='$desn' ";
//$sql="select * from Sub_Menu";

$book="";
$update="";
 $hub="";
  $registration="";
    $details="";
     $bill="";
      $report="";
      
$List= mysqli_query($con,$sql); 
while($row= mysqli_fetch_array($List))
{
    
if($row['Id']==1)
{
$book='<li ><a href="'.$row['Sub_Menu_Link'].'"title="'.$row['Sub_Menu_Name'].'">'.$row['Sub_Menu_Name'].'</a></li>'.$book;
}
if($row['Id']==2)
{
$update='<li ><a href="'.$row['Sub_Menu_Link'].'"title="'.$row['Sub_Menu_Name'].'">'.$row['Sub_Menu_Name'].'</a></li>'.$update;
}
if($row['Id']==3)
{
$hub='<li ><a href="'.$row['Sub_Menu_Link'].'"title="'.$row['Sub_Menu_Name'].'">'.$row['Sub_Menu_Name'].'</a></li>'.$hub;
}
if($row['Id']==4)
{
$registration='<li ><a href="'.$row['Sub_Menu_Link'].'"title="'.$row['Sub_Menu_Name'].'">'.$row['Sub_Menu_Name'].'</a></li>'.$registration;
}
if($row['Id']==5)
{
$details='<li ><a href="'.$row['Sub_Menu_Link'].'"title="'.$row['Sub_Menu_Name'].'">'.$row['Sub_Menu_Name'].'</a></li>'.$details;
}
if($row['Id']==6)
{
$bill ='<li ><a href="'.$row['Sub_Menu_Link'].'"title="'.$row['Sub_Menu_Name'].'">'.$row['Sub_Menu_Name'].'</a></li>'.$bill;
}
if($row['Id']==7)
{
$report='<li ><a href="'.$row['Sub_Menu_Link'].'"title="'.$row['Sub_Menu_Name'].'">'.$row['Sub_Menu_Name'].'</a></li>'.$report;
}

}

$date = date('Y-m-d');

 $sqlquery="select count(*) from tbl_courier where status_date='$date' ";
$resulttsql=mysqli_query($con,$sqlquery); 
while($row1= mysqli_fetch_array($resulttsql))
{
     $count=$row1['count(*)'];
}
?>

<head>
   	<meta charset="utf-8">
	<title>Vivanta Logistics</title>
   <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
</head>
<body>
	<div id="mainContainer" class="clearfix">

		<header>
			<div class="upperHeader">
				<div class="container">
					<ul class="pull-right inline">
					
						<li class="divider-vertical"></li>
						<?php
						if($usertype=="admin")
					{?>
					
					
                        <li><a class="invarseColor" href="dailynotification.php">
                         <span class="badge" style="background-color:white;color:black">
                             <i class="fa fa-bell" style="font-size:18px;color:#f16325"></i>
                       <?php echo $count; ?></span>
                        
                        </a></li><?php }?>
                        <li><a class="invarseColor" href="addOff.php">Add New Office</a></li>|
                        <li><a class="invarseColor" href="manadetail.php">Employee Details</a></li>|
                         <li><a class="invarseColor" href="addManager.php">Add New Employee </a></li>|
						<li><a class="invarseColor" href="process.php?action=logOut">Logout</a></li>|
							<li><a class="invarseColor">Welcome <font color="red"><?php echo $empname; ?></font></a></li>
					</ul>
				
				</div><!--end container-->
			</div><!--end upperHeader-->

			<div class="middleHeader">
				<div class="container">

					<div class="middleContainer clearfix">

					<div class="siteLogo pull-left">
						<h1><a href="welcome.php">Vivanta Logistics</a></h1>
					</div>

				
					</div><!--end middleCoontainer-->

				</div><!--end container-->
			</div><!--end middleHeader-->

			<div class="mainNav">
				<div class="container">
					<div class="navbar">
					      	
				      	
				      			<ul class="nav">
				      		<li class="active"><a href="welcome.php"><i class="icon-home"></i></a></li>
				      		<li>
				      			<a>&nbsp; Booking  &nbsp;<i class="icon-caret-down"></i></a>
				      			<div>
					      			<ul>
									    
					      				<?php echo $book;
					      			
					      				?>
										
					      			</ul>
					      		</div>
				      		</li>
					<li>
				      			<a>&nbsp; Track & Update &nbsp;<i class="icon-caret-down"></i></a>
				      			<div>
					      			<ul>
                                       	<?php echo $update; ?>
                                    </ul>
					      		</div>
				      		</li>
					<li>
				      			<a>&nbsp; Hub &nbsp;<i class="icon-caret-down"></i></a>
				      			<div>
					      			<ul>
                                       	<?php echo $hub; ?>
                                    </ul>
					      		</div>
				      		</li>

						<li>
				      			<a>&nbsp; Registration &nbsp;<i class="icon-caret-down"></i></a>
				      			<div>
					      			<ul>
                                       	<?php echo $registration; 	if($usertype=="admin")
					{
					echo '<li><a href="AccessControl.php" title="Access">Access Control </a></li>';
					}
					?>
                                    </ul>
					      		</div>
				      		</li>
					<li>
				      			<a>&nbsp; Details &nbsp;<i class="icon-caret-down"></i></a>
				      			<div>
					      			<ul>
                                       	<?php echo $details; ?>
                                    </ul>
					      		</div>
				      		</li>
					
									       
				  	<li>
				      			<a>&nbsp; Bill &nbsp;<i class="icon-caret-down"></i></a>
				      			<div>
					      			<ul>
                                       	<?php echo $bill; ?>
                                    </ul>
					      		</div>
				      		</li>
				       	<li>
				      			<a>&nbsp; Report &nbsp;<i class="icon-caret-down"></i></a>
				      			<div>
					      			<ul>
                                       	<?php echo $report; ?>
                                    </ul>
					      		</div>
				      		</li>
				       
					</div>
					
				</div><!--end container-->
			</div><!--end mainNav-->	
			
		</header>
		<!-- end header -->
</body>	<!-- end header -->
