<?php
session_start();
require_once('database.php');
require_once('library.php');
require 'connection.php';
isUser();

$a=$_SESSION['username'];
$sql="select * from login where username='$a'";
$result = mysqli_query($con, $sql);
$row = mysqli_fetch_array($result, MYSQLI_BOTH);
 $id=$row1['rid'];

 $sql="select cid,office,Manager_name from tbl_courier_officers where username='$a'";
$result = mysqli_query($con, $sql);
$row1 = mysqli_fetch_array($result, MYSQLI_BOTH);
$uoffice=$row1['office'];  $clerkname=$row1['Manager_name'];


date_default_timezone_set('Asia/Kolkata');
$date = date('d/m/Y h:i:s', time());

$sql1="select * from `tbl_offices` ORDER BY off_name ASC";
 $result2=mysqli_query($con,$sql1);
while($row2=mysqli_fetch_array($result2))
{
	if($company==$row2['id'])
	{
	$loc=$loc."<option value='".$row2['id']."' selected>".$row2['off_name']."</option>";
	}
	else{
	$loc=$loc."<option value='".$row2['id']."' >".$row2['off_name']."</option>";
	}
}

if($a!="admin")
{
$sql = "SELECT * FROM `custreg` WHERE userid='$id' ORDER BY custname ASC";
}
else{
$sql = "SELECT * FROM `custreg` ORDER BY custname ASC";
}
$result2=mysqli_query($con,$sql);
while($row2=mysqli_fetch_array($result2))
{
	if($company==$row2['custid'])
	{
	$drop1=$drop1."<option value='".$row2['custname']."' selected>".$row2['custname']."</option>";
	}
	else{
	$drop1=$drop1."<option value='".$row2['custname']."' >".$row2['custname']."</option>";
	}
}


$no=01;
$result= mysqli_query($con,"SELECT *,max(billid) as id FROM receipt where type='Invoice'");
while($row = mysqli_fetch_array($result))
  {
  $maxInvid=$row['id'];
 
   }

//$result1 = mysqli_query($con,"SELECT * FROM `` ORDER BY `id` ASC ");
 //$sql2="SELECT * FROM receipt where billid=".$maxInvid."";
 $sql2="SELECT * FROM receipt";
$result2= mysqli_query($con,$sql2);
while($row = mysqli_fetch_array($result2))
  {
 $maxInvid;
$no=$row['billid'];
 
}
 $_SESSION["no"]=$no;
 
?>

<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>
	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
<script language="JavaScript">
</script>
</head>
<?php include("header.php"); ?>
   <div class="container">
			<div class="row">
     			<div class="span2">
				         
				</div><!--end span3-->
                <form action="invoiceBill.php" name="billform" method="post" >
                <div class="span8">
					<div class="account-list-outer">

							<div class="titleHeader clearfix">
							<h3>Account Bill Generate</h3> 
						</div>
					     
						
						<div class="control-group">
                             <div class="controls">
							     <label class="control-label">Bill No.</label>
							     <input name="bilno" type="text" value="<?php echo $no;?>" readonly>
								<input name="tp" type="hidden" value="Invoice"> 
<input type="hidden" name="uoffice" value="<?php echo $uoffice;?>">
<input type="hidden" name="uaddress" value="<?php echo $clerkname;?>">
<input type="hidden" name="cid" value="<?php echo $id;?>">
<input name="todat" id="todat" type="hidden" value="<?php echo $date;?>" >
							 </div>
					    </div>
						<!--<div class="control-group">
                            <label>Branch Location :</label>
							<div class="controls">
	                            <select name="curntloca" id="curntloca" data-rule-required="true">
												<option value="">-- Please select --</option>
												<?php echo $loc; ?>
								</select>
							</div>
					    </div>-->
						
						<div class="control-group">
							    <label class="control-label">Customer Name: </label> 
							        <div class="controls">
									    <select name="cname" id="cname" >
									     <option value="">-- Please Select --</option>
												<?php echo $drop1; ?>
								        </select>
	                                </div>
						</div>
						<div class="control-group">
							    <label class="control-label">Fuel Charges (%): </label> 
							        <div class="controls">
									     <input name="fuelcharge" id="fuelcharge" type="text" placeholder="Fuel Charges" >
									</div>
						</div>
						<!--<div class="controls">
						     <label class="control-label"> Start Date </label>
							<div class="controls input-append date form_date" data-date="" data-date-format="dd MM yyyy" data-link-field="dtp_input2" data-link-format="yyyy-mm-dd">
										<input name="stdate" id="stdate" type="text" value="" readonly>
										<span class="add-on"><i class="icon-remove"></i></span>
										<span class="add-on"><i class="icon-th"></i></span>
							</div>
										<input type="hidden" id="dtp_input2" value="" />
					    </div>
						<!--<div class="controls">
						     <label class="control-label"> End Date </label>
							<div class="controls input-append date form_date" data-date="" data-date-format="dd MM yyyy" data-link-field="dtp_input2" data-link-format="yyyy-mm-dd">
										<input name="endate" id="endate" type="text" value="" readonly>
										<span class="add-on"><i class="icon-remove"></i></span>
										<span class="add-on"><i class="icon-th"></i></span>
							</div>
										<input type="hidden" id="dtp_input2" value="" />
					    </div>-->
						 
<div class="control-group">
						    <div class="controls"> 
								<input name="Submit" class="btn btn-primary" type="submit" value="SUBMIT" onClick="return validateForm()" >
							&nbsp;&nbsp;&nbsp;<button type="reset" class="btn ">CLEAR</button>
					        </div>
					    </div>
                   
					  
					   
                    </div><!--end -->
				</div><!--end span6-->
				</form>
			</div><!--end row-->
		</div><!--end conatiner-->
				
<script>
$(document).ready(function ()
  {
   $("#curntloca").change(function () { 
  
   $('#cname').find('option').remove().end().append('<option value="">-- Select Customer --</option>').val('');
    $.ajax({                                      
      url: 'ajaxGetCustName.php?compid='+$('#curntloca').val(),                  //the script to call to get data          
      data: "",                        //you can insert url argumnets here to pass to api.php
                                       //for example "id=5&parent=6"
      dataType: 'json',                //data format      
      success: function(data)          //on recieve of reply
      {
      $.each(data, function(index, data) {
        $('#cname').append( $('<option></option>').val(data.siteid).html(data.SiteName) );
       });
       }
       });       
    });
  }); 
  


</script>	

<link href="./bootstrap/css/bootstrap-datetimepicker.min.css" rel="stylesheet" media="screen">
<script type="text/javascript" src="./jquery/jquery-1.8.3.min.js" charset="UTF-8"></script>
<script type="text/javascript" src="./bootstrap/js/bootstrap.min.js"></script>
<script type="text/javascript" src="./jquery/bootstrap-datetimepicker.js" charset="UTF-8"></script>
<script type="text/javascript" src="./jquery/locales/bootstrap-datetimepicker.pl.js" charset="UTF-8"></script>
<script type="text/javascript">
	$('.form_date').datetimepicker({
        language:  'pl',
        weekStart: 1,
        todayBtn:  1,
		autoclose: 1,
		todayHighlight: 1,
		startView: 2,
		minView: 2,
		forceParse: 0
    });
	$('.form_time').datetimepicker({
        language:  'pl',
        weekStart: 1,
        todayBtn:  1,
		autoclose: 1,
		todayHighlight: 1,
		startView: 1,
		minView: 0,
		maxView: 1,
		forceParse: 0
    });
</script>
	

<script  type="text/javascript">
function validateForm()
{

  var cname1=document.forms["billform"]["cname"].value;
if (cname1==null || cname1=="")
  {
   alert("Please Select Customer Name ");
  return false;
  
  }
  
  var fuelcharge1=document.forms["billform"]["fuelcharge"].value;
if (fuelcharge1==null || fuelcharge1=="")
  {
  alert("Fuel Charges must be filled out");
  return false;
  }
  
}
</script>

	
<?php 
include("footer.php"); ?>