<?php
session_start();
require_once('database.php');
require_once('library.php');
isUser();

$num_rec_per_page=10;
if (isset($_GET["page"])) { $page  = $_GET["page"]; } else { $page=1; }; 


$start_from = ($page-1) * $num_rec_per_page; 

$a=$_SESSION['username'];
 $sql="select * from login where username='$a' LIMIT $start_from, $num_rec_per_page";
$result = mysqli_query($con, $sql);

// Initialize total_records and total_pages
$total_records = 0;
$total_pages = 0;

$row = mysqli_fetch_array($result, MYSQLI_BOTH);
   $id = ($row && isset($row['rid'])) ? $row['rid'] : 0;
   $userid=$_SESSION['desgn'];
   if($userid!=1)
   {
	if(isset($_POST['search'])){
	    $valueToSearch= $_POST['consno'];
	    $sql="SELECT cid, cons_no, ship_name, rev_name, book1_date, statusname,st
		FROM tbl_courier inner join status on tbl_courier.status=status.statusid
		WHERE ( `cons_no` LIKE '%$valueToSearch%' OR `st` LIKE '%$valueToSearch%' ) AND tbl_courier.userid=$userid";
	    //$sql="select cons_no,ship_name,rev_name,book1_date,statusname from tbl_courier inner join status on tbl_courier.status=status.statusid where CONCAT(cons_no,ship_name,rev_name,book1_date,statusname)LIKE '%.$valueToSearch.%'  ";
	   $result = mysqli_query($con,$sql);
	   $total_records = mysqli_num_rows($result);
	    
	
	

}else{
       // First count total records without LIMIT
       $count_sql = "SELECT COUNT(*) as total
	FROM tbl_courier  inner join status on tbl_courier.status=status.statusid
	WHERE tbl_courier.userid=$userid";
       $count_result = mysqli_query($con,$count_sql);
       $count_row = mysqli_fetch_assoc($count_result);
       $total_records = $count_row['total'];

       // Then get the paginated results
       $sql = "SELECT cid, cons_no, ship_name, rev_name, book1_date, statusname,st
		FROM tbl_courier  inner join status on tbl_courier.status=status.statusid
		WHERE tbl_courier.userid=$userid
		ORDER BY cid DESC
		LIMIT $start_from, $num_rec_per_page";

$result = mysqli_query($con,$sql);

	}
   }
   else
   {
       if(isset($_POST['search'])){
	    $valueToSearch= $_POST['consno'];
	    $sql="SELECT cid, cons_no, ship_name, rev_name, book1_date, statusname,st
		FROM tbl_courier inner join status on tbl_courier.status=status.statusid
		WHERE  `cons_no` LIKE '%$valueToSearch%' OR `st` LIKE '%$valueToSearch%'  ";
	    //$sql="select cons_no,ship_name,rev_name,book1_date,statusname from tbl_courier inner join status on tbl_courier.status=status.statusid where CONCAT(cons_no,ship_name,rev_name,book1_date,statusname)LIKE '%.$valueToSearch.%'  ";
	   $result = mysqli_query($con,$sql);
	   $total_records = mysqli_num_rows($result);
	    
	
	

}else{
       // First count total records without LIMIT for admin
       $count_sql = "SELECT COUNT(*) as total
	FROM tbl_courier  inner join status on tbl_courier.status=status.statusid";
       $count_result = mysqli_query($con,$count_sql);
       $count_row = mysqli_fetch_assoc($count_result);
       $total_records = $count_row['total'];

       // Then get the paginated results
       $sql = "SELECT cid, cons_no, ship_name, rev_name, book1_date, statusname,st
		FROM tbl_courier  inner join status on tbl_courier.status=status.statusid

		ORDER BY cid DESC
		LIMIT $start_from, $num_rec_per_page";

$result = mysqli_query($con,$sql);

	}
   }

// Calculate total pages for pagination
if ($total_records > 0) {
    $total_pages = ceil($total_records / $num_rec_per_page);
}

?>

<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>
	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	
<!--	<link rel="stylesheet" type="text/css" href="//cdn.datatables.net/1.10.21/css/jquery.dataTables.min.css"/>-->
<!--<script src="//cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>-->
<!--<script type="text/javascript" src="//cdn.datatables.net/1.10.21/js/jquery.dataTables.min.js"></script>-->
<!--    <link rel="stylesheet" type="text/css" href="//cdn.datatables.net/1.10.21/css/jquery.dataTables.min.css"/>-->

	
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
	





<script language="JavaScript">
var checkflag = "false";

function check(field) {
if (checkflag == "false")
 {
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll")
	{
	field[i].checked=true;	
	}
	}
	checkflag = "true";
}
else
{
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll")
	{
	field[i].checked=false;
	}
	}
	checkflag = "false";
}

}
function confirmDel(field,msg)
{
	count=0;
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll" && field[i].checked==true)
	{
	count++;
	}
	}
	
	if(count == 0)
	{
		alert("Select any one record to delete!");
		return false;
	}
	else
	{
		return confirm(msg);
	}
}

</script>
<?php
if(isset($_GET['msg2']))
{
    echo "<script>alert('".$_GET['msg2']."');</script>";
}
?>
</head>
<?php include("header.php");


?>
		<div class="container">
			<div class="row">
               <div class="span11">
					<div class="account-list-outer">

						<div class="titleHeader clearfix">
							<h3>Approve Shipment </h3>
						</div><!--end titleHeader-->
	<form method="post" action="approveCourier.php" class="form-horizontal">
 <div class="control-group success">
							    <label class="control-label" for="custzip">Docket No.: </label>
							    
							    <div class="controls">
							      <input type="text" name="consno"  id="consno" placeholder="Enter Docket No.">
							       
							      	<input name="search" class="btn btn-primary" type="submit" value="Search">
							   
							   </div>
							</div>
								
							</form>
								
						<table class="table" id="userTable">
						<thead>
							<tr>
								<th><h5>Consignment No </h5></th>
								<th><h5> NaShipperme</h5></th>
								<th><h5>Receiver Name</h5></th>
								<th><h5>Book Date</h5></th> &nbsp;  &nbsp;  &nbsp;  &nbsp;  &nbsp;  &nbsp; 
								<th><h5>Status</h5></th> &nbsp;  &nbsp;  &nbsp;  &nbsp; 
								<th><h5>Approve Status</h5></th>&nbsp;  &nbsp;
								<th><h5>Action</h5></th>
							</tr>
						</thead>
						<?php
							 while($data = mysqli_fetch_assoc($result)){
							 extract($data);
							 $date2=$data['book1_date'];
							 $date1=date('d-m-Y h:i:s', strtotime($date2));
							 
						  //echo "<pre>";
						  echo $total_records;
						  //die();
						 ?>
						<tbody>
							<tr>
								<td class="desc">
									<?php echo $cons_no; ?>
								</td>
								<input type="hidden" name="consno" id="consno" value="$cons_no">
								<td>
									<?php echo $ship_name; ?>
								</td>
								<td>
								<?php echo $rev_name; ?>
								</td>
								<td>
									<?php echo $book1_date; ?> &nbsp; &nbsp; &nbsp;  &nbsp;  &nbsp; 
								</td>
									<td>
									<?php echo $statusname; ?> &nbsp;  &nbsp;  &nbsp;  &nbsp; 
								</td>
								
								<td>
									<?php echo $st; ?> &nbsp;  &nbsp;  &nbsp;  &nbsp; 
								</td>
                                <td>
                                    <?php if($_SESSION['desgn']=='1'){
                                        
                                     ?>
                                    <a href="approveupdate.php?cid=<?php echo $cid; ?>">
									<button class="btn btn-small btn-primary" data-title="To Approve" data-placement="top" rel="tooltip" value="Approve" ><i class="">APPROVE</i></button></a></br>
										<a href="editshipment.php?cid=<?php echo $cid; ?>"><button class="btn btn-small btn-primary" data-title="To Edit" data-placement="top" rel="tooltip" value="Edit" ><i class="">EDIT</i></button></a>
								
									<?php } ?>
									
									<?php 
									if($_SESSION['desgn']==1)
									{
									$delete='<a href=""><button class="btn btn-small btn-danger" data-title="To Delete" data-placement="top" rel="tooltip" onClick="confirmDelete(this.value);" value="'. $cid.'" ><i class="">Delete</i></button></a>';
							       // echo $delete;
									} ?>
									<!--<button class="btn btn-small btn-danger" data-title="Remove" data-placement="top" rel="tooltip"><i class="icon-trash"></i></button>-->
								</td>
							</tr>
						</tbody>
						<?php	}//while ?>
					</table>
					</div><!--end -->
				 
		<div class="pagination">
      <ul>
          <?php if($page !=1){ ?>
        <li><a href ="?page=<?php echo ($page-1)?>"><b>Prev</b></a></li>
        <?php 
              
          }else{
            //   echo "<script> alert('You Are In First Page');</script>";
            
            
        }?>
        
        <!--<li><a href="?page=<?php echo 1 ?>" >1</a></li>-->
        <!--<li><a href="?page=<?php echo 2 ?>">2</a></li>-->
        <!--<li><a href="?page=<?php echo 3 ?>">3</a></li>-->
        <!--<li><a href="?page=<?php echo 4 ?>">4</a></li>-->
        <li><a href="?page=<?php echo ($page+1)?>"><b>Next</b></a></li>

        
      </ul>
    </div>
    </div><!--end span-->
			</div><!--end row-->
		</div><!--end conatiner-->
    <?php

								$msg = $_GET['msg'] ?? '';
								if($msg=="yes1")
								{
						echo "<script> alert('Docket Approved Successfully');</script>";
								}
								else if($msg=="no1"){
						echo "<script> alert('Docket Not Approved');</script>";
								}
								?>   
	<div class="pagination" align="center">
      <ul>
<!--       <?php if($currentPage != $firstPage) { ?>-->
<!--<li class="page-item">-->
<!--<a class="page-link" href="?page=<?php echo $firstPage ?>" tabindex="-1" aria-label="Previous">-->
<!--<span aria-hidden="true">Previous</span>-->
<!--</a>-->
<!--</li>-->
<!--<?php } ?>-->
<!--<?php if($currentPage >= 2) { ?>-->
<!--<li class="page-item"><a class="page-link" href="?page=<?php echo $previousPage ?>"><?php echo $previousPage ?></a></li>-->
<!--<?php } ?>-->
<!--<li class="page-item active"><a class="page-link" href="?page=<?php echo $currentPage ?>"><?php echo $currentPage ?></a></li>-->
<!--<?php if($currentPage != $lastPage) { ?>-->
<!--<li class="page-item"><a class="page-link" href="?page=<?php echo $nextPage ?>"><?php echo $nextPage ?></a></li>-->
<!--<li class="page-item">-->
<!--<a class="page-link" href="?page=<?php echo $lastPage ?>" aria-label="Next">-->
<!--<span aria-hidden="true">Next</span>-->
<!--</a>-->
<!--</li>-->
<!--<?php } ?>-->
      </ul>
    </div>
    <script>
        
        function confirmDelete(cid)
{
    var a=confirm('Do You Want to delete'); 
   
    if(a==true)
    {  
                   window.open( 'https://vivantalogistics.in/vivanta/deleteshipment.php?cid='+cid,'_blank');  
    }
}
    </script>