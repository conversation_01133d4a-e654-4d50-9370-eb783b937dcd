
<?php
session_start();
include("connection.php");

header('Content-Type: application/json');

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Get month parameter (format: YYYY-MM) - defaults to July 2025 for testing
$month = isset($_GET['month']) ? $_GET['month'] : '2025-07';

// Parse month
$month_parts = explode('-', $month);
$year = $month_parts[0];
$month_num = $month_parts[1];

try {
    // Test database connection
    if (!$con) {
        throw new Exception('Database connection failed: ' . mysqli_connect_error());
    }

    // Immediate test - show raw data
    echo "<!-- DEBUG: Testing database connection -->\n";
    echo "<!-- Month: $month, Year: $year, Month Num: $month_num -->\n";
    
    // Check if tbl_courier table exists
    $table_check = "SHOW TABLES LIKE 'tbl_courier'";
    $table_result = mysqli_query($con, $table_check);
    
    if (mysqli_num_rows($table_result) == 0) {
        throw new Exception('Table tbl_courier does not exist');
    }
    
    // Get table structure
    $structure_sql = "DESCRIBE tbl_courier";
    $structure_result = mysqli_query($con, $structure_sql);
    $fields = array();
    while ($field = mysqli_fetch_assoc($structure_result)) {
        $fields[] = $field['Field'];
    }
    
    // Test query for the specified month
    $test_sql = "SELECT 
                    COUNT(*) as total_records,
                    COUNT(CASE WHEN status = 'Delivered' THEN 1 END) as delivered_records,
                    COUNT(CASE WHEN status = 'Booked' THEN 1 END) as booked_records,
                    COUNT(CASE WHEN status = 'In Transit' THEN 1 END) as transit_records,
                    MIN(book_date) as earliest_date,
                    MAX(book_date) as latest_date
                FROM tbl_courier 
                WHERE YEAR(book_date) = '$year' 
                AND MONTH(book_date) = '$month_num'";
    
    $test_result = mysqli_query($con, $test_sql);
    
    if (!$test_result) {
        throw new Exception('Query failed: ' . mysqli_error($con));
    }
    
    $stats = mysqli_fetch_assoc($test_result);
    
    // Get sample delivered records
    $sample_sql = "SELECT 
                        cons_no,
                        book_date,
                        partno,
                        status,
                        ship_name,
                        rev_name
                    FROM tbl_courier 
                    WHERE YEAR(book_date) = '$year' 
                    AND MONTH(book_date) = '$month_num'
                    AND status = 'Completed'
                    LIMIT 5";
    
    $sample_result = mysqli_query($con, $sample_sql);
    $samples = array();
    
    if ($sample_result) {
        while ($row = mysqli_fetch_assoc($sample_result)) {
            $samples[] = $row;
        }
    }
    
    // Get all unique statuses for this month
    $status_sql = "SELECT DISTINCT status, COUNT(*) as count 
                   FROM tbl_courier 
                   WHERE YEAR(book_date) = '$year' 
                   AND MONTH(book_date) = '$month_num'
                   GROUP BY status
                   ORDER BY count DESC";
    
    $status_result = mysqli_query($con, $status_sql);
    $statuses = array();
    
    if ($status_result) {
        while ($row = mysqli_fetch_assoc($status_result)) {
            $statuses[] = $row;
        }
    }
    
    // Test the exact same query used in download_pod_file.php with date formatting
    $pod_test_sql = "SELECT
                c.cons_no as ConsignmentNo,
                DATE_FORMAT(c.book_date, '%d-%m-%Y') as book_date,
                c.partno as PartNo,
                c.noofpackage as noofpackages,
                c.qty as Qnty,
                c.weight as Weight,
                c.status,
                DATE_FORMAT(c.status_date, '%d-%m-%Y') as delivery_date,
                c.rev_name as receiver_name,
                c.ship_name as sender_name
            FROM tbl_courier c
            WHERE YEAR(c.book_date) = '$year'
            AND MONTH(c.book_date) = '$month_num'
            AND (c.status = 'Delivered' OR c.status = 'delivered' OR c.status = 'DELIVERED'
                 OR c.status = '6' OR c.status = 'Complete' OR c.status = 'Completed'
                 OR LOWER(c.status) LIKE '%deliver%' OR LOWER(c.status) LIKE '%complete%')
            ORDER BY c.book_date DESC
            LIMIT 5";

    $pod_test_result = mysqli_query($con, $pod_test_sql);
    $pod_test_data = array();

    if ($pod_test_result) {
        while ($row = mysqli_fetch_assoc($pod_test_result)) {
            $pod_test_data[] = $row;
        }
    }

    // Response
    echo json_encode(array(
        'success' => true,
        'month' => $month,
        'year' => $year,
        'month_num' => $month_num,
        'database_connection' => 'OK',
        'table_exists' => true,
        'table_fields' => $fields,
        'statistics' => $stats,
        'sample_delivered_records' => $samples,
        'status_breakdown' => $statuses,
        'query_executed' => $test_sql,
        'pod_query_test' => $pod_test_sql,
        'pod_test_results' => $pod_test_data,
        'pod_test_count' => count($pod_test_data)
    ));
    
} catch (Exception $e) {
    echo json_encode(array(
        'success' => false,
        'error' => $e->getMessage(),
        'month' => $month
    ));
}
?>

