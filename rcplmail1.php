<?php 
session_start();
require_once('database.php');
require_once('library.php');
require 'connection.php';
isUser();

if(isset($_POST['submit']))
{
	$mrId=mysqli_insert_id($con);
   $sql1 = "INSERT INTO custreg (`custname`, `custphone`, `custmail`, custadd, custzip, custsts, custcity,vattin, csttin, custstax,custpan) VALUES('".$_POST['custname']."','".$_POST['custphone']."','".$_POST['custemail']."','".$_POST['custaddress']."','".$_POST['custzip']."','".$_POST['states']."','".$_POST['city']."','".$_POST['custin']."','".$_POST['cstin']."','".$_POST['custstax']."','".$_POST['custpan']."')";	
	//dbQuery($sql1);
	
	mysqli_query($con,$sql);

/*
$mrId=mysqli_insert_id($con);
$sqllogin="INSERT INTO `login`( `UserId`, `userName`, `password`, `userType`) VALUES(".$mrId.",'".$_POST['mail']."','".$_POST['pswd']."','mr')";
if(!mysqli_query($con,$sqllogin))
{
echo "<br>".$msg='no';
}
*/
$my_email = "rcplexpress.com";
$headers = "MIME-Version: 1.0" . "\r\n";
$headers .= "Message-ID: <".gettimeofday()." TheSystem@Runanubandh>\r\n";
$headers .= "X-Mailer: PHP v Balaji\r\n";
// $headers = "From:<EMAIL> \r\n";
date_default_timezone_set( 'Asia/Calcutta' );
$headers .= "BCC: <EMAIL> ,".$_POST['custemail']."\r\n";
$headers .= "Content-type: text/html\r\n"; 
$subject="Welcome to the Indu Pharma Family.";
$message="Your registration details: <br>Username :".$_POST['custemail']."<br>Password:".$mrId;
//$subject = "Your Username And Password";
mail($my_email,$subject,$message,$headers);

}
header('Location: customeraddsuccess.php');
?>