<?php 
session_start();
require 'connection.php';
require_once('database.php');
require_once('library.php');
ob_start();

$sdate = $_GET['strdate'];
$ecddate1 = $_GET['date2'];
$custid = $_GET['id'];

$Cdate = date('Y-m-d', strtotime($sdate));
$Cdate1 = date('Y-m-d', strtotime($ecddate1));

$tr = "";

$sql = "SELECT userid, cons_no, weight, gtotamt, rev_name, r_add, ship_name, rate, oda_mis, status_date, book_mode, noofpackage, e_waybill, eway_start_date, eway_end_date, status, eway_expdate, gst, partno, remark, freight, invi_value, qty, assured_dly_date, book1_date, a.city_name AS city, type, invice_no, chweight, mode, statusname, vehicle 
        FROM (tbl_courier 
        INNER JOIN status ON tbl_courier.status = status.statusid) 
        JOIN tbl_city_code a ON tbl_courier.s_add = a.Id 
        WHERE tbl_courier.shipper_code = '$custid' 
        AND tbl_courier.book_date BETWEEN '$Cdate' AND '$Cdate1' 
        ORDER BY cons_no DESC";

$result = mysqli_query($con, $sql);
$count = 0;

while ($row = mysqli_fetch_array($result)) {
    $count++;

    $statusdate = ($row['status'] == '6' || $row['status'] == '50' || $row['status'] == '57') ? $row['status_date'] : '';
    $delivereddate = ($row['status'] == '5') ? $row['status_date'] : '';

    // Destination
    $destination = '';
    if (!empty($row['r_add'])) {
        $sql1 = "SELECT city_name FROM tbl_city_code WHERE Id = '" . $row['r_add'] . "'";
        $result1 = mysqli_query($con, $sql1);
        if ($row1 = mysqli_fetch_array($result1)) {
            $destination = $row1['city_name'];
        }
    }

    // Manager Info
    $Manager_name = $empcode = '';
    $sql2 = "SELECT Manager_name, empcode FROM tbl_courier_officers WHERE cid = '" . $row['userid'] . "'";
    $result2 = mysqli_query($con, $sql2);
    if ($row2 = mysqli_fetch_array($result2)) {
        $Manager_name = $row2['Manager_name'];
        $empcode = $row2['empcode'];
    }

    // Shipper Info
    $shipper_name = $shipper_phone = $shipper_mail = $shipper_add = $shipper_city = '';
    $sql3 = "SELECT * FROM custreg WHERE custname LIKE '" . $row['ship_name'] . "'";
    $result3 = mysqli_query($con, $sql3);
    if ($row_s = mysqli_fetch_array($result3)) {
        $shipper_name = $row_s['custname'];
        $shipper_phone = $row_s['custphone'];
        $shipper_mail = $row_s['custmail'];
        $shipper_add = $row_s['custadd'];
        $shipper_c = $row_s['custcity'];

        $sql4 = "SELECT cityname FROM city WHERE ctid = '$shipper_c'";
        $result4 = mysqli_query($con, $sql4);
        if ($row_c = mysqli_fetch_array($result4)) {
            $shipper_city = $row_c['cityname'];
        }
    }

    // Without GST value calculation
    $gst = isset($row['gst']) ? $row['gst'] : 0;
    $invi_value = isset($row['invi_value']) ? $row['invi_value'] : 0;
    $withoutgstvalue = $gst > 0 ? round($invi_value / (1 + ($gst / 100)), 2) : $invi_value;

    $tr .= "<tr>
        <td>{$count}</td>
        <td>{$Manager_name}</td>
        <td>{$empcode}</td>
        <td>{$shipper_name}</td>
        <td>{$shipper_phone}</td>
        <td>{$shipper_add}</td>
        <td>{$shipper_mail}</td>
        <td>{$shipper_city}</td>
        <td>{$row['status_date']}</td>
        <td>{$row['cons_no']}</td>
        <td>{$row['book1_date']}</td>
        <td>{$row['invice_no']}</td>
        <td>{$row['rev_name']}</td>
        <td>{$destination}</td>
        <td>{$row['noofpackage']}</td>
        <td>{$row['qty']}</td>
        <td>{$row['partno']}</td>
        <td>{$withoutgstvalue}</td>
        <td>{$invi_value}</td>
        <td>{$statusdate}</td>
        <td>{$row['book_mode']}</td>
        <td>{$row['statusname']}</td>
        <td>{$row['remark']}</td>
        <td>{$delivereddate}</td>
        <td>{$row['mode']}</td>
        <td>{$row['weight']}</td>
        <td>{$row['chweight']}</td>
        <td>{$row['e_waybill']}</td>
        <td>{$row['eway_start_date']}</td>
        <td>{$row['eway_end_date']}</td>
        <td>{$row['vehicle']}</td>
        <td>{$row['rate']}</td>
        <td>{$row['oda_mis']}</td>
        <td>{$row['freight']}</td>
        <td>{$gst}</td>
        <td>{$row['gtotamt']}</td>
    </tr>";
}

header("Content-Type: application/vnd.ms-excel; charset=utf-8");
header("Content-Disposition: attachment; filename=Customer_Report.xls");
header("Pragma: no-cache");
header("Expires: 0");

// Print table
echo '<table border="1" style="width:100%">
    <thead>
        <tr>
            <th>Sr No.</th>
            <th>Employee Name</th>
            <th>Emp ID</th>
            <th>Shipper Name</th>
            <th>Shipper Phone</th>
            <th>Shipper Address</th>
            <th>Shipper Mail</th>
            <th>Shipper City</th>
            <th>Scanning Date</th>
            <th>LR No</th>
            <th>BKG Date</th>
            <th>Invoice No</th>
            <th>Customer Name</th>
            <th>Destination</th>
            <th>Cases</th>
            <th>Qty</th>
            <th>Part No.</th>
            <th>Without GST Value</th>
            <th>Invoice Value</th>
            <th>Godown Receipt Date</th>
            <th>Type</th>
            <th>Remarks</th>
            <th>My Remarks</th>
            <th>Delivery Date</th>
            <th>Mode</th>
            <th>A/Weight</th>
            <th>C/Weight</th>
            <th>E WayBill No.</th>
            <th>E Way Start Date</th>
            <th>E Way End Date</th>
            <th>Vehicle No.</th>
            <th>Rate</th>
            <th>ODA</th>
            <th>Freight</th>
            <th>GST</th>
            <th>Total</th>
        </tr>
    </thead>
    <tbody>
        ' . $tr . '
    </tbody>
</table>';
?>
