<?php 
session_start();
require_once('library.php');
$rand = get_rand_id(8);
//echo $rand;
  $userid=$_SESSION['desgn'];
 $a=$_SESSION['username'];
/* $sql="select * from login where type='$a'";
$result = mysqli_query($con, $sql);
$row1 = mysqli_fetch_array($result);
  $userid=$row1['rid'];*/
	
$query = "SELECT MAX(id) AS rcid FROM receiver_reg ";  
    if($result = mysqli_query($con,$query))
    {
  while ($row = mysqli_fetch_assoc($result))
  {
        $count = $row['rcid'];
        $count = $count+1;

      $code_no = str_pad($count, 7, "600100", STR_PAD_LEFT);
  }
	}

	// Initialize POST variables
	$he = $_POST['cstin'] ?? 0;
	$we = $_POST['custstax'] ?? 0;
	$len = $_POST['custpan'] ?? 0;

	// echo $he;
	// echo $we;
	// echo $len;

	 $tot=($len*$we*$he)/6000;

	// Initialize state dropdown variables
	$stname = $_POST['stname'] ?? '';
	$statedrop = $_POST['statedrop'] ?? '';

 $statesql="SELECT * FROM state order by statename ASC";
 $stateresult=mysqli_query($con,$statesql);
 while($staterow=mysqli_fetch_array($stateresult))
{
	if($stname==$staterow['stid'])
	{
	 $statedrop=$statedrop."<option value='".$staterow['stid']."' selected>".$staterow['statename']."</option>";
	}
	else{
	  $statedrop=$statedrop."<option value='".$staterow['stid']."' >".$staterow['statename']."</option>";
	}
}

// Initialize city dropdown variables
$cityname = $_POST['cityname'] ?? '';
$citydrop = $_POST['citydrop'] ?? '';

$statesql="SELECT * FROM tbl_city_code";
 $cityresult=mysqli_query($con,$statesql);
 while($staterow=mysqli_fetch_array($cityresult))
{

	if($cityname==$staterow['Id'])
	{
	 $citydrop=$citydrop."<option value='".$staterow['Id']."' selected>".$staterow['city_name']."-".$staterow['city_code']."</option>";
	}
	else{
	  $citydrop=$citydrop."<option value='".$staterow['Id']."' >".$staterow['city_name']."-".$staterow['city_code']."</option>";
	}
}
?>
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>

	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	<title>Vivanta Logistics</title>
	<meta name="description" content="">
	<meta name="author" content="Ahmed Saeed">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
	
</head>
<?php
include("header.php");
?>



        <div class="container">

			<div class="row">

				<div class="span2">
				
				</div><!--end span8-->
                             
                <div class="span8">
					<div class="register">
						<div class="titleHeader clearfix">
							<h3>Receiver Registration</h3>
						</div><!--end titleHeader-->
						<form action="process.php?action=add-receiver" method="post" class="form-horizontal" onSubmit="return validate()" name="frmShipment">
						   	<legend>&nbsp;&nbsp;&nbsp;&nbsp;Receiver Information :</legend>
	<div class="control-group ">
							    <label class="control-label">Receiver Code : <span class="text-error" >*</span></label>
							    <div class="controls">
							      <input type="text" name="rccode" placeholder="Receiver Code" onKeyUp="myFunction(this.value)" value="<?php echo $code_no; ?>" readonly> <small class="errorText" id="hubunames" style="color:red"></small><small class="errorText" id="hubunamea" style="color:green"></small>
							     <!-- <span class="help-inline"><i class="icon-ok"></i> Avaliable input!</span>-->
							    </div>
							</div>
							<div class="control-group ">
							    <label class="control-label">Receiver Name : <span class="text-error" >*</span></label>
							    <div class="controls">
							      <input type="text" name="rcname" placeholder="Receiver Name">
							     <!-- <span class="help-inline"><i class="icon-ok"></i> Avaliable input!</span>-->
							    </div>
							</div><!--end control-group-->
							<input type="hidden" name="cid" id="cid" value="<?php echo $userid;?>">
								<div class="control-group ">
							    <label class="control-label" for="rcaddress"> Address : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="rcaddress" placeholder="Address">
							     <!--< <span class="help-inline">-->
							    </div>
							</div>
								<div class="control-group success">
							    <label class="control-label" for="custzip">Pin Code: <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="rczip" placeholder="Zip Code">
							      <!--<span class="help-inline"><i class="icon-ok"></i> Avaliable input!</span>-->
							    </div>
							</div>
							<div class="control-group">
							    <label class="control-label" for="custphone">Mobile No : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="rcphone" placeholder="Mobile No">
							    </div>
							</div><!--end control-group-->
							
						
							
								<div class="control-group">
							    <label class="control-label" for="custphone">Contact Person  : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="cphone" placeholder="Contact Person Name">
							    </div>
							</div><!--end control-group-->
							<div class="control-group">
							    <label class="control-label">E-Mail : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="email" name="rcemail" placeholder="<EMAIL>">
							    </div>
							</div><!--end control-group-->
						<!--end control-group   <i class="icon-remove"></i> Invalid input!</span>-->
						<!--end control-group-->
							<div class="control-group">
							    <div class="control-label">States: <span class="text-error">*</span></div>
							    <div class="controls">
								<select name="states" id="states" onChange="cityfun()">
									     <option  value="">-- Please Select --</option>
												<?php echo $statedrop; ?>
								</select></div>
							</div><!--end control-group-->
							<div class="control-group ">
							    <label class="control-label" for="Shipperaddress">Receiver City : <span class="text-error">*</span></label>
							    <div class="controls">
							      <select name="city" id="city" ><option value="" selected="selected">-- Select--</option>
							     <!--< <span class="help-inline">-->	<?php echo $citydrop; ?>
							     </select>
							    </div>
							</div><!--end control-group-->
						<!--end control-group-->

<!--end control-group-->    

							<div class="control-group">
							    <label class="control-label" for="custstax">GST No: </label>
							    <div class="controls">
							      <input type="text" name="rcgst" placeholder="GST No">
							    </div>
							</div><!--end control-group-->
							<div class="control-group ">
							    <label class="control-label" for="custpan">PAN No : </label>
							    <div class="controls">
							      <input type="text" name="rcpan" placeholder="PAN No">
							     <!--< <span class="help-inline">-->
							    </div>
							</div><!--end control-group   <i class="icon-remove"></i> Invalid input!</span>-->



					
							<div class="control-group">
							    <div class="controls">
							
									<input name="Submit" class="btn btn-primary" type="submit" value="Receiver Register" onClick="return validateForm()">
									<button type="reset" class="btn ">Clear</button>
							    </div>
							</div><!--end control-group-->
							
																				
						</form><!--end form-->

					</div><!--end register-->
				
				</div><!--end span-->
	
			</div><!--end row-->

        </div><!--end conatiner-->



<script>
function validate()
{
 var name=$("#Name").val();
 if(name=='')
 {
 	alert("Please Select Name Or User  Id Not set");
 	return false;
 }
 var address=$("#Address").val();
 if(address=='')
 {
 	alert("Please Enter Shipping address");
 	$("#Address").focus();
 	return false;
 }	
}
</script> 
		
<script  type="text/javascript">
function validateForm()
{
var x=document.forms["frmShipment"]["rcname"].value;
if (x==null || x=="")
  {
  alert("Receiver Name must be filled out");
  return false;
  }
  
  var phone=document.forms["frmShipment"]["rcphone"].value;
if (phone==null || phone=="")
  {
   alert("Mobile No must be 10 digit ");
  return false;
  
  }
  
 var custadd=document.forms["frmShipment"]["rcemail"].value;
if (custadd==null || custadd=="")
  {
  alert("Email must be filled out");
  return false;
  }
  var add=document.forms["frmShipment"]["rcaddress"].value;
if (add==null || add=="")
  {
  alert("Address must be filled out");
  return false;
  }
  //
  var x=document.forms["frmShipment"]["rczip"].value;
if (x==null || x=="")
  {
  alert("Zip Code must be filled out");
  return false;
  }
  
  var states1=document.forms["frmShipment"]["states"].selectedIndex;
if (states1==null || states1=="")
  {
   alert("State must be select");
  return false;
  
  }
  
 var city1=document.forms["frmShipment"]["city"].selectedIndex;
if (city1==null || city1=="")
  {
  alert("City must be select");
  return false;
  }
/*  var custin1=document.forms["frmShipment"]["cusvatin"].value;
if (custin1==null || custin1=="")
  {
  alert("VAT Tin must be filled out");
  return false;
  }

   var csttin1=document.forms["frmShipment"]["csttin"].value;
if (csttin1==null || csttin1=="")
  {
  alert("CST Tin must be filled out");
  return false;
  }
  
 var custstax1=document.forms["frmShipment"]["custstax"].value;
if (custstax1==null || custstax1=="")
  {
  alert("Service Tax must be filled out");
  return false;
  }
  var custpan1=document.forms["frmShipment"]["custpan"].value;
if (custpan1==null || custpan1=="")
  {
  alert("PAN No. must be filled out");
  return false;
  }
*/
}
</script>

<script  type="text/javascript">
function validateForm1()
{ 
  var x1=document.forms["frmShipment"]["rcphone"].value;
if (!x1>10 || x1=10)
  {
  alert("Mobile No must be 10 digit ");
  return false;
  }
  
 var x2=document.forms["frmShipment"]["rcemail"].value;
 var atposition=x2.indexOf("@");  
var dotposition=x2.lastIndexOf(".");  
if (atposition<1 || dotposition<atposition+2 || dotposition+2>=x.length){  
  alert("Please enter a valid e-mail address \n atpostion:"+atposition+"\n dotposition:"+dotposition);  
  return false;  
  }
  
 var x3=document.forms["frmShipment"]["rcaddress"].value;
if (x3==null || x3=="")
  {
  alert("Address must be filled out");
  return false;
  }
  
 var x4=document.forms["frmShipment"]["rczip"].value;
if (x4==6 || !x4>6)
  {
  alert("Zip code must be 6 Digit only ");
  return false;
  }
  
 var x5=document.forms["frmShipment"]["states"].value;
if (x5==null || x5=="")
  {
  alert("Please Select States");
  return false;
  }
  
   var x6=document.forms["frmShipment"]["city"].value;
if (x6==null || x6=="")
  {
  alert("Please Select City");
  return false;
  }
  
 var x7=document.forms["frmShipment"]["custin"].value;
if (isNaN(x7)){  
  document.getElementById("vati").innerHTML="Enter Numeric value only";  
  return false;  
}else{  
  return true;  
  }  
  
  var x8=document.forms["frmShipment"]["rcgst"].value;
if (isNaN(x8)){  
  document.getElementById("st").innerHTML="Enter Numeric value only";  
  return false;  
}else{  
  return true;  
  }  
}
</script>
    <script src="https://code.jquery.com/jquery-1.9.1.min.js"></script>
<script>
$(document).ready(function(){ 
         $("#add").click(function(){
        
  var rowCount=$('#tb tr').length;

$("#tb").append('<tr><td>'+rowCount+'</td><td><input name="sources'+rowCount+'" id="sources'+rowCount+'" class="input-small"></td><td><input name="destin'+rowCount+'" id="destin'+rowCount+'" class="input-small"></td><td><select name="mode'+rowCount+'" id="mode'+rowCount+'" class="input-small" ><option name="mode'+rowCount+'" id="mode'+rowCount+'" value="" selected>Select</option><option name="mode'+rowCount+'" id="mode'+rowCount+'" value="Air" >Air</option><option name="mode'+rowCount+'" id="mode'+rowCount+'" value="Road" >Road</option></select> </td><td><input name="fuelc'+rowCount+'" id="fuelc'+rowCount+'" class="input-small"></td><td><select name="ftl'+rowCount+'" id="ftl'+rowCount+'" class="input-small"><option name="ftl'+rowCount+'" id="ftl'+rowCount+'" class="input-small"  value="" selected>Select</option><option name="ftl'+rowCount+'" id="ftl'+rowCount+'" value="ftl" class="input-small">FTL</option><option name="ftl'+rowCount+'" id="ftl'+rowCount+'" value="per/kg" class="input-small" >Per/Kg</option></select> </td><td><input name="rate'+rowCount+'" id="rate'+rowCount+'" class="input-small"></td></tr>');



     $("#ct").val(rowCount);
    });
});
</script>
<script> 
  function del1()
{
rowCount=0;
 rowCount=$('#tb tr').length;
 if((rowCount-1)!=1)
 {
  	$('#hid_count1').val(rowCount-2);
 	if(rowCount!=2)
 	{
	var table = document.getElementById("tb");
 	table.deleteRow(rowCount -1);
  
	}
}
}
</script>
           
<script>
function cityfun() { 
 // alert("hhi");
   $('#city').find('option').remove().end().append('<option value="">----Select City----</option>').val('');
    $.ajax({                                      
      url: 'ajax_getCity.php?type='+$('#states').val(),                  //the script to call to get data          
      data: "",                        //you can insert url argumnets here to pass to api.php
                                       //for example "id=5&parent=6"
      dataType: 'json',                //data format      
      success: function(data)          //on recieve of reply
      {
      $.each(data, function(index, data) {
        $('#city').append( $('<option></option>').val(data.id).html(data.name) );
       });
       }
       });
       
    }
	
</script>
<!--<script  type="text/javascript">
function myFunction(q)
{

$.ajax({

  type: "POST",
  url: "rcode.php",
  data: {item:q},
  success:function(data){
 //alert(data);
  document.getElementById("hubunames").innerHTML=data;
  if(data=="Receiver Code Already Exist!!")
  {
  document.getElementById("bute").style.visibility = 'hidden';
  document.getElementById("hubunamea").style.visibility = 'hidden';
  }else
  {
	document.getElementById("hubunamea").style.visibility = 'visible';
	document.getElementById("hubunamea").innerHTML="Receiver Code Available!!";
	document.getElementById("bute").style.visibility = 'visible';  
  }
}
  });
}
</script> -->

<script>
function insur(insu){
	if(insu=="ys")
	{  
	document.getElementById("insurance").style.display="block";
	}
	else{
	document.getElementById("insurance").style.display="none";
	}
}
</script> 
<?php
											
								$msg = $_GET['msg'] ?? '';
								if($msg=="yes1")
								{
						echo "<script> alert('Receiver Registered Successfully');</script>";
								}
								else if($msg=="no1"){
						echo "<script> alert('Receiver Not Registered Successfully');</script>";
								}
								?>  
<?php
include("footer.php");
?>