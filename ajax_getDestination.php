<?php 
session_start();
require_once('database.php');
require_once('library.php');
require 'connection.php';
isUser();


//$utype=isUser();;
//$UserId=$_SESSION['UserId'];

$type=$_GET['type'];


$sql="SELECT crrid,crdesti,crrate,fuelc,perkgftp FROM `custrate` where crid='$type' order by crdesti ASC  ";

$result = mysqli_query($con,$sql); 
        
 $data = array();
 while($row = mysqli_fetch_array($result)){
	 
$row_data = array( 
   
   'id' => $row['crrid'],
   'name' => $row['crdesti'],
   'rate' => $row['crrate'],
   'fuel' => $row['fuelc'],
   'unit' => $row['perkgftp'],
     
   );
   
  array_push($data, $row_data);
  
 }
 
echo json_encode($data);
 
?>