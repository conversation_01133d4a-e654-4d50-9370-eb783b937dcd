<?php
session_start();
require_once('database.php');
require_once('library.php');
require 'connection.php';
$error = "";
if(isset($_POST['txtusername'])){
	$error = checkUser($_POST['txtusername'],$_POST['txtpassword'],$con);
}//if

//require_once('database.php');
$sql = "SELECT DISTINCT(off_name)
		FROM tbl_offices";
$result = mysqli_query($con,$sql);

?>
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>

	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<title>Vivanta Logistics Pvt. Ltd.</title>

	<!-- CSS
	
  ================================================== -->
  
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
<script language="javascript">

function memloginvalidate()
{
   if(document.form1.txtusername.value == "")
     {
        alert("Please enter admin UserName.");
        document.form1.txtusername.focus();
        return false;
     }
   if(document.form1.txtpassword.value == "")
     {
        alert("Please enter admin Password.");
        document.form1.txtpassword.focus();
        return false;
     }
   }


</script>
</head>
<body onLoad="document.form1.txtusername.focus();">

	<div id="mainContainer" class="clearfix">

		<!--begain header-->
		<header>
			<div class="middleHeader">
				<div class="container">

					<div class="middleContainer clearfix">

					<div class="siteLogo pull-left">
						<h1><a href="index.php">Vivanta Logistics</a></h1>
					</div>

				
					</div><!--end middleCoontainer-->

				</div><!--end container-->
			</div><!--end middleHeader-->

		</header>
		<!-- end header -->


		<div class="container">

			<div class="row">

				<div class="span4"></div><!--end span8-->
                
                
                    <div class="span4 box_login">
						<div class="titleHeader clearfix">
							<h3>Login</h3>
						</div>

						<div class="newslatter">
							<form  name="form1" id="form1" method="post" onSubmit="return memloginvalidate()">
								<input type="text" name="txtusername" class="input" placeholder="User Name" />
								<input type="password" name="txtpassword" class="input" placeholder="Password" />
								<p align="center"><button type="submit" name="Submit" class="btn btn-primary" >Login</button>
                                <button type="reset" class="btn">Reset</button></p>
							</form>
							
						</div>

					</div>






			</div><!--end row-->

		</div><!--end conatiner-->
		
		
	<?php
											
								$msg = $_GET['msg'] ?? '';
								if($msg=="no")
								{
						echo "<script> alert('Username and Password is incorrect');</script>";
								}
							
								?>  
<?php
include("footer.php");?>