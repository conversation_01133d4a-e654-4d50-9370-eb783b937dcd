/* @license C3.js v0.6.3 | (c) C3 Team and other contributors | http://c3js.org/ */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.c3=e()}(this,function(){"use strict";var Y={target:"c3-target",chart:"c3-chart",chartLine:"c3-chart-line",chartLines:"c3-chart-lines",chartBar:"c3-chart-bar",chartBars:"c3-chart-bars",chartText:"c3-chart-text",chartTexts:"c3-chart-texts",chartArc:"c3-chart-arc",chartArcs:"c3-chart-arcs",chartArcsTitle:"c3-chart-arcs-title",chartArcsBackground:"c3-chart-arcs-background",chartArcsGaugeUnit:"c3-chart-arcs-gauge-unit",chartArcsGaugeMax:"c3-chart-arcs-gauge-max",chartArcsGaugeMin:"c3-chart-arcs-gauge-min",selectedCircle:"c3-selected-circle",selectedCircles:"c3-selected-circles",eventRect:"c3-event-rect",eventRects:"c3-event-rects",eventRectsSingle:"c3-event-rects-single",eventRectsMultiple:"c3-event-rects-multiple",zoomRect:"c3-zoom-rect",brush:"c3-brush",focused:"c3-focused",defocused:"c3-defocused",region:"c3-region",regions:"c3-regions",title:"c3-title",tooltipContainer:"c3-tooltip-container",tooltip:"c3-tooltip",tooltipName:"c3-tooltip-name",shape:"c3-shape",shapes:"c3-shapes",line:"c3-line",lines:"c3-lines",bar:"c3-bar",bars:"c3-bars",circle:"c3-circle",circles:"c3-circles",arc:"c3-arc",arcLabelLine:"c3-arc-label-line",arcs:"c3-arcs",area:"c3-area",areas:"c3-areas",empty:"c3-empty",text:"c3-text",texts:"c3-texts",gaugeValue:"c3-gauge-value",grid:"c3-grid",gridLines:"c3-grid-lines",xgrid:"c3-xgrid",xgrids:"c3-xgrids",xgridLine:"c3-xgrid-line",xgridLines:"c3-xgrid-lines",xgridFocus:"c3-xgrid-focus",ygrid:"c3-ygrid",ygrids:"c3-ygrids",ygridLine:"c3-ygrid-line",ygridLines:"c3-ygrid-lines",axis:"c3-axis",axisX:"c3-axis-x",axisXLabel:"c3-axis-x-label",axisY:"c3-axis-y",axisYLabel:"c3-axis-y-label",axisY2:"c3-axis-y2",axisY2Label:"c3-axis-y2-label",legendBackground:"c3-legend-background",legendItem:"c3-legend-item",legendItemEvent:"c3-legend-item-event",legendItemTile:"c3-legend-item-tile",legendItemHidden:"c3-legend-item-hidden",legendItemFocused:"c3-legend-item-focused",dragarea:"c3-dragarea",EXPANDED:"_expanded_",SELECTED:"_selected_",INCLUDED:"_included_"};function i(t){var e=this;e.d3=window.d3?window.d3:"undefined"!=typeof require?require("d3"):void 0,e.api=t,e.config=e.getDefaultConfig(),e.data={},e.cache={},e.axes={}}var o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n={version:"0.6.3",chart:{fn:e.prototype,internal:{fn:i.prototype}}};function e(t){var e=this.internal=new i(this);e.loadConfig(t),e.beforeInit(t),e.init(),e.afterInit(t),function e(i,n,a){Object.keys(i).forEach(function(t){n[t]=i[t].bind(a),0<Object.keys(i[t]).length&&e(i[t],n[t],a)})}(n.chart.fn,this,this)}function a(t,e,i){this.owner=t,n.chart.internal[e]=i}n.generate=function(t){return new e(t)};var r,s,P=function(t){return t||0===t},h=function(t){return"function"==typeof t},u=function(t){return Array.isArray(t)},c=function(t){return"string"==typeof t},v=function(t){return void 0===t},k=function(t){return void 0!==t},d=function(t){return 10*Math.ceil(t/10)},l=function(t){return Math.ceil(t)+.5},D=function(t){return t[1]-t[0]},g=function(t){return null==t||c(t)&&0===t.length||"object"===(void 0===t?"undefined":o(t))&&0===Object.keys(t).length},C=function(t){return!n.chart.internal.fn.isEmpty(t)},N=function(t,e,i){return k(t[e])?t[e]:i},f=function(e,i){var n=!1;return Object.keys(e).forEach(function(t){e[t]===i&&(n=!0)}),n},_=function(t){return"string"==typeof t?t.replace(/</g,"&lt;").replace(/>/g,"&gt;"):t},y=function(t){var e=t.getBoundingClientRect(),i=[t.pathSegList.getItem(0),t.pathSegList.getItem(1)];return{x:i[0].x,y:Math.min(i[0].y,i[1].y),width:e.width,height:e.height}};function p(t,e){var i=this;i.component=t,i.params=e||{},i.d3=t.d3,i.scale=i.d3.scaleLinear(),i.range,i.orient="bottom",i.innerTickSize=6,i.outerTickSize=this.params.withOuterTick?6:0,i.tickPadding=3,i.tickValues=null,i.tickFormat,i.tickArguments,i.tickOffset=0,i.tickCulling=!0,i.tickCentered,i.tickTextCharSize,i.tickTextRotate=i.params.tickTextRotate,i.tickLength,i.axis=i.generateAxis()}(s=p.prototype).axisX=function(t,e,i){t.attr("transform",function(t){return"translate("+Math.ceil(e(t)+i)+", 0)"})},s.axisY=function(t,e){t.attr("transform",function(t){return"translate(0,"+Math.ceil(e(t))+")"})},s.scaleExtent=function(t){var e=t[0],i=t[t.length-1];return e<i?[e,i]:[i,e]},s.generateTicks=function(t){var e,i,n=[];if(t.ticks)return t.ticks.apply(t,this.tickArguments);for(i=t.domain(),e=Math.ceil(i[0]);e<i[1];e++)n.push(e);return 0<n.length&&0<n[0]&&n.unshift(n[0]-(n[1]-n[0])),n},s.copyScale=function(){var t,e=this.scale.copy();return this.params.isCategory&&(t=this.scale.domain(),e.domain([t[0],t[1]-1])),e},s.textFormatted=function(t){var e=this.tickFormat?this.tickFormat(t):t;return void 0!==e?e:""},s.updateRange=function(){var t=this;return t.range=t.scale.rangeExtent?t.scale.rangeExtent():t.scaleExtent(t.scale.range()),t.range},s.updateTickTextCharSize=function(t){var r=this;if(r.tickTextCharSize)return r.tickTextCharSize;var s={h:11.5,w:5.5};return t.select("text").text(function(t){return r.textFormatted(t)}).each(function(t){var e=this.getBoundingClientRect(),i=r.textFormatted(t),n=e.height,a=i?e.width/i.length:void 0;n&&a&&(s.h=n,s.w=a)}).text(""),r.tickTextCharSize=s},s.isVertical=function(){return"left"===this.orient||"right"===this.orient},s.tspanData=function(t,e,i){var n=this,a=n.params.tickMultiline?n.splitTickText(t,i):[].concat(n.textFormatted(t));return n.params.tickMultiline&&0<n.params.tickMultilineMax&&(a=n.ellipsify(a,n.params.tickMultilineMax)),a.map(function(t){return{index:e,splitted:t,length:a.length}})},s.splitTickText=function(t,e){var a,r,s,o=this,i=o.textFormatted(t),c=o.params.tickWidth;if("[object Array]"===Object.prototype.toString.call(i))return i;return(!c||c<=0)&&(c=o.isVertical()?95:o.params.isCategory?Math.ceil(e(1)-e(0))-12:110),function t(e,i){r=void 0;for(var n=1;n<i.length;n++)if(" "===i.charAt(n)&&(r=n),a=i.substr(0,n+1),s=o.tickTextCharSize.w*a.length,c<s)return t(e.concat(i.substr(0,r||n)),i.slice(r?r+1:n));return e.concat(i)}([],i+"")},s.ellipsify=function(t,e){if(t.length<=e)return t;for(var i=t.slice(0,e),n=3,a=e-1;0<=a;a--){var r=i[a].length;if(i[a]=i[a].substr(0,r-n).padEnd(r,"."),(n-=r)<=0)break}return i},s.updateTickLength=function(){this.tickLength=Math.max(this.innerTickSize,0)+this.tickPadding},s.lineY2=function(t){var e=this,i=e.scale(t)+(e.tickCentered?0:e.tickOffset);return e.range[0]<i&&i<e.range[1]?e.innerTickSize:0},s.textY=function(){var t=this.tickTextRotate;return t?11.5-t/15*2.5*(0<t?1:-1):this.tickLength},s.textTransform=function(){var t=this.tickTextRotate;return t?"rotate("+t+")":""},s.textTextAnchor=function(){var t=this.tickTextRotate;return t?0<t?"start":"end":"middle"},s.tspanDx=function(){var t=this.tickTextRotate;return t?8*Math.sin(Math.PI*(t/180)):0},s.tspanDy=function(t,e){var i=this.tickTextCharSize.h;return 0===e&&(i=this.isVertical()?-((t.length-1)*(this.tickTextCharSize.h/2)-3):".71em"),i},s.generateAxis=function(){var w=this,v=w.d3,b=w.params;function A(t,m){var S;return t.each(function(){var t,e,i,n=A.g=v.select(this),a=this.__chart__||w.scale,r=this.__chart__=w.copyScale(),s=w.tickValues?w.tickValues:w.generateTicks(r),o=n.selectAll(".tick").data(s,r),c=o.enter().insert("g",".domain").attr("class","tick").style("opacity",1e-6),d=o.exit().remove(),u=o.merge(c);b.isCategory?(w.tickOffset=Math.ceil((r(1)-r(0))/2),e=w.tickCentered?0:w.tickOffset,i=w.tickCentered?w.tickOffset:0):w.tickOffset=e=0,w.updateRange(),w.updateTickLength(),w.updateTickTextCharSize(n.select(".tick"));var l=u.select("line").merge(c.append("line")),h=u.select("text").merge(c.append("text")),g=u.selectAll("text").selectAll("tspan").data(function(t,e){return w.tspanData(t,e,r)}),f=g.enter().append("tspan").merge(g).text(function(t){return t.splitted});g.exit().remove();var p=n.selectAll(".domain").data([0]),_=p.enter().append("path").merge(p).attr("class","domain");switch(w.orient){case"bottom":t=w.axisX,l.attr("x1",e).attr("x2",e).attr("y2",function(t,e){return w.lineY2(t,e)}),h.attr("x",0).attr("y",function(t,e){return w.textY(t,e)}).attr("transform",function(t,e){return w.textTransform(t,e)}).style("text-anchor",function(t,e){return w.textTextAnchor(t,e)}),f.attr("x",0).attr("dy",function(t,e){return w.tspanDy(t,e)}).attr("dx",function(t,e){return w.tspanDx(t,e)}),_.attr("d","M"+w.range[0]+","+w.outerTickSize+"V0H"+w.range[1]+"V"+w.outerTickSize);break;case"top":t=w.axisX,l.attr("x1",e).attr("x2",e).attr("y2",function(t,e){return-1*w.lineY2(t,e)}),h.attr("x",0).attr("y",function(t,e){return-1*w.textY(t,e)-(b.isCategory?2:w.tickLength-2)}).attr("transform",function(t,e){return w.textTransform(t,e)}).style("text-anchor",function(t,e){return w.textTextAnchor(t,e)}),f.attr("x",0).attr("dy",function(t,e){return w.tspanDy(t,e)}).attr("dx",function(t,e){return w.tspanDx(t,e)}),_.attr("d","M"+w.range[0]+","+-w.outerTickSize+"V0H"+w.range[1]+"V"+-w.outerTickSize);break;case"left":t=w.axisY,l.attr("x2",-w.innerTickSize).attr("y1",i).attr("y2",i),h.attr("x",-w.tickLength).attr("y",w.tickOffset).style("text-anchor","end"),f.attr("x",-w.tickLength).attr("dy",function(t,e){return w.tspanDy(t,e)}),_.attr("d","M"+-w.outerTickSize+","+w.range[0]+"H0V"+w.range[1]+"H"+-w.outerTickSize);break;case"right":t=w.axisY,l.attr("x2",w.innerTickSize).attr("y1",i).attr("y2",i),h.attr("x",w.tickLength).attr("y",w.tickOffset).style("text-anchor","start"),f.attr("x",w.tickLength).attr("dy",function(t,e){return w.tspanDy(t,e)}),_.attr("d","M"+w.outerTickSize+","+w.range[0]+"H0V"+w.range[1]+"H"+w.outerTickSize)}if(r.rangeBand){var x=r,y=x.rangeBand()/2;a=r=function(t){return x(t)+y}}else a.rangeBand?a=r:d.call(t,r,w.tickOffset);c.call(t,a,w.tickOffset),S=(m?u.transition(m):u).style("opacity",1).call(t,r,w.tickOffset)}),S}return A.scale=function(t){return arguments.length?(w.scale=t,A):w.scale},A.orient=function(t){return arguments.length?(w.orient=t in{top:1,right:1,bottom:1,left:1}?t+"":"bottom",A):w.orient},A.tickFormat=function(t){return arguments.length?(w.tickFormat=t,A):w.tickFormat},A.tickCentered=function(t){return arguments.length?(w.tickCentered=t,A):w.tickCentered},A.tickOffset=function(){return w.tickOffset},A.tickInterval=function(){var t;return(t=b.isCategory?2*w.tickOffset:(A.g.select("path.domain").node().getTotalLength()-2*w.outerTickSize)/A.g.selectAll("line").size())===1/0?0:t},A.ticks=function(){return arguments.length?(w.tickArguments=arguments,A):w.tickArguments},A.tickCulling=function(t){return arguments.length?(w.tickCulling=t,A):w.tickCulling},A.tickValues=function(t){if("function"==typeof t)w.tickValues=function(){return t(w.scale.domain())};else{if(!arguments.length)return w.tickValues;w.tickValues=t}return A},A};var x,m,S=function(t){function n(t){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,n);var e={fn:r,internal:{fn:s}},i=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,t,"axis",e));return i.d3=t.d3,i.internal=p,i}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(n,a),n}();return(r=S.prototype).init=function(){var t=this.owner,e=t.config,i=t.main;t.axes.x=i.append("g").attr("class",Y.axis+" "+Y.axisX).attr("clip-path",e.axis_x_inner?"":t.clipPathForXAxis).attr("transform",t.getTranslate("x")).style("visibility",e.axis_x_show?"visible":"hidden"),t.axes.x.append("text").attr("class",Y.axisXLabel).attr("transform",e.axis_rotated?"rotate(-90)":"").style("text-anchor",this.textAnchorForXAxisLabel.bind(this)),t.axes.y=i.append("g").attr("class",Y.axis+" "+Y.axisY).attr("clip-path",e.axis_y_inner?"":t.clipPathForYAxis).attr("transform",t.getTranslate("y")).style("visibility",e.axis_y_show?"visible":"hidden"),t.axes.y.append("text").attr("class",Y.axisYLabel).attr("transform",e.axis_rotated?"":"rotate(-90)").style("text-anchor",this.textAnchorForYAxisLabel.bind(this)),t.axes.y2=i.append("g").attr("class",Y.axis+" "+Y.axisY2).attr("transform",t.getTranslate("y2")).style("visibility",e.axis_y2_show?"visible":"hidden"),t.axes.y2.append("text").attr("class",Y.axisY2Label).attr("transform",e.axis_rotated?"":"rotate(-90)").style("text-anchor",this.textAnchorForY2AxisLabel.bind(this))},r.getXAxis=function(t,e,i,n,a,r,s){var o=this.owner,c=o.config,d={isCategory:o.isCategorized(),withOuterTick:a,tickMultiline:c.axis_x_tick_multiline,tickMultilineMax:c.axis_x_tick_multiline?Number(c.axis_x_tick_multilineMax):0,tickWidth:c.axis_x_tick_width,tickTextRotate:s?0:c.axis_x_tick_rotate,withoutTransition:r},u=new this.internal(this,d).axis.scale(t).orient(e);return o.isTimeSeries()&&n&&"function"!=typeof n&&(n=n.map(function(t){return o.parseDate(t)})),u.tickFormat(i).tickValues(n),o.isCategorized()&&(u.tickCentered(c.axis_x_tick_centered),g(c.axis_x_tick_culling)&&(c.axis_x_tick_culling=!1)),u},r.updateXAxisTickValues=function(t,e){var i,n=this.owner,a=n.config;return(a.axis_x_tick_fit||a.axis_x_tick_count)&&(i=this.generateTickValues(n.mapTargetsToUniqueXs(t),a.axis_x_tick_count,n.isTimeSeries())),e?e.tickValues(i):(n.xAxis.tickValues(i),n.subXAxis.tickValues(i)),i},r.getYAxis=function(t,e,i,n,a,r,s){var o=this.owner,c=o.config,d={withOuterTick:a,withoutTransition:r,tickTextRotate:s?0:c.axis_y_tick_rotate},u=new this.internal(this,d).axis.scale(t).orient(e).tickFormat(i);return o.isTimeSeriesY()?u.ticks(c.axis_y_tick_time_type,c.axis_y_tick_time_interval):u.tickValues(n),u},r.getId=function(t){var e=this.owner.config;return t in e.data_axes?e.data_axes[t]:"y"},r.getXAxisTickFormat=function(){var e=this.owner,i=e.config,n=e.isTimeSeries()?e.defaultAxisTimeFormat:e.isCategorized()?e.categoryName:function(t){return t};return i.axis_x_tick_format&&(h(i.axis_x_tick_format)?n=i.axis_x_tick_format:e.isTimeSeries()&&(n=function(t){return t?e.axisTimeFormat(i.axis_x_tick_format)(t):""})),h(n)?function(t){return n.call(e,t)}:n},r.getTickValues=function(t,e){return t||(e?e.tickValues():void 0)},r.getXAxisTickValues=function(){return this.getTickValues(this.owner.config.axis_x_tick_values,this.owner.xAxis)},r.getYAxisTickValues=function(){return this.getTickValues(this.owner.config.axis_y_tick_values,this.owner.yAxis)},r.getY2AxisTickValues=function(){return this.getTickValues(this.owner.config.axis_y2_tick_values,this.owner.y2Axis)},r.getLabelOptionByAxisId=function(t){var e,i=this.owner.config;return"y"===t?e=i.axis_y_label:"y2"===t?e=i.axis_y2_label:"x"===t&&(e=i.axis_x_label),e},r.getLabelText=function(t){var e=this.getLabelOptionByAxisId(t);return c(e)?e:e?e.text:null},r.setLabelText=function(t,e){var i=this.owner.config,n=this.getLabelOptionByAxisId(t);c(n)?"y"===t?i.axis_y_label=e:"y2"===t?i.axis_y2_label=e:"x"===t&&(i.axis_x_label=e):n&&(n.text=e)},r.getLabelPosition=function(t,e){var i=this.getLabelOptionByAxisId(t),n=i&&"object"===(void 0===i?"undefined":o(i))&&i.position?i.position:e;return{isInner:0<=n.indexOf("inner"),isOuter:0<=n.indexOf("outer"),isLeft:0<=n.indexOf("left"),isCenter:0<=n.indexOf("center"),isRight:0<=n.indexOf("right"),isTop:0<=n.indexOf("top"),isMiddle:0<=n.indexOf("middle"),isBottom:0<=n.indexOf("bottom")}},r.getXAxisLabelPosition=function(){return this.getLabelPosition("x",this.owner.config.axis_rotated?"inner-top":"inner-right")},r.getYAxisLabelPosition=function(){return this.getLabelPosition("y",this.owner.config.axis_rotated?"inner-right":"inner-top")},r.getY2AxisLabelPosition=function(){return this.getLabelPosition("y2",this.owner.config.axis_rotated?"inner-right":"inner-top")},r.getLabelPositionById=function(t){return"y2"===t?this.getY2AxisLabelPosition():"y"===t?this.getYAxisLabelPosition():this.getXAxisLabelPosition()},r.textForXAxisLabel=function(){return this.getLabelText("x")},r.textForYAxisLabel=function(){return this.getLabelText("y")},r.textForY2AxisLabel=function(){return this.getLabelText("y2")},r.xForAxisLabel=function(t,e){var i=this.owner;return t?e.isLeft?0:e.isCenter?i.width/2:i.width:e.isBottom?-i.height:e.isMiddle?-i.height/2:0},r.dxForAxisLabel=function(t,e){return t?e.isLeft?"0.5em":e.isRight?"-0.5em":"0":e.isTop?"-0.5em":e.isBottom?"0.5em":"0"},r.textAnchorForAxisLabel=function(t,e){return t?e.isLeft?"start":e.isCenter?"middle":"end":e.isBottom?"start":e.isMiddle?"middle":"end"},r.xForXAxisLabel=function(){return this.xForAxisLabel(!this.owner.config.axis_rotated,this.getXAxisLabelPosition())},r.xForYAxisLabel=function(){return this.xForAxisLabel(this.owner.config.axis_rotated,this.getYAxisLabelPosition())},r.xForY2AxisLabel=function(){return this.xForAxisLabel(this.owner.config.axis_rotated,this.getY2AxisLabelPosition())},r.dxForXAxisLabel=function(){return this.dxForAxisLabel(!this.owner.config.axis_rotated,this.getXAxisLabelPosition())},r.dxForYAxisLabel=function(){return this.dxForAxisLabel(this.owner.config.axis_rotated,this.getYAxisLabelPosition())},r.dxForY2AxisLabel=function(){return this.dxForAxisLabel(this.owner.config.axis_rotated,this.getY2AxisLabelPosition())},r.dyForXAxisLabel=function(){var t=this.owner,e=t.config,i=this.getXAxisLabelPosition();return e.axis_rotated?i.isInner?"1.2em":-25-(t.config.axis_x_inner?0:this.getMaxTickWidth("x")):i.isInner?"-0.5em":e.axis_x_height?e.axis_x_height-10:"3em"},r.dyForYAxisLabel=function(){var t=this.owner,e=this.getYAxisLabelPosition();return t.config.axis_rotated?e.isInner?"-0.5em":"3em":e.isInner?"1.2em":-10-(t.config.axis_y_inner?0:this.getMaxTickWidth("y")+10)},r.dyForY2AxisLabel=function(){var t=this.owner,e=this.getY2AxisLabelPosition();return t.config.axis_rotated?e.isInner?"1.2em":"-2.2em":e.isInner?"-0.5em":15+(t.config.axis_y2_inner?0:this.getMaxTickWidth("y2")+15)},r.textAnchorForXAxisLabel=function(){var t=this.owner;return this.textAnchorForAxisLabel(!t.config.axis_rotated,this.getXAxisLabelPosition())},r.textAnchorForYAxisLabel=function(){var t=this.owner;return this.textAnchorForAxisLabel(t.config.axis_rotated,this.getYAxisLabelPosition())},r.textAnchorForY2AxisLabel=function(){var t=this.owner;return this.textAnchorForAxisLabel(t.config.axis_rotated,this.getY2AxisLabelPosition())},r.getMaxTickWidth=function(t,e){var i,n,a,r,s=this.owner,o=s.config,c=0;return e&&s.currentMaxTickWidths[t]||(s.svg&&(i=s.filterTargetsToShow(s.data.targets),"y"===t?(n=s.y.copy().domain(s.getYDomain(i,"y")),a=this.getYAxis(n,s.yOrient,o.axis_y_tick_format,s.yAxisTickValues,!1,!0,!0)):"y2"===t?(n=s.y2.copy().domain(s.getYDomain(i,"y2")),a=this.getYAxis(n,s.y2Orient,o.axis_y2_tick_format,s.y2AxisTickValues,!1,!0,!0)):(n=s.x.copy().domain(s.getXDomain(i)),a=this.getXAxis(n,s.xOrient,s.xAxisTickFormat,s.xAxisTickValues,!1,!0,!0),this.updateXAxisTickValues(i,a)),(r=s.d3.select("body").append("div").classed("c3",!0)).append("svg").style("visibility","hidden").style("position","fixed").style("top",0).style("left",0).append("g").call(a).each(function(){s.d3.select(this).selectAll("text").each(function(){var t=this.getBoundingClientRect();c<t.width&&(c=t.width)}),r.remove()})),s.currentMaxTickWidths[t]=c<=0?s.currentMaxTickWidths[t]:c),s.currentMaxTickWidths[t]},r.updateLabels=function(t){var e=this.owner,i=e.main.select("."+Y.axisX+" ."+Y.axisXLabel),n=e.main.select("."+Y.axisY+" ."+Y.axisYLabel),a=e.main.select("."+Y.axisY2+" ."+Y.axisY2Label);(t?i.transition():i).attr("x",this.xForXAxisLabel.bind(this)).attr("dx",this.dxForXAxisLabel.bind(this)).attr("dy",this.dyForXAxisLabel.bind(this)).text(this.textForXAxisLabel.bind(this)),(t?n.transition():n).attr("x",this.xForYAxisLabel.bind(this)).attr("dx",this.dxForYAxisLabel.bind(this)).attr("dy",this.dyForYAxisLabel.bind(this)).text(this.textForYAxisLabel.bind(this)),(t?a.transition():a).attr("x",this.xForY2AxisLabel.bind(this)).attr("dx",this.dxForY2AxisLabel.bind(this)).attr("dy",this.dyForY2AxisLabel.bind(this)).text(this.textForY2AxisLabel.bind(this))},r.getPadding=function(t,e,i,n){var a="number"==typeof t?t:t[e];return P(a)?"ratio"===t.unit?t[e]*n:this.convertPixelsToAxisPadding(a,n):i},r.convertPixelsToAxisPadding=function(t,e){var i=this.owner;return e*(t/(i.config.axis_rotated?i.width:i.height))},r.generateTickValues=function(t,e,i){var n,a,r,s,o,c,d,u=t;if(e)if(1===(n=h(e)?e():e))u=[t[0]];else if(2===n)u=[t[0],t[t.length-1]];else if(2<n){for(s=n-2,a=t[0],o=((r=t[t.length-1])-a)/(s+1),u=[a],c=0;c<s;c++)d=+a+o*(c+1),u.push(i?new Date(d):d);u.push(r)}return i||(u=u.sort(function(t,e){return t-e})),u},r.generateTransitions=function(t){var e=this.owner.axes;return{axisX:t?e.x.transition().duration(t):e.x,axisY:t?e.y.transition().duration(t):e.y,axisY2:t?e.y2.transition().duration(t):e.y2,axisSubX:t?e.subx.transition().duration(t):e.subx}},r.redraw=function(t,e){var i=this.owner,n=t?i.d3.transition().duration(t):null;i.axes.x.style("opacity",e?0:1).call(i.xAxis,n),i.axes.y.style("opacity",e?0:1).call(i.yAxis,n),i.axes.y2.style("opacity",e?0:1).call(i.y2Axis,n),i.axes.subx.style("opacity",e?0:1).call(i.subXAxis,n)},x=n.chart.fn,(m=n.chart.internal.fn).beforeInit=function(){},m.afterInit=function(){},m.init=function(){var t=this,e=t.config;if(t.initParams(),e.data_url)t.convertUrlToData(e.data_url,e.data_mimeType,e.data_headers,e.data_keys,t.initWithData);else if(e.data_json)t.initWithData(t.convertJsonToData(e.data_json,e.data_keys));else if(e.data_rows)t.initWithData(t.convertRowsToData(e.data_rows));else{if(!e.data_columns)throw Error("url or json or rows or columns is required.");t.initWithData(t.convertColumnsToData(e.data_columns))}},m.initParams=function(){var t=this,e=t.d3,i=t.config;t.clipId="c3-"+ +new Date+"-clip",t.clipIdForXAxis=t.clipId+"-xaxis",t.clipIdForYAxis=t.clipId+"-yaxis",t.clipIdForGrid=t.clipId+"-grid",t.clipIdForSubchart=t.clipId+"-subchart",t.clipPath=t.getClipPath(t.clipId),t.clipPathForXAxis=t.getClipPath(t.clipIdForXAxis),t.clipPathForYAxis=t.getClipPath(t.clipIdForYAxis),t.clipPathForGrid=t.getClipPath(t.clipIdForGrid),t.clipPathForSubchart=t.getClipPath(t.clipIdForSubchart),t.dragStart=null,t.dragging=!1,t.flowing=!1,t.cancelClick=!1,t.mouseover=!1,t.transiting=!1,t.color=t.generateColor(),t.levelColor=t.generateLevelColor(),t.dataTimeParse=(i.data_xLocaltime?e.timeParse:e.utcParse)(t.config.data_xFormat),t.axisTimeFormat=i.axis_x_localtime?e.timeFormat:e.utcFormat,t.defaultAxisTimeFormat=function(t){return t.getMilliseconds()?e.timeFormat(".%L")(t):t.getSeconds()?e.timeFormat(":%S")(t):t.getMinutes()?e.timeFormat("%I:%M")(t):t.getHours()?e.timeFormat("%I %p")(t):t.getDay()&&1!==t.getDate()?e.timeFormat("%-m/%-d")(t):1!==t.getDate()?e.timeFormat("%-m/%-d")(t):t.getMonth()?e.timeFormat("%-m/%-d")(t):e.timeFormat("%Y/%-m/%-d")(t)},t.hiddenTargetIds=[],t.hiddenLegendIds=[],t.focusedTargetIds=[],t.defocusedTargetIds=[],t.xOrient=i.axis_rotated?i.axis_x_inner?"right":"left":i.axis_x_inner?"top":"bottom",t.yOrient=i.axis_rotated?i.axis_y_inner?"top":"bottom":i.axis_y_inner?"right":"left",t.y2Orient=i.axis_rotated?i.axis_y2_inner?"bottom":"top":i.axis_y2_inner?"left":"right",t.subXOrient=i.axis_rotated?"left":"bottom",t.isLegendRight="right"===i.legend_position,t.isLegendInset="inset"===i.legend_position,t.isLegendTop="top-left"===i.legend_inset_anchor||"top-right"===i.legend_inset_anchor,t.isLegendLeft="top-left"===i.legend_inset_anchor||"bottom-left"===i.legend_inset_anchor,t.legendStep=0,t.legendItemWidth=0,t.legendItemHeight=0,t.currentMaxTickWidths={x:0,y:0,y2:0},t.rotated_padding_left=30,t.rotated_padding_right=i.axis_rotated&&!i.axis_x_show?0:30,t.rotated_padding_top=5,t.withoutFadeIn={},t.intervalForObserveInserted=void 0,t.axes.subx=e.selectAll([])},m.initChartElements=function(){this.initBar&&this.initBar(),this.initLine&&this.initLine(),this.initArc&&this.initArc(),this.initGauge&&this.initGauge(),this.initText&&this.initText()},m.initWithData=function(t){var e,i,n=this,a=n.d3,r=n.config,s=!0;n.axis=new S(n),r.bindto?"function"==typeof r.bindto.node?n.selectChart=r.bindto:n.selectChart=a.select(r.bindto):n.selectChart=a.selectAll([]),n.selectChart.empty()&&(n.selectChart=a.select(document.createElement("div")).style("opacity",0),n.observeInserted(n.selectChart),s=!1),n.selectChart.html("").classed("c3",!0),n.data.xs={},n.data.targets=n.convertDataToTargets(t),r.data_filter&&(n.data.targets=n.data.targets.filter(r.data_filter)),r.data_hide&&n.addHiddenTargetIds(!0===r.data_hide?n.mapToIds(n.data.targets):r.data_hide),r.legend_hide&&n.addHiddenLegendIds(!0===r.legend_hide?n.mapToIds(n.data.targets):r.legend_hide),n.updateSizes(),n.updateScales(),n.x.domain(a.extent(n.getXDomain(n.data.targets))),n.y.domain(n.getYDomain(n.data.targets,"y")),n.y2.domain(n.getYDomain(n.data.targets,"y2")),n.subX.domain(n.x.domain()),n.subY.domain(n.y.domain()),n.subY2.domain(n.y2.domain()),n.orgXDomain=n.x.domain(),n.svg=n.selectChart.append("svg").style("overflow","hidden").on("mouseenter",function(){return r.onmouseover.call(n)}).on("mouseleave",function(){return r.onmouseout.call(n)}),n.config.svg_classname&&n.svg.attr("class",n.config.svg_classname),e=n.svg.append("defs"),n.clipChart=n.appendClip(e,n.clipId),n.clipXAxis=n.appendClip(e,n.clipIdForXAxis),n.clipYAxis=n.appendClip(e,n.clipIdForYAxis),n.clipGrid=n.appendClip(e,n.clipIdForGrid),n.clipSubchart=n.appendClip(e,n.clipIdForSubchart),n.updateSvgSize(),i=n.main=n.svg.append("g").attr("transform",n.getTranslate("main")),n.initPie&&n.initPie(),n.initSubchart&&n.initSubchart(),n.initTooltip&&n.initTooltip(),n.initLegend&&n.initLegend(),n.initTitle&&n.initTitle(),n.initZoom&&n.initZoom(),n.initSubchartBrush&&n.initSubchartBrush(),i.append("text").attr("class",Y.text+" "+Y.empty).attr("text-anchor","middle").attr("dominant-baseline","middle"),n.initRegion(),n.initGrid(),i.append("g").attr("clip-path",n.clipPath).attr("class",Y.chart),r.grid_lines_front&&n.initGridLines(),n.initChartElements(),n.axis.init(),n.updateTargets(n.data.targets),n.initEventRect(),r.axis_x_selection&&n.brush.selectionAsValue(n.getDefaultSelection()),s&&(n.updateDimension(),n.config.oninit.call(n),n.redraw({withTransition:!1,withTransform:!0,withUpdateXDomain:!0,withUpdateOrgXDomain:!0,withTransitionForAxis:!1})),n.bindResize(),n.api.element=n.selectChart.node()},m.smoothLines=function(t,e){var r=this;"grid"===e&&t.each(function(){var t=r.d3.select(this),e=t.attr("x1"),i=t.attr("x2"),n=t.attr("y1"),a=t.attr("y2");t.attr({x1:Math.ceil(e),x2:Math.ceil(i),y1:Math.ceil(n),y2:Math.ceil(a)})})},m.updateSizes=function(){var t=this,e=t.config,i=t.legend?t.getLegendHeight():0,n=t.legend?t.getLegendWidth():0,a=t.isLegendRight||t.isLegendInset?0:i,r=t.hasArcType(),s=e.axis_rotated||r?0:t.getHorizontalAxisHeight("x"),o=e.subchart_show&&!r?e.subchart_size_height+s:0;t.currentWidth=t.getCurrentWidth(),t.currentHeight=t.getCurrentHeight(),t.margin=e.axis_rotated?{top:t.getHorizontalAxisHeight("y2")+t.getCurrentPaddingTop(),right:r?0:t.getCurrentPaddingRight(),bottom:t.getHorizontalAxisHeight("y")+a+t.getCurrentPaddingBottom(),left:o+(r?0:t.getCurrentPaddingLeft())}:{top:4+t.getCurrentPaddingTop(),right:r?0:t.getCurrentPaddingRight(),bottom:s+o+a+t.getCurrentPaddingBottom(),left:r?0:t.getCurrentPaddingLeft()},t.margin2=e.axis_rotated?{top:t.margin.top,right:NaN,bottom:20+a,left:t.rotated_padding_left}:{top:t.currentHeight-o-a,right:NaN,bottom:s+a,left:t.margin.left},t.margin3={top:0,right:NaN,bottom:0,left:0},t.updateSizeForLegend&&t.updateSizeForLegend(i,n),t.width=t.currentWidth-t.margin.left-t.margin.right,t.height=t.currentHeight-t.margin.top-t.margin.bottom,t.width<0&&(t.width=0),t.height<0&&(t.height=0),t.width2=e.axis_rotated?t.margin.left-t.rotated_padding_left-t.rotated_padding_right:t.width,t.height2=e.axis_rotated?t.height:t.currentHeight-t.margin2.top-t.margin2.bottom,t.width2<0&&(t.width2=0),t.height2<0&&(t.height2=0),t.arcWidth=t.width-(t.isLegendRight?n+10:0),t.arcHeight=t.height-(t.isLegendRight?0:10),t.hasType("gauge")&&!e.gauge_fullCircle&&(t.arcHeight+=t.height-t.getGaugeLabelHeight()),t.updateRadius&&t.updateRadius(),t.isLegendRight&&r&&(t.margin3.left=t.arcWidth/2+1.1*t.radiusExpanded)},m.updateTargets=function(t){var e=this;e.updateTargetsForText(t),e.updateTargetsForBar(t),e.updateTargetsForLine(t),e.hasArcType()&&e.updateTargetsForArc&&e.updateTargetsForArc(t),e.updateTargetsForSubchart&&e.updateTargetsForSubchart(t),e.showTargets()},m.showTargets=function(){var e=this;e.svg.selectAll("."+Y.target).filter(function(t){return e.isTargetToShow(t.id)}).transition().duration(e.config.transition_duration).style("opacity",1)},m.redraw=function(t,e){var i,n,a,r,s,o,c,d,u,l,h,g,f,p,_,x,y,m,S,w,v,b,A,T,P,C,L,V,G,E,O,I=this,R=I.main,k=I.d3,D=I.config,F=I.getShapeIndices(I.isAreaType),X=I.getShapeIndices(I.isBarType),M=I.getShapeIndices(I.isLineType),z=I.hasArcType(),H=I.filterTargetsToShow(I.data.targets),B=I.xv.bind(I);if(i=N(t=t||{},"withY",!0),n=N(t,"withSubchart",!0),a=N(t,"withTransition",!0),o=N(t,"withTransform",!1),c=N(t,"withUpdateXDomain",!1),d=N(t,"withUpdateOrgXDomain",!1),u=N(t,"withTrimXDomain",!0),f=N(t,"withUpdateXAxis",c),l=N(t,"withLegend",!1),h=N(t,"withEventRect",!0),g=N(t,"withDimension",!0),r=N(t,"withTransitionForExit",a),s=N(t,"withTransitionForAxis",a),S=a?D.transition_duration:0,w=r?S:0,v=s?S:0,e=e||I.axis.generateTransitions(v),l&&D.legend_show?I.updateLegend(I.mapToIds(I.data.targets),t,e):g&&I.updateDimension(!0),I.isCategorized()&&0===H.length&&I.x.domain([0,I.axes.x.selectAll(".tick").size()]),H.length?(I.updateXDomain(H,c,d,u),D.axis_x_tick_values||(C=I.axis.updateXAxisTickValues(H))):(I.xAxis.tickValues([]),I.subXAxis.tickValues([])),D.zoom_rescale&&!t.flow&&(G=I.x.orgDomain()),I.y.domain(I.getYDomain(H,"y",G)),I.y2.domain(I.getYDomain(H,"y2",G)),!D.axis_y_tick_values&&D.axis_y_tick_count&&I.yAxis.tickValues(I.axis.generateTickValues(I.y.domain(),D.axis_y_tick_count)),!D.axis_y2_tick_values&&D.axis_y2_tick_count&&I.y2Axis.tickValues(I.axis.generateTickValues(I.y2.domain(),D.axis_y2_tick_count)),I.axis.redraw(v,z),I.axis.updateLabels(a),(c||f)&&H.length)if(D.axis_x_tick_culling&&C){for(L=1;L<C.length;L++)if(C.length/L<D.axis_x_tick_culling_max){V=L;break}I.svg.selectAll("."+Y.axisX+" .tick text").each(function(t){var e=C.indexOf(t);0<=e&&k.select(this).style("display",e%V?"none":"block")})}else I.svg.selectAll("."+Y.axisX+" .tick text").style("display","block");p=I.generateDrawArea?I.generateDrawArea(F,!1):void 0,_=I.generateDrawBar?I.generateDrawBar(X):void 0,x=I.generateDrawLine?I.generateDrawLine(M,!1):void 0,y=I.generateXYForText(F,X,M,!0),m=I.generateXYForText(F,X,M,!1),I.updateCircleY(),E=(I.config.axis_rotated?I.circleY:I.circleX).bind(I),O=(I.config.axis_rotated?I.circleX:I.circleY).bind(I),i&&(I.subY.domain(I.getYDomain(H,"y")),I.subY2.domain(I.getYDomain(H,"y2"))),I.updateXgridFocus(),R.select("text."+Y.text+"."+Y.empty).attr("x",I.width/2).attr("y",I.height/2).text(D.data_empty_label_text).transition().style("opacity",H.length?0:1),h&&I.redrawEventRect(),I.updateGrid(S),I.updateRegion(S),I.updateBar(w),I.updateLine(w),I.updateArea(w),I.updateCircle(E,O),I.hasDataLabel()&&I.updateText(y,m,w),I.redrawTitle&&I.redrawTitle(),I.redrawArc&&I.redrawArc(S,w,o),I.redrawSubchart&&I.redrawSubchart(n,e,S,w,F,X,M),R.selectAll("."+Y.selectedCircles).filter(I.isBarType.bind(I)).selectAll("circle").remove(),t.flow&&(T=I.generateFlow({targets:H,flow:t.flow,duration:t.flow.duration,drawBar:_,drawLine:x,drawArea:p,cx:E,cy:O,xv:B,xForText:y,yForText:m})),I.isTabVisible()&&(S?(P=k.transition().duration(S),b=[],[I.redrawBar(_,!0,P),I.redrawLine(x,!0,P),I.redrawArea(p,!0,P),I.redrawCircle(E,O,!0,P),I.redrawText(y,m,t.flow,!0,P),I.redrawRegion(!0,P),I.redrawGrid(!0,P)].forEach(function(t){t.forEach(function(t){b.push(t)})}),A=I.generateWait(),b.forEach(function(t){A.add(t)}),A(function(){T&&T(),D.onrendered&&D.onrendered.call(I)})):(I.redrawBar(_),I.redrawLine(x),I.redrawArea(p),I.redrawCircle(E,O),I.redrawText(y,m,t.flow),I.redrawRegion(),I.redrawGrid(),T&&T(),D.onrendered&&D.onrendered.call(I))),I.mapToIds(I.data.targets).forEach(function(t){I.withoutFadeIn[t]=!0})},m.updateAndRedraw=function(t){var e,i=this,n=i.config;(t=t||{}).withTransition=N(t,"withTransition",!0),t.withTransform=N(t,"withTransform",!1),t.withLegend=N(t,"withLegend",!1),t.withUpdateXDomain=N(t,"withUpdateXDomain",!0),t.withUpdateOrgXDomain=N(t,"withUpdateOrgXDomain",!0),t.withTransitionForExit=!1,t.withTransitionForTransform=N(t,"withTransitionForTransform",t.withTransition),i.updateSizes(),t.withLegend&&n.legend_show||(e=i.axis.generateTransitions(t.withTransitionForAxis?n.transition_duration:0),i.updateScales(),i.updateSvgSize(),i.transformAll(t.withTransitionForTransform,e)),i.redraw(t,e)},m.redrawWithoutRescale=function(){this.redraw({withY:!1,withSubchart:!1,withEventRect:!1,withTransitionForAxis:!1})},m.isTimeSeries=function(){return"timeseries"===this.config.axis_x_type},m.isCategorized=function(){return 0<=this.config.axis_x_type.indexOf("categor")},m.isCustomX=function(){var t=this.config;return!this.isTimeSeries()&&(t.data_x||C(t.data_xs))},m.isTimeSeriesY=function(){return"timeseries"===this.config.axis_y_type},m.getTranslate=function(t){var e,i,n=this,a=n.config;return"main"===t?(e=l(n.margin.left),i=l(n.margin.top)):"context"===t?(e=l(n.margin2.left),i=l(n.margin2.top)):"legend"===t?(e=n.margin3.left,i=n.margin3.top):"x"===t?(e=0,i=a.axis_rotated?0:n.height):"y"===t?(e=0,i=a.axis_rotated?n.height:0):"y2"===t?(e=a.axis_rotated?0:n.width,i=a.axis_rotated?1:0):"subx"===t?(e=0,i=a.axis_rotated?0:n.height2):"arc"===t&&(e=n.arcWidth/2,i=n.arcHeight/2-(n.hasType("gauge")?6:0)),"translate("+e+","+i+")"},m.initialOpacity=function(t){return null!==t.value&&this.withoutFadeIn[t.id]?1:0},m.initialOpacityForCircle=function(t){return null!==t.value&&this.withoutFadeIn[t.id]?this.opacityForCircle(t):0},m.opacityForCircle=function(t){var e=(h(this.config.point_show)?this.config.point_show(t):this.config.point_show)?1:0;return P(t.value)?this.isScatterType(t)?.5:e:0},m.opacityForText=function(){return this.hasDataLabel()?1:0},m.xx=function(t){return t?this.x(t.x):null},m.xv=function(t){var e=this,i=t.value;return e.isTimeSeries()?i=e.parseDate(t.value):e.isCategorized()&&"string"==typeof t.value&&(i=e.config.axis_x_categories.indexOf(t.value)),Math.ceil(e.x(i))},m.yv=function(t){var e=t.axis&&"y2"===t.axis?this.y2:this.y;return Math.ceil(e(t.value))},m.subxx=function(t){return t?this.subX(t.x):null},m.transformMain=function(t,e){var i,n,a,r=this;e&&e.axisX?i=e.axisX:(i=r.main.select("."+Y.axisX),t&&(i=i.transition())),e&&e.axisY?n=e.axisY:(n=r.main.select("."+Y.axisY),t&&(n=n.transition())),e&&e.axisY2?a=e.axisY2:(a=r.main.select("."+Y.axisY2),t&&(a=a.transition())),(t?r.main.transition():r.main).attr("transform",r.getTranslate("main")),i.attr("transform",r.getTranslate("x")),n.attr("transform",r.getTranslate("y")),a.attr("transform",r.getTranslate("y2")),r.main.select("."+Y.chartArcs).attr("transform",r.getTranslate("arc"))},m.transformAll=function(t,e){var i=this;i.transformMain(t,e),i.config.subchart_show&&i.transformContext(t,e),i.legend&&i.transformLegend(t)},m.updateSvgSize=function(){var t=this,e=t.svg.select(".c3-brush .overlay");t.svg.attr("width",t.currentWidth).attr("height",t.currentHeight),t.svg.selectAll(["#"+t.clipId,"#"+t.clipIdForGrid]).select("rect").attr("width",t.width).attr("height",t.height),t.svg.select("#"+t.clipIdForXAxis).select("rect").attr("x",t.getXAxisClipX.bind(t)).attr("y",t.getXAxisClipY.bind(t)).attr("width",t.getXAxisClipWidth.bind(t)).attr("height",t.getXAxisClipHeight.bind(t)),t.svg.select("#"+t.clipIdForYAxis).select("rect").attr("x",t.getYAxisClipX.bind(t)).attr("y",t.getYAxisClipY.bind(t)).attr("width",t.getYAxisClipWidth.bind(t)).attr("height",t.getYAxisClipHeight.bind(t)),t.svg.select("#"+t.clipIdForSubchart).select("rect").attr("width",t.width).attr("height",e.size()?e.attr("height"):0),t.selectChart.style("max-height",t.currentHeight+"px")},m.updateDimension=function(t){var e=this;t||(e.config.axis_rotated?(e.axes.x.call(e.xAxis),e.axes.subx.call(e.subXAxis)):(e.axes.y.call(e.yAxis),e.axes.y2.call(e.y2Axis))),e.updateSizes(),e.updateScales(),e.updateSvgSize(),e.transformAll(!1)},m.observeInserted=function(e){var i,n=this;"undefined"!=typeof MutationObserver?(i=new MutationObserver(function(t){t.forEach(function(t){"childList"===t.type&&t.previousSibling&&(i.disconnect(),n.intervalForObserveInserted=window.setInterval(function(){e.node().parentNode&&(window.clearInterval(n.intervalForObserveInserted),n.updateDimension(),n.brush&&n.brush.update(),n.config.oninit.call(n),n.redraw({withTransform:!0,withUpdateXDomain:!0,withUpdateOrgXDomain:!0,withTransition:!1,withTransitionForTransform:!1,withLegend:!0}),e.transition().style("opacity",1))},10))})})).observe(e.node(),{attributes:!0,childList:!0,characterData:!0}):window.console.error("MutationObserver not defined.")},m.bindResize=function(){var t=this,e=t.config;if(t.resizeFunction=t.generateResize(),t.resizeFunction.add(function(){e.onresize.call(t)}),e.resize_auto&&t.resizeFunction.add(function(){void 0!==t.resizeTimeout&&window.clearTimeout(t.resizeTimeout),t.resizeTimeout=window.setTimeout(function(){delete t.resizeTimeout,t.updateAndRedraw({withUpdateXDomain:!1,withUpdateOrgXDomain:!1,withTransition:!1,withTransitionForTransform:!1,withLegend:!0}),t.brush&&t.brush.update()},100)}),t.resizeFunction.add(function(){e.onresized.call(t)}),t.resizeIfElementDisplayed=function(){null!=t.api&&t.api.element.offsetParent&&t.resizeFunction()},window.attachEvent)window.attachEvent("onresize",t.resizeIfElementDisplayed);else if(window.addEventListener)window.addEventListener("resize",t.resizeIfElementDisplayed,!1);else{var i=window.onresize;i?i.add&&i.remove||(i=t.generateResize()).add(window.onresize):i=t.generateResize(),i.add(t.resizeFunction),window.onresize=function(){t.api.element.offsetParent&&i()}}},m.generateResize=function(){var i=[];function t(){i.forEach(function(t){t()})}return t.add=function(t){i.push(t)},t.remove=function(t){for(var e=0;e<i.length;e++)if(i[e]===t){i.splice(e,1);break}},t},m.endall=function(t,e){var i=0;t.each(function(){++i}).on("end",function(){--i||e.apply(this,arguments)})},m.generateWait=function(){var n=[],t=function(t){var i=setInterval(function(){var e=0;n.forEach(function(t){if(t.empty())e+=1;else try{t.transition()}catch(t){e+=1}}),e===n.length&&(clearInterval(i),t&&t())},50)};return t.add=function(t){n.push(t)},t},m.parseDate=function(t){var e;return t instanceof Date?e=t:"string"==typeof t?e=this.dataTimeParse(t):"object"===(void 0===t?"undefined":o(t))?e=new Date(+t):"number"!=typeof t||isNaN(t)||(e=new Date(+t)),e&&!isNaN(+e)||window.console.error("Failed to parse x '"+t+"' to Date object"),e},m.isTabVisible=function(){var t;return void 0!==document.hidden?t="hidden":void 0!==document.mozHidden?t="mozHidden":void 0!==document.msHidden?t="msHidden":void 0!==document.webkitHidden&&(t="webkitHidden"),!document[t]},m.isValue=P,m.isFunction=h,m.isString=c,m.isUndefined=v,m.isDefined=k,m.ceil10=d,m.asHalfPixel=l,m.diffDomain=D,m.isEmpty=g,m.notEmpty=C,m.notEmpty=C,m.getOption=N,m.hasValue=f,m.sanitise=_,m.getPathBox=y,m.CLASS=Y,Function.prototype.bind||(Function.prototype.bind=function(t){if("function"!=typeof this)throw new TypeError("Function.prototype.bind - what is trying to be bound is not callable");var e=Array.prototype.slice.call(arguments,1),i=this,n=function(){},a=function(){return i.apply(this instanceof n?this:t,e.concat(Array.prototype.slice.call(arguments)))};return n.prototype=this.prototype,a.prototype=new n,a}),"SVGPathSeg"in window||(window.SVGPathSeg=function(t,e,i){this.pathSegType=t,this.pathSegTypeAsLetter=e,this._owningPathSegList=i},window.SVGPathSeg.prototype.classname="SVGPathSeg",window.SVGPathSeg.PATHSEG_UNKNOWN=0,window.SVGPathSeg.PATHSEG_CLOSEPATH=1,window.SVGPathSeg.PATHSEG_MOVETO_ABS=2,window.SVGPathSeg.PATHSEG_MOVETO_REL=3,window.SVGPathSeg.PATHSEG_LINETO_ABS=4,window.SVGPathSeg.PATHSEG_LINETO_REL=5,window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_ABS=6,window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_REL=7,window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_ABS=8,window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_REL=9,window.SVGPathSeg.PATHSEG_ARC_ABS=10,window.SVGPathSeg.PATHSEG_ARC_REL=11,window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_ABS=12,window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_REL=13,window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_ABS=14,window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_REL=15,window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_ABS=16,window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_REL=17,window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_ABS=18,window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_REL=19,window.SVGPathSeg.prototype._segmentChanged=function(){this._owningPathSegList&&this._owningPathSegList.segmentChanged(this)},window.SVGPathSegClosePath=function(t){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_CLOSEPATH,"z",t)},window.SVGPathSegClosePath.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegClosePath.prototype.toString=function(){return"[object SVGPathSegClosePath]"},window.SVGPathSegClosePath.prototype._asPathString=function(){return this.pathSegTypeAsLetter},window.SVGPathSegClosePath.prototype.clone=function(){return new window.SVGPathSegClosePath(void 0)},window.SVGPathSegMovetoAbs=function(t,e,i){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_MOVETO_ABS,"M",t),this._x=e,this._y=i},window.SVGPathSegMovetoAbs.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegMovetoAbs.prototype.toString=function(){return"[object SVGPathSegMovetoAbs]"},window.SVGPathSegMovetoAbs.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._x+" "+this._y},window.SVGPathSegMovetoAbs.prototype.clone=function(){return new window.SVGPathSegMovetoAbs(void 0,this._x,this._y)},Object.defineProperty(window.SVGPathSegMovetoAbs.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegMovetoAbs.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegMovetoRel=function(t,e,i){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_MOVETO_REL,"m",t),this._x=e,this._y=i},window.SVGPathSegMovetoRel.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegMovetoRel.prototype.toString=function(){return"[object SVGPathSegMovetoRel]"},window.SVGPathSegMovetoRel.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._x+" "+this._y},window.SVGPathSegMovetoRel.prototype.clone=function(){return new window.SVGPathSegMovetoRel(void 0,this._x,this._y)},Object.defineProperty(window.SVGPathSegMovetoRel.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegMovetoRel.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegLinetoAbs=function(t,e,i){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_LINETO_ABS,"L",t),this._x=e,this._y=i},window.SVGPathSegLinetoAbs.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegLinetoAbs.prototype.toString=function(){return"[object SVGPathSegLinetoAbs]"},window.SVGPathSegLinetoAbs.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._x+" "+this._y},window.SVGPathSegLinetoAbs.prototype.clone=function(){return new window.SVGPathSegLinetoAbs(void 0,this._x,this._y)},Object.defineProperty(window.SVGPathSegLinetoAbs.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegLinetoAbs.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegLinetoRel=function(t,e,i){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_LINETO_REL,"l",t),this._x=e,this._y=i},window.SVGPathSegLinetoRel.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegLinetoRel.prototype.toString=function(){return"[object SVGPathSegLinetoRel]"},window.SVGPathSegLinetoRel.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._x+" "+this._y},window.SVGPathSegLinetoRel.prototype.clone=function(){return new window.SVGPathSegLinetoRel(void 0,this._x,this._y)},Object.defineProperty(window.SVGPathSegLinetoRel.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegLinetoRel.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegCurvetoCubicAbs=function(t,e,i,n,a,r,s){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_ABS,"C",t),this._x=e,this._y=i,this._x1=n,this._y1=a,this._x2=r,this._y2=s},window.SVGPathSegCurvetoCubicAbs.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegCurvetoCubicAbs.prototype.toString=function(){return"[object SVGPathSegCurvetoCubicAbs]"},window.SVGPathSegCurvetoCubicAbs.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._x1+" "+this._y1+" "+this._x2+" "+this._y2+" "+this._x+" "+this._y},window.SVGPathSegCurvetoCubicAbs.prototype.clone=function(){return new window.SVGPathSegCurvetoCubicAbs(void 0,this._x,this._y,this._x1,this._y1,this._x2,this._y2)},Object.defineProperty(window.SVGPathSegCurvetoCubicAbs.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicAbs.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicAbs.prototype,"x1",{get:function(){return this._x1},set:function(t){this._x1=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicAbs.prototype,"y1",{get:function(){return this._y1},set:function(t){this._y1=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicAbs.prototype,"x2",{get:function(){return this._x2},set:function(t){this._x2=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicAbs.prototype,"y2",{get:function(){return this._y2},set:function(t){this._y2=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegCurvetoCubicRel=function(t,e,i,n,a,r,s){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_REL,"c",t),this._x=e,this._y=i,this._x1=n,this._y1=a,this._x2=r,this._y2=s},window.SVGPathSegCurvetoCubicRel.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegCurvetoCubicRel.prototype.toString=function(){return"[object SVGPathSegCurvetoCubicRel]"},window.SVGPathSegCurvetoCubicRel.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._x1+" "+this._y1+" "+this._x2+" "+this._y2+" "+this._x+" "+this._y},window.SVGPathSegCurvetoCubicRel.prototype.clone=function(){return new window.SVGPathSegCurvetoCubicRel(void 0,this._x,this._y,this._x1,this._y1,this._x2,this._y2)},Object.defineProperty(window.SVGPathSegCurvetoCubicRel.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicRel.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicRel.prototype,"x1",{get:function(){return this._x1},set:function(t){this._x1=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicRel.prototype,"y1",{get:function(){return this._y1},set:function(t){this._y1=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicRel.prototype,"x2",{get:function(){return this._x2},set:function(t){this._x2=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicRel.prototype,"y2",{get:function(){return this._y2},set:function(t){this._y2=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegCurvetoQuadraticAbs=function(t,e,i,n,a){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_ABS,"Q",t),this._x=e,this._y=i,this._x1=n,this._y1=a},window.SVGPathSegCurvetoQuadraticAbs.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegCurvetoQuadraticAbs.prototype.toString=function(){return"[object SVGPathSegCurvetoQuadraticAbs]"},window.SVGPathSegCurvetoQuadraticAbs.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._x1+" "+this._y1+" "+this._x+" "+this._y},window.SVGPathSegCurvetoQuadraticAbs.prototype.clone=function(){return new window.SVGPathSegCurvetoQuadraticAbs(void 0,this._x,this._y,this._x1,this._y1)},Object.defineProperty(window.SVGPathSegCurvetoQuadraticAbs.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoQuadraticAbs.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoQuadraticAbs.prototype,"x1",{get:function(){return this._x1},set:function(t){this._x1=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoQuadraticAbs.prototype,"y1",{get:function(){return this._y1},set:function(t){this._y1=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegCurvetoQuadraticRel=function(t,e,i,n,a){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_REL,"q",t),this._x=e,this._y=i,this._x1=n,this._y1=a},window.SVGPathSegCurvetoQuadraticRel.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegCurvetoQuadraticRel.prototype.toString=function(){return"[object SVGPathSegCurvetoQuadraticRel]"},window.SVGPathSegCurvetoQuadraticRel.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._x1+" "+this._y1+" "+this._x+" "+this._y},window.SVGPathSegCurvetoQuadraticRel.prototype.clone=function(){return new window.SVGPathSegCurvetoQuadraticRel(void 0,this._x,this._y,this._x1,this._y1)},Object.defineProperty(window.SVGPathSegCurvetoQuadraticRel.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoQuadraticRel.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoQuadraticRel.prototype,"x1",{get:function(){return this._x1},set:function(t){this._x1=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoQuadraticRel.prototype,"y1",{get:function(){return this._y1},set:function(t){this._y1=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegArcAbs=function(t,e,i,n,a,r,s,o){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_ARC_ABS,"A",t),this._x=e,this._y=i,this._r1=n,this._r2=a,this._angle=r,this._largeArcFlag=s,this._sweepFlag=o},window.SVGPathSegArcAbs.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegArcAbs.prototype.toString=function(){return"[object SVGPathSegArcAbs]"},window.SVGPathSegArcAbs.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._r1+" "+this._r2+" "+this._angle+" "+(this._largeArcFlag?"1":"0")+" "+(this._sweepFlag?"1":"0")+" "+this._x+" "+this._y},window.SVGPathSegArcAbs.prototype.clone=function(){return new window.SVGPathSegArcAbs(void 0,this._x,this._y,this._r1,this._r2,this._angle,this._largeArcFlag,this._sweepFlag)},Object.defineProperty(window.SVGPathSegArcAbs.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegArcAbs.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegArcAbs.prototype,"r1",{get:function(){return this._r1},set:function(t){this._r1=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegArcAbs.prototype,"r2",{get:function(){return this._r2},set:function(t){this._r2=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegArcAbs.prototype,"angle",{get:function(){return this._angle},set:function(t){this._angle=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegArcAbs.prototype,"largeArcFlag",{get:function(){return this._largeArcFlag},set:function(t){this._largeArcFlag=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegArcAbs.prototype,"sweepFlag",{get:function(){return this._sweepFlag},set:function(t){this._sweepFlag=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegArcRel=function(t,e,i,n,a,r,s,o){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_ARC_REL,"a",t),this._x=e,this._y=i,this._r1=n,this._r2=a,this._angle=r,this._largeArcFlag=s,this._sweepFlag=o},window.SVGPathSegArcRel.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegArcRel.prototype.toString=function(){return"[object SVGPathSegArcRel]"},window.SVGPathSegArcRel.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._r1+" "+this._r2+" "+this._angle+" "+(this._largeArcFlag?"1":"0")+" "+(this._sweepFlag?"1":"0")+" "+this._x+" "+this._y},window.SVGPathSegArcRel.prototype.clone=function(){return new window.SVGPathSegArcRel(void 0,this._x,this._y,this._r1,this._r2,this._angle,this._largeArcFlag,this._sweepFlag)},Object.defineProperty(window.SVGPathSegArcRel.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegArcRel.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegArcRel.prototype,"r1",{get:function(){return this._r1},set:function(t){this._r1=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegArcRel.prototype,"r2",{get:function(){return this._r2},set:function(t){this._r2=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegArcRel.prototype,"angle",{get:function(){return this._angle},set:function(t){this._angle=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegArcRel.prototype,"largeArcFlag",{get:function(){return this._largeArcFlag},set:function(t){this._largeArcFlag=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegArcRel.prototype,"sweepFlag",{get:function(){return this._sweepFlag},set:function(t){this._sweepFlag=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegLinetoHorizontalAbs=function(t,e){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_ABS,"H",t),this._x=e},window.SVGPathSegLinetoHorizontalAbs.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegLinetoHorizontalAbs.prototype.toString=function(){return"[object SVGPathSegLinetoHorizontalAbs]"},window.SVGPathSegLinetoHorizontalAbs.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._x},window.SVGPathSegLinetoHorizontalAbs.prototype.clone=function(){return new window.SVGPathSegLinetoHorizontalAbs(void 0,this._x)},Object.defineProperty(window.SVGPathSegLinetoHorizontalAbs.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegLinetoHorizontalRel=function(t,e){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_REL,"h",t),this._x=e},window.SVGPathSegLinetoHorizontalRel.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegLinetoHorizontalRel.prototype.toString=function(){return"[object SVGPathSegLinetoHorizontalRel]"},window.SVGPathSegLinetoHorizontalRel.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._x},window.SVGPathSegLinetoHorizontalRel.prototype.clone=function(){return new window.SVGPathSegLinetoHorizontalRel(void 0,this._x)},Object.defineProperty(window.SVGPathSegLinetoHorizontalRel.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegLinetoVerticalAbs=function(t,e){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_ABS,"V",t),this._y=e},window.SVGPathSegLinetoVerticalAbs.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegLinetoVerticalAbs.prototype.toString=function(){return"[object SVGPathSegLinetoVerticalAbs]"},window.SVGPathSegLinetoVerticalAbs.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._y},window.SVGPathSegLinetoVerticalAbs.prototype.clone=function(){return new window.SVGPathSegLinetoVerticalAbs(void 0,this._y)},Object.defineProperty(window.SVGPathSegLinetoVerticalAbs.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegLinetoVerticalRel=function(t,e){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_REL,"v",t),this._y=e},window.SVGPathSegLinetoVerticalRel.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegLinetoVerticalRel.prototype.toString=function(){return"[object SVGPathSegLinetoVerticalRel]"},window.SVGPathSegLinetoVerticalRel.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._y},window.SVGPathSegLinetoVerticalRel.prototype.clone=function(){return new window.SVGPathSegLinetoVerticalRel(void 0,this._y)},Object.defineProperty(window.SVGPathSegLinetoVerticalRel.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegCurvetoCubicSmoothAbs=function(t,e,i,n,a){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_ABS,"S",t),this._x=e,this._y=i,this._x2=n,this._y2=a},window.SVGPathSegCurvetoCubicSmoothAbs.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegCurvetoCubicSmoothAbs.prototype.toString=function(){return"[object SVGPathSegCurvetoCubicSmoothAbs]"},window.SVGPathSegCurvetoCubicSmoothAbs.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._x2+" "+this._y2+" "+this._x+" "+this._y},window.SVGPathSegCurvetoCubicSmoothAbs.prototype.clone=function(){return new window.SVGPathSegCurvetoCubicSmoothAbs(void 0,this._x,this._y,this._x2,this._y2)},Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothAbs.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothAbs.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothAbs.prototype,"x2",{get:function(){return this._x2},set:function(t){this._x2=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothAbs.prototype,"y2",{get:function(){return this._y2},set:function(t){this._y2=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegCurvetoCubicSmoothRel=function(t,e,i,n,a){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_REL,"s",t),this._x=e,this._y=i,this._x2=n,this._y2=a},window.SVGPathSegCurvetoCubicSmoothRel.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegCurvetoCubicSmoothRel.prototype.toString=function(){return"[object SVGPathSegCurvetoCubicSmoothRel]"},window.SVGPathSegCurvetoCubicSmoothRel.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._x2+" "+this._y2+" "+this._x+" "+this._y},window.SVGPathSegCurvetoCubicSmoothRel.prototype.clone=function(){return new window.SVGPathSegCurvetoCubicSmoothRel(void 0,this._x,this._y,this._x2,this._y2)},Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothRel.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothRel.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothRel.prototype,"x2",{get:function(){return this._x2},set:function(t){this._x2=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoCubicSmoothRel.prototype,"y2",{get:function(){return this._y2},set:function(t){this._y2=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegCurvetoQuadraticSmoothAbs=function(t,e,i){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_ABS,"T",t),this._x=e,this._y=i},window.SVGPathSegCurvetoQuadraticSmoothAbs.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegCurvetoQuadraticSmoothAbs.prototype.toString=function(){return"[object SVGPathSegCurvetoQuadraticSmoothAbs]"},window.SVGPathSegCurvetoQuadraticSmoothAbs.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._x+" "+this._y},window.SVGPathSegCurvetoQuadraticSmoothAbs.prototype.clone=function(){return new window.SVGPathSegCurvetoQuadraticSmoothAbs(void 0,this._x,this._y)},Object.defineProperty(window.SVGPathSegCurvetoQuadraticSmoothAbs.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoQuadraticSmoothAbs.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),window.SVGPathSegCurvetoQuadraticSmoothRel=function(t,e,i){window.SVGPathSeg.call(this,window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_REL,"t",t),this._x=e,this._y=i},window.SVGPathSegCurvetoQuadraticSmoothRel.prototype=Object.create(window.SVGPathSeg.prototype),window.SVGPathSegCurvetoQuadraticSmoothRel.prototype.toString=function(){return"[object SVGPathSegCurvetoQuadraticSmoothRel]"},window.SVGPathSegCurvetoQuadraticSmoothRel.prototype._asPathString=function(){return this.pathSegTypeAsLetter+" "+this._x+" "+this._y},window.SVGPathSegCurvetoQuadraticSmoothRel.prototype.clone=function(){return new window.SVGPathSegCurvetoQuadraticSmoothRel(void 0,this._x,this._y)},Object.defineProperty(window.SVGPathSegCurvetoQuadraticSmoothRel.prototype,"x",{get:function(){return this._x},set:function(t){this._x=t,this._segmentChanged()},enumerable:!0}),Object.defineProperty(window.SVGPathSegCurvetoQuadraticSmoothRel.prototype,"y",{get:function(){return this._y},set:function(t){this._y=t,this._segmentChanged()},enumerable:!0}),window.SVGPathElement.prototype.createSVGPathSegClosePath=function(){return new window.SVGPathSegClosePath(void 0)},window.SVGPathElement.prototype.createSVGPathSegMovetoAbs=function(t,e){return new window.SVGPathSegMovetoAbs(void 0,t,e)},window.SVGPathElement.prototype.createSVGPathSegMovetoRel=function(t,e){return new window.SVGPathSegMovetoRel(void 0,t,e)},window.SVGPathElement.prototype.createSVGPathSegLinetoAbs=function(t,e){return new window.SVGPathSegLinetoAbs(void 0,t,e)},window.SVGPathElement.prototype.createSVGPathSegLinetoRel=function(t,e){return new window.SVGPathSegLinetoRel(void 0,t,e)},window.SVGPathElement.prototype.createSVGPathSegCurvetoCubicAbs=function(t,e,i,n,a,r){return new window.SVGPathSegCurvetoCubicAbs(void 0,t,e,i,n,a,r)},window.SVGPathElement.prototype.createSVGPathSegCurvetoCubicRel=function(t,e,i,n,a,r){return new window.SVGPathSegCurvetoCubicRel(void 0,t,e,i,n,a,r)},window.SVGPathElement.prototype.createSVGPathSegCurvetoQuadraticAbs=function(t,e,i,n){return new window.SVGPathSegCurvetoQuadraticAbs(void 0,t,e,i,n)},window.SVGPathElement.prototype.createSVGPathSegCurvetoQuadraticRel=function(t,e,i,n){return new window.SVGPathSegCurvetoQuadraticRel(void 0,t,e,i,n)},window.SVGPathElement.prototype.createSVGPathSegArcAbs=function(t,e,i,n,a,r,s){return new window.SVGPathSegArcAbs(void 0,t,e,i,n,a,r,s)},window.SVGPathElement.prototype.createSVGPathSegArcRel=function(t,e,i,n,a,r,s){return new window.SVGPathSegArcRel(void 0,t,e,i,n,a,r,s)},window.SVGPathElement.prototype.createSVGPathSegLinetoHorizontalAbs=function(t){return new window.SVGPathSegLinetoHorizontalAbs(void 0,t)},window.SVGPathElement.prototype.createSVGPathSegLinetoHorizontalRel=function(t){return new window.SVGPathSegLinetoHorizontalRel(void 0,t)},window.SVGPathElement.prototype.createSVGPathSegLinetoVerticalAbs=function(t){return new window.SVGPathSegLinetoVerticalAbs(void 0,t)},window.SVGPathElement.prototype.createSVGPathSegLinetoVerticalRel=function(t){return new window.SVGPathSegLinetoVerticalRel(void 0,t)},window.SVGPathElement.prototype.createSVGPathSegCurvetoCubicSmoothAbs=function(t,e,i,n){return new window.SVGPathSegCurvetoCubicSmoothAbs(void 0,t,e,i,n)},window.SVGPathElement.prototype.createSVGPathSegCurvetoCubicSmoothRel=function(t,e,i,n){return new window.SVGPathSegCurvetoCubicSmoothRel(void 0,t,e,i,n)},window.SVGPathElement.prototype.createSVGPathSegCurvetoQuadraticSmoothAbs=function(t,e){return new window.SVGPathSegCurvetoQuadraticSmoothAbs(void 0,t,e)},window.SVGPathElement.prototype.createSVGPathSegCurvetoQuadraticSmoothRel=function(t,e){return new window.SVGPathSegCurvetoQuadraticSmoothRel(void 0,t,e)},"getPathSegAtLength"in window.SVGPathElement.prototype||(window.SVGPathElement.prototype.getPathSegAtLength=function(t){if(void 0===t||!isFinite(t))throw"Invalid arguments.";var e=document.createElementNS("http://www.w3.org/2000/svg","path");e.setAttribute("d",this.getAttribute("d"));var i=e.pathSegList.numberOfItems-1;if(i<=0)return 0;do{if(e.pathSegList.removeItem(i),t>e.getTotalLength())break;i--}while(0<i);return i})),"SVGPathSegList"in window||(window.SVGPathSegList=function(t){this._pathElement=t,this._list=this._parsePath(this._pathElement.getAttribute("d")),this._mutationObserverConfig={attributes:!0,attributeFilter:["d"]},this._pathElementMutationObserver=new MutationObserver(this._updateListFromPathMutations.bind(this)),this._pathElementMutationObserver.observe(this._pathElement,this._mutationObserverConfig)},window.SVGPathSegList.prototype.classname="SVGPathSegList",Object.defineProperty(window.SVGPathSegList.prototype,"numberOfItems",{get:function(){return this._checkPathSynchronizedToList(),this._list.length},enumerable:!0}),Object.defineProperty(window.SVGPathElement.prototype,"pathSegList",{get:function(){return this._pathSegList||(this._pathSegList=new window.SVGPathSegList(this)),this._pathSegList},enumerable:!0}),Object.defineProperty(window.SVGPathElement.prototype,"normalizedPathSegList",{get:function(){return this.pathSegList},enumerable:!0}),Object.defineProperty(window.SVGPathElement.prototype,"animatedPathSegList",{get:function(){return this.pathSegList},enumerable:!0}),Object.defineProperty(window.SVGPathElement.prototype,"animatedNormalizedPathSegList",{get:function(){return this.pathSegList},enumerable:!0}),window.SVGPathSegList.prototype._checkPathSynchronizedToList=function(){this._updateListFromPathMutations(this._pathElementMutationObserver.takeRecords())},window.SVGPathSegList.prototype._updateListFromPathMutations=function(t){if(this._pathElement){var e=!1;t.forEach(function(t){"d"==t.attributeName&&(e=!0)}),e&&(this._list=this._parsePath(this._pathElement.getAttribute("d")))}},window.SVGPathSegList.prototype._writeListToPath=function(){this._pathElementMutationObserver.disconnect(),this._pathElement.setAttribute("d",window.SVGPathSegList._pathSegArrayAsString(this._list)),this._pathElementMutationObserver.observe(this._pathElement,this._mutationObserverConfig)},window.SVGPathSegList.prototype.segmentChanged=function(t){this._writeListToPath()},window.SVGPathSegList.prototype.clear=function(){this._checkPathSynchronizedToList(),this._list.forEach(function(t){t._owningPathSegList=null}),this._list=[],this._writeListToPath()},window.SVGPathSegList.prototype.initialize=function(t){return this._checkPathSynchronizedToList(),this._list=[t],(t._owningPathSegList=this)._writeListToPath(),t},window.SVGPathSegList.prototype._checkValidIndex=function(t){if(isNaN(t)||t<0||t>=this.numberOfItems)throw"INDEX_SIZE_ERR"},window.SVGPathSegList.prototype.getItem=function(t){return this._checkPathSynchronizedToList(),this._checkValidIndex(t),this._list[t]},window.SVGPathSegList.prototype.insertItemBefore=function(t,e){return this._checkPathSynchronizedToList(),e>this.numberOfItems&&(e=this.numberOfItems),t._owningPathSegList&&(t=t.clone()),this._list.splice(e,0,t),(t._owningPathSegList=this)._writeListToPath(),t},window.SVGPathSegList.prototype.replaceItem=function(t,e){return this._checkPathSynchronizedToList(),t._owningPathSegList&&(t=t.clone()),this._checkValidIndex(e),((this._list[e]=t)._owningPathSegList=this)._writeListToPath(),t},window.SVGPathSegList.prototype.removeItem=function(t){this._checkPathSynchronizedToList(),this._checkValidIndex(t);var e=this._list[t];return this._list.splice(t,1),this._writeListToPath(),e},window.SVGPathSegList.prototype.appendItem=function(t){return this._checkPathSynchronizedToList(),t._owningPathSegList&&(t=t.clone()),this._list.push(t),(t._owningPathSegList=this)._writeListToPath(),t},window.SVGPathSegList._pathSegArrayAsString=function(t){var e="",i=!0;return t.forEach(function(t){i?(i=!1,e+=t._asPathString()):e+=" "+t._asPathString()}),e},window.SVGPathSegList.prototype._parsePath=function(t){if(!t||0==t.length)return[];var n=this,e=function(){this.pathSegList=[]};e.prototype.appendSegment=function(t){this.pathSegList.push(t)};var i=function(t){this._string=t,this._currentIndex=0,this._endIndex=this._string.length,this._previousCommand=window.SVGPathSeg.PATHSEG_UNKNOWN,this._skipOptionalSpaces()};i.prototype._isCurrentSpace=function(){var t=this._string[this._currentIndex];return t<=" "&&(" "==t||"\n"==t||"\t"==t||"\r"==t||"\f"==t)},i.prototype._skipOptionalSpaces=function(){for(;this._currentIndex<this._endIndex&&this._isCurrentSpace();)this._currentIndex++;return this._currentIndex<this._endIndex},i.prototype._skipOptionalSpacesOrDelimiter=function(){return!(this._currentIndex<this._endIndex&&!this._isCurrentSpace()&&","!=this._string.charAt(this._currentIndex))&&(this._skipOptionalSpaces()&&this._currentIndex<this._endIndex&&","==this._string.charAt(this._currentIndex)&&(this._currentIndex++,this._skipOptionalSpaces()),this._currentIndex<this._endIndex)},i.prototype.hasMoreData=function(){return this._currentIndex<this._endIndex},i.prototype.peekSegmentType=function(){var t=this._string[this._currentIndex];return this._pathSegTypeFromChar(t)},i.prototype._pathSegTypeFromChar=function(t){switch(t){case"Z":case"z":return window.SVGPathSeg.PATHSEG_CLOSEPATH;case"M":return window.SVGPathSeg.PATHSEG_MOVETO_ABS;case"m":return window.SVGPathSeg.PATHSEG_MOVETO_REL;case"L":return window.SVGPathSeg.PATHSEG_LINETO_ABS;case"l":return window.SVGPathSeg.PATHSEG_LINETO_REL;case"C":return window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_ABS;case"c":return window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_REL;case"Q":return window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_ABS;case"q":return window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_REL;case"A":return window.SVGPathSeg.PATHSEG_ARC_ABS;case"a":return window.SVGPathSeg.PATHSEG_ARC_REL;case"H":return window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_ABS;case"h":return window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_REL;case"V":return window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_ABS;case"v":return window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_REL;case"S":return window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_ABS;case"s":return window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_REL;case"T":return window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_ABS;case"t":return window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_REL;default:return window.SVGPathSeg.PATHSEG_UNKNOWN}},i.prototype._nextCommandHelper=function(t,e){return("+"==t||"-"==t||"."==t||"0"<=t&&t<="9")&&e!=window.SVGPathSeg.PATHSEG_CLOSEPATH?e==window.SVGPathSeg.PATHSEG_MOVETO_ABS?window.SVGPathSeg.PATHSEG_LINETO_ABS:e==window.SVGPathSeg.PATHSEG_MOVETO_REL?window.SVGPathSeg.PATHSEG_LINETO_REL:e:window.SVGPathSeg.PATHSEG_UNKNOWN},i.prototype.initialCommandIsMoveTo=function(){if(!this.hasMoreData())return!0;var t=this.peekSegmentType();return t==window.SVGPathSeg.PATHSEG_MOVETO_ABS||t==window.SVGPathSeg.PATHSEG_MOVETO_REL},i.prototype._parseNumber=function(){var t=0,e=0,i=1,n=0,a=1,r=1,s=this._currentIndex;if(this._skipOptionalSpaces(),this._currentIndex<this._endIndex&&"+"==this._string.charAt(this._currentIndex)?this._currentIndex++:this._currentIndex<this._endIndex&&"-"==this._string.charAt(this._currentIndex)&&(this._currentIndex++,a=-1),!(this._currentIndex==this._endIndex||(this._string.charAt(this._currentIndex)<"0"||"9"<this._string.charAt(this._currentIndex))&&"."!=this._string.charAt(this._currentIndex))){for(var o=this._currentIndex;this._currentIndex<this._endIndex&&"0"<=this._string.charAt(this._currentIndex)&&this._string.charAt(this._currentIndex)<="9";)this._currentIndex++;if(this._currentIndex!=o)for(var c=this._currentIndex-1,d=1;o<=c;)e+=d*(this._string.charAt(c--)-"0"),d*=10;if(this._currentIndex<this._endIndex&&"."==this._string.charAt(this._currentIndex)){if(this._currentIndex++,this._currentIndex>=this._endIndex||this._string.charAt(this._currentIndex)<"0"||"9"<this._string.charAt(this._currentIndex))return;for(;this._currentIndex<this._endIndex&&"0"<=this._string.charAt(this._currentIndex)&&this._string.charAt(this._currentIndex)<="9";)i*=10,n+=(this._string.charAt(this._currentIndex)-"0")/i,this._currentIndex+=1}if(this._currentIndex!=s&&this._currentIndex+1<this._endIndex&&("e"==this._string.charAt(this._currentIndex)||"E"==this._string.charAt(this._currentIndex))&&"x"!=this._string.charAt(this._currentIndex+1)&&"m"!=this._string.charAt(this._currentIndex+1)){if(this._currentIndex++,"+"==this._string.charAt(this._currentIndex)?this._currentIndex++:"-"==this._string.charAt(this._currentIndex)&&(this._currentIndex++,r=-1),this._currentIndex>=this._endIndex||this._string.charAt(this._currentIndex)<"0"||"9"<this._string.charAt(this._currentIndex))return;for(;this._currentIndex<this._endIndex&&"0"<=this._string.charAt(this._currentIndex)&&this._string.charAt(this._currentIndex)<="9";)t*=10,t+=this._string.charAt(this._currentIndex)-"0",this._currentIndex++}var u=e+n;if(u*=a,t&&(u*=Math.pow(10,r*t)),s!=this._currentIndex)return this._skipOptionalSpacesOrDelimiter(),u}},i.prototype._parseArcFlag=function(){if(!(this._currentIndex>=this._endIndex)){var t=!1,e=this._string.charAt(this._currentIndex++);if("0"==e)t=!1;else{if("1"!=e)return;t=!0}return this._skipOptionalSpacesOrDelimiter(),t}},i.prototype.parseSegment=function(){var t=this._string[this._currentIndex],e=this._pathSegTypeFromChar(t);if(e==window.SVGPathSeg.PATHSEG_UNKNOWN){if(this._previousCommand==window.SVGPathSeg.PATHSEG_UNKNOWN)return null;if((e=this._nextCommandHelper(t,this._previousCommand))==window.SVGPathSeg.PATHSEG_UNKNOWN)return null}else this._currentIndex++;switch(this._previousCommand=e){case window.SVGPathSeg.PATHSEG_MOVETO_REL:return new window.SVGPathSegMovetoRel(n,this._parseNumber(),this._parseNumber());case window.SVGPathSeg.PATHSEG_MOVETO_ABS:return new window.SVGPathSegMovetoAbs(n,this._parseNumber(),this._parseNumber());case window.SVGPathSeg.PATHSEG_LINETO_REL:return new window.SVGPathSegLinetoRel(n,this._parseNumber(),this._parseNumber());case window.SVGPathSeg.PATHSEG_LINETO_ABS:return new window.SVGPathSegLinetoAbs(n,this._parseNumber(),this._parseNumber());case window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_REL:return new window.SVGPathSegLinetoHorizontalRel(n,this._parseNumber());case window.SVGPathSeg.PATHSEG_LINETO_HORIZONTAL_ABS:return new window.SVGPathSegLinetoHorizontalAbs(n,this._parseNumber());case window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_REL:return new window.SVGPathSegLinetoVerticalRel(n,this._parseNumber());case window.SVGPathSeg.PATHSEG_LINETO_VERTICAL_ABS:return new window.SVGPathSegLinetoVerticalAbs(n,this._parseNumber());case window.SVGPathSeg.PATHSEG_CLOSEPATH:return this._skipOptionalSpaces(),new window.SVGPathSegClosePath(n);case window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_REL:var i={x1:this._parseNumber(),y1:this._parseNumber(),x2:this._parseNumber(),y2:this._parseNumber(),x:this._parseNumber(),y:this._parseNumber()};return new window.SVGPathSegCurvetoCubicRel(n,i.x,i.y,i.x1,i.y1,i.x2,i.y2);case window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_ABS:return i={x1:this._parseNumber(),y1:this._parseNumber(),x2:this._parseNumber(),y2:this._parseNumber(),x:this._parseNumber(),y:this._parseNumber()},new window.SVGPathSegCurvetoCubicAbs(n,i.x,i.y,i.x1,i.y1,i.x2,i.y2);case window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_REL:return i={x2:this._parseNumber(),y2:this._parseNumber(),x:this._parseNumber(),y:this._parseNumber()},new window.SVGPathSegCurvetoCubicSmoothRel(n,i.x,i.y,i.x2,i.y2);case window.SVGPathSeg.PATHSEG_CURVETO_CUBIC_SMOOTH_ABS:return i={x2:this._parseNumber(),y2:this._parseNumber(),x:this._parseNumber(),y:this._parseNumber()},new window.SVGPathSegCurvetoCubicSmoothAbs(n,i.x,i.y,i.x2,i.y2);case window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_REL:return i={x1:this._parseNumber(),y1:this._parseNumber(),x:this._parseNumber(),y:this._parseNumber()},new window.SVGPathSegCurvetoQuadraticRel(n,i.x,i.y,i.x1,i.y1);case window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_ABS:return i={x1:this._parseNumber(),y1:this._parseNumber(),x:this._parseNumber(),y:this._parseNumber()},new window.SVGPathSegCurvetoQuadraticAbs(n,i.x,i.y,i.x1,i.y1);case window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_REL:return new window.SVGPathSegCurvetoQuadraticSmoothRel(n,this._parseNumber(),this._parseNumber());case window.SVGPathSeg.PATHSEG_CURVETO_QUADRATIC_SMOOTH_ABS:return new window.SVGPathSegCurvetoQuadraticSmoothAbs(n,this._parseNumber(),this._parseNumber());case window.SVGPathSeg.PATHSEG_ARC_REL:return i={x1:this._parseNumber(),y1:this._parseNumber(),arcAngle:this._parseNumber(),arcLarge:this._parseArcFlag(),arcSweep:this._parseArcFlag(),x:this._parseNumber(),y:this._parseNumber()},new window.SVGPathSegArcRel(n,i.x,i.y,i.x1,i.y1,i.arcAngle,i.arcLarge,i.arcSweep);case window.SVGPathSeg.PATHSEG_ARC_ABS:return i={x1:this._parseNumber(),y1:this._parseNumber(),arcAngle:this._parseNumber(),arcLarge:this._parseArcFlag(),arcSweep:this._parseArcFlag(),x:this._parseNumber(),y:this._parseNumber()},new window.SVGPathSegArcAbs(n,i.x,i.y,i.x1,i.y1,i.arcAngle,i.arcLarge,i.arcSweep);default:throw"Unknown path seg type."}};var a=new e,r=new i(t);if(!r.initialCommandIsMoveTo())return[];for(;r.hasMoreData();){var s=r.parseSegment();if(!s)return[];a.appendSegment(s)}return a.pathSegList}),String.prototype.padEnd||(String.prototype.padEnd=function(t,e){return t>>=0,e=String(void 0!==e?e:" "),this.length>t?String(this):((t-=this.length)>e.length&&(e+=e.repeat(t/e.length)),String(this)+e.slice(0,t))}),x.axis=function(){},x.axis.labels=function(e){var i=this.internal;arguments.length&&(Object.keys(e).forEach(function(t){i.axis.setLabelText(t,e[t])}),i.axis.updateLabels())},x.axis.max=function(t){var e=this.internal,i=e.config;if(!arguments.length)return{x:i.axis_x_max,y:i.axis_y_max,y2:i.axis_y2_max};"object"===(void 0===t?"undefined":o(t))?(P(t.x)&&(i.axis_x_max=t.x),P(t.y)&&(i.axis_y_max=t.y),P(t.y2)&&(i.axis_y2_max=t.y2)):i.axis_y_max=i.axis_y2_max=t,e.redraw({withUpdateOrgXDomain:!0,withUpdateXDomain:!0})},x.axis.min=function(t){var e=this.internal,i=e.config;if(!arguments.length)return{x:i.axis_x_min,y:i.axis_y_min,y2:i.axis_y2_min};"object"===(void 0===t?"undefined":o(t))?(P(t.x)&&(i.axis_x_min=t.x),P(t.y)&&(i.axis_y_min=t.y),P(t.y2)&&(i.axis_y2_min=t.y2)):i.axis_y_min=i.axis_y2_min=t,e.redraw({withUpdateOrgXDomain:!0,withUpdateXDomain:!0})},x.axis.range=function(t){if(!arguments.length)return{max:this.axis.max(),min:this.axis.min()};k(t.max)&&this.axis.max(t.max),k(t.min)&&this.axis.min(t.min)},x.category=function(t,e){var i=this.internal,n=i.config;return 1<arguments.length&&(n.axis_x_categories[t]=e,i.redraw()),n.axis_x_categories[t]},x.categories=function(t){var e=this.internal,i=e.config;return arguments.length&&(i.axis_x_categories=t,e.redraw()),i.axis_x_categories},x.resize=function(t){var e=this.internal.config;e.size_width=t?t.width:null,e.size_height=t?t.height:null,this.flush()},x.flush=function(){this.internal.updateAndRedraw({withLegend:!0,withTransition:!1,withTransitionForTransform:!1})},x.destroy=function(){var e=this.internal;if(window.clearInterval(e.intervalForObserveInserted),void 0!==e.resizeTimeout&&window.clearTimeout(e.resizeTimeout),window.detachEvent)window.detachEvent("onresize",e.resizeIfElementDisplayed);else if(window.removeEventListener)window.removeEventListener("resize",e.resizeIfElementDisplayed);else{var t=window.onresize;t&&t.add&&t.remove&&t.remove(e.resizeFunction)}return e.resizeFunction.remove(),e.selectChart.classed("c3",!1).html(""),Object.keys(e).forEach(function(t){e[t]=null}),null},x.color=function(t){return this.internal.color(t)},x.data=function(e){var t=this.internal.data.targets;return void 0===e?t:t.filter(function(t){return 0<=[].concat(e).indexOf(t.id)})},x.data.shown=function(t){return this.internal.filterTargetsToShow(this.data(t))},x.data.values=function(t){var e,i=null;return t&&(i=(e=this.data(t))[0]?e[0].values.map(function(t){return t.value}):null),i},x.data.names=function(t){return this.internal.clearLegendItemTextBoxCache(),this.internal.updateDataAttributes("names",t)},x.data.colors=function(t){return this.internal.updateDataAttributes("colors",t)},x.data.axes=function(t){return this.internal.updateDataAttributes("axes",t)},x.flow=function(t){var a,e,i,n,r,s,o,c=this.internal,d=[],u=c.getMaxDataCount(),l=0,h=0;if(t.json)e=c.convertJsonToData(t.json,t.keys);else if(t.rows)e=c.convertRowsToData(t.rows);else{if(!t.columns)return;e=c.convertColumnsToData(t.columns)}a=c.convertDataToTargets(e,!0),c.data.targets.forEach(function(t){var e,i,n=!1;for(e=0;e<a.length;e++)if(t.id===a[e].id){for(n=!0,t.values[t.values.length-1]&&(h=t.values[t.values.length-1].index+1),l=a[e].values.length,i=0;i<l;i++)a[e].values[i].index=h+i,c.isTimeSeries()||(a[e].values[i].x=h+i);t.values=t.values.concat(a[e].values),a.splice(e,1);break}n||d.push(t.id)}),c.data.targets.forEach(function(t){var e,i;for(e=0;e<d.length;e++)if(t.id===d[e])for(h=t.values[t.values.length-1].index+1,i=0;i<l;i++)t.values.push({id:t.id,index:h+i,x:c.isTimeSeries()?c.getOtherTargetX(h+i):h+i,value:null})}),c.data.targets.length&&a.forEach(function(t){var e,i=[];for(e=c.data.targets[0].values[0].index;e<h;e++)i.push({id:t.id,index:e,x:c.isTimeSeries()?c.getOtherTargetX(e):e,value:null});t.values.forEach(function(t){t.index+=h,c.isTimeSeries()||(t.x+=h)}),t.values=i.concat(t.values)}),c.data.targets=c.data.targets.concat(a),c.getMaxDataCount(),r=(n=c.data.targets[0]).values[0],k(t.to)?(l=0,o=c.isTimeSeries()?c.parseDate(t.to):t.to,n.values.forEach(function(t){t.x<o&&l++})):k(t.length)&&(l=t.length),u?1===u&&c.isTimeSeries()&&(s=(n.values[n.values.length-1].x-r.x)/2,i=[new Date(+r.x-s),new Date(+r.x+s)],c.updateXDomain(null,!0,!0,!1,i)):(s=c.isTimeSeries()?1<n.values.length?n.values[n.values.length-1].x-r.x:r.x-c.getXDomain(c.data.targets)[0]:1,i=[r.x-s,r.x],c.updateXDomain(null,!0,!0,!1,i)),c.updateTargets(c.data.targets),c.redraw({flow:{index:r.index,length:l,duration:P(t.duration)?t.duration:c.config.transition_duration,done:t.done,orgDataCount:u},withLegend:!0,withTransition:1<u,withTrimXDomain:!1,withUpdateXAxis:!0})},m.generateFlow=function(E){var O=this,I=O.config,R=O.d3;return function(){var t,e,i,n,a,r,s,o,c,d,u,l=E.targets,h=E.flow,g=E.drawBar,f=E.drawLine,p=E.drawArea,_=E.cx,x=E.cy,y=E.xv,m=E.xForText,S=E.yForText,w=E.duration,v=h.index,b=h.length,A=O.getValueOnIndex(O.data.targets[0].values,v),T=O.getValueOnIndex(O.data.targets[0].values,v+b),P=O.x.domain(),C=h.duration||w,L=h.done||function(){},V=O.generateWait();O.flowing=!0,O.data.targets.forEach(function(t){t.values.splice(0,b)}),i=O.updateXDomain(l,!0,!0),O.updateXGrid&&O.updateXGrid(!0),n=O.xgrid||R.selectAll([]),a=O.xgridLines||R.selectAll([]),r=O.mainRegion||R.selectAll([]),s=O.mainText||R.selectAll([]),o=O.mainBar||R.selectAll([]),c=O.mainLine||R.selectAll([]),d=O.mainArea||R.selectAll([]),u=O.mainCircle||R.selectAll([]),h.orgDataCount?t=1===h.orgDataCount||(A&&A.x)===(T&&T.x)?O.x(P[0])-O.x(i[0]):O.isTimeSeries()?O.x(P[0])-O.x(i[0]):O.x(A.x)-O.x(T.x):1!==O.data.targets[0].values.length?t=O.x(P[0])-O.x(i[0]):O.isTimeSeries()?(A=O.getValueOnIndex(O.data.targets[0].values,0),T=O.getValueOnIndex(O.data.targets[0].values,O.data.targets[0].values.length-1),t=O.x(A.x)-O.x(T.x)):t=D(i)/2,e="translate("+t+",0) scale("+D(P)/D(i)+",1)",O.hideXGridFocus();var G=R.transition().ease(R.easeLinear).duration(C);V.add(O.xAxis(O.axes.x,G)),V.add(o.transition(G).attr("transform",e)),V.add(c.transition(G).attr("transform",e)),V.add(d.transition(G).attr("transform",e)),V.add(u.transition(G).attr("transform",e)),V.add(s.transition(G).attr("transform",e)),V.add(r.filter(O.isRegionOnX).transition(G).attr("transform",e)),V.add(n.transition(G).attr("transform",e)),V.add(a.transition(G).attr("transform",e)),V(function(){var t,e=[],i=[];if(b){for(t=0;t<b;t++)e.push("."+Y.shape+"-"+(v+t)),i.push("."+Y.text+"-"+(v+t));O.svg.selectAll("."+Y.shapes).selectAll(e).remove(),O.svg.selectAll("."+Y.texts).selectAll(i).remove(),O.svg.select("."+Y.xgrid).remove()}n.attr("transform",null).attr("x1",O.xgridAttr.x1).attr("x2",O.xgridAttr.x2).attr("y1",O.xgridAttr.y1).attr("y2",O.xgridAttr.y2).style("opacity",O.xgridAttr.opacity),a.attr("transform",null),a.select("line").attr("x1",I.axis_rotated?0:y).attr("x2",I.axis_rotated?O.width:y),a.select("text").attr("x",I.axis_rotated?O.width:0).attr("y",y),o.attr("transform",null).attr("d",g),c.attr("transform",null).attr("d",f),d.attr("transform",null).attr("d",p),u.attr("transform",null).attr("cx",_).attr("cy",x),s.attr("transform",null).attr("x",m).attr("y",S).style("fill-opacity",O.opacityForText.bind(O)),r.attr("transform",null),r.filter(O.isRegionOnX).attr("x",O.regionX.bind(O)).attr("width",O.regionWidth.bind(O)),L(),O.flowing=!1})}},x.focus=function(e){var t,i=this.internal;e=i.mapToTargetIds(e),t=i.svg.selectAll(i.selectorTargets(e.filter(i.isTargetToShow,i))),this.revert(),this.defocus(),t.classed(Y.focused,!0).classed(Y.defocused,!1),i.hasArcType()&&i.expandArc(e),i.toggleFocusLegend(e,!0),i.focusedTargetIds=e,i.defocusedTargetIds=i.defocusedTargetIds.filter(function(t){return e.indexOf(t)<0})},x.defocus=function(e){var t=this.internal;e=t.mapToTargetIds(e),t.svg.selectAll(t.selectorTargets(e.filter(t.isTargetToShow,t))).classed(Y.focused,!1).classed(Y.defocused,!0),t.hasArcType()&&t.unexpandArc(e),t.toggleFocusLegend(e,!1),t.focusedTargetIds=t.focusedTargetIds.filter(function(t){return e.indexOf(t)<0}),t.defocusedTargetIds=e},x.revert=function(t){var e=this.internal;t=e.mapToTargetIds(t),e.svg.selectAll(e.selectorTargets(t)).classed(Y.focused,!1).classed(Y.defocused,!1),e.hasArcType()&&e.unexpandArc(t),e.config.legend_show&&(e.showLegend(t.filter(e.isLegendToShow.bind(e))),e.legend.selectAll(e.selectorLegends(t)).filter(function(){return e.d3.select(this).classed(Y.legendItemFocused)}).classed(Y.legendItemFocused,!1)),e.focusedTargetIds=[],e.defocusedTargetIds=[]},x.xgrids=function(t){var e=this.internal,i=e.config;return t&&(i.grid_x_lines=t,e.redrawWithoutRescale()),i.grid_x_lines},x.xgrids.add=function(t){var e=this.internal;return this.xgrids(e.config.grid_x_lines.concat(t||[]))},x.xgrids.remove=function(t){this.internal.removeGridLines(t,!0)},x.ygrids=function(t){var e=this.internal,i=e.config;return t&&(i.grid_y_lines=t,e.redrawWithoutRescale()),i.grid_y_lines},x.ygrids.add=function(t){var e=this.internal;return this.ygrids(e.config.grid_y_lines.concat(t||[]))},x.ygrids.remove=function(t){this.internal.removeGridLines(t,!1)},x.groups=function(t){var e=this.internal,i=e.config;return v(t)||(i.data_groups=t,e.redraw()),i.data_groups},x.legend=function(){},x.legend.show=function(t){var e=this.internal;e.showLegend(e.mapToTargetIds(t)),e.updateAndRedraw({withLegend:!0})},x.legend.hide=function(t){var e=this.internal;e.hideLegend(e.mapToTargetIds(t)),e.updateAndRedraw({withLegend:!0})},x.load=function(e){var t=this.internal,i=t.config;e.xs&&t.addXs(e.xs),"names"in e&&x.data.names.bind(this)(e.names),"classes"in e&&Object.keys(e.classes).forEach(function(t){i.data_classes[t]=e.classes[t]}),"categories"in e&&t.isCategorized()&&(i.axis_x_categories=e.categories),"axes"in e&&Object.keys(e.axes).forEach(function(t){i.data_axes[t]=e.axes[t]}),"colors"in e&&Object.keys(e.colors).forEach(function(t){i.data_colors[t]=e.colors[t]}),"cacheIds"in e&&t.hasCaches(e.cacheIds)?t.load(t.getCaches(e.cacheIds),e.done):"unload"in e?t.unload(t.mapToTargetIds("boolean"==typeof e.unload&&e.unload?null:e.unload),function(){t.loadFromArgs(e)}):t.loadFromArgs(e)},x.unload=function(t){var e=this.internal;(t=t||{})instanceof Array?t={ids:t}:"string"==typeof t&&(t={ids:[t]}),e.unload(e.mapToTargetIds(t.ids),function(){e.redraw({withUpdateOrgXDomain:!0,withUpdateXDomain:!0,withLegend:!0}),t.done&&t.done()})},x.regions=function(t){var e=this.internal,i=e.config;return t&&(i.regions=t,e.redrawWithoutRescale()),i.regions},x.regions.add=function(t){var e=this.internal,i=e.config;return t&&(i.regions=i.regions.concat(t),e.redrawWithoutRescale()),i.regions},x.regions.remove=function(t){var e,i,n,a=this.internal,r=a.config;return t=t||{},e=a.getOption(t,"duration",r.transition_duration),i=a.getOption(t,"classes",[Y.region]),n=a.main.select("."+Y.regions).selectAll(i.map(function(t){return"."+t})),(e?n.transition().duration(e):n).style("opacity",0).remove(),r.regions=r.regions.filter(function(t){var e=!1;return!t.class||(t.class.split(" ").forEach(function(t){0<=i.indexOf(t)&&(e=!0)}),!e)}),r.regions},x.selected=function(t){var e=this.internal,i=e.d3;return i.merge(e.main.selectAll("."+Y.shapes+e.getTargetSelectorSuffix(t)).selectAll("."+Y.shape).filter(function(){return i.select(this).classed(Y.SELECTED)}).map(function(t){return t.map(function(t){var e=t.__data__;return e.data?e.data:e})}))},x.select=function(c,d,u){var l=this.internal,h=l.d3,g=l.config;g.data_selection_enabled&&l.main.selectAll("."+Y.shapes).selectAll("."+Y.shape).each(function(t,e){var i=h.select(this),n=t.data?t.data.id:t.id,a=l.getToggle(this,t).bind(l),r=g.data_selection_grouped||!c||0<=c.indexOf(n),s=!d||0<=d.indexOf(e),o=i.classed(Y.SELECTED);i.classed(Y.line)||i.classed(Y.area)||(r&&s?g.data_selection_isselectable(t)&&!o&&a(!0,i.classed(Y.SELECTED,!0),t,e):k(u)&&u&&o&&a(!1,i.classed(Y.SELECTED,!1),t,e))})},x.unselect=function(c,d){var u=this.internal,l=u.d3,h=u.config;h.data_selection_enabled&&u.main.selectAll("."+Y.shapes).selectAll("."+Y.shape).each(function(t,e){var i=l.select(this),n=t.data?t.data.id:t.id,a=u.getToggle(this,t).bind(u),r=h.data_selection_grouped||!c||0<=c.indexOf(n),s=!d||0<=d.indexOf(e),o=i.classed(Y.SELECTED);i.classed(Y.line)||i.classed(Y.area)||r&&s&&h.data_selection_isselectable(t)&&o&&a(!1,i.classed(Y.SELECTED,!1),t,e)})},x.show=function(t,e){var i,n=this.internal;t=n.mapToTargetIds(t),e=e||{},n.removeHiddenTargetIds(t),(i=n.svg.selectAll(n.selectorTargets(t))).transition().style("display","initial","important").style("opacity",1,"important").call(n.endall,function(){i.style("opacity",null).style("opacity",1)}),e.withLegend&&n.showLegend(t),n.redraw({withUpdateOrgXDomain:!0,withUpdateXDomain:!0,withLegend:!0})},x.hide=function(t,e){var i,n=this.internal;t=n.mapToTargetIds(t),e=e||{},n.addHiddenTargetIds(t),(i=n.svg.selectAll(n.selectorTargets(t))).transition().style("opacity",0,"important").call(n.endall,function(){i.style("opacity",null).style("opacity",0),i.style("display","none")}),e.withLegend&&n.hideLegend(t),n.redraw({withUpdateOrgXDomain:!0,withUpdateXDomain:!0,withLegend:!0})},x.toggle=function(t,e){var i=this,n=this.internal;n.mapToTargetIds(t).forEach(function(t){n.isTargetToShow(t)?i.hide(t,e):i.show(t,e)})},x.tooltip=function(){},x.tooltip.show=function(e){var t,i,n=this.internal,a={};e.mouse?a=e.mouse:(e.data?i=e.data:void 0!==e.x&&(t=e.id?n.data.targets.filter(function(t){return t.id===e.id}):n.data.targets,i=n.filterByX(t,e.x).slice(0,1)[0]),a=i?n.getMousePosition(i):null),n.dispatchEvent("mousemove",a),n.config.tooltip_onshow.call(n,i)},x.tooltip.hide=function(){this.internal.dispatchEvent("mouseout",0),this.internal.config.tooltip_onhide.call(this)},x.transform=function(t,e){var i=this.internal,n=0<=["pie","donut"].indexOf(t)?{withTransform:!0}:null;i.transformTo(e,t,n)},m.transformTo=function(t,e,i){var n=this,a=!n.hasArcType(),r=i||{withTransitionForAxis:a};r.withTransitionForTransform=!1,n.transiting=!1,n.setTargetType(t,e),n.updateTargets(n.data.targets),n.updateAndRedraw(r)},x.x=function(t){var e=this.internal;return arguments.length&&(e.updateTargetX(e.data.targets,t),e.redraw({withUpdateOrgXDomain:!0,withUpdateXDomain:!0})),e.data.xs},x.xs=function(t){var e=this.internal;return arguments.length&&(e.updateTargetXs(e.data.targets,t),e.redraw({withUpdateOrgXDomain:!0,withUpdateXDomain:!0})),e.data.xs},x.zoom=function(t){var e=this.internal;return t?(e.isTimeSeries()&&(t=t.map(function(t){return e.parseDate(t)})),e.config.subchart_show?e.brush.selectionAsValue(t,!0):(e.updateXDomain(null,!0,!1,!1,t),e.redraw({withY:e.config.zoom_rescale,withSubchart:!1})),e.config.zoom_onzoom.call(this,e.x.orgDomain()),t):e.x.domain()},x.zoom.enable=function(t){var e=this.internal;e.config.zoom_enabled=t,e.updateAndRedraw()},x.unzoom=function(){var t=this.internal;t.config.subchart_show?t.brush.clear():(t.updateXDomain(null,!0,!1,!1,t.subX.domain()),t.redraw({withY:t.config.zoom_rescale,withSubchart:!1}))},x.zoom.max=function(t){var e=this.internal,i=e.config,n=e.d3;if(0!==t&&!t)return i.zoom_x_max;i.zoom_x_max=n.max([e.orgXDomain[1],t])},x.zoom.min=function(t){var e=this.internal,i=e.config,n=e.d3;if(0!==t&&!t)return i.zoom_x_min;i.zoom_x_min=n.min([e.orgXDomain[0],t])},x.zoom.range=function(t){if(!arguments.length)return{max:this.domain.max(),min:this.domain.min()};k(t.max)&&this.domain.max(t.max),k(t.min)&&this.domain.min(t.min)},m.initPie=function(){var t=this,e=t.d3;t.pie=e.pie().value(function(t){return t.values.reduce(function(t,e){return t+e.value},0)});var i=t.getOrderFunction();if(i&&(t.isOrderAsc()||t.isOrderDesc())){var n=i;i=function(t,e){return-1*n(t,e)}}t.pie.sort(i||null)},m.updateRadius=function(){var t=this,e=t.config,i=e.gauge_width||e.donut_width,n=t.filterTargetsToShow(t.data.targets).length*t.config.gauge_arcs_minWidth;t.radiusExpanded=Math.min(t.arcWidth,t.arcHeight)/2*(t.hasType("gauge")?.85:1),t.radius=.95*t.radiusExpanded,t.innerRadiusRatio=i?(t.radius-i)/t.radius:.6,t.innerRadius=t.hasType("donut")||t.hasType("gauge")?t.radius*t.innerRadiusRatio:0,t.gaugeArcWidth=i||(n<=t.radius-t.innerRadius?t.radius-t.innerRadius:n<=t.radius?n:t.radius)},m.updateArc=function(){var t=this;t.svgArc=t.getSvgArc(),t.svgArcExpanded=t.getSvgArcExpanded(),t.svgArcExpandedSub=t.getSvgArcExpanded(.98)},m.updateAngle=function(e){var t,i,n,a,r=this,s=r.config,o=!1,c=0;return s?(r.pie(r.filterTargetsToShow(r.data.targets)).forEach(function(t){o||t.data.id!==e.data.id||(o=!0,(e=t).index=c),c++}),isNaN(e.startAngle)&&(e.startAngle=0),isNaN(e.endAngle)&&(e.endAngle=e.startAngle),r.isGaugeType(e.data)&&(t=s.gauge_min,i=s.gauge_max,n=Math.PI*(s.gauge_fullCircle?2:1)/(i-t),a=e.value<t?0:e.value<i?e.value-t:i-t,e.startAngle=s.gauge_startingAngle,e.endAngle=e.startAngle+n*a),o?e:null):null},m.getSvgArc=function(){var n=this,e=n.hasType("gauge"),i=n.gaugeArcWidth/n.filterTargetsToShow(n.data.targets).length,a=n.d3.arc().outerRadius(function(t){return e?n.radius-i*t.index:n.radius}).innerRadius(function(t){return e?n.radius-i*(t.index+1):n.innerRadius}),t=function(t,e){var i;return e?a(t):(i=n.updateAngle(t))?a(i):"M 0 0"};return t.centroid=a.centroid,t},m.getSvgArcExpanded=function(e){e=e||1;var i=this,n=i.hasType("gauge"),a=i.gaugeArcWidth/i.filterTargetsToShow(i.data.targets).length,r=Math.min(i.radiusExpanded*e-i.radius,.8*a-100*(1-e)),s=i.d3.arc().outerRadius(function(t){return n?i.radius-a*t.index+r:i.radiusExpanded*e}).innerRadius(function(t){return n?i.radius-a*(t.index+1):i.innerRadius});return function(t){var e=i.updateAngle(t);return e?s(e):"M 0 0"}},m.getArc=function(t,e,i){return i||this.isArcType(t.data)?this.svgArc(t,e):"M 0 0"},m.transformForArcLabel=function(t){var e,i,n,a,r,s=this,o=s.config,c=s.updateAngle(t),d="",u=s.hasType("gauge");if(c&&!u)e=this.svgArc.centroid(c),i=isNaN(e[0])?0:e[0],n=isNaN(e[1])?0:e[1],a=Math.sqrt(i*i+n*n),d="translate("+i*(r=s.hasType("donut")&&o.donut_label_ratio?h(o.donut_label_ratio)?o.donut_label_ratio(t,s.radius,a):o.donut_label_ratio:s.hasType("pie")&&o.pie_label_ratio?h(o.pie_label_ratio)?o.pie_label_ratio(t,s.radius,a):o.pie_label_ratio:s.radius&&a?(.375<36/s.radius?1.175-36/s.radius:.8)*s.radius/a:0)+","+n*r+")";else if(c&&u&&1<s.filterTargetsToShow(s.data.targets).length){var l=Math.sin(c.endAngle-Math.PI/2);d="translate("+(i=Math.cos(c.endAngle-Math.PI/2)*(s.radiusExpanded+25))+","+(n=l*(s.radiusExpanded+15-Math.abs(10*l))+3)+")"}return d},m.getArcRatio=function(t){var e=this.config,i=Math.PI*(this.hasType("gauge")&&!e.gauge_fullCircle?1:2);return t?(t.endAngle-t.startAngle)/i:null},m.convertToArcData=function(t){return this.addName({id:t.data.id,value:t.value,ratio:this.getArcRatio(t),index:t.index})},m.textForArcLabel=function(t){var e,i,n,a,r,s=this;return s.shouldShowArcLabel()?(i=(e=s.updateAngle(t))?e.value:null,n=s.getArcRatio(e),a=t.data.id,s.hasType("gauge")||s.meetsArcLabelThreshold(n)?(r=s.getArcLabelFormat())?r(i,n,a):s.defaultArcValueFormat(i,n):""):""},m.textForGaugeMinMax=function(t,e){var i=this.getGaugeLabelExtents();return i?i(t,e):t},m.expandArc=function(t){var e,i=this;i.transiting?e=window.setInterval(function(){i.transiting||(window.clearInterval(e),0<i.legend.selectAll(".c3-legend-item-focused").size()&&i.expandArc(t))},10):(t=i.mapToTargetIds(t),i.svg.selectAll(i.selectorTargets(t,"."+Y.chartArc)).each(function(t){i.shouldExpand(t.data.id)&&i.d3.select(this).selectAll("path").transition().duration(i.expandDuration(t.data.id)).attr("d",i.svgArcExpanded).transition().duration(2*i.expandDuration(t.data.id)).attr("d",i.svgArcExpandedSub).each(function(t){i.isDonutType(t.data)})}))},m.unexpandArc=function(t){var e=this;e.transiting||(t=e.mapToTargetIds(t),e.svg.selectAll(e.selectorTargets(t,"."+Y.chartArc)).selectAll("path").transition().duration(function(t){return e.expandDuration(t.data.id)}).attr("d",e.svgArc),e.svg.selectAll("."+Y.arc))},m.expandDuration=function(t){var e=this.config;return this.isDonutType(t)?e.donut_expand_duration:this.isGaugeType(t)?e.gauge_expand_duration:this.isPieType(t)?e.pie_expand_duration:50},m.shouldExpand=function(t){var e=this.config;return this.isDonutType(t)&&e.donut_expand||this.isGaugeType(t)&&e.gauge_expand||this.isPieType(t)&&e.pie_expand},m.shouldShowArcLabel=function(){var t=this.config,e=!0;return this.hasType("donut")?e=t.donut_label_show:this.hasType("pie")&&(e=t.pie_label_show),e},m.meetsArcLabelThreshold=function(t){var e=this.config;return(this.hasType("donut")?e.donut_label_threshold:e.pie_label_threshold)<=t},m.getArcLabelFormat=function(){var t=this.config,e=t.pie_label_format;return this.hasType("gauge")?e=t.gauge_label_format:this.hasType("donut")&&(e=t.donut_label_format),e},m.getGaugeLabelExtents=function(){return this.config.gauge_label_extents},m.getArcTitle=function(){return this.hasType("donut")?this.config.donut_title:""},m.updateTargetsForArc=function(t){var e,i=this,n=i.main,a=i.classChartArc.bind(i),r=i.classArcs.bind(i),s=i.classFocus.bind(i);(e=n.select("."+Y.chartArcs).selectAll("."+Y.chartArc).data(i.pie(t)).attr("class",function(t){return a(t)+s(t.data)}).enter().append("g").attr("class",a)).append("g").attr("class",r),e.append("text").attr("dy",i.hasType("gauge")?"-.1em":".35em").style("opacity",0).style("text-anchor","middle").style("pointer-events","none")},m.initArc=function(){var t=this;t.arcs=t.main.select("."+Y.chart).append("g").attr("class",Y.chartArcs).attr("transform",t.getTranslate("arc")),t.arcs.append("text").attr("class",Y.chartArcsTitle).style("text-anchor","middle").text(t.getArcTitle())},m.redrawArc=function(t,e,i){var n,a,r,s,o,u=this,l=u.d3,c=u.config,d=u.main,h=u.hasType("gauge");if(a=(n=d.selectAll("."+Y.arcs).selectAll("."+Y.arc).data(u.arcData.bind(u))).enter().append("path").attr("class",u.classArc.bind(u)).style("fill",function(t){return u.color(t.data)}).style("cursor",function(t){return c.interaction_enabled&&c.data_selection_isselectable(t)?"pointer":null}).each(function(t){u.isGaugeType(t.data)&&(t.startAngle=t.endAngle=c.gauge_startingAngle),this._current=t}).merge(n),h&&(o=(s=d.selectAll("."+Y.arcs).selectAll("."+Y.arcLabelLine).data(u.arcData.bind(u))).enter().append("rect").attr("class",function(t){return Y.arcLabelLine+" "+Y.target+" "+Y.target+"-"+t.data.id}).merge(s),1===u.filterTargetsToShow(u.data.targets).length?o.style("display","none"):o.style("fill",function(t){return 0<c.color_pattern.length?u.levelColor(t.data.values[0].value):u.color(t.data)}).style("display",c.gauge_labelLine_show?"":"none").each(function(t){var e=0,i=0,n=0,a="";if(u.hiddenTargetIds.indexOf(t.data.id)<0){var r=u.updateAngle(t),s=u.gaugeArcWidth/u.filterTargetsToShow(u.data.targets).length*(r.index+1),o=r.endAngle-Math.PI/2,c=u.radius-s,d=o-(0===c?0:1/c);e=u.radiusExpanded-u.radius+s,i=Math.cos(d)*c,n=Math.sin(d)*c,a="rotate("+180*o/Math.PI+", "+i+", "+n+")"}l.select(this).attr("x",i).attr("y",n).attr("width",e).attr("height",2).attr("transform",a).style("stroke-dasharray","0, "+(e+2)+", 0")})),a.attr("transform",function(t){return!u.isGaugeType(t.data)&&i?"scale(0)":""}).on("mouseover",c.interaction_enabled?function(t){var e,i;u.transiting||(e=u.updateAngle(t))&&(i=u.convertToArcData(e),u.expandArc(e.data.id),u.api.focus(e.data.id),u.toggleFocusLegend(e.data.id,!0),u.config.data_onmouseover(i,this))}:null).on("mousemove",c.interaction_enabled?function(t){var e,i=u.updateAngle(t);i&&(e=[u.convertToArcData(i)],u.showTooltip(e,this))}:null).on("mouseout",c.interaction_enabled?function(t){var e,i;u.transiting||(e=u.updateAngle(t))&&(i=u.convertToArcData(e),u.unexpandArc(e.data.id),u.api.revert(),u.revertLegend(),u.hideTooltip(),u.config.data_onmouseout(i,this))}:null).on("click",c.interaction_enabled?function(t,e){var i,n=u.updateAngle(t);n&&(i=u.convertToArcData(n),u.toggleShape&&u.toggleShape(this,i,e),u.config.data_onclick.call(u.api,i,this))}:null).each(function(){u.transiting=!0}).transition().duration(t).attrTween("d",function(i){var n,t=u.updateAngle(i);return t?(isNaN(this._current.startAngle)&&(this._current.startAngle=0),isNaN(this._current.endAngle)&&(this._current.endAngle=this._current.startAngle),n=l.interpolate(this._current,t),this._current=n(0),function(t){var e=n(t);return e.data=i.data,u.getArc(e,!0)}):function(){return"M 0 0"}}).attr("transform",i?"scale(1)":"").style("fill",function(t){return u.levelColor?u.levelColor(t.data.values[0].value):u.color(t.data.id)}).call(u.endall,function(){u.transiting=!1}),n.exit().transition().duration(e).style("opacity",0).remove(),d.selectAll("."+Y.chartArc).select("text").style("opacity",0).attr("class",function(t){return u.isGaugeType(t.data)?Y.gaugeValue:""}).text(u.textForArcLabel.bind(u)).attr("transform",u.transformForArcLabel.bind(u)).style("font-size",function(t){return u.isGaugeType(t.data)&&1===u.filterTargetsToShow(u.data.targets).length?Math.round(u.radius/5)+"px":""}).transition().duration(t).style("opacity",function(t){return u.isTargetToShow(t.data.id)&&u.isArcType(t.data)?1:0}),d.select("."+Y.chartArcsTitle).style("opacity",u.hasType("donut")||h?1:0),h){var g=0;(r=u.arcs.select("g."+Y.chartArcsBackground).selectAll("path."+Y.chartArcsBackground).data(u.data.targets)).enter().append("path").attr("class",function(t,e){return Y.chartArcsBackground+" "+Y.chartArcsBackground+"-"+e}).attr("d",function(t){if(0<=u.hiddenTargetIds.indexOf(t.id))return"M 0 0";var e={data:[{value:c.gauge_max}],startAngle:c.gauge_startingAngle,endAngle:-1*c.gauge_startingAngle*(c.gauge_fullCircle?Math.PI:1),index:g++};return u.getArc(e,!0,!0)}),r.exit().remove(),u.arcs.select("."+Y.chartArcsGaugeUnit).attr("dy",".75em").text(c.gauge_label_show?c.gauge_units:""),u.arcs.select("."+Y.chartArcsGaugeMin).attr("dx",-1*(u.innerRadius+(u.radius-u.innerRadius)/(c.gauge_fullCircle?1:2))+"px").attr("dy","1.2em").text(c.gauge_label_show?u.textForGaugeMinMax(c.gauge_min,!1):""),u.arcs.select("."+Y.chartArcsGaugeMax).attr("dx",u.innerRadius+(u.radius-u.innerRadius)/(c.gauge_fullCircle?1:2)+"px").attr("dy","1.2em").text(c.gauge_label_show?u.textForGaugeMinMax(c.gauge_max,!0):"")}},m.initGauge=function(){var t=this.arcs;this.hasType("gauge")&&(t.append("g").attr("class",Y.chartArcsBackground),t.append("text").attr("class",Y.chartArcsGaugeUnit).style("text-anchor","middle").style("pointer-events","none"),t.append("text").attr("class",Y.chartArcsGaugeMin).style("text-anchor","middle").style("pointer-events","none"),t.append("text").attr("class",Y.chartArcsGaugeMax).style("text-anchor","middle").style("pointer-events","none"))},m.getGaugeLabelHeight=function(){return this.config.gauge_label_show?20:0},m.hasCaches=function(t){for(var e=0;e<t.length;e++)if(!(t[e]in this.cache))return!1;return!0},m.addCache=function(t,e){this.cache[t]=this.cloneTarget(e)},m.getCaches=function(t){var e,i=[];for(e=0;e<t.length;e++)t[e]in this.cache&&i.push(this.cloneTarget(this.cache[t[e]]));return i},m.categoryName=function(t){var e=this.config;return t<e.axis_x_categories.length?e.axis_x_categories[t]:t},m.generateTargetClass=function(t){return t||0===t?("-"+t).replace(/\s/g,"-"):""},m.generateClass=function(t,e){return" "+t+" "+t+this.generateTargetClass(e)},m.classText=function(t){return this.generateClass(Y.text,t.index)},m.classTexts=function(t){return this.generateClass(Y.texts,t.id)},m.classShape=function(t){return this.generateClass(Y.shape,t.index)},m.classShapes=function(t){return this.generateClass(Y.shapes,t.id)},m.classLine=function(t){return this.classShape(t)+this.generateClass(Y.line,t.id)},m.classLines=function(t){return this.classShapes(t)+this.generateClass(Y.lines,t.id)},m.classCircle=function(t){return this.classShape(t)+this.generateClass(Y.circle,t.index)},m.classCircles=function(t){return this.classShapes(t)+this.generateClass(Y.circles,t.id)},m.classBar=function(t){return this.classShape(t)+this.generateClass(Y.bar,t.index)},m.classBars=function(t){return this.classShapes(t)+this.generateClass(Y.bars,t.id)},m.classArc=function(t){return this.classShape(t.data)+this.generateClass(Y.arc,t.data.id)},m.classArcs=function(t){return this.classShapes(t.data)+this.generateClass(Y.arcs,t.data.id)},m.classArea=function(t){return this.classShape(t)+this.generateClass(Y.area,t.id)},m.classAreas=function(t){return this.classShapes(t)+this.generateClass(Y.areas,t.id)},m.classRegion=function(t,e){return this.generateClass(Y.region,e)+" "+("class"in t?t.class:"")},m.classEvent=function(t){return this.generateClass(Y.eventRect,t.index)},m.classTarget=function(t){var e=this.config.data_classes[t],i="";return e&&(i=" "+Y.target+"-"+e),this.generateClass(Y.target,t)+i},m.classFocus=function(t){return this.classFocused(t)+this.classDefocused(t)},m.classFocused=function(t){return" "+(0<=this.focusedTargetIds.indexOf(t.id)?Y.focused:"")},m.classDefocused=function(t){return" "+(0<=this.defocusedTargetIds.indexOf(t.id)?Y.defocused:"")},m.classChartText=function(t){return Y.chartText+this.classTarget(t.id)},m.classChartLine=function(t){return Y.chartLine+this.classTarget(t.id)},m.classChartBar=function(t){return Y.chartBar+this.classTarget(t.id)},m.classChartArc=function(t){return Y.chartArc+this.classTarget(t.data.id)},m.getTargetSelectorSuffix=function(t){return this.generateTargetClass(t).replace(/([?!@#$%^&*()_=+,.<>'":;\[\]\/|~`{}\\])/g,"\\$1")},m.selectorTarget=function(t,e){return(e||"")+"."+Y.target+this.getTargetSelectorSuffix(t)},m.selectorTargets=function(t,e){var i=this;return(t=t||[]).length?t.map(function(t){return i.selectorTarget(t,e)}):null},m.selectorLegend=function(t){return"."+Y.legendItem+this.getTargetSelectorSuffix(t)},m.selectorLegends=function(t){var e=this;return t&&t.length?t.map(function(t){return e.selectorLegend(t)}):null},m.getClipPath=function(t){return"url("+(0<=window.navigator.appVersion.toLowerCase().indexOf("msie 9.")?"":document.URL.split("#")[0])+"#"+t+")"},m.appendClip=function(t,e){return t.append("clipPath").attr("id",e).append("rect")},m.getAxisClipX=function(t){var e=Math.max(30,this.margin.left);return t?-(1+e):-(e-1)},m.getAxisClipY=function(t){return t?-20:-this.margin.top},m.getXAxisClipX=function(){return this.getAxisClipX(!this.config.axis_rotated)},m.getXAxisClipY=function(){return this.getAxisClipY(!this.config.axis_rotated)},m.getYAxisClipX=function(){return this.config.axis_y_inner?-1:this.getAxisClipX(this.config.axis_rotated)},m.getYAxisClipY=function(){return this.getAxisClipY(this.config.axis_rotated)},m.getAxisClipWidth=function(t){var e=Math.max(30,this.margin.left),i=Math.max(30,this.margin.right);return t?this.width+2+e+i:this.margin.left+20},m.getAxisClipHeight=function(t){return(t?this.margin.bottom:this.margin.top+this.height)+20},m.getXAxisClipWidth=function(){return this.getAxisClipWidth(!this.config.axis_rotated)},m.getXAxisClipHeight=function(){return this.getAxisClipHeight(!this.config.axis_rotated)},m.getYAxisClipWidth=function(){return this.getAxisClipWidth(this.config.axis_rotated)+(this.config.axis_y_inner?20:0)},m.getYAxisClipHeight=function(){return this.getAxisClipHeight(this.config.axis_rotated)},m.generateColor=function(){var t=this.config,e=this.d3,n=t.data_colors,a=C(t.color_pattern)?t.color_pattern:e.schemeCategory10,r=t.data_color,s=[];return function(t){var e,i=t.id||t.data&&t.data.id||t;return n[i]instanceof Function?e=n[i](t):n[i]?e=n[i]:(s.indexOf(i)<0&&s.push(i),e=a[s.indexOf(i)%a.length],n[i]=e),r instanceof Function?r(e,t):e}},m.generateLevelColor=function(){var t=this.config,n=t.color_pattern,e=t.color_threshold,a="value"===e.unit,r=e.values&&e.values.length?e.values:[],s=e.max||100;return C(t.color_threshold)?function(t){var e,i=n[n.length-1];for(e=0;e<r.length;e++)if((a?t:100*t/s)<r[e]){i=n[e];break}return i}:null},m.getDefaultConfig=function(){var e={bindto:"#chart",svg_classname:void 0,size_width:void 0,size_height:void 0,padding_left:void 0,padding_right:void 0,padding_top:void 0,padding_bottom:void 0,resize_auto:!0,zoom_enabled:!1,zoom_initialRange:void 0,zoom_privileged:!1,zoom_rescale:!1,zoom_onzoom:function(){},zoom_onzoomstart:function(){},zoom_onzoomend:function(){},zoom_x_min:void 0,zoom_x_max:void 0,interaction_brighten:!0,interaction_enabled:!0,onmouseover:function(){},onmouseout:function(){},onresize:function(){},onresized:function(){},oninit:function(){},onrendered:function(){},transition_duration:350,data_x:void 0,data_xs:{},data_xFormat:"%Y-%m-%d",data_xLocaltime:!0,data_xSort:!0,data_idConverter:function(t){return t},data_names:{},data_classes:{},data_groups:[],data_axes:{},data_type:void 0,data_types:{},data_labels:{},data_order:"desc",data_regions:{},data_color:void 0,data_colors:{},data_hide:!1,data_filter:void 0,data_selection_enabled:!1,data_selection_grouped:!1,data_selection_isselectable:function(){return!0},data_selection_multiple:!0,data_selection_draggable:!1,data_onclick:function(){},data_onmouseover:function(){},data_onmouseout:function(){},data_onselected:function(){},data_onunselected:function(){},data_url:void 0,data_headers:void 0,data_json:void 0,data_rows:void 0,data_columns:void 0,data_mimeType:void 0,data_keys:void 0,data_empty_label_text:"",subchart_show:!1,subchart_size_height:60,subchart_axis_x_show:!0,subchart_onbrush:function(){},color_pattern:[],color_threshold:{},legend_show:!0,legend_hide:!1,legend_position:"bottom",legend_inset_anchor:"top-left",legend_inset_x:10,legend_inset_y:0,legend_inset_step:void 0,legend_item_onclick:void 0,legend_item_onmouseover:void 0,legend_item_onmouseout:void 0,legend_equally:!1,legend_padding:0,legend_item_tile_width:10,legend_item_tile_height:10,axis_rotated:!1,axis_x_show:!0,axis_x_type:"indexed",axis_x_localtime:!0,axis_x_categories:[],axis_x_tick_centered:!1,axis_x_tick_format:void 0,axis_x_tick_culling:{},axis_x_tick_culling_max:10,axis_x_tick_count:void 0,axis_x_tick_fit:!0,axis_x_tick_values:null,axis_x_tick_rotate:0,axis_x_tick_outer:!0,axis_x_tick_multiline:!0,axis_x_tick_multilineMax:0,axis_x_tick_width:null,axis_x_max:void 0,axis_x_min:void 0,axis_x_padding:{},axis_x_height:void 0,axis_x_selection:void 0,axis_x_label:{},axis_x_inner:void 0,axis_y_show:!0,axis_y_type:void 0,axis_y_max:void 0,axis_y_min:void 0,axis_y_inverted:!1,axis_y_center:void 0,axis_y_inner:void 0,axis_y_label:{},axis_y_tick_format:void 0,axis_y_tick_outer:!0,axis_y_tick_values:null,axis_y_tick_rotate:0,axis_y_tick_count:void 0,axis_y_tick_time_type:void 0,axis_y_tick_time_interval:void 0,axis_y_padding:{},axis_y_default:void 0,axis_y2_show:!1,axis_y2_max:void 0,axis_y2_min:void 0,axis_y2_inverted:!1,axis_y2_center:void 0,axis_y2_inner:void 0,axis_y2_label:{},axis_y2_tick_format:void 0,axis_y2_tick_outer:!0,axis_y2_tick_values:null,axis_y2_tick_count:void 0,axis_y2_padding:{},axis_y2_default:void 0,grid_x_show:!1,grid_x_type:"tick",grid_x_lines:[],grid_y_show:!1,grid_y_lines:[],grid_y_ticks:10,grid_focus_show:!0,grid_lines_front:!0,point_show:!0,point_r:2.5,point_sensitivity:10,point_focus_expand_enabled:!0,point_focus_expand_r:void 0,point_select_r:void 0,line_connectNull:!1,line_step_type:"step",bar_width:void 0,bar_width_ratio:.6,bar_width_max:void 0,bar_zerobased:!0,bar_space:0,area_zerobased:!0,area_above:!1,pie_label_show:!0,pie_label_format:void 0,pie_label_threshold:.05,pie_label_ratio:void 0,pie_expand:{},pie_expand_duration:50,gauge_fullCircle:!1,gauge_label_show:!0,gauge_labelLine_show:!0,gauge_label_format:void 0,gauge_min:0,gauge_max:100,gauge_startingAngle:-1*Math.PI/2,gauge_label_extents:void 0,gauge_units:void 0,gauge_width:void 0,gauge_arcs_minWidth:5,gauge_expand:{},gauge_expand_duration:50,donut_label_show:!0,donut_label_format:void 0,donut_label_threshold:.05,donut_label_ratio:void 0,donut_width:void 0,donut_title:"",donut_expand:{},donut_expand_duration:50,spline_interpolation_type:"cardinal",regions:[],tooltip_show:!0,tooltip_grouped:!0,tooltip_order:void 0,tooltip_format_title:void 0,tooltip_format_name:void 0,tooltip_format_value:void 0,tooltip_position:void 0,tooltip_contents:function(t,e,i,n){return this.getTooltipContent?this.getTooltipContent(t,e,i,n):""},tooltip_init_show:!1,tooltip_init_x:0,tooltip_init_position:{top:"0px",left:"50px"},tooltip_onshow:function(){},tooltip_onhide:function(){},title_text:void 0,title_padding:{top:0,right:0,bottom:0,left:0},title_position:"top-center"};return Object.keys(this.additionalConfig).forEach(function(t){e[t]=this.additionalConfig[t]},this),e},m.additionalConfig={},m.loadConfig=function(e){var i,n,a,r=this.config;Object.keys(r).forEach(function(t){i=e,n=t.split("_"),a=function t(){var e=n.shift();return e&&i&&"object"===(void 0===i?"undefined":o(i))&&e in i?(i=i[e],t()):e?void 0:i}(),k(a)&&(r[t]=a)})},m.convertUrlToData=function(t,e,i,n,a){var r,s,o=this,c=e||"csv";"json"===c?(r=o.d3.json,s=o.convertJsonToData):(r="tsv"===c?o.d3.tsv:o.d3.csv,s=o.convertXsvToData),r(t,i).then(function(t){a.call(o,s.call(o,t,n))}).catch(function(t){throw t})},m.convertXsvToData=function(t){var e=t.columns;return 0===t.length?{keys:e,rows:[e.reduce(function(t,e){return Object.assign(t,(a=null,(n=e)in(i={})?Object.defineProperty(i,n,{value:a,enumerable:!0,configurable:!0,writable:!0}):i[n]=a,i));var i,n,a},{})]}:{keys:e,rows:[].concat(t)}},m.convertJsonToData=function(e,t){var a,i,r=this,s=[];return t?(t.x?(a=t.value.concat(t.x),r.config.data_x=t.x):a=t.value,s.push(a),e.forEach(function(i){var n=[];a.forEach(function(t){var e=r.findValueInJson(i,t);v(e)&&(e=null),n.push(e)}),s.push(n)}),i=r.convertRowsToData(s)):(Object.keys(e).forEach(function(t){s.push([t].concat(e[t]))}),i=r.convertColumnsToData(s)),i},m.findValueInJson=function(t,e){for(var i=(e=(e=e.replace(/\[(\w+)\]/g,".$1")).replace(/^\./,"")).split("."),n=0;n<i.length;++n){var a=i[n];if(!(a in t))return;t=t[a]}return t},m.convertRowsToData=function(t){for(var e=[],i=t[0],n=1;n<t.length;n++){for(var a={},r=0;r<t[n].length;r++){if(v(t[n][r]))throw new Error("Source data is missing a component at ("+n+","+r+")!");a[i[r]]=t[n][r]}e.push(a)}return{keys:i,rows:e}},m.convertColumnsToData=function(t){for(var e=[],i=[],n=0;n<t.length;n++){for(var a=t[n][0],r=1;r<t[n].length;r++){if(v(e[r-1])&&(e[r-1]={}),v(t[n][r]))throw new Error("Source data is missing a component at ("+n+","+r+")!");e[r-1][a]=t[n][r]}i.push(a)}return{keys:i,rows:e}},m.convertDataToTargets=function(t,n){var e,i,a,r,c=this,d=c.config;return u(t)?r=Object.keys(t[0]):(r=t.keys,t=t.rows),i=r.filter(c.isNotX,c),a=r.filter(c.isX,c),i.forEach(function(i){var e=c.getXKey(i);c.isCustomX()||c.isTimeSeries()?0<=a.indexOf(e)?c.data.xs[i]=(n&&c.data.xs[i]?c.data.xs[i]:[]).concat(t.map(function(t){return t[e]}).filter(P).map(function(t,e){return c.generateTargetX(t,i,e)})):d.data_x?c.data.xs[i]=c.getOtherTargetXs():C(d.data_xs)&&(c.data.xs[i]=c.getXValuesOfXKey(e,c.data.targets)):c.data.xs[i]=t.map(function(t,e){return e})}),i.forEach(function(t){if(!c.data.xs[t])throw new Error('x is not defined for id = "'+t+'".')}),(e=i.map(function(r,s){var o=d.data_idConverter(r);return{id:o,id_org:r,values:t.map(function(t,e){var i,n=t[c.getXKey(r)],a=null===t[r]||isNaN(t[r])?null:+t[r];return c.isCustomX()&&c.isCategorized()&&!v(n)?(0===s&&0===e&&(d.axis_x_categories=[]),-1===(i=d.axis_x_categories.indexOf(n))&&(i=d.axis_x_categories.length,d.axis_x_categories.push(n))):i=c.generateTargetX(n,r,e),(v(t[r])||c.data.xs[r].length<=e)&&(i=void 0),{x:i,value:a,id:o}}).filter(function(t){return k(t.x)})}})).forEach(function(t){var e;d.data_xSort&&(t.values=t.values.sort(function(t,e){return(t.x||0===t.x?t.x:1/0)-(e.x||0===e.x?e.x:1/0)})),e=0,t.values.forEach(function(t){t.index=e++}),c.data.xs[t.id].sort(function(t,e){return t-e})}),c.hasNegativeValue=c.hasNegativeValueInTargets(e),c.hasPositiveValue=c.hasPositiveValueInTargets(e),d.data_type&&c.setTargetType(c.mapToIds(e).filter(function(t){return!(t in d.data_types)}),d.data_type),e.forEach(function(t){c.addCache(t.id_org,t)}),e},m.isX=function(t){var e=this.config;return e.data_x&&t===e.data_x||C(e.data_xs)&&f(e.data_xs,t)},m.isNotX=function(t){return!this.isX(t)},m.getXKey=function(t){var e=this.config;return e.data_x?e.data_x:C(e.data_xs)?e.data_xs[t]:null},m.getXValuesOfXKey=function(e,t){var i,n=this;return(t&&C(t)?n.mapToIds(t):[]).forEach(function(t){n.getXKey(t)===e&&(i=n.data.xs[t])}),i},m.getXValue=function(t,e){return t in this.data.xs&&this.data.xs[t]&&P(this.data.xs[t][e])?this.data.xs[t][e]:e},m.getOtherTargetXs=function(){var t=Object.keys(this.data.xs);return t.length?this.data.xs[t[0]]:null},m.getOtherTargetX=function(t){var e=this.getOtherTargetXs();return e&&t<e.length?e[t]:null},m.addXs=function(e){var i=this;Object.keys(e).forEach(function(t){i.config.data_xs[t]=e[t]})},m.addName=function(t){var e;return t&&(e=this.config.data_names[t.id],t.name=void 0!==e?e:t.id),t},m.getValueOnIndex=function(t,e){var i=t.filter(function(t){return t.index===e});return i.length?i[0]:null},m.updateTargetX=function(t,n){var a=this;t.forEach(function(i){i.values.forEach(function(t,e){t.x=a.generateTargetX(n[e],i.id,e)}),a.data.xs[i.id]=n})},m.updateTargetXs=function(t,e){var i=this;t.forEach(function(t){e[t.id]&&i.updateTargetX([t],e[t.id])})},m.generateTargetX=function(t,e,i){var n=this;return n.isTimeSeries()?t?n.parseDate(t):n.parseDate(n.getXValue(e,i)):n.isCustomX()&&!n.isCategorized()?P(t)?+t:n.getXValue(e,i):i},m.cloneTarget=function(t){return{id:t.id,id_org:t.id_org,values:t.values.map(function(t){return{x:t.x,value:t.value,id:t.id}})}},m.getMaxDataCount=function(){return this.d3.max(this.data.targets,function(t){return t.values.length})},m.mapToIds=function(t){return t.map(function(t){return t.id})},m.mapToTargetIds=function(t){return t?[].concat(t):this.mapToIds(this.data.targets)},m.hasTarget=function(t,e){var i,n=this.mapToIds(t);for(i=0;i<n.length;i++)if(n[i]===e)return!0;return!1},m.isTargetToShow=function(t){return this.hiddenTargetIds.indexOf(t)<0},m.isLegendToShow=function(t){return this.hiddenLegendIds.indexOf(t)<0},m.filterTargetsToShow=function(t){var e=this;return t.filter(function(t){return e.isTargetToShow(t.id)})},m.mapTargetsToUniqueXs=function(t){var e=this.d3.set(this.d3.merge(t.map(function(t){return t.values.map(function(t){return+t.x})}))).values();return(e=this.isTimeSeries()?e.map(function(t){return new Date(+t)}):e.map(function(t){return+t})).sort(function(t,e){return t<e?-1:e<t?1:e<=t?0:NaN})},m.addHiddenTargetIds=function(t){t=t instanceof Array?t:new Array(t);for(var e=0;e<t.length;e++)this.hiddenTargetIds.indexOf(t[e])<0&&(this.hiddenTargetIds=this.hiddenTargetIds.concat(t[e]))},m.removeHiddenTargetIds=function(e){this.hiddenTargetIds=this.hiddenTargetIds.filter(function(t){return e.indexOf(t)<0})},m.addHiddenLegendIds=function(t){t=t instanceof Array?t:new Array(t);for(var e=0;e<t.length;e++)this.hiddenLegendIds.indexOf(t[e])<0&&(this.hiddenLegendIds=this.hiddenLegendIds.concat(t[e]))},m.removeHiddenLegendIds=function(e){this.hiddenLegendIds=this.hiddenLegendIds.filter(function(t){return e.indexOf(t)<0})},m.getValuesAsIdKeyed=function(t){var i={};return t.forEach(function(e){i[e.id]=[],e.values.forEach(function(t){i[e.id].push(t.value)})}),i},m.checkValueInTargets=function(t,e){var i,n,a,r=Object.keys(t);for(i=0;i<r.length;i++)for(a=t[r[i]].values,n=0;n<a.length;n++)if(e(a[n].value))return!0;return!1},m.hasNegativeValueInTargets=function(t){return this.checkValueInTargets(t,function(t){return t<0})},m.hasPositiveValueInTargets=function(t){return this.checkValueInTargets(t,function(t){return 0<t})},m.isOrderDesc=function(){var t=this.config;return"string"==typeof t.data_order&&"desc"===t.data_order.toLowerCase()},m.isOrderAsc=function(){var t=this.config;return"string"==typeof t.data_order&&"asc"===t.data_order.toLowerCase()},m.getOrderFunction=function(){var t=this.config,a=this.isOrderAsc(),e=this.isOrderDesc();if(a||e){var r=function(t,e){return t+Math.abs(e.value)};return function(t,e){var i=t.values.reduce(r,0),n=e.values.reduce(r,0);return a?n-i:i-n}}if(h(t.data_order))return t.data_order;if(u(t.data_order)){var i=t.data_order;return function(t,e){return i.indexOf(t.id)-i.indexOf(e.id)}}},m.orderTargets=function(t){var e=this.getOrderFunction();return e&&t.sort(e),t},m.filterByX=function(t,e){return this.d3.merge(t.map(function(t){return t.values})).filter(function(t){return t.x-e==0})},m.filterRemoveNull=function(t){return t.filter(function(t){return P(t.value)})},m.filterByXDomain=function(t,e){return t.map(function(t){return{id:t.id,id_org:t.id_org,values:t.values.filter(function(t){return e[0]<=t.x&&t.x<=e[1]})}})},m.hasDataLabel=function(){var t=this.config;return!("boolean"!=typeof t.data_labels||!t.data_labels)||!("object"!==o(t.data_labels)||!C(t.data_labels))},m.getDataLabelLength=function(t,e,i){var n=this,a=[0,0];return n.selectChart.select("svg").selectAll(".dummy").data([t,e]).enter().append("text").text(function(t){return n.dataLabelFormat(t.id)(t)}).each(function(t,e){a[e]=1.3*this.getBoundingClientRect()[i]}).remove(),a},m.isNoneArc=function(t){return this.hasTarget(this.data.targets,t.id)},m.isArc=function(t){return"data"in t&&this.hasTarget(this.data.targets,t.data.id)},m.findClosestFromTargets=function(t,e){var i,n=this;return i=t.map(function(t){return n.findClosest(t.values,e)}),n.findClosest(i,e)},m.findClosest=function(t,i){var n,a=this,r=a.config.point_sensitivity;return t.filter(function(t){return t&&a.isBarType(t.id)}).forEach(function(t){var e=a.main.select("."+Y.bars+a.getTargetSelectorSuffix(t.id)+" ."+Y.bar+"-"+t.index).node();!n&&a.isWithinBar(a.d3.mouse(e),e)&&(n=t)}),t.filter(function(t){return t&&!a.isBarType(t.id)}).forEach(function(t){var e=a.dist(t,i);e<r&&(r=e,n=t)}),n},m.dist=function(t,e){var i=this.config,n=i.axis_rotated?1:0,a=i.axis_rotated?0:1,r=this.circleY(t,t.index),s=this.x(t.x);return Math.sqrt(Math.pow(s-e[n],2)+Math.pow(r-e[a],2))},m.convertValuesToStep=function(t){var e,i=[].concat(t);if(!this.isCategorized())return t;for(e=t.length+1;0<e;e--)i[e]=i[e-1];return i[0]={x:i[0].x-1,value:i[0].value,id:i[0].id},i[t.length+1]={x:i[t.length].x+1,value:i[t.length].value,id:i[t.length].id},i},m.updateDataAttributes=function(t,e){var i=this.config["data_"+t];return void 0===e||(Object.keys(e).forEach(function(t){i[t]=e[t]}),this.redraw({withLegend:!0})),i},m.load=function(i,n){var a=this;i&&(n.filter&&(i=i.filter(n.filter)),(n.type||n.types)&&i.forEach(function(t){var e=n.types&&n.types[t.id]?n.types[t.id]:n.type;a.setTargetType(t.id,e)}),a.data.targets.forEach(function(t){for(var e=0;e<i.length;e++)if(t.id===i[e].id){t.values=i[e].values,i.splice(e,1);break}}),a.data.targets=a.data.targets.concat(i)),a.updateTargets(a.data.targets),a.redraw({withUpdateOrgXDomain:!0,withUpdateXDomain:!0,withLegend:!0}),n.done&&n.done()},m.loadFromArgs=function(e){var i=this;e.data?i.load(i.convertDataToTargets(e.data),e):e.url?i.convertUrlToData(e.url,e.mimeType,e.headers,e.keys,function(t){i.load(i.convertDataToTargets(t),e)}):e.json?i.load(i.convertDataToTargets(i.convertJsonToData(e.json,e.keys)),e):e.rows?i.load(i.convertDataToTargets(i.convertRowsToData(e.rows)),e):e.columns?i.load(i.convertDataToTargets(i.convertColumnsToData(e.columns)),e):i.load(null,e)},m.unload=function(t,e){var i=this;e||(e=function(){}),(t=t.filter(function(t){return i.hasTarget(i.data.targets,t)}))&&0!==t.length?(i.svg.selectAll(t.map(function(t){return i.selectorTarget(t)})).transition().style("opacity",0).remove().call(i.endall,e),t.forEach(function(e){i.withoutFadeIn[e]=!1,i.legend&&i.legend.selectAll("."+Y.legendItem+i.getTargetSelectorSuffix(e)).remove(),i.data.targets=i.data.targets.filter(function(t){return t.id!==e})})):e()},m.getYDomainMin=function(t){var e,i,n,a,r,s,o=this,c=o.config,d=o.mapToIds(t),u=o.getValuesAsIdKeyed(t);if(0<c.data_groups.length)for(s=o.hasNegativeValueInTargets(t),e=0;e<c.data_groups.length;e++)if(0!==(a=c.data_groups[e].filter(function(t){return 0<=d.indexOf(t)})).length)for(n=a[0],s&&u[n]&&u[n].forEach(function(t,e){u[n][e]=t<0?t:0}),i=1;i<a.length;i++)r=a[i],u[r]&&u[r].forEach(function(t,e){o.axis.getId(r)!==o.axis.getId(n)||!u[n]||s&&0<+t||(u[n][e]+=+t)});return o.d3.min(Object.keys(u).map(function(t){return o.d3.min(u[t])}))},m.getYDomainMax=function(t){var e,i,n,a,r,s,o=this,c=o.config,d=o.mapToIds(t),u=o.getValuesAsIdKeyed(t);if(0<c.data_groups.length)for(s=o.hasPositiveValueInTargets(t),e=0;e<c.data_groups.length;e++)if(0!==(a=c.data_groups[e].filter(function(t){return 0<=d.indexOf(t)})).length)for(n=a[0],s&&u[n]&&u[n].forEach(function(t,e){u[n][e]=0<t?t:0}),i=1;i<a.length;i++)r=a[i],u[r]&&u[r].forEach(function(t,e){o.axis.getId(r)!==o.axis.getId(n)||!u[n]||s&&+t<0||(u[n][e]+=+t)});return o.d3.max(Object.keys(u).map(function(t){return o.d3.max(u[t])}))},m.getYDomain=function(t,e,i){var n,a,r,s,o,c,d,u,l,h,g=this,f=g.config,p=t.filter(function(t){return g.axis.getId(t.id)===e}),_=i?g.filterByXDomain(p,i):p,x="y2"===e?f.axis_y2_min:f.axis_y_min,y="y2"===e?f.axis_y2_max:f.axis_y_max,m=g.getYDomainMin(_),S=g.getYDomainMax(_),w="y2"===e?f.axis_y2_center:f.axis_y_center,v=g.hasType("bar",_)&&f.bar_zerobased||g.hasType("area",_)&&f.area_zerobased,b="y2"===e?f.axis_y2_inverted:f.axis_y_inverted,A=g.hasDataLabel()&&f.axis_rotated,T=g.hasDataLabel()&&!f.axis_rotated;return m=P(x)?x:P(y)?m<y?m:y-10:m,S=P(y)?y:P(x)?x<S?S:x+10:S,0===_.length?"y2"===e?g.y2.domain():g.y.domain():(isNaN(m)&&(m=0),isNaN(S)&&(S=m),m===S&&(m<0?S=0:m=0),l=0<=m&&0<=S,h=m<=0&&S<=0,(P(x)&&l||P(y)&&h)&&(v=!1),v&&(l&&(m=0),h&&(S=0)),r=s=.1*(a=Math.abs(S-m)),void 0!==w&&(S=w+(o=Math.max(Math.abs(m),Math.abs(S))),m=w-o),A?(c=g.getDataLabelLength(m,S,"width"),d=D(g.y.range()),r+=a*((u=[c[0]/d,c[1]/d])[1]/(1-u[0]-u[1])),s+=a*(u[0]/(1-u[0]-u[1]))):T&&(c=g.getDataLabelLength(m,S,"height"),r+=g.axis.convertPixelsToAxisPadding(c[1],a),s+=g.axis.convertPixelsToAxisPadding(c[0],a)),"y"===e&&C(f.axis_y_padding)&&(r=g.axis.getPadding(f.axis_y_padding,"top",r,a),s=g.axis.getPadding(f.axis_y_padding,"bottom",s,a)),"y2"===e&&C(f.axis_y2_padding)&&(r=g.axis.getPadding(f.axis_y2_padding,"top",r,a),s=g.axis.getPadding(f.axis_y2_padding,"bottom",s,a)),v&&(l&&(s=m),h&&(r=-S)),n=[m-s,S+r],b?n.reverse():n)},m.getXDomainMin=function(t){var e=this,i=e.config;return k(i.axis_x_min)?e.isTimeSeries()?this.parseDate(i.axis_x_min):i.axis_x_min:e.d3.min(t,function(t){return e.d3.min(t.values,function(t){return t.x})})},m.getXDomainMax=function(t){var e=this,i=e.config;return k(i.axis_x_max)?e.isTimeSeries()?this.parseDate(i.axis_x_max):i.axis_x_max:e.d3.max(t,function(t){return e.d3.max(t.values,function(t){return t.x})})},m.getXDomainPadding=function(t){var e,i,n,a,r=this.config,s=t[1]-t[0];return i=this.isCategorized()?0:this.hasType("bar")?1<(e=this.getMaxDataCount())?s/(e-1)/2:.5:.01*s,"object"===o(r.axis_x_padding)&&C(r.axis_x_padding)?(n=P(r.axis_x_padding.left)?r.axis_x_padding.left:i,a=P(r.axis_x_padding.right)?r.axis_x_padding.right:i):n=a="number"==typeof r.axis_x_padding?r.axis_x_padding:i,{left:n,right:a}},m.getXDomain=function(t){var e=this,i=[e.getXDomainMin(t),e.getXDomainMax(t)],n=i[0],a=i[1],r=e.getXDomainPadding(i),s=0,o=0;return n-a!=0||e.isCategorized()||(e.isTimeSeries()?(n=new Date(.5*n.getTime()),a=new Date(1.5*a.getTime())):(n=0===n?1:.5*n,a=0===a?-1:1.5*a)),(n||0===n)&&(s=e.isTimeSeries()?new Date(n.getTime()-r.left):n-r.left),(a||0===a)&&(o=e.isTimeSeries()?new Date(a.getTime()+r.right):a+r.right),[s,o]},m.updateXDomain=function(t,e,i,n,a){var r=this,s=r.config;return i&&(r.x.domain(a||r.d3.extent(r.getXDomain(t))),r.orgXDomain=r.x.domain(),s.zoom_enabled&&r.zoom.update(),r.subX.domain(r.x.domain()),r.brush&&r.brush.updateScale(r.subX)),e&&r.x.domain(a||(!r.brush||r.brush.empty()?r.orgXDomain:r.brush.selectionAsValue())),n&&r.x.domain(r.trimXDomain(r.x.orgDomain())),r.x.domain()},m.trimXDomain=function(t){var e=this.getZoomDomain(),i=e[0],n=e[1];return t[0]<=i&&(t[1]=+t[1]+(i-t[0]),t[0]=i),n<=t[1]&&(t[0]=+t[0]-(t[1]-n),t[1]=n),t},m.drag=function(t){var e,i,n,a,h,g,f,p,_=this,r=_.config,s=_.main,x=_.d3;_.hasArcType()||r.data_selection_enabled&&r.data_selection_multiple&&(e=_.dragStart[0],i=_.dragStart[1],n=t[0],a=t[1],h=Math.min(e,n),g=Math.max(e,n),f=r.data_selection_grouped?_.margin.top:Math.min(i,a),p=r.data_selection_grouped?_.height:Math.max(i,a),s.select("."+Y.dragarea).attr("x",h).attr("y",f).attr("width",g-h).attr("height",p-f),s.selectAll("."+Y.shapes).selectAll("."+Y.shape).filter(function(t){return r.data_selection_isselectable(t)}).each(function(t,e){var i,n,a,r,s,o,c=x.select(this),d=c.classed(Y.SELECTED),u=c.classed(Y.INCLUDED),l=!1;if(c.classed(Y.circle))i=1*c.attr("cx"),n=1*c.attr("cy"),s=_.togglePoint,l=h<i&&i<g&&f<n&&n<p;else{if(!c.classed(Y.bar))return;i=(o=y(this)).x,n=o.y,a=o.width,r=o.height,s=_.togglePath,l=!(g<i||i+a<h||p<n||n+r<f)}l^u&&(c.classed(Y.INCLUDED,!u),c.classed(Y.SELECTED,!d),s.call(_,!d,c,t,e))}))},m.dragstart=function(t){var e=this,i=e.config;e.hasArcType()||i.data_selection_enabled&&(e.dragStart=t,e.main.select("."+Y.chart).append("rect").attr("class",Y.dragarea).style("opacity",.1),e.dragging=!0)},m.dragend=function(){var t=this,e=t.config;t.hasArcType()||e.data_selection_enabled&&(t.main.select("."+Y.dragarea).transition().duration(100).style("opacity",0).remove(),t.main.selectAll("."+Y.shape).classed(Y.INCLUDED,!1),t.dragging=!1)},m.getYFormat=function(t){var n=this,a=t&&!n.hasType("gauge")?n.defaultArcValueFormat:n.yFormat,r=t&&!n.hasType("gauge")?n.defaultArcValueFormat:n.y2Format;return function(t,e,i){return("y2"===n.axis.getId(i)?r:a).call(n,t,e)}},m.yFormat=function(t){var e=this.config;return(e.axis_y_tick_format?e.axis_y_tick_format:this.defaultValueFormat)(t)},m.y2Format=function(t){var e=this.config;return(e.axis_y2_tick_format?e.axis_y2_tick_format:this.defaultValueFormat)(t)},m.defaultValueFormat=function(t){return P(t)?+t:""},m.defaultArcValueFormat=function(t,e){return(100*e).toFixed(1)+"%"},m.dataLabelFormat=function(t){var e=this.config.data_labels,i=function(t){return P(t)?+t:""};return"function"==typeof e.format?e.format:"object"===o(e.format)?e.format[t]?!0===e.format[t]?i:e.format[t]:function(){return""}:i},m.initGrid=function(){var t=this,e=t.config,i=t.d3;t.grid=t.main.append("g").attr("clip-path",t.clipPathForGrid).attr("class",Y.grid),e.grid_x_show&&t.grid.append("g").attr("class",Y.xgrids),e.grid_y_show&&t.grid.append("g").attr("class",Y.ygrids),e.grid_focus_show&&t.grid.append("g").attr("class",Y.xgridFocus).append("line").attr("class",Y.xgridFocus),t.xgrid=i.selectAll([]),e.grid_lines_front||t.initGridLines()},m.initGridLines=function(){var t=this,e=t.d3;t.gridLines=t.main.append("g").attr("clip-path",t.clipPathForGrid).attr("class",Y.grid+" "+Y.gridLines),t.gridLines.append("g").attr("class",Y.xgridLines),t.gridLines.append("g").attr("class",Y.ygridLines),t.xgridLines=e.selectAll([])},m.updateXGrid=function(t){var e=this,i=e.config,n=e.d3,a=e.generateGridData(i.grid_x_type,e.x),r=e.isCategorized()?e.xAxis.tickOffset():0;e.xgridAttr=i.axis_rotated?{x1:0,x2:e.width,y1:function(t){return e.x(t)-r},y2:function(t){return e.x(t)-r}}:{x1:function(t){return e.x(t)+r},x2:function(t){return e.x(t)+r},y1:0,y2:e.height},e.xgridAttr.opacity=function(){return+n.select(this).attr(i.axis_rotated?"y1":"x1")===(i.axis_rotated?e.height:0)?0:1};var s=e.main.select("."+Y.xgrids).selectAll("."+Y.xgrid).data(a),o=s.enter().append("line").attr("class",Y.xgrid).attr("x1",e.xgridAttr.x1).attr("x2",e.xgridAttr.x2).attr("y1",e.xgridAttr.y1).attr("y2",e.xgridAttr.y2).style("opacity",0);e.xgrid=o.merge(s),t||e.xgrid.attr("x1",e.xgridAttr.x1).attr("x2",e.xgridAttr.x2).attr("y1",e.xgridAttr.y1).attr("y2",e.xgridAttr.y2).style("opacity",e.xgridAttr.opacity),s.exit().remove()},m.updateYGrid=function(){var t=this,e=t.config,i=t.yAxis.tickValues()||t.y.ticks(e.grid_y_ticks),n=t.main.select("."+Y.ygrids).selectAll("."+Y.ygrid).data(i),a=n.enter().append("line").attr("class",Y.ygrid);t.ygrid=a.merge(n),t.ygrid.attr("x1",e.axis_rotated?t.y:0).attr("x2",e.axis_rotated?t.y:t.width).attr("y1",e.axis_rotated?0:t.y).attr("y2",e.axis_rotated?t.height:t.y),n.exit().remove(),t.smoothLines(t.ygrid,"grid")},m.gridTextAnchor=function(t){return t.position?t.position:"end"},m.gridTextDx=function(t){return"start"===t.position?4:"middle"===t.position?0:-4},m.xGridTextX=function(t){return"start"===t.position?-this.height:"middle"===t.position?-this.height/2:0},m.yGridTextX=function(t){return"start"===t.position?0:"middle"===t.position?this.width/2:this.width},m.updateGrid=function(t){var e,i,n,a,r=this,s=r.main,o=r.config,c=r.xv.bind(r),d=r.yv.bind(r),u=r.xGridTextX.bind(r),l=r.yGridTextX.bind(r);r.grid.style("visibility",r.hasArcType()?"hidden":"visible"),s.select("line."+Y.xgridFocus).style("visibility","hidden"),o.grid_x_show&&r.updateXGrid(),(i=(e=s.select("."+Y.xgridLines).selectAll("."+Y.xgridLine).data(o.grid_x_lines)).enter().append("g").attr("class",function(t){return Y.xgridLine+(t.class?" "+t.class:"")})).append("line").attr("x1",o.axis_rotated?0:c).attr("x2",o.axis_rotated?r.width:c).attr("y1",o.axis_rotated?c:0).attr("y2",o.axis_rotated?c:r.height).style("opacity",0),i.append("text").attr("text-anchor",r.gridTextAnchor).attr("transform",o.axis_rotated?"":"rotate(-90)").attr("x",o.axis_rotated?l:u).attr("y",c).attr("dx",r.gridTextDx).attr("dy",-5).style("opacity",0),r.xgridLines=i.merge(e),e.exit().transition().duration(t).style("opacity",0).remove(),o.grid_y_show&&r.updateYGrid(),(a=(n=s.select("."+Y.ygridLines).selectAll("."+Y.ygridLine).data(o.grid_y_lines)).enter().append("g").attr("class",function(t){return Y.ygridLine+(t.class?" "+t.class:"")})).append("line").attr("x1",o.axis_rotated?d:0).attr("x2",o.axis_rotated?d:r.width).attr("y1",o.axis_rotated?0:d).attr("y2",o.axis_rotated?r.height:d).style("opacity",0),a.append("text").attr("text-anchor",r.gridTextAnchor).attr("transform",o.axis_rotated?"rotate(-90)":"").attr("x",o.axis_rotated?u:l).attr("y",d).attr("dx",r.gridTextDx).attr("dy",-5).style("opacity",0),r.ygridLines=a.merge(n),r.ygridLines.select("line").transition().duration(t).attr("x1",o.axis_rotated?d:0).attr("x2",o.axis_rotated?d:r.width).attr("y1",o.axis_rotated?0:d).attr("y2",o.axis_rotated?r.height:d).style("opacity",1),r.ygridLines.select("text").transition().duration(t).attr("x",o.axis_rotated?r.xGridTextX.bind(r):r.yGridTextX.bind(r)).attr("y",d).text(function(t){return t.text}).style("opacity",1),n.exit().transition().duration(t).style("opacity",0).remove()},m.redrawGrid=function(t,e){var i=this,n=i.config,a=i.xv.bind(i),r=i.xgridLines.select("line"),s=i.xgridLines.select("text");return[(t?r.transition(e):r).attr("x1",n.axis_rotated?0:a).attr("x2",n.axis_rotated?i.width:a).attr("y1",n.axis_rotated?a:0).attr("y2",n.axis_rotated?a:i.height).style("opacity",1),(t?s.transition(e):s).attr("x",n.axis_rotated?i.yGridTextX.bind(i):i.xGridTextX.bind(i)).attr("y",a).text(function(t){return t.text}).style("opacity",1)]},m.showXGridFocus=function(t){var e=this,i=e.config,n=t.filter(function(t){return t&&P(t.value)}),a=e.main.selectAll("line."+Y.xgridFocus),r=e.xx.bind(e);i.tooltip_show&&(e.hasType("scatter")||e.hasArcType()||(a.style("visibility","visible").data([n[0]]).attr(i.axis_rotated?"y1":"x1",r).attr(i.axis_rotated?"y2":"x2",r),e.smoothLines(a,"grid")))},m.hideXGridFocus=function(){this.main.select("line."+Y.xgridFocus).style("visibility","hidden")},m.updateXgridFocus=function(){var t=this.config;this.main.select("line."+Y.xgridFocus).attr("x1",t.axis_rotated?0:-10).attr("x2",t.axis_rotated?this.width:-10).attr("y1",t.axis_rotated?-10:0).attr("y2",t.axis_rotated?-10:this.height)},m.generateGridData=function(t,e){var i,n,a,r,s=[],o=this.main.select("."+Y.axisX).selectAll(".tick").size();if("year"===t)for(n=(i=this.getXDomain())[0].getFullYear(),a=i[1].getFullYear(),r=n;r<=a;r++)s.push(new Date(r+"-01-01 00:00:00"));else(s=e.ticks(10)).length>o&&(s=s.filter(function(t){return(""+t).indexOf(".")<0}));return s},m.getGridFilterToRemove=function(t){return t?function(e){var i=!1;return[].concat(t).forEach(function(t){("value"in t&&e.value===t.value||"class"in t&&e.class===t.class)&&(i=!0)}),i}:function(){return!0}},m.removeGridLines=function(t,e){var i=this.config,n=this.getGridFilterToRemove(t),a=function(t){return!n(t)},r=e?Y.xgridLines:Y.ygridLines,s=e?Y.xgridLine:Y.ygridLine;this.main.select("."+r).selectAll("."+s).filter(n).transition().duration(i.transition_duration).style("opacity",0).remove(),e?i.grid_x_lines=i.grid_x_lines.filter(a):i.grid_y_lines=i.grid_y_lines.filter(a)},m.initEventRect=function(){var t=this,e=t.config;t.main.select("."+Y.chart).append("g").attr("class",Y.eventRects).style("fill-opacity",0),t.eventRect=t.main.select("."+Y.eventRects).append("rect").attr("class",Y.eventRect),e.zoom_enabled&&t.zoom&&(t.eventRect.call(t.zoom).on("dblclick.zoom",null),e.zoom_initialRange&&t.eventRect.transition().duration(0).call(t.zoom.transform,t.zoomTransform(e.zoom_initialRange)))},m.redrawEventRect=function(){var t,e,a=this,r=a.d3,s=a.config;function o(){a.svg.select("."+Y.eventRect).style("cursor",null),a.hideXGridFocus(),a.hideTooltip(),a.unexpandCircles(),a.unexpandBars()}t=a.width,e=a.height,a.main.select("."+Y.eventRects).style("cursor",s.zoom_enabled?s.axis_rotated?"ns-resize":"ew-resize":null),a.eventRect.attr("x",0).attr("y",0).attr("width",t).attr("height",e).on("mouseout",s.interaction_enabled?function(){s&&(a.hasArcType()||o())}:null).on("mousemove",s.interaction_enabled?function(){var t,e,i,n;a.dragging||a.hasArcType(t)||(t=a.filterTargetsToShow(a.data.targets),e=r.mouse(this),i=a.findClosestFromTargets(t,e),!a.mouseover||i&&i.id===a.mouseover.id||(s.data_onmouseout.call(a.api,a.mouseover),a.mouseover=void 0),i?(n=(a.isScatterType(i)||!s.tooltip_grouped?[i]:a.filterByX(t,i.x)).map(function(t){return a.addName(t)}),a.showTooltip(n,this),s.point_focus_expand_enabled&&(a.unexpandCircles(),n.forEach(function(t){a.expandCircles(t.index,t.id,!1)})),a.expandBars(i.index,i.id,!0),a.showXGridFocus(n),(a.isBarType(i.id)||a.dist(i,e)<s.point_sensitivity)&&(a.svg.select("."+Y.eventRect).style("cursor","pointer"),a.mouseover||(s.data_onmouseover.call(a.api,i),a.mouseover=i))):o())}:null).on("click",s.interaction_enabled?function(){var t,e,i;a.hasArcType(t)||(t=a.filterTargetsToShow(a.data.targets),e=r.mouse(this),(i=a.findClosestFromTargets(t,e))&&(a.isBarType(i.id)||a.dist(i,e)<s.point_sensitivity)&&(a.isScatterType(i)||!s.data_selection_grouped?[i]:a.filterByX(t,i.x)).forEach(function(t){a.main.selectAll("."+Y.shapes+a.getTargetSelectorSuffix(t.id)).selectAll("."+Y.shape+"-"+t.index).each(function(){(s.data_selection_grouped||a.isWithinShape(this,t))&&(a.toggleShape(this,t,t.index),s.data_onclick.call(a.api,t,this))})}))}:null).call(s.interaction_enabled&&s.data_selection_draggable&&a.drag?r.drag().on("drag",function(){a.drag(r.mouse(this))}).on("start",function(){a.dragstart(r.mouse(this))}).on("end",function(){a.dragend()}):function(){})},m.getMousePosition=function(t){return[this.x(t.x),this.getYScale(t.id)(t.value)]},m.dispatchEvent=function(t,e){var i="."+Y.eventRect,n=this.main.select(i).node(),a=n.getBoundingClientRect(),r=a.left+(e?e[0]:0),s=a.top+(e?e[1]:0),o=document.createEvent("MouseEvents");o.initMouseEvent(t,!0,!0,window,0,r,s,r,s,!1,!1,!1,!1,0,null),n.dispatchEvent(o)},m.initLegend=function(){var t=this;if(t.legendItemTextBox={},t.legendHasRendered=!1,t.legend=t.svg.append("g").attr("transform",t.getTranslate("legend")),!t.config.legend_show)return t.legend.style("visibility","hidden"),void(t.hiddenLegendIds=t.mapToIds(t.data.targets));t.updateLegendWithDefaults()},m.updateLegendWithDefaults=function(){this.updateLegend(this.mapToIds(this.data.targets),{withTransform:!1,withTransitionForTransform:!1,withTransition:!1})},m.updateSizeForLegend=function(t,e){var i=this,n=i.config,a={top:i.isLegendTop?i.getCurrentPaddingTop()+n.legend_inset_y****:i.currentHeight-t-i.getCurrentPaddingBottom()-n.legend_inset_y,left:i.isLegendLeft?i.getCurrentPaddingLeft()+n.legend_inset_x+.5:i.currentWidth-e-i.getCurrentPaddingRight()-n.legend_inset_x+.5};i.margin3={top:i.isLegendRight?0:i.isLegendInset?a.top:i.currentHeight-t,right:NaN,bottom:0,left:i.isLegendRight?i.currentWidth-e:i.isLegendInset?a.left:0}},m.transformLegend=function(t){(t?this.legend.transition():this.legend).attr("transform",this.getTranslate("legend"))},m.updateLegendStep=function(t){this.legendStep=t},m.updateLegendItemWidth=function(t){this.legendItemWidth=t},m.updateLegendItemHeight=function(t){this.legendItemHeight=t},m.getLegendWidth=function(){var t=this;return t.config.legend_show?t.isLegendRight||t.isLegendInset?t.legendItemWidth*(t.legendStep+1):t.currentWidth:0},m.getLegendHeight=function(){var t=this,e=0;return t.config.legend_show&&(e=t.isLegendRight?t.currentHeight:Math.max(20,t.legendItemHeight)*(t.legendStep+1)),e},m.opacityForLegend=function(t){return t.classed(Y.legendItemHidden)?null:1},m.opacityForUnfocusedLegend=function(t){return t.classed(Y.legendItemHidden)?null:.3},m.toggleFocusLegend=function(e,t){var i=this;e=i.mapToTargetIds(e),i.legend.selectAll("."+Y.legendItem).filter(function(t){return 0<=e.indexOf(t)}).classed(Y.legendItemFocused,t).transition().duration(100).style("opacity",function(){return(t?i.opacityForLegend:i.opacityForUnfocusedLegend).call(i,i.d3.select(this))})},m.revertLegend=function(){var t=this,e=t.d3;t.legend.selectAll("."+Y.legendItem).classed(Y.legendItemFocused,!1).transition().duration(100).style("opacity",function(){return t.opacityForLegend(e.select(this))})},m.showLegend=function(t){var e=this,i=e.config;i.legend_show||(i.legend_show=!0,e.legend.style("visibility","visible"),e.legendHasRendered||e.updateLegendWithDefaults()),e.removeHiddenLegendIds(t),e.legend.selectAll(e.selectorLegends(t)).style("visibility","visible").transition().style("opacity",function(){return e.opacityForLegend(e.d3.select(this))})},m.hideLegend=function(t){var e=this,i=e.config;i.legend_show&&g(t)&&(i.legend_show=!1,e.legend.style("visibility","hidden")),e.addHiddenLegendIds(t),e.legend.selectAll(e.selectorLegends(t)).style("opacity",0).style("visibility","hidden")},m.clearLegendItemTextBoxCache=function(){this.legendItemTextBox={}},m.updateLegend=function(p,t,e){var i,n,a,r,s,o,c,d,u,l,h,g,f,_,x,y,m=this,S=m.config,w=4,v=10,b=0,A=0,T=10,P=S.legend_item_tile_width+5,C=0,L={},V={},G={},E=[0],O={},I=0;function R(t,e,i){var n,a,r,s,o=0===i,c=i===p.length-1,d=(r=t,s=e,m.legendItemTextBox[s]||(m.legendItemTextBox[s]=m.getTextRect(r.textContent,Y.legendItem,r)),m.legendItemTextBox[s]),u=d.width+P+(!c||m.isLegendRight||m.isLegendInset?v:0)+S.legend_padding,l=d.height+w,h=m.isLegendRight||m.isLegendInset?l:u,g=m.isLegendRight||m.isLegendInset?m.getLegendHeight():m.getLegendWidth();function f(t,e){e||(n=(g-C-h)/2)<T&&(n=(g-h)/2,C=0,I++),O[t]=I,E[I]=m.isLegendInset?10:n,L[t]=C,C+=h}o&&(A=b=I=C=0),!S.legend_show||m.isLegendToShow(e)?(V[e]=u,G[e]=l,(!b||b<=u)&&(b=u),(!A||A<=l)&&(A=l),a=m.isLegendRight||m.isLegendInset?A:b,S.legend_equally?(Object.keys(V).forEach(function(t){V[t]=b}),Object.keys(G).forEach(function(t){G[t]=A}),(n=(g-a*p.length)/2)<T?(I=C=0,p.forEach(function(t){f(t)})):f(e,!0)):f(e)):V[e]=G[e]=O[e]=L[e]=0}p=p.filter(function(t){return!k(S.data_names[t])||null!==S.data_names[t]}),h=N(t=t||{},"withTransition",!0),g=N(t,"withTransitionForTransform",!0),m.isLegendInset&&(I=S.legend_inset_step?S.legend_inset_step:p.length,m.updateLegendStep(I)),m.isLegendRight?(i=function(t){return b*O[t]},r=function(t){return E[O[t]]+L[t]}):m.isLegendInset?(i=function(t){return b*O[t]+10},r=function(t){return E[O[t]]+L[t]}):(i=function(t){return E[O[t]]+L[t]},r=function(t){return A*O[t]}),n=function(t,e){return i(t,e)+4+S.legend_item_tile_width},s=function(t,e){return r(t,e)+9},a=function(t,e){return i(t,e)},o=function(t,e){return r(t,e)-5},c=function(t,e){return i(t,e)-2},d=function(t,e){return i(t,e)-2+S.legend_item_tile_width},u=function(t,e){return r(t,e)+4},(l=m.legend.selectAll("."+Y.legendItem).data(p).enter().append("g").attr("class",function(t){return m.generateClass(Y.legendItem,t)}).style("visibility",function(t){return m.isLegendToShow(t)?"visible":"hidden"}).style("cursor","pointer").on("click",function(t){S.legend_item_onclick?S.legend_item_onclick.call(m,t):m.d3.event.altKey?(m.api.hide(),m.api.show(t)):(m.api.toggle(t),m.isTargetToShow(t)?m.api.focus(t):m.api.revert())}).on("mouseover",function(t){S.legend_item_onmouseover?S.legend_item_onmouseover.call(m,t):(m.d3.select(this).classed(Y.legendItemFocused,!0),!m.transiting&&m.isTargetToShow(t)&&m.api.focus(t))}).on("mouseout",function(t){S.legend_item_onmouseout?S.legend_item_onmouseout.call(m,t):(m.d3.select(this).classed(Y.legendItemFocused,!1),m.api.revert())})).append("text").text(function(t){return k(S.data_names[t])?S.data_names[t]:t}).each(function(t,e){R(this,t,e)}).style("pointer-events","none").attr("x",m.isLegendRight||m.isLegendInset?n:-200).attr("y",m.isLegendRight||m.isLegendInset?-200:s),l.append("rect").attr("class",Y.legendItemEvent).style("fill-opacity",0).attr("x",m.isLegendRight||m.isLegendInset?a:-200).attr("y",m.isLegendRight||m.isLegendInset?-200:o),l.append("line").attr("class",Y.legendItemTile).style("stroke",m.color).style("pointer-events","none").attr("x1",m.isLegendRight||m.isLegendInset?c:-200).attr("y1",m.isLegendRight||m.isLegendInset?-200:u).attr("x2",m.isLegendRight||m.isLegendInset?d:-200).attr("y2",m.isLegendRight||m.isLegendInset?-200:u).attr("stroke-width",S.legend_item_tile_height),y=m.legend.select("."+Y.legendBackground+" rect"),m.isLegendInset&&0<b&&0===y.size()&&(y=m.legend.insert("g","."+Y.legendItem).attr("class",Y.legendBackground).append("rect")),f=m.legend.selectAll("text").data(p).text(function(t){return k(S.data_names[t])?S.data_names[t]:t}).each(function(t,e){R(this,t,e)}),(h?f.transition():f).attr("x",n).attr("y",s),_=m.legend.selectAll("rect."+Y.legendItemEvent).data(p),(h?_.transition():_).attr("width",function(t){return V[t]}).attr("height",function(t){return G[t]}).attr("x",a).attr("y",o),x=m.legend.selectAll("line."+Y.legendItemTile).data(p),(h?x.transition():x).style("stroke",m.levelColor?function(t){return m.levelColor(m.cache[t].values[0].value)}:m.color).attr("x1",c).attr("y1",u).attr("x2",d).attr("y2",u),y&&(h?y.transition():y).attr("height",m.getLegendHeight()-12).attr("width",b*(I+1)+10),m.legend.selectAll("."+Y.legendItem).classed(Y.legendItemHidden,function(t){return!m.isTargetToShow(t)}),m.updateLegendItemWidth(b),m.updateLegendItemHeight(A),m.updateLegendStep(I),m.updateSizes(),m.updateScales(),m.updateSvgSize(),m.transformAll(g,e),m.legendHasRendered=!0},m.initRegion=function(){this.region=this.main.append("g").attr("clip-path",this.clipPath).attr("class",Y.regions)},m.updateRegion=function(t){var e=this,i=e.config;e.region.style("visibility",e.hasArcType()?"hidden":"visible");var n=e.main.select("."+Y.regions).selectAll("."+Y.region).data(i.regions),a=n.enter().append("rect").attr("x",e.regionX.bind(e)).attr("y",e.regionY.bind(e)).attr("width",e.regionWidth.bind(e)).attr("height",e.regionHeight.bind(e)).style("fill-opacity",0);e.mainRegion=a.merge(n).attr("class",e.classRegion.bind(e)),n.exit().transition().duration(t).style("opacity",0).remove()},m.redrawRegion=function(t,e){var i=this,n=i.mainRegion;return[(t?n.transition(e):n).attr("x",i.regionX.bind(i)).attr("y",i.regionY.bind(i)).attr("width",i.regionWidth.bind(i)).attr("height",i.regionHeight.bind(i)).style("fill-opacity",function(t){return P(t.opacity)?t.opacity:.1})]},m.regionX=function(t){var e=this,i=e.config,n="y"===t.axis?e.y:e.y2;return"y"===t.axis||"y2"===t.axis?i.axis_rotated&&"start"in t?n(t.start):0:i.axis_rotated?0:"start"in t?e.x(e.isTimeSeries()?e.parseDate(t.start):t.start):0},m.regionY=function(t){var e=this,i=e.config,n="y"===t.axis?e.y:e.y2;return"y"===t.axis||"y2"===t.axis?i.axis_rotated?0:"end"in t?n(t.end):0:i.axis_rotated&&"start"in t?e.x(e.isTimeSeries()?e.parseDate(t.start):t.start):0},m.regionWidth=function(t){var e,i=this,n=i.config,a=i.regionX(t),r="y"===t.axis?i.y:i.y2;return(e="y"===t.axis||"y2"===t.axis?n.axis_rotated&&"end"in t?r(t.end):i.width:n.axis_rotated?i.width:"end"in t?i.x(i.isTimeSeries()?i.parseDate(t.end):t.end):i.width)<a?0:e-a},m.regionHeight=function(t){var e,i=this,n=i.config,a=this.regionY(t),r="y"===t.axis?i.y:i.y2;return(e="y"===t.axis||"y2"===t.axis?n.axis_rotated?i.height:"start"in t?r(t.start):i.height:n.axis_rotated&&"end"in t?i.x(i.isTimeSeries()?i.parseDate(t.end):t.end):i.height)<a?0:e-a},m.isRegionOnX=function(t){return!t.axis||"x"===t.axis},m.getScale=function(t,e,i){return(i?this.d3.scaleTime():this.d3.scaleLinear()).range([t,e])},m.getX=function(t,e,i,n){var a,r=this.getScale(t,e,this.isTimeSeries()),s=i?r.domain(i):r;for(a in this.isCategorized()?(n=n||function(){return 0},r=function(t,e){var i=s(t)+n(t);return e?i:Math.ceil(i)}):r=function(t,e){var i=s(t);return e?i:Math.ceil(i)},s)r[a]=s[a];return r.orgDomain=function(){return s.domain()},this.isCategorized()&&(r.domain=function(t){return arguments.length?(s.domain(t),r):[(t=this.orgDomain())[0],t[1]+1]}),r},m.getY=function(t,e,i){var n=this.getScale(t,e,this.isTimeSeriesY());return i&&n.domain(i),n},m.getYScale=function(t){return"y2"===this.axis.getId(t)?this.y2:this.y},m.getSubYScale=function(t){return"y2"===this.axis.getId(t)?this.subY2:this.subY},m.updateScales=function(){var e=this,t=e.config,i=!e.x;e.xMin=t.axis_rotated?1:0,e.xMax=t.axis_rotated?e.height:e.width,e.yMin=t.axis_rotated?0:e.height,e.yMax=t.axis_rotated?e.width:1,e.subXMin=e.xMin,e.subXMax=e.xMax,e.subYMin=t.axis_rotated?0:e.height2,e.subYMax=t.axis_rotated?e.width2:1,e.x=e.getX(e.xMin,e.xMax,i?void 0:e.x.orgDomain(),function(){return e.xAxis.tickOffset()}),e.y=e.getY(e.yMin,e.yMax,i?t.axis_y_default:e.y.domain()),e.y2=e.getY(e.yMin,e.yMax,i?t.axis_y2_default:e.y2.domain()),e.subX=e.getX(e.xMin,e.xMax,e.orgXDomain,function(t){return t%1?0:e.subXAxis.tickOffset()}),e.subY=e.getY(e.subYMin,e.subYMax,i?t.axis_y_default:e.subY.domain()),e.subY2=e.getY(e.subYMin,e.subYMax,i?t.axis_y2_default:e.subY2.domain()),e.xAxisTickFormat=e.axis.getXAxisTickFormat(),e.xAxisTickValues=e.axis.getXAxisTickValues(),e.yAxisTickValues=e.axis.getYAxisTickValues(),e.y2AxisTickValues=e.axis.getY2AxisTickValues(),e.xAxis=e.axis.getXAxis(e.x,e.xOrient,e.xAxisTickFormat,e.xAxisTickValues,t.axis_x_tick_outer),e.subXAxis=e.axis.getXAxis(e.subX,e.subXOrient,e.xAxisTickFormat,e.xAxisTickValues,t.axis_x_tick_outer),e.yAxis=e.axis.getYAxis(e.y,e.yOrient,t.axis_y_tick_format,e.yAxisTickValues,t.axis_y_tick_outer),e.y2Axis=e.axis.getYAxis(e.y2,e.y2Orient,t.axis_y2_tick_format,e.y2AxisTickValues,t.axis_y2_tick_outer),i||e.brush&&e.brush.updateScale(e.subX),e.updateArc&&e.updateArc()},m.selectPoint=function(t,e,i){var n=this,a=n.config,r=(a.axis_rotated?n.circleY:n.circleX).bind(n),s=(a.axis_rotated?n.circleX:n.circleY).bind(n),o=n.pointSelectR.bind(n);a.data_onselected.call(n.api,e,t.node()),n.main.select("."+Y.selectedCircles+n.getTargetSelectorSuffix(e.id)).selectAll("."+Y.selectedCircle+"-"+i).data([e]).enter().append("circle").attr("class",function(){return n.generateClass(Y.selectedCircle,i)}).attr("cx",r).attr("cy",s).attr("stroke",function(){return n.color(e)}).attr("r",function(t){return 1.4*n.pointSelectR(t)}).transition().duration(100).attr("r",o)},m.unselectPoint=function(t,e,i){this.config.data_onunselected.call(this.api,e,t.node()),this.main.select("."+Y.selectedCircles+this.getTargetSelectorSuffix(e.id)).selectAll("."+Y.selectedCircle+"-"+i).transition().duration(100).attr("r",0).remove()},m.togglePoint=function(t,e,i,n){t?this.selectPoint(e,i,n):this.unselectPoint(e,i,n)},m.selectPath=function(t,e){var i=this;i.config.data_onselected.call(i,e,t.node()),i.config.interaction_brighten&&t.transition().duration(100).style("fill",function(){return i.d3.rgb(i.color(e)).brighter(.75)})},m.unselectPath=function(t,e){var i=this;i.config.data_onunselected.call(i,e,t.node()),i.config.interaction_brighten&&t.transition().duration(100).style("fill",function(){return i.color(e)})},m.togglePath=function(t,e,i,n){t?this.selectPath(e,i,n):this.unselectPath(e,i,n)},m.getToggle=function(t,e){var i;return"circle"===t.nodeName?i=this.isStepType(e)?function(){}:this.togglePoint:"path"===t.nodeName&&(i=this.togglePath),i},m.toggleShape=function(t,e,i){var n=this,a=n.d3,r=n.config,s=a.select(t),o=s.classed(Y.SELECTED),c=n.getToggle(t,e).bind(n);r.data_selection_enabled&&r.data_selection_isselectable(e)&&(r.data_selection_multiple||n.main.selectAll("."+Y.shapes+(r.data_selection_grouped?n.getTargetSelectorSuffix(e.id):"")).selectAll("."+Y.shape).each(function(t,e){var i=a.select(this);i.classed(Y.SELECTED)&&c(!1,i.classed(Y.SELECTED,!1),t,e)}),s.classed(Y.SELECTED,!o),c(!o,s,e,i))},m.initBar=function(){this.main.select("."+Y.chart).append("g").attr("class",Y.chartBars)},m.updateTargetsForBar=function(t){var e=this,i=e.config,n=e.classChartBar.bind(e),a=e.classBars.bind(e),r=e.classFocus.bind(e);e.main.select("."+Y.chartBars).selectAll("."+Y.chartBar).data(t).attr("class",function(t){return n(t)+r(t)}).enter().append("g").attr("class",n).style("pointer-events","none").append("g").attr("class",a).style("cursor",function(t){return i.data_selection_isselectable(t)?"pointer":null})},m.updateBar=function(t){var e=this,i=e.barData.bind(e),n=e.classBar.bind(e),a=e.initialOpacity.bind(e),r=function(t){return e.color(t.id)},s=e.main.selectAll("."+Y.bars).selectAll("."+Y.bar).data(i),o=s.enter().append("path").attr("class",n).style("stroke",r).style("fill",r);e.mainBar=o.merge(s).style("opacity",a),s.exit().transition().duration(t).style("opacity",0)},m.redrawBar=function(t,e,i){return[(e?this.mainBar.transition(i):this.mainBar).attr("d",t).style("stroke",this.color).style("fill",this.color).style("opacity",1)]},m.getBarW=function(t,e){var i=this.config,n="number"==typeof i.bar_width?i.bar_width:e?t.tickInterval()*i.bar_width_ratio/e:0;return i.bar_width_max&&n>i.bar_width_max?i.bar_width_max:n},m.getBars=function(t,e){return(e?this.main.selectAll("."+Y.bars+this.getTargetSelectorSuffix(e)):this.main).selectAll("."+Y.bar+(P(t)?"-"+t:""))},m.expandBars=function(t,e,i){i&&this.unexpandBars(),this.getBars(t,e).classed(Y.EXPANDED,!0)},m.unexpandBars=function(t){this.getBars(t).classed(Y.EXPANDED,!1)},m.generateDrawBar=function(t,e){var r=this.config,s=this.generateGetBarPoints(t,e);return function(t,e){var i=s(t,e),n=r.axis_rotated?1:0,a=r.axis_rotated?0:1;return"M "+i[0][n]+","+i[0][a]+" L"+i[1][n]+","+i[1][a]+" L"+i[2][n]+","+i[2][a]+" L"+i[3][n]+","+i[3][a]+" z"}},m.generateGetBarPoints=function(t,e){var s=this,i=e?s.subXAxis:s.xAxis,n=t.__max__+1,o=s.getBarW(i,n),c=s.getShapeX(o,n,t,!!e),d=s.getShapeY(!!e),u=s.getShapeOffset(s.isBarType,t,!!e),l=o*(s.config.bar_space/2),h=e?s.getSubYScale:s.getYScale;return function(t,e){var i=h.call(s,t.id)(0),n=u(t,e)||i,a=c(t),r=d(t);return s.config.axis_rotated&&(0<t.value&&r<i||t.value<0&&i<r)&&(r=i),[[a+l,n],[a+l,r-(i-n)],[a+o-l,r-(i-n)],[a+o-l,n]]}},m.isWithinBar=function(t,e){var i=e.getBoundingClientRect(),n=e.pathSegList.getItem(0),a=e.pathSegList.getItem(1),r=Math.min(n.x,a.x),s=Math.min(n.y,a.y),o=r+i.width+2,c=s+i.height+2,d=s-2;return r-2<t[0]&&t[0]<o&&d<t[1]&&t[1]<c},m.getShapeIndices=function(t){var e,i,n=this.config,a={},r=0;return this.filterTargetsToShow(this.data.targets.filter(t,this)).forEach(function(t){for(e=0;e<n.data_groups.length;e++)if(!(n.data_groups[e].indexOf(t.id)<0))for(i=0;i<n.data_groups[e].length;i++)if(n.data_groups[e][i]in a){a[t.id]=a[n.data_groups[e][i]];break}v(a[t.id])&&(a[t.id]=r++)}),a.__max__=r-1,a},m.getShapeX=function(i,n,a,t){var r=t?this.subX:this.x;return function(t){var e=t.id in a?a[t.id]:0;return t.x||0===t.x?r(t.x)-i*(n/2-e):0}},m.getShapeY=function(e){var i=this;return function(t){return(e?i.getSubYScale(t.id):i.getYScale(t.id))(t.value)}},m.getShapeOffset=function(t,o,e){var c=this,d=c.orderTargets(c.filterTargetsToShow(c.data.targets.filter(t,c))),u=d.map(function(t){return t.id});return function(i,n){var a=e?c.getSubYScale(i.id):c.getYScale(i.id),r=a(0),s=r;return d.forEach(function(t){var e=c.isStepType(i)?c.convertValuesToStep(t.values):t.values;t.id!==i.id&&o[t.id]===o[i.id]&&u.indexOf(t.id)<u.indexOf(i.id)&&(void 0!==e[n]&&+e[n].x==+i.x||(n=-1,e.forEach(function(t,e){t.x===i.x&&(n=e)})),n in e&&0<=e[n].value*i.value&&(s+=a(e[n].value)-r))}),s}},m.isWithinShape=function(t,e){var i,n=this,a=n.d3.select(t);return n.isTargetToShow(e.id)?"circle"===t.nodeName?i=n.isStepType(e)?n.isWithinStep(t,n.getYScale(e.id)(e.value)):n.isWithinCircle(t,1.5*n.pointSelectR(e)):"path"===t.nodeName&&(i=!a.classed(Y.bar)||n.isWithinBar(n.d3.mouse(t),t)):i=!1,i},m.getInterpolate=function(t){var e=this,i=e.d3,n={linear:i.curveLinear,"linear-closed":i.curveLinearClosed,basis:i.curveBasis,"basis-open":i.curveBasisOpen,"basis-closed":i.curveBasisClosed,bundle:i.curveBundle,cardinal:i.curveCardinal,"cardinal-open":i.curveCardinalOpen,"cardinal-closed":i.curveCardinalClosed,monotone:i.curveMonotoneX,step:i.curveStep,"step-before":i.curveStepBefore,"step-after":i.curveStepAfter};return e.isSplineType(t)?n[e.config.spline_interpolation_type]||n.cardinal:e.isStepType(t)?n[e.config.line_step_type]:n.linear},m.initLine=function(){this.main.select("."+Y.chart).append("g").attr("class",Y.chartLines)},m.updateTargetsForLine=function(t){var e,i=this,n=i.config,a=i.classChartLine.bind(i),r=i.classLines.bind(i),s=i.classAreas.bind(i),o=i.classCircles.bind(i),c=i.classFocus.bind(i);(e=i.main.select("."+Y.chartLines).selectAll("."+Y.chartLine).data(t).attr("class",function(t){return a(t)+c(t)}).enter().append("g").attr("class",a).style("opacity",0).style("pointer-events","none")).append("g").attr("class",r),e.append("g").attr("class",s),e.append("g").attr("class",function(t){return i.generateClass(Y.selectedCircles,t.id)}),e.append("g").attr("class",o).style("cursor",function(t){return n.data_selection_isselectable(t)?"pointer":null}),t.forEach(function(e){i.main.selectAll("."+Y.selectedCircles+i.getTargetSelectorSuffix(e.id)).selectAll("."+Y.selectedCircle).each(function(t){t.value=e.values[t.index].value})})},m.updateLine=function(t){var e=this,i=e.main.selectAll("."+Y.lines).selectAll("."+Y.line).data(e.lineData.bind(e)),n=i.enter().append("path").attr("class",e.classLine.bind(e)).style("stroke",e.color);e.mainLine=n.merge(i).style("opacity",e.initialOpacity.bind(e)).style("shape-rendering",function(t){return e.isStepType(t)?"crispEdges":""}).attr("transform",null),i.exit().transition().duration(t).style("opacity",0)},m.redrawLine=function(t,e,i){return[(e?this.mainLine.transition(i):this.mainLine).attr("d",t).style("stroke",this.color).style("opacity",1)]},m.generateDrawLine=function(t,o){var c=this,d=c.config,u=c.d3.line(),i=c.generateGetLinePoints(t,o),l=o?c.getSubYScale:c.getYScale,e=function(t){return(o?c.subxx:c.xx).call(c,t)},n=function(t,e){return 0<d.data_groups.length?i(t,e)[0][1]:l.call(c,t.id)(t.value)};return u=d.axis_rotated?u.x(n).y(e):u.x(e).y(n),d.line_connectNull||(u=u.defined(function(t){return null!=t.value})),function(t){var e,i=d.line_connectNull?c.filterRemoveNull(t.values):t.values,n=o?c.subX:c.x,a=l.call(c,t.id),r=0,s=0;return c.isLineType(t)?d.data_regions[t.id]?e=c.lineWithRegions(i,n,a,d.data_regions[t.id]):(c.isStepType(t)&&(i=c.convertValuesToStep(i)),e=u.curve(c.getInterpolate(t))(i)):(i[0]&&(r=n(i[0].x),s=a(i[0].value)),e=d.axis_rotated?"M "+s+" "+r:"M "+r+" "+s),e||"M 0 0"}},m.generateGetLinePoints=function(t,e){var s=this,o=s.config,i=t.__max__+1,c=s.getShapeX(0,i,t,!!e),d=s.getShapeY(!!e),u=s.getShapeOffset(s.isLineType,t,!!e),l=e?s.getSubYScale:s.getYScale;return function(t,e){var i=l.call(s,t.id)(0),n=u(t,e)||i,a=c(t),r=d(t);return o.axis_rotated&&(0<t.value&&r<i||t.value<0&&i<r)&&(r=i),[[a,r-(i-n)],[a,r-(i-n)],[a,r-(i-n)],[a,r-(i-n)]]}},m.lineWithRegions=function(t,c,d,e){var i,n,a,r,u,s,o,l,h,g,f,p=this,_=p.config,x="M",y=p.isCategorized()?.5:0,m=[];function S(t,e){var i;for(i=0;i<e.length;i++)if(e[i].start<t&&t<=e[i].end)return!0;return!1}if(k(e))for(i=0;i<e.length;i++)m[i]={},v(e[i].start)?m[i].start=t[0].x:m[i].start=p.isTimeSeries()?p.parseDate(e[i].start):e[i].start,v(e[i].end)?m[i].end=t[t.length-1].x:m[i].end=p.isTimeSeries()?p.parseDate(e[i].end):e[i].end;function w(t){return"M"+t[0][0]+" "+t[0][1]+" "+t[1][0]+" "+t[1][1]}for(g=_.axis_rotated?function(t){return d(t.value)}:function(t){return c(t.x)},f=_.axis_rotated?function(t){return c(t.x)}:function(t){return d(t.value)},a=p.isTimeSeries()?function(t,e,i,n){var a=t.x.getTime(),r=e.x-t.x,s=new Date(a+r*i),o=new Date(a+r*(i+n));return w(_.axis_rotated?[[d(u(i)),c(s)],[d(u(i+n)),c(o)]]:[[c(s),d(u(i))],[c(o),d(u(i+n))]])}:function(t,e,i,n){return w(_.axis_rotated?[[d(u(i),!0),c(r(i))],[d(u(i+n),!0),c(r(i+n))]]:[[c(r(i),!0),d(u(i))],[c(r(i+n),!0),d(u(i+n))]])},i=0;i<t.length;i++){if(v(m)||!S(t[i].x,m))x+=" "+g(t[i])+" "+f(t[i]);else for(r=p.getScale(t[i-1].x+y,t[i].x+y,p.isTimeSeries()),u=p.getScale(t[i-1].value,t[i].value),s=c(t[i].x)-c(t[i-1].x),o=d(t[i].value)-d(t[i-1].value),h=2*(l=2/Math.sqrt(Math.pow(s,2)+Math.pow(o,2))),n=l;n<=1;n+=h)x+=a(t[i-1],t[i],n,l);t[i].x}return x},m.updateArea=function(t){var e=this,i=e.d3,n=e.main.selectAll("."+Y.areas).selectAll("."+Y.area).data(e.lineData.bind(e)),a=n.enter().append("path").attr("class",e.classArea.bind(e)).style("fill",e.color).style("opacity",function(){return e.orgAreaOpacity=+i.select(this).style("opacity"),0});e.mainArea=a.merge(n).style("opacity",e.orgAreaOpacity),n.exit().transition().duration(t).style("opacity",0)},m.redrawArea=function(t,e,i){return[(e?this.mainArea.transition(i):this.mainArea).attr("d",t).style("fill",this.color).style("opacity",this.orgAreaOpacity)]},m.generateDrawArea=function(t,e){var r=this,s=r.config,o=r.d3.area(),i=r.generateGetAreaPoints(t,e),n=e?r.getSubYScale:r.getYScale,a=function(t){return(e?r.subxx:r.xx).call(r,t)},c=function(t,e){return 0<s.data_groups.length?i(t,e)[0][1]:n.call(r,t.id)(r.getAreaBaseValue(t.id))},d=function(t,e){return 0<s.data_groups.length?i(t,e)[1][1]:n.call(r,t.id)(t.value)};return o=s.axis_rotated?o.x0(c).x1(d).y(a):o.x(a).y0(s.area_above?0:c).y1(d),s.line_connectNull||(o=o.defined(function(t){return null!==t.value})),function(t){var e,i=s.line_connectNull?r.filterRemoveNull(t.values):t.values,n=0,a=0;return r.isAreaType(t)?(r.isStepType(t)&&(i=r.convertValuesToStep(i)),e=o.curve(r.getInterpolate(t))(i)):(i[0]&&(n=r.x(i[0].x),a=r.getYScale(t.id)(i[0].value)),e=s.axis_rotated?"M "+a+" "+n:"M "+n+" "+a),e||"M 0 0"}},m.getAreaBaseValue=function(){return 0},m.generateGetAreaPoints=function(t,e){var s=this,o=s.config,i=t.__max__+1,c=s.getShapeX(0,i,t,!!e),d=s.getShapeY(!!e),u=s.getShapeOffset(s.isAreaType,t,!!e),l=e?s.getSubYScale:s.getYScale;return function(t,e){var i=l.call(s,t.id)(0),n=u(t,e)||i,a=c(t),r=d(t);return o.axis_rotated&&(0<t.value&&r<i||t.value<0&&i<r)&&(r=i),[[a,n],[a,r-(i-n)],[a,r-(i-n)],[a,n]]}},m.updateCircle=function(t,e){var i=this,n=i.main.selectAll("."+Y.circles).selectAll("."+Y.circle).data(i.lineOrScatterData.bind(i)),a=n.enter().append("circle").attr("class",i.classCircle.bind(i)).attr("cx",t).attr("cy",e).attr("r",i.pointR.bind(i)).style("fill",i.color);i.mainCircle=a.merge(n).style("opacity",i.initialOpacityForCircle.bind(i)),n.exit().style("opacity",0)},m.redrawCircle=function(t,e,i,n){var a=this,r=a.main.selectAll("."+Y.selectedCircle);return[(i?a.mainCircle.transition(n):a.mainCircle).style("opacity",this.opacityForCircle.bind(a)).style("fill",a.color).attr("cx",t).attr("cy",e),(i?r.transition(n):r).attr("cx",t).attr("cy",e)]},m.circleX=function(t){return t.x||0===t.x?this.x(t.x):null},m.updateCircleY=function(){var t,i,e=this;0<e.config.data_groups.length?(t=e.getShapeIndices(e.isLineType),i=e.generateGetLinePoints(t),e.circleY=function(t,e){return i(t,e)[0][1]}):e.circleY=function(t){return e.getYScale(t.id)(t.value)}},m.getCircles=function(t,e){return(e?this.main.selectAll("."+Y.circles+this.getTargetSelectorSuffix(e)):this.main).selectAll("."+Y.circle+(P(t)?"-"+t:""))},m.expandCircles=function(t,e,i){var n=this.pointExpandedR.bind(this);i&&this.unexpandCircles(),this.getCircles(t,e).classed(Y.EXPANDED,!0).attr("r",n)},m.unexpandCircles=function(t){var e=this,i=e.pointR.bind(e);e.getCircles(t).filter(function(){return e.d3.select(this).classed(Y.EXPANDED)}).classed(Y.EXPANDED,!1).attr("r",i)},m.pointR=function(t){var e=this.config;return this.isStepType(t)?0:h(e.point_r)?e.point_r(t):e.point_r},m.pointExpandedR=function(t){var e=this.config;return e.point_focus_expand_enabled?h(e.point_focus_expand_r)?e.point_focus_expand_r(t):e.point_focus_expand_r?e.point_focus_expand_r:1.75*this.pointR(t):this.pointR(t)},m.pointSelectR=function(t){var e=this.config;return h(e.point_select_r)?e.point_select_r(t):e.point_select_r?e.point_select_r:4*this.pointR(t)},m.isWithinCircle=function(t,e){var i=this.d3,n=i.mouse(t),a=i.select(t),r=+a.attr("cx"),s=+a.attr("cy");return Math.sqrt(Math.pow(r-n[0],2)+Math.pow(s-n[1],2))<e},m.isWithinStep=function(t,e){return Math.abs(e-this.d3.mouse(t)[1])<30},m.getCurrentWidth=function(){var t=this.config;return t.size_width?t.size_width:this.getParentWidth()},m.getCurrentHeight=function(){var t=this.config,e=t.size_height?t.size_height:this.getParentHeight();return 0<e?e:320/(this.hasType("gauge")&&!t.gauge_fullCircle?2:1)},m.getCurrentPaddingTop=function(){var t=this.config,e=P(t.padding_top)?t.padding_top:0;return this.title&&this.title.node()&&(e+=this.getTitlePadding()),e},m.getCurrentPaddingBottom=function(){var t=this.config;return P(t.padding_bottom)?t.padding_bottom:0},m.getCurrentPaddingLeft=function(t){var e=this.config;return P(e.padding_left)?e.padding_left:e.axis_rotated?!e.axis_x_show||e.axis_x_inner?1:Math.max(d(this.getAxisWidthByAxisId("x",t)),40):!e.axis_y_show||e.axis_y_inner?this.axis.getYAxisLabelPosition().isOuter?30:1:d(this.getAxisWidthByAxisId("y",t))},m.getCurrentPaddingRight=function(){var t=this,e=t.config,i=t.isLegendRight?t.getLegendWidth()+20:0;return P(e.padding_right)?e.padding_right+1:e.axis_rotated?10+i:!e.axis_y2_show||e.axis_y2_inner?2+i+(t.axis.getY2AxisLabelPosition().isOuter?20:0):d(t.getAxisWidthByAxisId("y2"))+i},m.getParentRectValue=function(e){for(var i,n=this.selectChart.node();n&&"BODY"!==n.tagName;){try{i=n.getBoundingClientRect()[e]}catch(t){"width"===e&&(i=n.offsetWidth)}if(i)break;n=n.parentNode}return i},m.getParentWidth=function(){return this.getParentRectValue("width")},m.getParentHeight=function(){var t=this.selectChart.style("height");return 0<t.indexOf("px")?+t.replace("px",""):0},m.getSvgLeft=function(t){var e=this,i=e.config,n=i.axis_rotated||!i.axis_rotated&&!i.axis_y_inner,a=i.axis_rotated?Y.axisX:Y.axisY,r=e.main.select("."+a).node(),s=r&&n?r.getBoundingClientRect():{right:0},o=e.selectChart.node().getBoundingClientRect(),c=e.hasArcType(),d=s.right-o.left-(c?0:e.getCurrentPaddingLeft(t));return 0<d?d:0},m.getAxisWidthByAxisId=function(t,e){var i=this.axis.getLabelPositionById(t);return this.axis.getMaxTickWidth(t,e)+(i.isInner?20:40)},m.getHorizontalAxisHeight=function(t){var e=this,i=e.config,n=30;return"x"!==t||i.axis_x_show?"x"===t&&i.axis_x_height?i.axis_x_height:"y"!==t||i.axis_y_show?"y2"!==t||i.axis_y2_show?("x"===t&&!i.axis_rotated&&i.axis_x_tick_rotate&&(n=30+e.axis.getMaxTickWidth(t)*Math.cos(Math.PI*(90-Math.abs(i.axis_x_tick_rotate))/180)),"y"===t&&i.axis_rotated&&i.axis_y_tick_rotate&&(n=30+e.axis.getMaxTickWidth(t)*Math.cos(Math.PI*(90-Math.abs(i.axis_y_tick_rotate))/180)),n+(e.axis.getLabelPositionById(t).isInner?0:10)+("y2"===t?-10:0)):e.rotated_padding_top:!i.legend_show||e.isLegendRight||e.isLegendInset?1:10:8},m.initBrush=function(t){var a=this,e=a.d3;return a.brush=(a.config.axis_rotated?e.brushY():e.brushX()).on("brush",function(){var t=e.event.sourceEvent;t&&"zoom"===t.type||a.redrawForBrush()}).on("end",function(){var t=e.event.sourceEvent;t&&"zoom"===t.type||a.brush.empty()&&t&&"end"!==t.type&&a.brush.clear()}),a.brush.updateExtent=function(){var t,e=this.scale.range();return t=a.config.axis_rotated?[[0,e[0]],[a.width2,e[1]]]:[[e[0],0],[e[1],a.height2]],this.extent(t),this},a.brush.updateScale=function(t){return this.scale=t,this},a.brush.update=function(t){this.updateScale(t||a.subX).updateExtent(),a.context.select("."+Y.brush).call(this)},a.brush.clear=function(){a.context.select("."+Y.brush).call(a.brush.move,null)},a.brush.selection=function(){return e.brushSelection(a.context.select("."+Y.brush).node())},a.brush.selectionAsValue=function(t,e){var i,n;return t?(a.context&&(i=[this.scale(t[0]),this.scale(t[1])],n=a.context.select("."+Y.brush),e&&(n=n.transition()),a.brush.move(n,i)),[]):(i=a.brush.selection()||[0,0],[this.scale.invert(i[0]),this.scale.invert(i[1])])},a.brush.empty=function(){var t=a.brush.selection();return!t||t[0]===t[1]},a.brush.updateScale(t)},m.initSubchart=function(){var t=this,e=t.config,i=t.context=t.svg.append("g").attr("transform",t.getTranslate("context")),n=e.subchart_show?"visible":"hidden";i.style("visibility",n),i.append("g").attr("clip-path",t.clipPathForSubchart).attr("class",Y.chart),i.select("."+Y.chart).append("g").attr("class",Y.chartBars),i.select("."+Y.chart).append("g").attr("class",Y.chartLines),i.append("g").attr("clip-path",t.clipPath).attr("class",Y.brush),t.axes.subx=i.append("g").attr("class",Y.axisX).attr("transform",t.getTranslate("subx")).attr("clip-path",e.axis_rotated?"":t.clipPathForXAxis)},m.initSubchartBrush=function(){this.initBrush(this.subX).updateExtent(),this.context.select("."+Y.brush).call(this.brush)},m.updateTargetsForSubchart=function(t){var e,i,n,a,r=this,s=r.context,o=r.config,c=r.classChartBar.bind(r),d=r.classBars.bind(r),u=r.classChartLine.bind(r),l=r.classLines.bind(r),h=r.classAreas.bind(r);o.subchart_show&&((n=(a=s.select("."+Y.chartBars).selectAll("."+Y.chartBar).data(t)).enter().append("g").style("opacity",0)).merge(a).attr("class",c),n.append("g").attr("class",d),(e=(i=s.select("."+Y.chartLines).selectAll("."+Y.chartLine).data(t)).enter().append("g").style("opacity",0)).merge(i).attr("class",u),e.append("g").attr("class",l),e.append("g").attr("class",h),s.selectAll("."+Y.brush+" rect").attr(o.axis_rotated?"width":"height",o.axis_rotated?r.width2:r.height2))},m.updateBarForSubchart=function(t){var e=this,i=e.context.selectAll("."+Y.bars).selectAll("."+Y.bar).data(e.barData.bind(e)),n=i.enter().append("path").attr("class",e.classBar.bind(e)).style("stroke","none").style("fill",e.color);i.exit().transition().duration(t).style("opacity",0).remove(),e.contextBar=n.merge(i).style("opacity",e.initialOpacity.bind(e))},m.redrawBarForSubchart=function(t,e,i){(e?this.contextBar.transition(Math.random().toString()).duration(i):this.contextBar).attr("d",t).style("opacity",1)},m.updateLineForSubchart=function(t){var e=this,i=e.context.selectAll("."+Y.lines).selectAll("."+Y.line).data(e.lineData.bind(e)),n=i.enter().append("path").attr("class",e.classLine.bind(e)).style("stroke",e.color);i.exit().transition().duration(t).style("opacity",0).remove(),e.contextLine=n.merge(i).style("opacity",e.initialOpacity.bind(e))},m.redrawLineForSubchart=function(t,e,i){(e?this.contextLine.transition(Math.random().toString()).duration(i):this.contextLine).attr("d",t).style("opacity",1)},m.updateAreaForSubchart=function(t){var e=this,i=e.d3,n=e.context.selectAll("."+Y.areas).selectAll("."+Y.area).data(e.lineData.bind(e)),a=n.enter().append("path").attr("class",e.classArea.bind(e)).style("fill",e.color).style("opacity",function(){return e.orgAreaOpacity=+i.select(this).style("opacity"),0});n.exit().transition().duration(t).style("opacity",0).remove(),e.contextArea=a.merge(n).style("opacity",0)},m.redrawAreaForSubchart=function(t,e,i){(e?this.contextArea.transition(Math.random().toString()).duration(i):this.contextArea).attr("d",t).style("fill",this.color).style("opacity",this.orgAreaOpacity)},m.redrawSubchart=function(t,e,i,n,a,r,s){var o,c,d,u=this,l=u.d3,h=u.config;u.context.style("visibility",h.subchart_show?"visible":"hidden"),h.subchart_show&&(l.event&&"zoom"===l.event.type&&u.brush.selectionAsValue(u.x.orgDomain()),t&&(u.brush.empty()||u.brush.selectionAsValue(u.x.orgDomain()),o=u.generateDrawArea(a,!0),c=u.generateDrawBar(r,!0),d=u.generateDrawLine(s,!0),u.updateBarForSubchart(i),u.updateLineForSubchart(i),u.updateAreaForSubchart(i),u.redrawBarForSubchart(c,i,i),u.redrawLineForSubchart(d,i,i),u.redrawAreaForSubchart(o,i,i)))},m.redrawForBrush=function(){var t,e=this,i=e.x,n=e.d3;e.redraw({withTransition:!1,withY:e.config.zoom_rescale,withSubchart:!1,withUpdateXDomain:!0,withEventRect:!1,withDimension:!1}),t=n.event.selection||e.brush.scale.range(),e.main.select("."+Y.eventRect).call(e.zoom.transform,n.zoomIdentity.scale(e.width/(t[1]-t[0])).translate(-t[0],0)),e.config.subchart_onbrush.call(e.api,i.orgDomain())},m.transformContext=function(t,e){var i;e&&e.axisSubX?i=e.axisSubX:(i=this.context.select("."+Y.axisX),t&&(i=i.transition())),this.context.attr("transform",this.getTranslate("context")),i.attr("transform",this.getTranslate("subx"))},m.getDefaultSelection=function(){var t=this,e=t.config,i=h(e.axis_x_selection)?e.axis_x_selection(t.getXDomain(t.data.targets)):e.axis_x_selection;return t.isTimeSeries()&&(i=[t.parseDate(i[0]),t.parseDate(i[1])]),i},m.initText=function(){this.main.select("."+Y.chart).append("g").attr("class",Y.chartTexts),this.mainText=this.d3.selectAll([])},m.updateTargetsForText=function(t){var e=this,i=e.classChartText.bind(e),n=e.classTexts.bind(e),a=e.classFocus.bind(e),r=e.main.select("."+Y.chartTexts).selectAll("."+Y.chartText).data(t),s=r.enter().append("g").attr("class",i).style("opacity",0).style("pointer-events","none");s.append("g").attr("class",n),s.merge(r).attr("class",function(t){return i(t)+a(t)})},m.updateText=function(t,e,i){var n=this,a=n.config,r=n.barOrLineData.bind(n),s=n.classText.bind(n),o=n.main.selectAll("."+Y.texts).selectAll("."+Y.text).data(r),c=o.enter().append("text").attr("class",s).attr("text-anchor",function(t){return a.axis_rotated?t.value<0?"end":"start":"middle"}).style("stroke","none").attr("x",t).attr("y",e).style("fill",function(t){return n.color(t)}).style("fill-opacity",0);n.mainText=c.merge(o).text(function(t,e,i){return n.dataLabelFormat(t.id)(t.value,t.id,e,i)}),o.exit().transition().duration(i).style("fill-opacity",0).remove()},m.redrawText=function(t,e,i,n,a){return[(n?this.mainText.transition(a):this.mainText).attr("x",t).attr("y",e).style("fill",this.color).style("fill-opacity",i?0:this.opacityForText.bind(this))]},m.getTextRect=function(t,e,i){var n,a=this.d3.select("body").append("div").classed("c3",!0),r=a.append("svg").style("visibility","hidden").style("position","fixed").style("top",0).style("left",0),s=this.d3.select(i).style("font");return r.selectAll(".dummy").data([t]).enter().append("text").classed(e||"",!0).style("font",s).text(t).each(function(){n=this.getBoundingClientRect()}),a.remove(),n},m.generateXYForText=function(t,e,i,n){var a=this,r=a.generateGetAreaPoints(t,!1),s=a.generateGetBarPoints(e,!1),o=a.generateGetLinePoints(i,!1),c=n?a.getXForText:a.getYForText;return function(t,e){var i=a.isAreaType(t)?r:a.isBarType(t)?s:o;return c.call(a,i(t,e),t,this)}},m.getXForText=function(t,e,i){var n,a,r=this,s=i.getBoundingClientRect();return r.config.axis_rotated?(a=r.isBarType(e)?4:6,n=t[2][1]+a*(e.value<0?-1:1)):n=r.hasType("bar")?(t[2][0]+t[0][0])/2:t[0][0],null===e.value&&(n>r.width?n=r.width-s.width:n<0&&(n=4)),n},m.getYForText=function(t,e,i){var n,a=this,r=i.getBoundingClientRect();return a.config.axis_rotated?n=(t[0][0]+t[2][0]+.6*r.height)/2:(n=t[2][1],e.value<0||0===e.value&&!a.hasPositiveValue?(n+=r.height,a.isBarType(e)&&a.isSafari()?n-=3:!a.isBarType(e)&&a.isChrome()&&(n+=3)):n+=a.isBarType(e)?-3:-6),null!==e.value||a.config.axis_rotated||(n<r.height?n=r.height:n>this.height&&(n=this.height-4)),n},m.initTitle=function(){this.title=this.svg.append("text").text(this.config.title_text).attr("class",this.CLASS.title)},m.redrawTitle=function(){var t=this;t.title.attr("x",t.xForTitle.bind(t)).attr("y",t.yForTitle.bind(t))},m.xForTitle=function(){var t=this,e=t.config,i=e.title_position||"left";return 0<=i.indexOf("right")?t.currentWidth-t.getTextRect(t.title.node().textContent,t.CLASS.title,t.title.node()).width-e.title_padding.right:0<=i.indexOf("center")?(t.currentWidth-t.getTextRect(t.title.node().textContent,t.CLASS.title,t.title.node()).width)/2:e.title_padding.left},m.yForTitle=function(){var t=this;return t.config.title_padding.top+t.getTextRect(t.title.node().textContent,t.CLASS.title,t.title.node()).height},m.getTitlePadding=function(){return this.yForTitle()+this.config.title_padding.bottom},m.initTooltip=function(){var t,e=this,i=e.config;if(e.tooltip=e.selectChart.style("position","relative").append("div").attr("class",Y.tooltipContainer).style("position","absolute").style("pointer-events","none").style("display","none"),i.tooltip_init_show){if(e.isTimeSeries()&&c(i.tooltip_init_x)){for(i.tooltip_init_x=e.parseDate(i.tooltip_init_x),t=0;t<e.data.targets[0].values.length&&e.data.targets[0].values[t].x-i.tooltip_init_x!=0;t++);i.tooltip_init_x=t}e.tooltip.html(i.tooltip_contents.call(e,e.data.targets.map(function(t){return e.addName(t.values[i.tooltip_init_x])}),e.axis.getXAxisTickFormat(),e.getYFormat(e.hasArcType()),e.color)),e.tooltip.style("top",i.tooltip_init_position.top).style("left",i.tooltip_init_position.left).style("display","block")}},m.getTooltipSortFunction=function(){var t=this,e=t.config;if(0!==e.data_groups.length&&void 0===e.tooltip_order){var i=t.orderTargets(t.data.targets).map(function(t){return t.id});return(t.isOrderAsc()||t.isOrderDesc())&&(i=i.reverse()),function(t,e){return i.indexOf(t.id)-i.indexOf(e.id)}}var n=e.tooltip_order;void 0===n&&(n=e.data_order);var a=function(t){return t?t.value:null};if(c(n)&&"asc"===n.toLowerCase())return function(t,e){return a(t)-a(e)};if(c(n)&&"desc"===n.toLowerCase())return function(t,e){return a(e)-a(t)};if(h(n)){var r=n;return void 0===e.tooltip_order&&(r=function(t,e){return n(t?{id:t.id,values:[t]}:null,e?{id:e.id,values:[e]}:null)}),r}return u(n)?function(t,e){return n.indexOf(t.id)-n.indexOf(e.id)}:void 0},m.getTooltipContent=function(t,e,i,n){var a,r,s,o,c,d,u=this,l=u.config,h=l.tooltip_format_title||e,g=l.tooltip_format_name||function(t){return t},f=l.tooltip_format_value||i,p=this.getTooltipSortFunction();for(p&&t.sort(p),r=0;r<t.length;r++)if(t[r]&&(t[r].value||0===t[r].value)&&(a||(s=_(h?h(t[r].x):t[r].x),a="<table class='"+u.CLASS.tooltip+"'>"+(s||0===s?"<tr><th colspan='2'>"+s+"</th></tr>":"")),void 0!==(o=_(f(t[r].value,t[r].ratio,t[r].id,t[r].index,t))))){if(null===t[r].name)continue;c=_(g(t[r].name,t[r].ratio,t[r].id,t[r].index)),d=u.levelColor?u.levelColor(t[r].value):n(t[r].id),a+="<tr class='"+u.CLASS.tooltipName+"-"+u.getTargetSelectorSuffix(t[r].id)+"'>",a+="<td class='name'><span style='background-color:"+d+"'></span>"+c+"</td>",a+="<td class='value'>"+o+"</td>",a+="</tr>"}return a+"</table>"},m.tooltipPosition=function(t,e,i,n){var a,r,s,o,c,d=this,u=d.config,l=d.d3,h=d.hasArcType(),g=l.mouse(n);return h?(r=(d.width-(d.isLegendRight?d.getLegendWidth():0))/2+g[0],o=(d.hasType("gauge")?d.height:d.height/2)+g[1]+20):(a=d.getSvgLeft(!0),u.axis_rotated?(s=(r=a+g[0]+100)+e,c=d.currentWidth-d.getCurrentPaddingRight(),o=d.x(t[0].x)+20):(s=(r=a+d.getCurrentPaddingLeft(!0)+d.x(t[0].x)+20)+e,c=a+d.currentWidth-d.getCurrentPaddingRight(),o=g[1]+15),c<s&&(r-=s-c+20),o+i>d.currentHeight&&(o-=i+30)),o<0&&(o=0),{top:o,left:r}},m.showTooltip=function(t,e){var i,n,a,r=this,s=r.config,o=r.hasArcType(),c=t.filter(function(t){return t&&P(t.value)}),d=s.tooltip_position||m.tooltipPosition;0!==c.length&&s.tooltip_show&&(r.tooltip.html(s.tooltip_contents.call(r,t,r.axis.getXAxisTickFormat(),r.getYFormat(o),r.color)).style("display","block"),i=r.tooltip.property("offsetWidth"),n=r.tooltip.property("offsetHeight"),a=d.call(this,c,i,n,e),r.tooltip.style("top",a.top+"px").style("left",a.left+"px"))},m.hideTooltip=function(){this.tooltip.style("display","none")},m.setTargetType=function(t,e){var i=this,n=i.config;i.mapToTargetIds(t).forEach(function(t){i.withoutFadeIn[t]=e===n.data_types[t],n.data_types[t]=e}),t||(n.data_type=e)},m.hasType=function(i,t){var n=this.config.data_types,a=!1;return(t=t||this.data.targets)&&t.length?t.forEach(function(t){var e=n[t.id];(e&&0<=e.indexOf(i)||!e&&"line"===i)&&(a=!0)}):Object.keys(n).length?Object.keys(n).forEach(function(t){n[t]===i&&(a=!0)}):a=this.config.data_type===i,a},m.hasArcType=function(t){return this.hasType("pie",t)||this.hasType("donut",t)||this.hasType("gauge",t)},m.isLineType=function(t){var e=this.config,i=c(t)?t:t.id;return!e.data_types[i]||0<=["line","spline","area","area-spline","step","area-step"].indexOf(e.data_types[i])},m.isStepType=function(t){var e=c(t)?t:t.id;return 0<=["step","area-step"].indexOf(this.config.data_types[e])},m.isSplineType=function(t){var e=c(t)?t:t.id;return 0<=["spline","area-spline"].indexOf(this.config.data_types[e])},m.isAreaType=function(t){var e=c(t)?t:t.id;return 0<=["area","area-spline","area-step"].indexOf(this.config.data_types[e])},m.isBarType=function(t){var e=c(t)?t:t.id;return"bar"===this.config.data_types[e]},m.isScatterType=function(t){var e=c(t)?t:t.id;return"scatter"===this.config.data_types[e]},m.isPieType=function(t){var e=c(t)?t:t.id;return"pie"===this.config.data_types[e]},m.isGaugeType=function(t){var e=c(t)?t:t.id;return"gauge"===this.config.data_types[e]},m.isDonutType=function(t){var e=c(t)?t:t.id;return"donut"===this.config.data_types[e]},m.isArcType=function(t){return this.isPieType(t)||this.isDonutType(t)||this.isGaugeType(t)},m.lineData=function(t){return this.isLineType(t)?[t]:[]},m.arcData=function(t){return this.isArcType(t.data)?[t]:[]},m.barData=function(t){return this.isBarType(t)?t.values:[]},m.lineOrScatterData=function(t){return this.isLineType(t)||this.isScatterType(t)?t.values:[]},m.barOrLineData=function(t){return this.isBarType(t)||this.isLineType(t)?t.values:[]},m.isSafari=function(){var t=window.navigator.userAgent;return 0<=t.indexOf("Safari")&&t.indexOf("Chrome")<0},m.isChrome=function(){return 0<=window.navigator.userAgent.indexOf("Chrome")},m.initZoom=function(){var e,i=this,n=i.d3,a=i.config;return i.zoom=n.zoom().on("start",function(){var t=n.event.sourceEvent;t&&"brush"===t.type||(e=t,a.zoom_onzoomstart.call(i.api,t))}).on("zoom",function(){var t=n.event.sourceEvent;t&&"brush"===t.type||i.redrawForZoom.call(i)}).on("end",function(){var t=n.event.sourceEvent;t&&"brush"===t.type||t&&e.clientX===t.clientX&&e.clientY===t.clientY||a.zoom_onzoomend.call(i.api,i.x.orgDomain())}),i.zoom.updateDomain=function(){return n.event&&n.event.transform&&i.x.domain(n.event.transform.rescaleX(i.subX).domain()),this},i.zoom.updateExtent=function(){return this.scaleExtent([1,1/0]).translateExtent([[0,0],[i.width,i.height]]).extent([[0,0],[i.width,i.height]]),this},i.zoom.update=function(){return this.updateExtent().updateDomain()},i.zoom.updateExtent()},m.zoomTransform=function(t){var e=[this.x(t[0]),this.x(t[1])];return this.d3.zoomIdentity.scale(this.width/(e[1]-e[0])).translate(-e[0],0)},m.getZoomDomain=function(){var t=this.config,e=this.d3;return[e.min([this.orgXDomain[0],t.zoom_x_min]),e.max([this.orgXDomain[1],t.zoom_x_max])]},m.redrawForZoom=function(){var t=this,e=t.d3,i=t.config,n=t.zoom,a=t.x;i.zoom_enabled&&0!==t.filterTargetsToShow(t.data.targets).length&&(n.update(),t.isCategorized()&&a.orgDomain()[0]===t.orgXDomain[0]&&a.domain([t.orgXDomain[0]-1e-10,a.orgDomain()[1]]),t.redraw({withTransition:!1,withY:i.zoom_rescale,withSubchart:!1,withEventRect:!1,withDimension:!1}),e.event.sourceEvent&&"mousemove"===e.event.sourceEvent.type&&(t.cancelClick=!0),i.zoom_onzoom.call(t.api,a.orgDomain()))},n});