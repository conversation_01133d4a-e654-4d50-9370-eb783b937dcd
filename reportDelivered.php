
<?php
session_start();
include("connection.php");
require_once('database.php');
require_once('library.php');
isUser();

$a = $_SESSION['username'];
$sql = "SELECT * FROM login WHERE username='$a'";
$result = mysqli_query($con, $sql);
$row1 = mysqli_fetch_array($result, MYSQLI_BOTH);
$userid = ($row1 && isset($row1['rid'])) ? $row1['rid'] : 0;
$withoutgstvalue='';

// Date filtering with multiple format support
$dateFilter = "";
if (isset($_GET['start_date']) && isset($_GET['end_date']) && !empty($_GET['start_date']) && !empty($_GET['end_date'])) {
    $start_date_input = trim($_GET['start_date']);
    $end_date_input = trim($_GET['end_date']);

    // Handle different date formats
    if (preg_match('/^(\d{1,2})-(\d{1,2})-(\d{4})$/', $start_date_input, $matches1)) {
        // DD-MM-YYYY format
        $start_date = $matches1[3] . '-' . sprintf('%02d', $matches1[2]) . '-' . sprintf('%02d', $matches1[1]);
    } elseif (preg_match('/^(\d{4})-(\d{1,2})-(\d{1,2})$/', $start_date_input)) {
        // YYYY-MM-DD format (already correct)
        $start_date = $start_date_input;
    } else {
        // Try strtotime as fallback
        $timestamp = strtotime($start_date_input);
        if ($timestamp !== false) {
            $start_date = date('Y-m-d', $timestamp);
        }
    }

    if (preg_match('/^(\d{1,2})-(\d{1,2})-(\d{4})$/', $end_date_input, $matches2)) {
        // DD-MM-YYYY format
        $end_date = $matches2[3] . '-' . sprintf('%02d', $matches2[2]) . '-' . sprintf('%02d', $matches2[1]);
    } elseif (preg_match('/^(\d{4})-(\d{1,2})-(\d{1,2})$/', $end_date_input)) {
        // YYYY-MM-DD format (already correct)
        $end_date = $end_date_input;
    } else {
        // Try strtotime as fallback
        $timestamp = strtotime($end_date_input);
        if ($timestamp !== false) {
            $end_date = date('Y-m-d', $timestamp);
        }
    }

    // Only apply filter if both dates are valid
    if (!empty($start_date) && !empty($end_date)) {
        $start_date = mysqli_real_escape_string($con, $start_date);
        $end_date = mysqli_real_escape_string($con, $end_date);
        $dateFilter = " AND DATE(status_date) BETWEEN '$start_date' AND '$end_date'";
    }
}

// Pagination setup
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 50;
if (!in_array($limit, [50, 100])) {
    $limit = 50;
}
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$start_from = ($page - 1) * $limit;

// Build the main query - Filter for delivered records only
$whereClause = "WHERE (tbl_courier.status = '5' OR tbl_courier.status = 'Delivered' OR tbl_courier.status = 'delivered' OR tbl_courier.status = 'DELIVERED' OR LOWER(tbl_courier.status) LIKE '%deliver%')";
if (!empty($dateFilter)) {
    $whereClause .= $dateFilter;
}

$sql = "SELECT cons_no,weight,gtotamt,rev_name,r_add,ship_name,userid,status_date,book_mode,noofpackage,tbl_courier.status,e_waybill,eway_expdate,eway_start_date,eway_end_date,gst,partno,remark,freight,invi_value,qty,
assured_dly_date,book1_date,COALESCE(a.city_name, 'Unknown') as city,type,invice_no,chweight,mode,rate,oda_mis,COALESCE(status.statusname, 'Unknown') as statusname,vehicle
FROM tbl_courier
LEFT JOIN status ON tbl_courier.status=status.statusid
LEFT JOIN tbl_city_code a ON tbl_courier.s_add=a.Id 
$whereClause 
ORDER BY cons_no DESC 
LIMIT $start_from, $limit";

$result = mysqli_query($con, $sql);

// Check number of rows returned
$num_rows = mysqli_num_rows($result);
$count = $start_from; // Start count from the current page offset
$tr = '';

// Debug: Check if query executed successfully
if (!$result) {
    die("Query failed: " . mysqli_error($con));
}

// Debug: Check number of rows returned
$num_rows = mysqli_num_rows($result);

// Check if query executed successfully
if (!$result) {
    die("Query failed: " . mysqli_error($con));
}

// Check number of rows returned
$num_rows = mysqli_num_rows($result);

// Check number of rows returned
$num_rows = mysqli_num_rows($result);

while($row = mysqli_fetch_array($result)) {
    // Shipper details
    // Get shipper information with null checks
    $sql_s = "SELECT * FROM `custreg` WHERE `custname` LIKE '".$row['ship_name']."'";
    $result_s = mysqli_query($con, $sql_s);
    $row_s = mysqli_fetch_array($result_s);

    if ($row_s) {
        $shipper_name = $row_s['custname'];
        $shipper_phone = $row_s['custphone'];
        $shipper_mail = $row_s['custmail'];
        $shipper_add = $row_s['custadd'];
        $shipper_c = $row_s['custcity'];
    } else {
        $shipper_name = $row['ship_name'];
        $shipper_phone = '';
        $shipper_mail = '';
        $shipper_add = '';
        $shipper_c = '';
    }

    // Get city information with null checks
    if (!empty($shipper_c)) {
        $sql_s = "SELECT * FROM `city` WHERE ctid=".$shipper_c;
        $result_s = mysqli_query($con, $sql_s);
        $row_s = mysqli_fetch_array($result_s);
        $shipper_city = ($row_s && isset($row_s['cityname'])) ? $row_s['cityname'] : 'Unknown';
    } else {
        $shipper_city = 'Unknown';
    }
    
    // Status dates
    if($row['status']=='6' || $row['status']=='50' || $row['status']=='57') {
        $statusdate = $row['status_date'];
    } else {
        $statusdate = "";
    }
    
    // Delivered date - initialize for all records
    if($row['status']=='5') {
        $delivereddate = $row['status_date'];
    } else {
        $delivereddate = ''; // Initialize as empty for non-delivered records
    }
    
    // Destination with null checks
    if($row['r_add'] == "") {
        $destination = "";
    } else {
        $sql1 = "SELECT city_name FROM tbl_city_code WHERE Id = '".$row['r_add']."'";
        $result1 = mysqli_query($con, $sql1);
        $row1 = mysqli_fetch_array($result1);
        $destination = ($row1 && isset($row1['city_name'])) ? $row1['city_name'] : 'Unknown';
    }
    
    // Manager details with null checks
    $sql2 = "SELECT Manager_name,empcode FROM tbl_courier_officers WHERE cid = '".$row['userid']."'";
    $result2 = mysqli_query($con, $sql2);
    $row2 = mysqli_fetch_array($result2);

    if ($row2) {
        $Manager_name = $row2['Manager_name'];  
        $empcode = $row2['empcode'];
    } else {
        $Manager_name = 'Unknown';
        $empcode = 'Unknown';
    }
    
    $count++;
    
    // Calculate GST and Total values
    $gst_value = isset($row['gst']) ? $row['gst'] : 0;
    $total_amount = isset($row['gtotamt']) ? $row['gtotamt'] : 0;

    $tr .= "<tr>
        <td>".$count."</td>
        <td>".$Manager_name."</td>
        <td>".$empcode."</td>
        <td>".$shipper_name."</td>
        <td>".$shipper_city."</td>
        <td>".$row['status_date']."</td>
        <td>".$row['cons_no']."</td>
        <td>".$row['book1_date']."</td>
        <td>".$row['invice_no']."</td>
        <td>".$row['rev_name']."</td>
        <td>".$destination."</td>
        <td>".$row['noofpackage']."</td>
        <td>".$row['qty']."</td>
        <td>".$row['partno']."</td>
        <td>".$withoutgstvalue."</td>
        <td>".$row['invi_value']."</td>
        <td>".$statusdate."</td>
        <td>".$row['book_mode']."</td>
        <td>".$row['statusname']."</td>
        <td>".$row['remark']."</td>
        <td>".$delivereddate."</td>
        <td>".$row['mode']."</td>
        <td>".$row['weight']."</td>
        <td>".$row['chweight']."</td>
        <td>".$row['e_waybill']."</td>
        <td>".$row['eway_start_date']."</td>
        <td>".$row['eway_end_date']."</td>
        <td>".$row['vehicle']."</td>
        <td>".$row['rate']."</td>
        <td>".$row['oda_mis']."</td>
        <td>".$row['freight']."</td>
        <td>".$gst_value."</td>
        <td>".$total_amount."</td>
    </tr>";
}

// Get total count for pagination
$count_sql = "SELECT COUNT(*) as total FROM tbl_courier LEFT JOIN status ON tbl_courier.status=status.statusid LEFT JOIN tbl_city_code a ON tbl_courier.s_add=a.Id $whereClause";
$count_result = mysqli_query($con, $count_sql);
$count_row = mysqli_fetch_array($count_result);
$total_records = $count_row['total'];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <style>
        .table1 {
            font-family: arial, sans-serif;
            border-collapse: collapse;
            width: 100%;
        }

        .table1 td, .table1 th {
            border: 1px solid #dddddd;
            text-align: left;
            padding: 8px;
        }

        .table1 th {
            background-color: #f2f2f2;
            font-weight: bold;
            color: #333;
            text-align: center;
        }

        /* Change date format to MM-DD-YYYY and hide placeholder */
        input[type="date"] {
            -webkit-locale: "en-US";
            color: transparent;  /* Hide placeholder initially */
        }

        input[type="date"]:focus {
            color: #000;  /* Show text when focused */
        }

        input[type="date"]:valid {
            color: #000;  /* Show text when date is selected */
        }

        /* Force MM-DD-YYYY format for WebKit browsers */
        input[type="date"]::-webkit-datetime-edit-fields-wrapper {
            display: flex;
        }

        input[type="date"]::-webkit-datetime-edit-month-field {
            order: 1;  /* Month first */
        }

        input[type="date"]::-webkit-datetime-edit-day-field {
            order: 3;  /* Day second */
        }

        input[type="date"]::-webkit-datetime-edit-year-field {
            order: 5;  /* Year last */
        }

        /* Hide the placeholder text */
        input[type="date"]::-webkit-datetime-edit-text {
            color: transparent;
        }

        input[type="date"]:focus::-webkit-datetime-edit-text {
            color: #000;
        }

        input[type="date"]:valid::-webkit-datetime-edit-text {
            color: #000;
        }

        .table1 tr:nth-child(even) {
            background-color: #dddddd;
        }
    </style>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="Content-Language" content="en-US">
    <title>Delivery Report</title>
    <link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
    <link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
    <link rel="stylesheet" href="css/customize.css">
    <link rel="stylesheet" href="css/font-awesome.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/flexslider.css">
    <link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
    <!--[if lt IE 9]>
        <script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
        <script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
        <link rel="stylesheet" href="css/font-awesome-ie7.css">
    <![endif]-->
</head>
<body>
    <?php include("header.php"); ?>
    
    <div class="container">
        <div class="row">
            <div class="span12">
                <div class="account-list-outer">
                    <div class="titleHeader clearfix">
                        <h3>Delivery Report</h3>
                        <?php if (isset($_GET['start_date']) && isset($_GET['end_date'])): ?>
                            <p style="color: #666; font-size: 14px; margin: 5px 0;">
                                <strong>Filtered by:</strong> <?php echo $_GET['start_date']; ?> to <?php echo $_GET['end_date']; ?>
                                (Total Records: <?php echo $total_records; ?>)
                            </p>
                        <?php else: ?>
                            <p style="color: #666; font-size: 14px; margin: 5px 0;">
                                <strong>Showing all delivery records</strong> (Total: <?php echo $total_records; ?>)
                            </p>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Date Filter Form -->
                    <div class="row control-group" style="margin-bottom: 15px;">
                        <div class="col-md-12">
                            <form method="get" class="form-inline">
                                <div class="form-group">
                                    <label for="start_date" style="margin-right: 5px;">Start Date: <span class="text-error">*</span></label>
                                    <div class="controls input-append date form_date" data-date="" data-date-format="dd MM yyyy" data-link-field="dtp_input1" data-link-format="yyyy-mm-dd">
                                        <input type="text" value="<?php echo isset($_GET['start_date']) ? $_GET['start_date'] : ''; ?>" id="start_date" name="start_date" readonly>
                                        <span class="add-on"><i class="icon-remove"></i></span>
                                        <span class="add-on"><i class="icon-th"></i></span>
                                    </div>
                                    <input type="hidden" id="dtp_input1" value="" />
                                </div>
                                <div class="form-group" style="margin-left: 10px;">
                                    <label for="end_date" style="margin-right: 5px;">End Date: <span class="text-error">*</span></label>
                                    <div class="controls input-append date form_date" data-date="" data-date-format="dd MM yyyy" data-link-field="dtp_input2" data-link-format="yyyy-mm-dd">
                                        <input type="text" value="<?php echo isset($_GET['end_date']) ? $_GET['end_date'] : ''; ?>" id="end_date" name="end_date" readonly>
                                        <span class="add-on"><i class="icon-remove"></i></span>
                                        <span class="add-on"><i class="icon-th"></i></span>
                                    </div>
                                    <input type="hidden" id="dtp_input2" value="" />
                                </div>
                                <button type="submit" class="btn btn-primary btn-sm">Filter</button>
                                <a href="reportDelivered.php" class="btn btn-default btn-sm">Clear</a>
                            </form>
                        </div>
                    </div>

                    <!-- Pagination Controls -->
                    <div class="row control-group" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 15px;">
                        <div class="col-md-4">
                            <form method="get" id="entryForm" class="form-inline">
                                <?php if (isset($_GET['start_date'])): ?>
                                    <input type="hidden" name="start_date" value="<?php echo $_GET['start_date']; ?>">
                                <?php endif; ?>
                                <?php if (isset($_GET['end_date'])): ?>
                                    <input type="hidden" name="end_date" value="<?php echo $_GET['end_date']; ?>">
                                <?php endif; ?>
                                <label for="limit" style="margin-right: 5px;">Show</label>
                                <select name="limit" id="limit" onchange="document.getElementById('entryForm').submit();" class="form-control input-sm">
                                    <option value="50" <?php if ($limit == 50) echo 'selected'; ?>>50</option>
                                    <option value="100" <?php if ($limit == 100) echo 'selected'; ?>>100</option>
                                </select>
                                entries
                            </form>
                        </div>

                        <div class="col-md-2 text-right">
                            <input class="btn btn-primary" type="button" value="Export" onclick="exportReport();">
                        </div>
                    </div>

                    
                    <div class="one-third last" style="height:450px;overflow:scroll;width=8000px;">
                        <table class="table1">
                            <thead>
                                <tr>
                                    <th>Sr No.</th>
                                    <th>Employee Name</th>
                                    <th>Emp-ID</th>
                                    <th>Shipper Name</th>
                                    <th>Shipper City</th>
                                    <th>Scanning Date</th>
                                    <th>LR No</th>
                                    <th>BKG Date</th>
                                    <th>Invoice No</th>
                                    <th>Customer Name</th>
                                    <th>Destination</th>
                                    <th>Cases</th>
                                    <th>Qty</th>
                                    <th>Part No.</th>
                                    <th>Without GST Value</th>
                                    <th>Invoice Value</th>
                                    <th>Godown Receipt Date</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                    <th>Remarks</th>
                                    <th>Delivery Date</th>
                                    <th>Mode</th>
                                    <th>A/Weight</th>
                                    <th>C/Weight</th>
                                    <th>E-WayBill No.</th>
                                    <th>E-Way Start Date</th>
                                    <th>E-Way End Date</th>
                                    <th>Vehicle No.</th>
                                    <th>Rate</th>
                                    <th>ODA</th>
                                    <th>Freight</th>
                                    <th>GST</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                if ($num_rows > 0) {
                                    echo $tr;
                                } else {
                                    echo "<tr><td colspan='33' style='text-align: center; padding: 20px; color: #666;'>";
                                    if (isset($_GET['start_date']) && isset($_GET['end_date'])) {
                                        echo "No records found for the selected date range (" . $_GET['start_date'] . " to " . $_GET['end_date'] . ").";
                                    } else {
                                        echo "No records found in the system.";
                                    }
                                    echo "</td></tr>";
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>

    <?php
    // Count total records for pagination
    $count_sql = "SELECT COUNT(*) as total FROM tbl_courier LEFT JOIN status ON tbl_courier.status=status.statusid LEFT JOIN tbl_city_code a ON tbl_courier.s_add=a.Id $whereClause";
    $count_result = mysqli_query($con, $count_sql);
    $count_row = mysqli_fetch_assoc($count_result);
    $total_records = $count_row['total'];
    $total_pages = ceil($total_records / $limit);

    // Pagination settings
    $adjacents = 2;
    $start_loop = ($page > $adjacents) ? $page - $adjacents : 1;
    $end_loop = ($page < ($total_pages - $adjacents)) ? $page + $adjacents : $total_pages;

    // Build URL parameters for pagination links
    $url_params = "";
    if (isset($_GET['start_date']) && isset($_GET['end_date'])) {
        $url_params = "&start_date=" . urlencode($_GET['start_date']) . "&end_date=" . urlencode($_GET['end_date']);
    }

    // Styling
    echo "<style>
    .pagination-wrapper {
        display: flex;
        justify-content: center;
        margin: 30px 0;
    }
    .pagination {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
    }
    .pagination a {
        padding: 6px 12px;
        background-color: white;
        border: 1px solid rgb(255, 106, 0);
        color: #f16325;
        text-decoration: none;
        border-radius: 4px;
    }
    .pagination a.active {
        background-color: #f16325;
        color: white;
        font-weight: bold;
    }
    .pagination a:hover {
        background-color: #f16325;
        color: white;
    }
    </style>";

    // Output pagination
    echo "<div class='pagination-wrapper'>";
    echo "<div class='pagination'>";

    // First/Previous
    if ($page > 1) {
        echo "<a href='?page=1&limit=$limit$url_params'>&laquo;</a>";
        echo "<a href='?page=" . ($page - 1) . "&limit=$limit$url_params'>Previous</a>";
    }

    // Page numbers
    for ($i = $start_loop; $i <= $end_loop; $i++) {
        if ($i == $page)
            echo "<a class='active' href='?page=$i&limit=$limit$url_params'>$i</a>";
        else
            echo "<a href='?page=$i&limit=$limit$url_params'>$i</a>";
    }

    // Next/Last
    if ($page < $total_pages) {
        echo "<a href='?page=" . ($page + 1) . "&limit=$limit$url_params'>Next</a>";
        echo "<a href='?page=$total_pages&limit=$limit$url_params'>&raquo;</a>";
    }

    echo "</div></div>";

    // Add record count info
    echo "<div style='text-align: center; margin: 10px 0; color: #666;'>";
    echo "Showing " . ($start_from + 1) . " to " . min($start_from + $limit, $total_records) . " of $total_records entries";
    echo "</div>";
    ?>
    
    <script>
        function exportReport() {
            var startDate = document.getElementById('start_date').value;
            var endDate = document.getElementById('end_date').value;

            if (!startDate || !endDate) {
                alert('Please select both Start Date and End Date before exporting.');
                return false;
            }

            if (new Date(endDate) < new Date(startDate)) {
                alert('End Date cannot be before Start Date.');
                return false;
            }

            window.location.href = 'deliveredReport_excel.php?start_date=' + encodeURIComponent(startDate) +
                                 '&end_date=' + encodeURIComponent(endDate);
        }
        
        function printpage() {
            var printButton = document.getElementById("printpagebutton");
            var back = document.getElementById("backbutton");
            printButton.style.visibility = 'hidden';
            back.style.visibility = 'hidden';
            window.print();
            printButton.style.visibility = 'visible';
            back.style.visibility = 'visible';
        }

        // Force MM-DD-YYYY format on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Set locale for date inputs
            var dateInputs = document.querySelectorAll('input[type="date"]');
            dateInputs.forEach(function(input) {
                input.setAttribute('lang', 'en-US');
            });
        });
    </script>

    <!-- Bootstrap Datetimepicker CSS and JS -->
    <link href="./bootstrap/css/bootstrap-datetimepicker.min.css" rel="stylesheet" media="screen">
    <script type="text/javascript" src="./jquery/jquery-1.8.3.min.js" charset="UTF-8"></script>
    <script type="text/javascript" src="./bootstrap/js/bootstrap.min.js"></script>
    <script type="text/javascript" src="./jquery/bootstrap-datetimepicker.js" charset="UTF-8"></script>
    <script type="text/javascript">
        $(".form_date").datetimepicker({
            language: "pl",
            weekStart: 1,
            todayBtn: 1,
            autoclose: 1,
            todayHighlight: 1,
            startView: 2,
            minView: 2,
            forceParse: 0
        });
    </script>

    <?php include("footer.php"); ?>
</body>
</html>

