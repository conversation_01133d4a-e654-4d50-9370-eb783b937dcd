/**
 * Hungarian translation for bootstrap-datetimepicker
 * darevish <http://github.com/darevish>
 */
;(function($){
	$.fn.datetimepicker.dates['hu'] = {
		days: ["<PERSON>as<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>t<PERSON>rtök", "Péntek", "Szombat", "Vasárnap"],
		daysShort: ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Vas"],
		daysMin: ["V", "H", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "V"],
		months: ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>pt<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "November", "December"],
		monthsShort: ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"],
		today: "<PERSON>",
		suffix: [],
		meridiem: [],
		weekStart: 1
	};
}(j<PERSON>uer<PERSON>));
