<?php
require_once('database.php');
require 'connection.php';


 $unm=$_POST["txtusername"];
 $pass=$_POST["txtpassword"];
 $uname = $con->real_escape_string($_POST["txtusername"]);
$password = $con->real_escape_string($_POST["txtpassword"]);
$flag=1;


echo $sql="select * from login where username='".$uname."' and password= '".$password."' ";

 $result=mysqli_query($con,$sql);
//$sql=mysqli_query($con,"select * from Login where (usrname='$_POST[uname]' and  password='$_POST[pass]')");

 while($row = mysqli_fetch_array($result))
 {
echo	$flag=0;
	session_start(); 
echo	$htht=$row['username'];
	$_SESSION['uname']=$unm;
	$name=$unm;
	$_SESSION['upass']=$pass;
	$_SESSION['utype']=$row['userType'];
	$_SESSION['UserId']=$row['UserId'];
	$_SESSION['last_activity']=time();
		
	$flag2=true;
	
header("Location:addCourier.php");   
			    
			    
 
}
if($flag==1)
{
header("Location:index.php?err=* The User Name or password you entered is incorrect.<br>");
}
?>