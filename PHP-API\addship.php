<?php
require 'conn.php';
date_default_timezone_set('Asia/Kolkata');
$date = date('Y/m/d h:i:s', time());

$date1= date('d/m/Y');	 
$id=$_POST['id'];
$qrcode=$_POST['qrcode'];
$status=$_POST['status'];
$cityid=$_POST['city_ID'];
$remark=$_POST['remark'];

//$target_path = "images/attach/";
/*$target_path = $target_path.$_FILES['photo']['name']; 
if(move_uploaded_file($_FILES['photo']['tmp_name'], $target_path))
{
}
else
{
	
} */

$Sql_Query = "UPDATE `tbl_courier` SET status='$status',status_date=NOW(),userid='$id' where cons_no='$qrcode' ";
//$Sql_Query = "UPDATE `tbl_courier` SET status='$status',status_date = NOW(),userid='$id', pod_img='".$_FILES['photo']['name']."',remark='$remark',status_update_datetime ='$date' where cons_no='$qrcode' ";


 
if(mysqli_query($con,$Sql_Query))
{
 
echo $target_path;
  }
else{
 
 echo 'Try Again';
 
 }
 
  $insertquery="INSERT INTO `status_history_tbl`(`Cons_Id`, `status`, `Date`, `current_loc`,`remark`) VALUES ('$qrcode','$status','$date','$cityid','$remark')";
 mysqli_query($con,$insertquery);	
 
 mysqli_close($con);
 
?>