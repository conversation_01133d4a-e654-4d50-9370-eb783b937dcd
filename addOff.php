<?php 
session_start();
require_once('library.php');
$rand = get_rand_id(8);
//echo $rand;

$a=$_SESSION['username'];
 $sql="select * from login where username='$a'";
$result = mysqli_query($con, $sql);
$row1 = mysqli_fetch_array($result, MYSQLI_BOTH);
$userid = ($row1 && isset($row1['rid'])) ? $row1['rid'] : 0;

?>
<!DOCTYPE html >
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>

	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
<script type="text/JavaScript">
<!--
function MM_findObj(n, d) { //v4.01
  var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {
    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}
  if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];
  for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);
  if(!x && d.getElementById) x=d.getElementById(n); return x;
}

function MM_validateForm() { //v4.0
  var i,p,q,nm,test,num,min,max,errors='',args=MM_validateForm.arguments;
  for (i=0; i<(args.length-2); i+=3) { test=args[i+2]; val=MM_findObj(args[i]);
    if (val) { nm=val.name; if ((val=val.value)!="") {
      if (test.indexOf('isEmail')!=-1) { p=val.indexOf('@');
        if (p<1 || p==(val.length-1)) errors+='- '+nm+' must contain an e-mail address.\n';
      } else if (test!='R') { num = parseFloat(val);
        if (isNaN(val)) errors+='- '+nm+' must contain a number.\n';
        if (test.indexOf('inRange') != -1) { p=test.indexOf(':');
          min=test.substring(8,p); max=test.substring(p+1);
          if (num<min || max<num) errors+='- '+nm+' must contain a number between '+min+' and '+max+'.\n';
    } } } else if (test.charAt(0) == 'R') errors += '- '+nm+' is required.\n'; }
  } if (errors) alert('The following error(s) occurred:\n'+errors);
  document.MM_returnValue = (errors == '');
}
//-->
</script>
	
</head>
<?php
include("header.php");
?>

		<div class="container">

			<div class="row">

				<div class="span2">
				
				</div><!--end span8-->
                
                
                <div class="span8">
					<div class="register">

						<div class="titleHeader clearfix">
							<h3>Register New Office </h3>
						</div><!--end titleHeader-->

						<form action="process.php?action=add-office" method="post" name="shipoff" class="form-horizontal">

                           <legend>&nbsp;&nbsp;&nbsp;&nbsp; New Office Information :</legend>

							<div class="control-group">
							    <label class="control-label" for="OfficeName">Office Name : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="OfficeName" placeholder="office name">
							      <!--<span class="help-inline"><i class="icon-ok"></i> Avaliable input!</span>--><input type="hidden" name="userid" value="<?php echo $userid;?>">
							    </div>
							</div><!--end control-group-->
							<div class="control-group">
							    <label class="control-label" for="PhoneNo">Phone No : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="number" name="PhoneNo" placeholder="phone no">
							    </div>
							</div><!--end control-group-->
							
								<div class="control-group">
							    <label class="control-label" for="ContactPerson">Contact Person : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="ContactPerson" placeholder="contact name">
							    </div>
							</div><!--end control-group-->
							
								<div class="control-group">
							    <label class="control-label" for="PhoneNo">Mobile No : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="number" name="mobno" placeholder="Mobile no">
							    </div>
							</div><!--end control-group-->
							<div class="control-group">
							    <label class="control-label" for="OfficeEmail">E-Mail : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="email" name="OfficeEmail" placeholder="<EMAIL>">
							    </div>
							</div><!--end control-group-->
							<div class="control-group ">
							    <label class="control-label" for="OfficeAddress">Address : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="OfficeAddress" placeholder="office address">
							      <!--<span class="help-inline">-->
							    </div>
							</div><!--end control-group   <i class="icon-remove"></i> Invalid input!</span>-->
<div class="control-group success">
							    <label class="control-label" for="custzip">Zip Code: <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="officezip" id="officezip" placeholder="Zip Code">
							      <!--<span class="help-inline"><i class="ion-ok"></i> Avaliable input!</span>-->
							    </div>
							</div><!--end control-group-->
							<div class="control-group">
							    <label class="control-label" for="City">City : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="City" placeholder="city">
							    </div>
							</div><!--end control-group-->

							<!--<div class="control-group">
							    <label class="control-label" for="inputPostCode">Post Code : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="inputPostCode" placeholder="post code">
							    </div>
							</div><!--end control-group-->

							<!--<div class="control-group">
							    <div class="control-label">Contury : <span class="text-error">*</span></div>
							    <div class="controls">
							      <select name="">
								    <option selected="selected" value="">--Select Contury--</option>
							      	<option value="#">Contury1</option>
							      	<option value="#">Contury2</option>
							      	<option value="#">Contury3</option>
							      	<option value="#">Contury4</option>
							      	<option value="#">Contury5</option> 
							      </select>
							    </div>
							</div> end control-group-->

							<div class="control-group ">
							    <label class="control-label" for="OfficeTiming">Office Timing : </label>
							    <div class="controls">
							      <input type="text" name="OfficeTiming" placeholder="office timing ">
							      <!--<span class="help-inline"><i class="icon-ok"></i> Avaliable input!</span>-->
							    </div>
							</div><!--end control-group-->
						
							
<div class="control-group">
							    <label class="control-label" for="inputPostCode">Add To HUB : </label>
							    <div class="controls">
							      <input type="checkbox" name="addhub" value="hub">
							    </div>
							</div><!--end control-group--><br>



							
    						<div class="control-group">
							    <div class="controls">
							        <button type="submit" class="btn btn-primary" name="submit" onClick="return validateForm()">Register</button>&nbsp;&nbsp;
									<button type="reset" class="btn">Clear</button>
							    </div>
							</div><!--end control-group-->
							
							

						</form><!--end form-->
						
						

					</div><!--end register-->
				
				</div><!--end span-->


				
			</div><!--end row-->



			<!--end row-->


			<!--end row-->

		<script  type="text/javascript">
function validateForm()
{
var x=document.forms["shipoff"]["OfficeName"].value;
if (x==null || x=="")
  {
  alert("Office Name must be filled out");
  return false;
  }
  
  var phone=document.forms["shipoff"]["PhoneNo"].value;
if (phone==null || phone=="")
  {
   alert("Phone No must be filled out");
  return false;
  
  }
  
 var custemail=document.forms["shipoff"]["OfficeEmail"].value;
if (custemail==null || custemail=="")
  {
  alert("Email must be filled out");
  return false;
  }
  var officeadd=document.forms["shipoff"]["OfficeAddress"].value;
if (officeadd==null || officeadd=="")
  {
  alert("Address must be filled out");
  return false;
  }
  
 var officezip1=document.forms["shipoff"]["officezip"].value;
if (officezip1==null || officezip1=="")
  {
  alert("Please filled zip code");
  return false;
  }

  var officecity=document.forms["shipoff"]["City"].value;
if (officecity==null || officecity=="")
  {
  alert("City must be filled out");
  return false;
  }
  
 
  var add=document.forms["shipoff"]["ContactPerson"].value;
if (add==null || add=="")
  {
  alert("Contact Name must be filled out");
  return false;
  }

}
</script>
 <?php
include("footer.php");
?>