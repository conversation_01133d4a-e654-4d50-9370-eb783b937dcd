<?php
session_start();
require 'connection.php';
require_once('database.php');
require_once('library.php');
isUser();

// Set filters from POST or GET (for pagination)
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $_SESSION['date2'] = $_POST['date2'];
    $_SESSION['date4'] = $_POST['date4'];
    $_SESSION['rep'] = $_POST['rep'];
} elseif (isset($_GET['date2']) && isset($_GET['date4']) && isset($_GET['rep'])) {
    $_SESSION['date2'] = $_GET['date2'];
    $_SESSION['date4'] = $_GET['date4'];
    $_SESSION['rep'] = $_GET['rep'];
}

// Initialize variables
$date = isset($_SESSION['date2']) ? $_SESSION['date2'] : '';
$date2 = isset($_SESSION['date4']) ? $_SESSION['date4'] : '';
$rep = isset($_SESSION['rep']) ? $_SESSION['rep'] : '';

// Pagination setup
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 50;
if (!in_array($limit, [50, 100])) {
    $limit = 50;
}
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$start_from = ($page - 1) * $limit;

// Initialize dropdown variable
$drop1 = '';
$sql="select * from status";
$result = mysqli_query($con,$sql);
while($row = mysqli_fetch_array($result)) {
    $selected = ($rep == $row['statusid']) ? 'selected' : '';
    $drop1=$drop1."<option value='".$row['statusid']."' $selected>".$row['statusname']."</option>";
}

// Generate table data if filters are set
$tr = '';
if (!empty($date) && !empty($date2) && !empty($rep)) {
    $Cdate = date('Y-m-d', strtotime($date));
    $Cdate2 = date('Y-m-d', strtotime($date2));

    // Debug output
    echo "<!-- Debug: date=$date ($Cdate), date2=$date2 ($Cdate2), rep=$rep -->";

    $sql = "SELECT * FROM tbl_courier inner join status on tbl_courier.status=status.statusid WHERE tbl_courier.status='$rep' and tbl_courier.book_date between '$Cdate' and '$Cdate2' ORDER BY cons_no DESC LIMIT $start_from, $limit";

    echo "<!-- Debug Query: $sql -->";

    $result = mysqli_query($con,$sql);

    if (!$result) {
        echo "<!-- SQL Error: " . mysqli_error($con) . " -->";
        $tr = '<tr><td colspan="11">Database error occurred</td></tr>';
    } else {
        $num_rows = mysqli_num_rows($result);
        echo "<!-- Debug: Found $num_rows rows -->";

        if ($num_rows == 0) {
            $tr = '<tr><td colspan="11">No records found for the selected criteria.</td></tr>';
        } else {
            $count = $start_from;
            while($row=mysqli_fetch_array($result)) {
                $count++;
                // Handle missing fields safely
                $city_name = isset($row['city_name']) ? $row['city_name'] : '';
                $type = isset($row['type']) ? $row['type'] : '';
                $weight = isset($row['weight']) ? $row['weight'] : '';
                $invice_no = isset($row['invice_no']) ? $row['invice_no'] : '';
                $mode = isset($row['mode']) ? $row['mode'] : '';
                $book_mode = isset($row['book_mode']) ? $row['book_mode'] : '';

                $tr=$tr.'<tr><td>'.$count.'</td><td>'.$row['cons_no'].'</td><td>'.$row['ship_name'].'</td><td>'.$row['rev_name'].'</td><td>'.$row['statusname'].'</td><td>'.$city_name.'</td><td>'.$type.'</td><td>'.$weight.'</td><td>'.$invice_no.'</td><td>'.$mode.'</td><td>'.$book_mode.'</td></tr>';
            }
        }
    }
} else {
    echo "<!-- Debug: Missing filters - date: '$date', date2: '$date2', rep: '$rep' -->";
}
?>
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>

	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons 
	================================================== -->
<script src="js/jquery-1.7.2.min.js"></script>
   
</head>
<?php include("header.php"); ?>

   <div class="container">
			<div class="row">
     			<div class="span2">
				<div class="control-group ">
				
				</div>
				</div><!--end span3-->
                
                <div class="span8">
					<div class="account-list-outer">
						<div class="titleHeader clearfix">
							<h3>Status Report</h3>
						</div><!--end titleHeader-->

						<form method="post" action="">
						<div class="control-group ">
						   <div class="controls">
							 <label class="control-label"> Status Report : <span class="text-error">*</span></label>
				                 <select name="rep" id="rep">
										<option value="">------- Please Select-------</option>
									<?php echo $drop1; ?>
								 </select>

          					</div>
						</div>
						<div class="control-group ">
						   <div class="controls">
							<label for="rep"> Start Date : <span class="text-error">*</span></label>
								<div class="controls input-append date form_date" data-date="" data-date-format="dd MM yyyy" data-link-field="dtp_input2" data-link-format="yyyy-mm-dd">
									 <input  type="text" value="<?php echo htmlspecialchars($date); ?>" id="date2" name="date2" readonly>
										<span class="add-on"><i class="icon-remove"></i></span>
										 <span class="add-on"><i class="icon-th"></i></span>
								</div>
									 <input type="hidden" id="dtp_input2" value="" />
						    </div>
						</div>
						<div class="control-group ">
						   <div class="controls">
							<label class="control-label" for="rep"> End Date : <span class="text-error">*</span></label>
								<div  class="controls input-append date form_date" data-date="" data-date-format="dd MM yyyy" data-link-field="dtp_input2" data-link-format="yyyy-mm-dd">
									<input  type="text" value="<?php echo htmlspecialchars($date2); ?>" id="date4" name="date4" readonly>
										<span class="add-on"><i class="icon-remove"></i></span>
										 <span class="add-on"><i class="icon-th"></i></span>
								</div>
									 <input type="hidden" id="dtp_input2" value="" />
						   </div>
						</div>

							<div class="control-group ">
							     <div class="controls">
								     <input class="btn btn-primary" type="submit" value="Submit">
								       <input class="btn btn-primary" type="button" value="Export" onclick="exportToExcel();" >
							     </div>
							</div>
							</form>

					<?php if (!empty($date) && !empty($date2) && !empty($rep)): ?>
					<!-- Records per page selector -->
					<div class="row control-group" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 15px;">
					    <div class="col-md-4">
					        <form method="get" id="entryForm" class="form-inline">
					            <label for="limit" style="margin-right: 5px;">Show</label>
					            <select name="limit" id="limit" onchange="document.getElementById('entryForm').submit();" class="form-control input-sm">
					                <option value="50" <?php if ($limit == 50) echo 'selected'; ?>>50</option>
					                <option value="100" <?php if ($limit == 100) echo 'selected'; ?>>100</option>
					            </select>
					            entries

					            <!-- Keep filters in form -->
					            <input type="hidden" name="date2" value="<?php echo htmlspecialchars($date); ?>">
					            <input type="hidden" name="date4" value="<?php echo htmlspecialchars($date2); ?>">
					            <input type="hidden" name="rep" value="<?php echo htmlspecialchars($rep); ?>">
					            <input type="hidden" name="page" value="<?php echo $page; ?>">
					        </form>
					    </div>

					    <div class="col-md-2 text-right">
					        <input class="btn btn-primary" type="button" value="Export" onclick="exportToExcel();">
					    </div>
					</div>

					<div style="overflow-x:auto!important; height:350px;">
						<table class="table" align="center">
						<thead>
							<tr>
							     <th><h6>Sr No. </h6></th>
								<th><h6>Docket No </h6></th>
								<th><h6>Shipper_Name </h6></th>
								<th><h6>Receiver_Name </h6></th>
								<th><h6>Status </h6></th>
								<th><h6>Receiver_Address </h6></th>
								<th><h6>Type of Shipment </h6></th>
								<th><h6>Weight </h6></th>
								<th><h6>Invoice No </h6></th>
								<th><h6>Mode </h6></th>
								<th><h6>Booking Mode </h6></th>
							</tr>
						</thead>
						<tbody>
							<?php echo $tr; ?>
						</tbody>
					</table>
					</div>
					<?php endif; ?>

					</div><!--end -->
				</div><!--end span6-->
			</div><!--end row-->
		</div><!--end conatiner-->

		<?php
		// Add pagination if data is loaded
		if (!empty($date) && !empty($date2) && !empty($rep)) {
		    // Count total records for pagination
		    $Cdate = date('Y-m-d', strtotime($date));
		    $Cdate2 = date('Y-m-d', strtotime($date2));
		    $count_sql = "SELECT COUNT(*) as total FROM tbl_courier inner join status on tbl_courier.status=status.statusid WHERE tbl_courier.status='$rep' and tbl_courier.book_date between '$Cdate' and '$Cdate2'";
		    $count_result = mysqli_query($con, $count_sql);
		    $count_row = mysqli_fetch_assoc($count_result);
		    $total_records = $count_row['total'];
		    $total_pages = ceil($total_records / $limit);

		    $date2_encoded = urlencode($date);
		    $date4_encoded = urlencode($date2);
		    $rep_encoded = urlencode($rep);

		    $adjacents = 2;
		    $start_loop = ($page > $adjacents) ? $page - $adjacents : 1;
		    $end_loop = ($page < ($total_pages - $adjacents)) ? $page + $adjacents : $total_pages;

		    // Styling
		    echo "<style>
		    .pagination-wrapper {
		        display: flex;
		        justify-content: center;
		        margin: 30px 0;
		    }
		    .pagination {
		        display: flex;
		        flex-wrap: wrap;
		        gap: 5px;
		    }
		    .pagination a {
		        padding: 6px 12px;
		        background-color: white;
		        border: 1px solid rgb(255, 106, 0);
		        color: #f16325;
		        text-decoration: none;
		        border-radius: 4px;
		    }
		    .pagination a.active {
		        background-color: #f16325;
		        color: white;
		        font-weight: bold;
		    }
		    .pagination a:hover {
		        background-color: #f16325;
		        color: white;
		    }
		    </style>";

		    // Output pagination
		    echo "<div class='pagination-wrapper'>";
		    echo "<div class='pagination'>";

		    // First/Previous
		    if ($page > 1) {
		        echo "<a href='?page=1&date2=$date2_encoded&date4=$date4_encoded&rep=$rep_encoded&limit=$limit'>&laquo;</a>";
		        echo "<a href='?page=" . ($page - 1) . "&date2=$date2_encoded&date4=$date4_encoded&rep=$rep_encoded&limit=$limit'>Previous</a>";
		    }

		    // Page numbers
		    for ($i = $start_loop; $i <= $end_loop; $i++) {
		        if ($i == $page)
		            echo "<a class='active' href='?page=$i&date2=$date2_encoded&date4=$date4_encoded&rep=$rep_encoded&limit=$limit'>$i</a>";
		        else
		            echo "<a href='?page=$i&date2=$date2_encoded&date4=$date4_encoded&rep=$rep_encoded&limit=$limit'>$i</a>";
		    }

		    // Next/Last
		    if ($page < $total_pages) {
		        echo "<a href='?page=" . ($page + 1) . "&date2=$date2_encoded&date4=$date4_encoded&rep=$rep_encoded&limit=$limit'>Next</a>";
		        echo "<a href='?page=$total_pages&date2=$date2_encoded&date4=$date4_encoded&rep=$rep_encoded&limit=$limit'>&raquo;</a>";
		    }

		    echo "</div></div>";

		    // Add record count info
		    echo "<div style='text-align: center; margin: 10px 0; color: #666;'>";
		    echo "Showing " . ($start_from + 1) . " to " . min($start_from + $limit, $total_records) . " of $total_records entries";
		    echo "</div>";
		}
		?>
<!-- <link href="./bootstrap/css/bootstrap.min.css" rel="stylesheet" media="screen">-->
    <link href="./bootstrap/css/bootstrap-datetimepicker.min.css" rel="stylesheet" media="screen">		
<script type="text/javascript" src="./jquery/jquery-1.8.3.min.js" charset="UTF-8"></script>
<script type="text/javascript" src="./bootstrap/js/bootstrap.min.js"></script>
<script type="text/javascript" src="./jquery/bootstrap-datetimepicker.js" charset="UTF-8"></script>
<!--<script type="text/javascript" src="./jquery/locales/bootstrap-datetimepicker.pl.js" charset="UTF-8"></script>-->
<script type="text/javascript">
   	$('.form_date').datetimepicker({
        language:  'pl',
        weekStart: 1,
        todayBtn:  1,
		autoclose: 1,
		todayHighlight: 1,
		startView: 2,
		minView: 2,
		forceParse: 0
    });
	
</script>


<script>
function exportToExcel()
{
var date=document.getElementById("date2").value;
var date2=document.getElementById("date4").value;
var rep=document.getElementById("rep").value;

if(rep=='')
{
alert('Please Select Status Report Name');
return;
}
else if(date=='')
{
alert('Please Fill Start Date');
return;
}
else if(date2=='')
{
alert('Please Fill End Date');
return;
}

location.href='statuswise_exel.php?id='+rep+'&date1='+date+'&date2='+date2;
}
</script>
		
		
<?php 


include("footer.php"); ?>