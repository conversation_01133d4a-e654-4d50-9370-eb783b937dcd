<?php
require_once('connection_inventory.php');

if (isset($_POST['part_no'])) {
    $partNo = mysqli_real_escape_string($conInventory, $_POST['part_no']);
    
    $query = "SELECT noofpackages, qty_inovice FROM inward_material WHERE part_no = '$partNo' LIMIT 1";
    $result = mysqli_query($conInventory, $query);
    
    if (!$result) {
        echo json_encode(['error' => mysqli_error($conInventory)]);
        exit;
    }
    
    if (mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        // Rename the key for consistency in JavaScript
        $response = [
            'noofpackages' => $row['noofpackages'],
            'qty_invoice' => $row['qty_inovice']
        ];
        echo json_encode($response);
    } else {
        echo json_encode(['error' => 'No data found']);
    }
} else {
    echo json_encode(['error' => 'No part number provided']);
}
?>



