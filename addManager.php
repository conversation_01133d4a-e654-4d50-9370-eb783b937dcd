<?php
session_start();
require_once('database.php');
require_once('library.php');
isUser();

 $a=$_SESSION['username'];
$sql="select * from login where username='$a'";
$result = mysqli_query($con,$sql);
$row1 = mysqli_fetch_array($result, MYSQLI_BOTH);
$userid = ($row1 && isset($row1['rid'])) ? $row1['rid'] : 0;

// Initialize variables
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$company = $_POST['company'] ?? '';
$loc = $_POST['loc'] ?? '';

$sql = "SELECT DISTINCT(off_name)
		FROM tbl_offices";
$result = mysqli_query($con,$sql);
$_SESSION['is']=$id;
$sql3="select * from `tbl_courier_officers`where cid='".$id."' ";
 $result3=mysqli_query($con,$sql3);
while($row3=mysqli_fetch_array($result3)){

}
$sql1="select * from `tbl_offices` ORDER BY off_name ASC";
 $result2=mysqli_query($con,$sql1);
while($row2=mysqli_fetch_array($result2))
{
	if($company==$row2['id'])
	{
	$loc=$loc."<option value='".$row2['id']."' selected>".$row2['off_name']."</option>";
	}
	else{
	$loc=$loc."<option value='".$row2['id']."' >".$row2['off_name']."</option>";
	}
}

$query = "SELECT MAX(cid) AS EMPid FROM tbl_courier_officers ";  
    if($result = mysqli_query($con,$query))
    {
  while ($row = mysqli_fetch_assoc($result))
  {
        $count = $row['EMPid'];
        $count = $count+1;

      $code_no = str_pad($count, 7, "EMP000", STR_PAD_LEFT);
  }
	}

?>

<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>

	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
	
</head>

<?php
include("header.php");
?>

		<div class="container">

			<div class="row">

				<div class="span2">
				
				</div><!--end span8-->
                
                
                <div class="span8">
					<div class="register">

						<div class="titleHeader clearfix">
							<h3>Register Employee  </h3>
						</div><!--end titleHeader-->

						<form action="process.php?action=add-manager" method="post" name="manage" class="form-horizontal">

                           <legend>&nbsp;&nbsp;&nbsp;&nbsp;  Employee Personal Information :</legend>
                           	<div class="control-group">
							    <label class="control-label" for="ManagerName">Employee Code : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="Emcode" placeholder="Employee Code" value="<?php echo $code_no; ?>" readonly>
							      <!--<span class="help-inline"><i class="icon-ok"></i> Avaliable input!</span>-->
							    </div>
							</div><!--end control-group-->
<div class="control-group">
 <label class="control-label" for="OfficeName">Select Office Name : <span class="text-error">*</span></label>
							    <div class="controls">
							      <select name="OfficeName"  id="OfficeName" >
												<option value="">-- Please select --</option>
												<?php echo $loc; ?>
								</select>
							    </div>
							</div> <!--end control-group-->

							<div class="control-group">
							    <label class="control-label" for="ManagerName">Employee Name : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="ManagerName" placeholder="Employee Name">
							      <!--<span class="help-inline"><i class="icon-ok"></i> Avaliable input!</span>-->
							    </div>
							</div><!--end control-group-->
						<!--end control-group-->
							
							<div class="control-group">
							    <label class="control-label" for="PhoneNo">Phone No : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="PhoneNo" placeholder="phone no">
							    </div>
							</div><!--end control-group-->
							<div class="control-group">
							    <label class="control-label" for="Email">E-Mail : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="email" name="Email" placeholder="<EMAIL>">
							    </div>
							</div><!--end control-group-->
							<div class="control-group ">
							    <label class="control-label" for="Address">Current Address : <span class="text-error">*</span></label>
							    <div class="controls">
							      <textarea type="text" name="Address" placeholder=" Address" ></textarea>
							      <!--<span class="help-inline">-->
							    </div>
							</div><!--end control-group   <i class="icon-remove"></i> Invalid input!</span>-->
															<div class="control-group ">
							    <label class="control-label" for="Address">Permanant Address : <span class="text-error">*</span></label>
							    <div class="controls">
							      <textarea type="text" name="Address1" placeholder=" Address" ></textarea>
							      <!--<span class="help-inline">-->
							    </div>
							</div>
							
								<div class="control-group ">
							    <label class="control-label" for="Address">Blood Group : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="bloodgroup" placeholder="Blood Group">
							      <!--<span class="help-inline">-->
							    </div>
							</div>
							 <!--<div class="control-group ">
				<label class="control-label">  Upload Photo : </label>
			       <div class="controls">
						<input type="file" name="photo" id="photo" />
					</div>
			</div>--><!--end control-group  -->
         
							
						
								<div class="control-group ">
							    <label class="control-label" for="Address">Designation : <span class="text-error">*</span></label>
							    <div class="controls">
							       <input type="text" name="desgn" placeholder="Designation">
							      <!--<span class="help-inline">-->
							    </div>
							</div>
							
							
						
								<input type="hidden" name="cid" value="<?php echo $userid;?>">
							<div class="control-group">
							    <label class="control-label" for="uname">User Name : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="uname" placeholder="User Name">
							      <!--<span class="help-inline"><i class="icon-ok"></i> Avaliable input!</span>-->
							    </div>
							</div><!--end control-group-->
							
							<div class="control-group">
							    <label class="control-label" for="fPassword">Password: <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="password" name="fPassword" placeholder="**********">
							    </div>
							</div><!--end control-group-->

							<div class="control-group">
							    <label class="control-label" for="Password">Re-Type Password: <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="password" name="Password" placeholder="**********">
							    </div>
							</div>
							
    						<div class="control-group">
							    <div class="controls"><br>
							        <button type="submit" class="btn btn-primary" name="submit" onClick="return validateForm()">Register</button>&nbsp;&nbsp;
									<button type="reset" class="btn">Clear</button>
							    </div>
							</div><!--end control-group-->
							
							

						</form><!--end form-->
						
							
      
	
						
						<!--<div class="control-group">
							    <label class="control-label" for="inputCity">City : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="inputCity" placeholder="city">
							    </div>
							</div><!--end control-group-->

							<!--<div class="control-group">
							    <label class="control-label" for="inputPostCode">Post Code : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="inputPostCode" placeholder="post code">
							    </div>
							</div><!--end control-group-->

							<!--<div class="control-group">
							    <div class="control-label">Contury : <span class="text-error">*</span></div>
							    <div class="controls">
							      <select name="">
								    <option selected="selected" value="">--Select Contury--</option>
							      	<option value="#">Contury1</option>
							      	<option value="#">Contury2</option>
							      	<option value="#">Contury3</option>
							      	<option value="#">Contury4</option>
							      	<option value="#">Contury5</option> 
							      </select>
							    </div>
							</div> end control-group-->

					</div><!--end register-->
				
				</div><!--end span-->


				
			</div><!--end row-->



			<!--end row-->


			<!--end row-->


		</div><!--end conatiner-->

	
<script  type="text/javascript">


function validateForm()
{
    var OfficeName1=document.forms["manage"]["OfficeName"].value;
if (OfficeName1==null || OfficeName1=="")
  {
   alert("Please Select Office Name ");
  return false;
  
  }

var x=document.forms["manage"]["ManagerName"].value;
if (x==null || x=="")
  {
  alert("Manager Name must be filled out");
  return false;
  }
  
  var phone=document.forms["manage"]["uname"].value;
if (phone==null || phone=="")
  {
   alert("User Name must be filled out");
  return false;
  
  }
  
 var custadd=document.forms["manage"]["fPassword"].value;
if (custadd==null || custadd=="")
  {
  alert("Password must be filled out");
  return false;
  }
  var add=document.forms["manage"]["Password"].value;
if (add==null || add=="")
  {
  alert("Re-Type Password must be filled out");
  return false;
  }
  if(custadd!=add)
  {
	  alert("Password Not Match");
	  return false;
  }
  
  var x=document.forms["manage"]["PhoneNo"].value;
if (x==null || x=="")
  {
  alert("Phone No. must be filled out");
  return false;
  }
  
  var phone=document.forms["manage"]["Email"].value;
if (phone==null || phone=="")
  {
   alert("Email must be filled out");
  return false;
  
  }
  
  var x=document.forms["manage"]["Address"].value;
if (x==null || x=="")
  {
  alert("Address must be filled out");
  return false;
  }
  


}
</script>		


<?php
include("footer.php");
?>