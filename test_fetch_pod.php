<?php
// Clean output buffer
if (ob_get_level()) {
    ob_end_clean();
}
ob_start();

// Set headers
header('Content-Type: application/json');
ini_set('display_errors', 0);
error_reporting(0);

// Clean any previous output
ob_clean();

// Test database connection
include("connection.php");

if (!$con) {
    echo json_encode(['status' => 'error', 'message' => 'Database connection failed']);
    exit;
}

// Test basic query
try {
    $test_sql = "SELECT COUNT(*) as total FROM tbl_courier WHERE YEAR(book_date) = 2025 AND MONTH(book_date) = 7";
    $result = mysqli_query($con, $test_sql);
    
    if (!$result) {
        echo json_encode(['status' => 'error', 'message' => 'Query failed: ' . mysqli_error($con)]);
        exit;
    }
    
    $data = mysqli_fetch_assoc($result);
    
    echo json_encode([
        'status' => 'success',
        'message' => 'Database test successful',
        'total_records' => $data['total'],
        'test_time' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    echo json_encode(['status' => 'error', 'message' => 'Exception: ' . $e->getMessage()]);
}
exit;
?>
