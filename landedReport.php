<?php
session_start();
include("connection.php");
require_once('database.php');
require_once('library.php');
isUser();

$a=$_SESSION['username'];
//echo $a;
$sql="select * from login where username='$a'";
$result = mysqli_query($con, $sql);
$row1 = mysqli_fetch_array($result, MYSQLI_BOTH);
$userid=$row1['rid'];

$num_rec_per_page=20;
if (isset($_GET["page"])) { $page  = $_GET["page"]; } else { $page=1; }; 
$start_from = ($page-1) * $num_rec_per_page; 

//$sql = "SELECT cons_no, ship_name, rev_name,STATUS , r_add,TYPE , weight, invice_no,MODE , book_mode FROM tbl_courier WHERE STATUS = 3 ORDER BY cons_no DESC LIMIT $start_from, $num_rec_per_page";


$sql="SELECT cons_no,book1_date,a.city_name as city ,tbl_city_code.city_name , chweight,mode,weight,noofpackage,ship_name,rev_name,type,dod_cod,dock_charg,qty,oda_mis,invi_value,invice_no,statusname,book_mode FROM (tbl_courier inner join status on tbl_courier.status=status.statusid) join tbl_city_code on tbl_city_code.Id= tbl_courier.r_add join tbl_city_code a on tbl_courier.s_add=a.Id WHERE status = '3' ORDER BY cons_no DESC LIMIT $start_from, $num_rec_per_page";
$result = mysqli_query($con,$sql);	
$count=0;
while($row=mysqli_fetch_array($result))
{
	$count++;
	
	$tr=$tr.'<tr><td>'.$count.'</td><td>'.$row['cons_no'].'</td><td>'.$row['ship_name'].'</td><td>'.$row['rev_name'].'</td><td>'.$row['statusname'].'</td><td>'.$row['city_name'].'</td><td>'.$row['type'].'</td><td>'.$row['weight'].'</td><td>'.$row['invice_no'].'</td><td>'.$row['mode'].'</td><td>'.$row['book_mode'].'</td></tr>';
	
}
	
?>
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>

	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->

<script language="JavaScript">
var checkflag = "false";

function check(field) {
if (checkflag == "false")
 {
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll")
	{
	field[i].checked=true;	
	}
	}
	checkflag = "true";
}
else
{
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll")
	{
	field[i].checked=false;
	}
	}
	checkflag = "false";
}

}
function confirmDel(field,msg)
{
	count=0;
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll" && field[i].checked==true)
	{
	count++;
	}
	}
	
	if(count == 0)
	{
		alert("Select any one record to delete!");
		return false;
	}
	else
	{
		return confirm(msg);
	}
}
</script>
	
</head> 

<?php include("header.php"); ?>
		<div class="container">

			<div class="row">

                <div class="span12">
					<div class="account-list-outer">

						<div class="titleHeader clearfix">
							<h3>Landed Report</h3>
						</div><!--end titleHeader-->

						<table class="table">
						<thead>
						
							<tr>
							     <th><h6>Sr No. </h6></th>
								<th><h6>Consignment No </h6></th>
								<th><h6>Shipper_N </h6></th>
								<th><h6>Receiver_N </h6></th>
								<th><h6>Status </h6></th>
								<th><h6>R_Address </h6></th>
								<th><h6>Type of Shipment </h6></th>
								<th><h6>Weight </h6></th>
								<th><h6>Invoice No </h6></th>
								<th><h6>Mode </h6></th>
								<th><h6>Booking Mode </h6></th>
							</tr>
						</thead> 
						<tbody>
							<tr><?php echo $tr; ?>	</tr>
						</tbody>
					</table>
					</div><!--end -->
					<!--<input type="button" class='btn btn-primary' id="printpagebutton" onclick="printpage();" value="Print">&nbsp;&nbsp;<a id="backbutton" href="landedReport.php"><input type="button" id="backbutton" class='btn' onclick="closeWin();" value="Close"> </a></p>-->
					
				</div><!--end span-->
		
			</div><!--end row-->

		</div><!--end conatiner-->
	
	
<script type="text/javascript">
    function printpage() {
        //Get the print button and put it into a variable
        var printButton = document.getElementById("printpagebutton");
        var back=document.getElementById("backbutton");
        //Set the print button visibility to 'hidden' 
        printButton.style.visibility = 'hidden';
        back.style.visibility = 'hidden';
        //Print the page content
        window.print();
        //Set the print button to 'visible' again 
        //[Delete this line if you want it to stay hidden after printing]
        printButton.style.visibility = 'visible';
         back.style.visibility = 'visible';
    }
</script>
<script type="text/javascript">

function closeWin() {
    back.close();
}
    window.onbeforeunload = function() {
        
    }
</script>

<?php 
$sql = "SELECT cons_no,book1_date,a.city_name as city ,tbl_city_code.city_name , chweight,mode,weight,noofpackage,ship_name,rev_name,type,dod_cod,dock_charg,qty,oda_mis,invi_value,invice_no,statusname,book_mode FROM (tbl_courier inner join status on tbl_courier.status=status.statusid) join tbl_city_code on tbl_city_code.Id= tbl_courier.r_add join tbl_city_code a on tbl_courier.s_add=a.Id WHERE status = '3' ORDER BY cons_no DESC"; 
$rs_result = mysql_query($sql); //run the query
$total_records = mysql_num_rows($rs_result);  //count number of records
$total_pages = ceil($total_records / $num_rec_per_page); 

echo "<a href='landedReport.php?page=1'>".'|<'."</a> "; // Goto 1st page  

for ($i=1; $i<=$total_pages; $i++) { 
            echo "<a href='landedReport.php?page=".$i."'>".$i."</a> "; 
}; 

include("footer.php"); ?>