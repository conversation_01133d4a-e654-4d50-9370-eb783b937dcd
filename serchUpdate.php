<?php
session_start();
require_once('database.php');
require 'connection.php';
require_once('library.php');
isUser();

  $userid = isset($_SESSION['desgn']) ? $_SESSION['desgn'] : '';
$a = isset($_SESSION['username']) ? $_SESSION['username'] : '';
 /*$sql="select * from tbl_courier_officers where username='$a'";
$result = mysqli_query($con, $sql);
$row1 = mysqli_fetch_array($result, MYSQLI_BOTH);
 $uoffice=$row1['office'];  $uaddress=$row1['address'];
  $id=$row1['cid'];*/

$cid = isset($_GET['cid']) ? (int)$_GET['cid'] : 0;
$consign = isset($_GET['consign']) ? $_GET['consign'] : '';

$sql = "SELECT * FROM tbl_courier WHERE cid = '$cid'";
 $result = mysqli_query($con,$sql);
// Initialize variables to prevent undefined variable warnings
$statusname = '';
while($data = mysqli_fetch_array($result))
extract($data);

$sql_1 = "SELECT DISTINCT(off_name)FROM tbl_offices";
$result_1 = mysqli_query($con,$sql_1);

 $statesql="SELECT * FROM status order by statusname ASC";
 $stateresult=mysqli_query($con,$statesql);
 $statedrop = ""; // Initialize statedrop variable
 while($staterow=mysqli_fetch_array($stateresult))
{
	if($statusname==$staterow['statusid'])
	{
	 $statedrop=$statedrop."<option value='".$staterow['statusid']."' selected>".$staterow['statusname']."</option>";
	}
	else{
	  $statedrop=$statedrop."<option value='".$staterow['statusid']."' >".$staterow['statusname']."</option>";
	}
}

?>
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>
	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
	
</head>
<?php
include("header.php");
?>
      
<div class="container">
	<div class="row">
				 <div class="span2">
				 </div><!--end span2-->
        <form action="process.php?action=update-status" method="post" name="frmShipment" id="frmShipment" enctype="multipart/form-data"> 
        <div class="span12">
 					<div class="account-list-outer">
						<div class="titleHeader clearfix">
							<h3>Search Result. </h3>
						</div><!--end titleHeader-->
					</div>	              
			<div class="span3">
                 <div class="control-group ">
			         <label class="control-label"> <h4> <b> Consignment Number : </b></h4> </label>
			             <div class="controls">
					         <input type="text" name="cons_no" id="cons_no" value="<?php echo $cons_no;?>" readonly>
 <input type="hidden" name="offic" id="offic" value="<?php echo $id;?>">
 <input type="hidden" name="user" id="user" value="<?php echo $_SESSION['desgn']; ?>">
				         </div>
	            </div><!--end control-group  -->
 
                <div class="control-group ">
					 <label class="control-label"> <h4> <b>New Location: : </b></h4> </label>
			             <div class="controls">
					         <select name="OfficeName"><?php while($data = mysqli_fetch_array($result_1)){?>
								<option value=" <?php echo $data['off_name']; ?>"><?php echo $data['off_name']; ?></option>
									<?php 
										}//while
									?>
						     </select>
						</div>
	             </div><!--end control-group  -->
             </div>  
             <div class="span3">			 
                   <div class="control-group ">
			        <label class="control-label"> <h4> <b>Select New Status : </b></h4> </label>
			       <div class="controls">
					<select name="status">
							<option value="">Please Select</option>
						<?php echo $statedrop; ?>
						</select>
							<a href="process.php?action=delivered&cid=<?php echo $cid; ?>">Marked this shipment as to be <span>DELIVERED </span></a><span class="style1"></span>
					</div><!--end control-group  -->
                     </div>
			 <div class="control-group ">
				<label class="control-label"> <h4> <b> Attach Photo : </b></h4> </label>
			       <div class="controls">
						<input type="file" name="photo" id="photo" />
					</div>
			</div><!--end control-group  -->
         </div>
		  <div class="span3">
            		
            <div class="control-group ">
				<label class="control-label"> <h4> <b> Remark : </b></h4> </label>
			       <div class="controls">
						<textarea name="comments" id="comments"></textarea>
                                            <input name="cid" id="cid" value="<?php echo $cid; ?>" type="hidden">
					</div>
			</div><!--end control-group  -->
        	
            <div class="control-group ">
				<div class="controls">
						<input name="submit" value="Submit" type="submit">
						<input name="cancle" value="Clear" type="reset">
						
				</div>		
	        </div><!--end control-group  -->
        </div>
			</div>	
			</form>	
	</div><!-- row -->	
</div><!--end row-->
		
		
<?php
include("footer.php");
?>   
