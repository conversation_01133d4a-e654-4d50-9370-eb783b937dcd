<?php
session_start();
include("connection.php");
require_once('database.php');
require_once('library.php');
isUser();

// Get the file parameter (table name expected here for export & zipping)
$table = isset($_GET['table']) ? $_GET['table'] : '';
$email = isset($_GET['email']) ? $_GET['email'] : '';

if (empty($table)) {
    http_response_code(400);
    die('Table parameter is required');
}

// Validate table name to prevent SQL injection
if (!preg_match('/^[a-zA-Z0-9_]+$/', $table)) {
    http_response_code(400);
    die('Invalid table name');
}

// Fetch data from table
$query = "SELECT * FROM `$table`";
$result = mysqli_query($con, $query);

if (!$result || mysqli_num_rows($result) == 0) {
    http_response_code(404);
    die('No data found for the given table');
}

// Generate filenames
$timestamp = date('Y-m-d_H-i-s');
$csvFilename = "export_{$table}_{$timestamp}.csv";
$zipFilename = "export_{$table}_{$timestamp}.zip";

// CSV Creation
$csvFile = fopen($csvFilename, 'w');

// Write CSV headers
$fields = mysqli_fetch_fields($result);
$headers = [];
foreach ($fields as $field) {
    $headers[] = $field->name;
}
fputcsv($csvFile, $headers);

// Write data rows
while ($row = mysqli_fetch_assoc($result)) {
    fputcsv($csvFile, $row);
}
fclose($csvFile);

// Create ZIP archive
$zip = new ZipArchive();
if ($zip->open($zipFilename, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== TRUE) {
    http_response_code(500);
    die('Failed to create ZIP file');
}
$zip->addFile($csvFilename);
$zip->close();

// If email is provided, send download link
if (!empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL)) {
    $downloadUrl = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) . "/download_file.php?table=" . urlencode($table);
    
    // Send email
    $subject = "Your Data Export Download Link";
    $message = "Dear User,\n\n";
    $message .= "Your data export for table $table is ready.\n\n";
    $message .= "Download here: $downloadUrl\n\n";
    $message .= "This link will be valid for 24 hours.\n\n";
    $message .= "Thank you,\nSystem Admin";
    
    mail($email, $subject, $message);
}

// Serve ZIP file
header('Content-Type: application/zip');
header('Content-Disposition: attachment; filename="' . basename($zipFilename) . '"');
header('Content-Length: ' . filesize($zipFilename));
header('Cache-Control: no-cache, must-revalidate');
header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

readfile($zipFilename);

// Clean up: delete CSV and ZIP after download
unlink($csvFilename);
unlink($zipFilename);

exit;
?>