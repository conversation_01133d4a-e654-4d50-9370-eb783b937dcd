<?php 
session_start();
require 'connection.php';
require_once('database.php');
require_once('library.php');
ob_start();

// Get date parameter from URL (for dashboard integration)
$selected_date = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');

// Legacy parameters for backward compatibility
$date1=$_GET['date1'] ?? null;
$date2=$_GET['date2'] ?? null;
$custid=$_GET['id'] ?? null;

if($date1 && $date2) {
    $Cdate=date('Y-m-d',strtotime($date1));
    $Cdate1=date('Y-m-d',strtotime($date2));
} else {
    $Cdate = $selected_date;
    $Cdate1 = $selected_date;
}

// Add date filtering to the query - In Transit uses status_date
$sql = "SELECT cons_no,weight,gtotamt,rev_name,r_add,ship_name,userid,status_date,book_mode,noofpackage,status,e_waybill,eway_expdate,gst,partno,remark,freight,book_mode,invi_value,qty,
assured_dly_date,book1_date,a.city_name as city ,type,invice_no,chweight,mode,rate,oda_mis,statusname FROM (tbl_courier inner join status on tbl_courier.status=status.statusid)
join  tbl_city_code a on tbl_courier.s_add=a.Id
WHERE status = '28' AND DATE(status_date) = '$selected_date' ORDER BY cons_no DESC ";
$result = mysqli_query($con,$sql);

$count=0;
$tr = ''; // Initialize table rows variable
while($row=mysqli_fetch_array($result))
{
    // Initialize variables for each row
    $statusdate = '';
    $delivereddate = '';
    $destination = '';
    $Manager_name = '';
    $empcode = '';
    $withoutgstvalue = '';

     if($row['status']=='6' || $row['status']=='50' || $row['status']=='57'){
     $statusdate=$row['status_date'];
 }

  if($row['status']=='5'){
     $delivereddate=$row['status_date'];
 }
 
 if($row['r_add']==""){
         $destination="";
     }else{
  $sql1="SELECT city_name,r_add from tbl_city_code inner join tbl_courier on tbl_city_code.Id= tbl_courier.r_add 
  WHERE tbl_courier.r_add = '".$row['r_add']."' " ;

$result1 = mysqli_query($con,$sql1);
$row1 = mysqli_fetch_array($result1);
if($row1) {
    $destination=$row1['city_name'];
}
     }

      $sql2="SELECT Manager_name,empcode from tbl_courier_officers inner join tbl_courier on tbl_courier_officers.cid=tbl_courier.userid
  WHERE tbl_courier.userid = '".$row['userid']."' " ;

$result2 = mysqli_query($con,$sql2);
$row2 = mysqli_fetch_array($result2);
if($row2) {
    $Manager_name=$row2['Manager_name'];
    $empcode=$row2['empcode'];
}
 $invoicevalue=$row['invi_value'];
 $withoutgstvalue=$invoicevalue;
	$count++;
$tr=$tr."<tr><td>".$count."</td><td>".$Manager_name."</td><td>".$empcode."</td><td>".$row['status_date']."</td><td>".$row['cons_no']."</td><td>".$row['book1_date']."</td><td>".$row['invice_no']."</td>
<td>".$row['rev_name']."</td><td>".$destination."</td><td>".$row['noofpackage']."</td><td>".$row['qty']."</td><td>".$row['partno']."</td><td>".$withoutgstvalue."</td><td>".$row['invi_value']."</td><td>".$statusdate."</td>
<td>".$row['book_mode']."</td><td>".$row['statusname']."</td><td>".$row['remark']."</td><td>".$delivereddate."</td><td>".$row['mode']."</td><td>".$row['weight']."</td><td>".$row['chweight']."</td><td>".$row['e_waybill']."</td><td>".$row['rate']."</td>
<td>".$row['oda_mis']."</td><td>".$row['freight']."</td><td></td><td></td></tr>";
}

header("Content-Type:   application/vnd.ms-excel; charset=utf-8");
header("Content-type:   application/x-msexcel; charset=utf-8");
header("Content-Disposition: attachment; filename=Transit_Report.xls"); 
header("Expires: 0");
header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
header("Cache-Control: private",false);

	
	
		echo' <table class="table1"  style="width:100%"  border="1">
						<thead>
					<tr>
							     <th><h6>Sr No. </h6></th>
							      <th><h6>Employee Name </h6></th>
							      <th><h6>Emp=ID</h6></th>
							       <th><h6>Scanning Date</h6></th>
							  <th><h6>LR No </h6></th>
							     <th><h6>BKG Date</h6></th>
							     <th><h6>Invoice No </h6></th>
							     <th><h6>Customer Name </h6></th>
						         <th><h6>Destinantion</h6></th>
						         	<th><h6>Cases </h6></th>
									<th><h6>Qty </h6></th>
										<th><h6>Part No. </h6></th>
								
								 
								  <th><h6>Without GST Value </h6></th>
								    <th><h6>Invoice Value </h6></th>
								  	 
									
								   <th><h6>Godown Receipt Date </h6></th>
								<th><h6>Type</h6></th>
								<th><h6>Remarks</h6></th>
								<th><h6>My Remarks</h6></th>
								  <th><h6>Delivery date </h6></th>
								    <th><h6>Mode</h6></th>
							    <th><h6>A/Weight </h6></th>
								<th><h6>C/Weight </h6></th>
									<th><h6>E WayBill No. </h6></th>
									<th><h6>Rate </h6></th>
										<th><h6>ODA </h6></th>
											<th><h6>Fright </h6></th>
											<th><h6>GST </h6></th>
								
								<th><h6>Total </h6></th>
							</tr>
						</thead> ';
						echo '<tbody>';
						 echo $tr; 	
						echo'</tbody>
					</table>';
	
?>
	
	