/**
 * Portuguese translation for bootstrap-datetimepicker
 * Original code: <PERSON><PERSON><PERSON> <<EMAIL>>
 * <PERSON><PERSON><PERSON> <<EMAIL>>
 */
;(function($){
	$.fn.datetimepicker.dates['pt'] = {
		days: ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Sábad<PERSON>", "Domingo"],
		daysShort: ["<PERSON>", "Se<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Sex", "<PERSON>áb", "Dom"],
		daysMin: ["<PERSON>", "Se", "<PERSON>", "Qu", "Qu", "Se", "Sa", "Do"],
		months: ["Janeiro", "Fevereiro", "Março", "Abri<PERSON>", "Mai<PERSON>", "Jun<PERSON>", "Jul<PERSON>", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"],
		monthsShort: ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Out", "Nov", "Dez"],
		suffix: [],
		meridiem: ["am","pm"],
		today: "Hoje"
	};
}(j<PERSON><PERSON>y));
