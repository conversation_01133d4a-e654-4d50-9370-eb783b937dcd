<?php 
session_start();
require 'connection.php';
require_once('database.php');
require_once('library.php');
ob_start();
 $count=$_POST['count'];  
for($i=1;$i<=$count;$i++) {
$cnt=$_POST['cnt'.$i];
$docketno=$_POST['docketno'.$i];
$bookdate=$_POST['bookdate'.$i];
$destination=$_POST['destination'.$i];
$chweight=$_POST['chweight'.$i];
$rate=$_POST['rate'.$i];
$freight=$_POST['freight'.$i];
$vehiclecharge=$_POST['vehiclecharge'.$i];
$oda=$_POST['oda'.$i];
$totfreght=$_POST['totfreght'.$i];
$sgst=$_POST['sgst'.$i];
$cgst=$_POST['cgst'.$i];

$total=$_POST['total'.$i];
 $tr=$tr."<tr>
<td>".$i."</td>
<td>".$docketno."</td>
<td>".$bookdate."</td>
<td>".$destination."</td>
<td>".$chweight."</td>
<td>".$rate."</td>
<td>".$freight."</td>
<td>".$vehiclecharge."</td>
<td>".$oda."</td>
<td>".$totfreght."</td>
<td>".$sgst."</td>
<td>".$cgst."</td>

<td>".$total."</td>
</tr>";			

}




header("Content-Type:   application/vnd.ms-excel; charset=utf-8");
header("Content-type:   application/x-msexcel; charset=utf-8");
header("Content-Disposition: attachment; filename=Booked_Report.xls"); 
header("Expires: 0");
header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
header("Cache-Control: private",false);

	
	
		echo' <table class="table1"  style="width:100%"  border="1">
						<thead>
				 <th>Sr. No.</th>
           
           <th>Docket No.</th>
           <th>Date</th>
           <th>Destin.</th>
           <th><span id="Weight" class="remove remove-col">Weight</span></th>
           <th><span id="Rate" class="remove remove-col">Rate</span></th>
           <th><span id="Freight" class="remove remove-col">Freight</span> </th>
           <th><span id="invalue" class=" remove remove-col">Specail Veh.Charges</span></th>
           <th><span id="oda" class="remove remove-col">ODA/Other</span></th>
           <th><span id="othercharges" class=" remove remove-col">Total Frieght</span> </th>
          
          
          
          <th><span id="cgst" class="remove remove-col">SGST</span></th>
                      <th><span id="cgst" class="remove remove-col">CGST</span></th>
           <th><span id="total" class="remove remove-col">Total</span></th>
							  
							</tr>
						</thead> ';
						echo '<tbody>';
						 echo $tr; 	
						echo'</tbody>
					</table>';
	
?>
	
	