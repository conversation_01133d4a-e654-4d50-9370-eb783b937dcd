<?php 
session_start();
require 'connection.php';
require_once('database.php');
require_once('library.php');
ob_start();

$date1 = $_GET['date1'];
$date2 = $_GET['date2'];
$statusid = $_GET['id'];

$Cdate = date('Y-m-d', strtotime($date1));
$Cdate1 = date('Y-m-d', strtotime($date2));

if ($statusid == '1') {
    $sql = "SELECT cons_no, weight, gtotamt, rev_name, pod_img, ship_name, userid, rate, oda_mis, noofpackage, gst, eway_expdate, book_mode, e_waybill, r_add, status, qty, partno, freight, invi_value, status_date, assured_dly_date, book1_date, type, invice_no, chweight, mode, statusname, remark FROM tbl_courier 
            INNER JOIN status ON tbl_courier.status = status.statusid 
            WHERE tbl_courier.status = '$statusid' AND tbl_courier.book_date BETWEEN '$Cdate' AND '$Cdate1'";
} else {
    $sql = "SELECT cons_no, weight, gtotamt, rev_name, pod_img, ship_name, userid, rate, oda_mis, noofpackage, gst, eway_expdate, book_mode, e_waybill, r_add, status, qty, partno, freight, invi_value, status_date, assured_dly_date, book1_date, type, invice_no, chweight, mode, statusname, remark FROM tbl_courier 
            INNER JOIN status ON tbl_courier.status = status.statusid 
            WHERE tbl_courier.status = '$statusid' AND tbl_courier.status_date BETWEEN '$Cdate' AND '$Cdate1'";
}

$count = 0;
$tr = '';
$result = mysqli_query($con, $sql);

while ($row = mysqli_fetch_array($result)) {
    $statusdate = '';
    $delivereddate = '';
    $withoutgstvalue = 0;
    $destination = '';

    if ($row['status'] == '6' || $row['status'] == '50' || $row['status'] == '57') {
        $statusdate = $row['status_date'];
    }

    if ($row['status'] == '5') {
        $delivereddate = $row['status_date'];
    }

    // Destination
    if (!empty($row['r_add'])) {
        $sql1 = "SELECT city_name FROM tbl_city_code WHERE Id = '".$row['r_add']."'";
        $result1 = mysqli_query($con, $sql1);
        if ($row1 = mysqli_fetch_array($result1)) {
            $destination = $row1['city_name'];
        }
    }

    // Manager info
    $Manager_name = '';
    $empcode = '';
    $sql2 = "SELECT Manager_name, empcode FROM tbl_courier_officers WHERE cid = '".$row['userid']."'";
    $result2 = mysqli_query($con, $sql2);
    if ($row2 = mysqli_fetch_array($result2)) {
        $Manager_name = $row2['Manager_name'];
        $empcode = $row2['empcode'];
    }

    // Example calculation for without GST value (optional)
    $gst = isset($row['gst']) ? $row['gst'] : 0;
    $invi_value = isset($row['invi_value']) ? $row['invi_value'] : 0;
    if ($gst > 0) {
        $withoutgstvalue = round($invi_value / (1 + ($gst / 100)), 2);
    }

    $count++;
    $tr .= "<tr>
        <td>{$count}</td>
        <td>{$Manager_name}</td>
        <td>{$empcode}</td>
        <td>{$row['status_date']}</td>
        <td>{$row['cons_no']}</td>
        <td>{$row['book1_date']}</td>
        <td>{$row['invice_no']}</td>
        <td>{$row['rev_name']}</td>
        <td>{$destination}</td>
        <td>{$row['noofpackage']}</td>
        <td>{$row['qty']}</td>
        <td>{$row['partno']}</td>
        <td>{$withoutgstvalue}</td>
        <td>{$row['invi_value']}</td>
        <td>{$statusdate}</td>
        <td>{$row['book_mode']}</td>
        <td>{$row['statusname']}</td>
        <td>{$row['remark']}</td>
        <td>{$delivereddate}</td>
        <td>{$row['mode']}</td>
        <td>{$row['weight']}</td>
        <td>{$row['chweight']}</td>
        <td>{$row['e_waybill']}</td>
        <td>{$row['rate']}</td>
        <td>{$row['oda_mis']}</td>
        <td>{$row['freight']}</td>
        <td>{$gst}</td>
        <td>{$row['gtotamt']}</td>
    </tr>";
}

// Excel Headers
header("Content-Type: application/vnd.ms-excel; charset=utf-8");
header("Content-Disposition: attachment; filename=Statuswise_Report.xls");
header("Pragma: no-cache");
header("Expires: 0");

// Output Table
echo '<table border="1">
    <thead>
        <tr>
            <th>Sr No.</th>
            <th>Employee Name</th>
            <th>Emp ID</th>
            <th>Scanning Date</th>
            <th>LR No</th>
            <th>BKG Date</th>
            <th>Invoice No</th>
            <th>Customer Name</th>
            <th>Destination</th>
            <th>Cases</th>
            <th>Qty</th>
            <th>Part No.</th>
            <th>Without GST Value</th>
            <th>Invoice Value</th>
            <th>Godown Receipt Date</th>
            <th>Type</th>
            <th>Remarks</th>
            <th>My Remarks</th>
            <th>Delivery Date</th>
            <th>Mode</th>
            <th>A/Weight</th>
            <th>C/Weight</th>
            <th>E WayBill No.</th>
            <th>Rate</th>
            <th>ODA</th>
            <th>Freight</th>
            <th>GST</th>
            <th>Total</th>
        </tr>
    </thead>
    <tbody>
        ' . $tr . '
    </tbody>
</table>';
?>
