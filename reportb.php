<?php
session_start();
include("connection.php");
require_once('database.php');
require_once('library.php');
isUser();

// Set filters from POST or GET (for pagination)
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $_SESSION['date2'] = $_POST['date2'];
    $_SESSION['date4'] = $_POST['date4'];
    $_SESSION['rep'] = $_POST['rep'];
} elseif (isset($_GET['date2']) && isset($_GET['date4']) && isset($_GET['rep'])) {
    $_SESSION['date2'] = $_GET['date2'];
    $_SESSION['date4'] = $_GET['date4'];
    $_SESSION['rep'] = $_GET['rep'];
}

// Initialize variables
$date = isset($_SESSION['date2']) ? $_SESSION['date2'] : '';
$date2 = isset($_SESSION['date4']) ? $_SESSION['date4'] : '';
$id = isset($_SESSION['rep']) ? $_SESSION['rep'] : '';

// Pagination setup
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 50;
if (!in_array($limit, [50, 100])) {
    $limit = 50;
}
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
if ($page < 1) $page = 1;

$start_from = ($page - 1) * $limit;

$Cdate = date('Y-m-d', strtotime($date));
$Cdate1 = date('Y-m-d', strtotime($date2));


// Generate table data if filters are set
$tr = '';
if (!empty($date) && !empty($date2) && !empty($id)) {
    $sql = "SELECT userid,cons_no,weight,gtotamt,rev_name,r_add,ship_name,rate,oda_mis,status_date,book_mode,noofpackage,e_waybill,eway_start_date,eway_end_date,status,eway_expdate,gst,partno,remark,freight,book_mode,invi_value,qty,assured_dly_date,book1_date,a.city_name as city
,type,invice_no,chweight,mode,statusname,vehicle FROM (tbl_courier inner join status on tbl_courier.status=status.statusid) join  tbl_city_code a on tbl_courier.s_add=a.Id
WHERE tbl_courier.branch_id ='$id' and tbl_courier.book_date between '$Cdate' and '$Cdate1' ORDER BY cons_no DESC LIMIT $start_from, $limit";
    $result = mysqli_query($con,$sql);
    $count = $start_from;
while($row=mysqli_fetch_array($result))
{
    
    
    //shipper details 
     
       $sql_s="SELECT * FROM `custreg` where `custname`  like '".$row['ship_name']."'" ;
  
$result_s = mysqli_query($con,$sql_s);
$row_s = mysqli_fetch_array($result_s);
$shipper_name = $row_s ? $row_s['custname'] : '';
$shipper_phone = $row_s ? $row_s['custphone'] : '';
$shipper_mail = $row_s ? $row_s['custmail'] : '';
$shipper_add = $row_s ? $row_s['custadd'] : '';
$shipper_c = $row_s ? $row_s['custcity'] : '';
$sql_s="SELECT * FROM `city` where ctid=".$shipper_c ;

$result_s = mysqli_query($con,$sql_s);
$row_s = mysqli_fetch_array($result_s);
$shipper_city = $row_s ? $row_s['cityname'] : '';
     /* shipper details end <td>".$Manager_name."</td><td>".$empcode."</td><td>".$shipper_name."</td><td>".$shipper_phone."</td><td>".$shipper_add."</td><td>".$shipper_mail."</td><td>".$shipper_city."</td>
           <th><h6>Shipper Name</h6></th>
							        <th><h6>Shipper Phone</h6></th>
							         <th><h6>Shipper Address</h6></th>
							          <th><h6>Shipper Email</h6></th>
							          <th><h6>Shipper City</h6></th>
							 */
   
    
 if($row['status']=='6' || $row['status']=='50' || $row['status']=='57'){
      $statusdate=$row['status_date'];
 }else{
     $statusdate="";
 }


  if($row['status']=='5'){
     $delivereddate=$row['status_date'];
 } else {
     $delivereddate="";
 }

 // Initialize withoutgstvalue
 $withoutgstvalue = "";
 
 if($row['r_add']==""){
         $destination="";
     }else{
  $sql1="SELECT city_name,r_add from tbl_city_code inner join tbl_courier on tbl_city_code.Id= tbl_courier.r_add 
  WHERE tbl_courier.r_add = '".$row['r_add']."' " ;

$result1 = mysqli_query($con,$sql1);
$row1 = mysqli_fetch_array($result1);
$destination = $row1 ? $row1['city_name'] : '';
     }
     
       $sql2="SELECT Manager_name,empcode from tbl_courier_officers inner join tbl_courier on tbl_courier_officers.cid=tbl_courier.userid
  WHERE tbl_courier.userid = '".$row['userid']."' " ;

$result2 = mysqli_query($con,$sql2);
$row2 = mysqli_fetch_array($result2);
$Manager_name = $row2 ? $row2['Manager_name'] : '';
$empcode = $row2 ? $row2['empcode'] : '';
$count++;
$tr=$tr."<tr><td>".$count."</td><td>".$Manager_name."</td><td>".$empcode."</td><td>".$shipper_name."</td> <td>".$shipper_city."</td><td>".$row['status_date']."</td><td>".$row['cons_no']."</td><td>".$row['book1_date']."</td><td>".$row['invice_no']."</td>
<td>".$row['rev_name']."</td><td>".$destination."</td><td>".$row['noofpackage']."</td><td>".$row['qty']."</td><td>".$row['partno']."</td><td>".$withoutgstvalue."</td><td>".$row['invi_value']."</td><td>".$statusdate."</td>
<td>".$row['book_mode']."</td><td>".$row['statusname']."</td><td>".$row['remark']."</td><td>".$delivereddate."</td><td>".$row['mode']."</td><td>".$row['weight']."</td><td>".$row['chweight']."</td><td>".$row['e_waybill']."</td><td>".$row['eway_start_date']."</td><td>".$row['eway_end_date']."</td><td>".$row['vehicle']."</td><td>".$row['rate']."</td>
<td>".$row['oda_mis']."</td><td>".$row['freight']."</td><td></td><td></td></tr>";



    }
}

?>
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>
    <style>
table1 {
  font-family: arial, sans-serif;
  border-collapse: collapse;
  width: 100%;
}

td, th {
  border: 1px solid #dddddd;
  text-align: left;
  padding: 8px;
}

tr:nth-child(even) {
  background-color: #dddddd;
}
</style>

	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->

<script language="JavaScript">
var checkflag = "false";

function check(field) {
if (checkflag == "false")
 {
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll")
	{
	field[i].checked=true;	
	}
	}
	checkflag = "true";
}
else
{
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll")
	{
	field[i].checked=false;
	}
	}
	checkflag = "false";
}

}
function confirmDel(field,msg)
{
	count=0;
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll" && field[i].checked==true)
	{
	count++;
	}
	}
	
	if(count == 0)
	{
		alert("Select any one record to delete!");
		return false;
	}
	else
	{
		return confirm(msg);
	}
}
</script>
	
</head> 

<?php include("header.php"); ?>
		<div class="container">

			<div class="row">

                <div class="span12">
					<div class="account-list-outer">

						<div class="titleHeader clearfix">
							<h3>Branch Wise Report</h3>
						</div><!--end titleHeader-->
						<div class="control-group ">
							     <div class="controls">
								     <input class="btn btn-primary"  type="button" value="Export" onclick="abc();" >
								     
							     </div>
							</div>

							<?php if (!empty($date) && !empty($date2) && !empty($id)): ?>
							<!-- Records per page selector -->
							<div class="row control-group" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 15px;">
							    <div class="col-md-4">
							        <form method="get" id="entryForm" class="form-inline">
							            <label for="limit" style="margin-right: 5px;">Show</label>
							            <select name="limit" id="limit" onchange="document.getElementById('entryForm').submit();" class="form-control input-sm">
							                <option value="50" <?php if ($limit == 50) echo 'selected'; ?>>50</option>
							                <option value="100" <?php if ($limit == 100) echo 'selected'; ?>>100</option>
							            </select>
							            entries

							            <!-- Keep filters in form -->
							            <input type="hidden" name="date2" value="<?php echo htmlspecialchars($date); ?>">
							            <input type="hidden" name="date4" value="<?php echo htmlspecialchars($date2); ?>">
							            <input type="hidden" name="rep" value="<?php echo htmlspecialchars($id); ?>">
							            <input type="hidden" name="page" value="<?php echo $page; ?>">
							        </form>
							    </div>
							</div>
							<?php endif; ?>

	<div class="one-third last" style="height:450px;overflow:scroll;width=8000px;">
						<table class="table1"  style="width:100%">
						<thead>
					 <th><h6>Sr No. </h6></th>
							      <th><h6>Employee Name </h6></th>
							      <th><h6>Emp=ID</h6></th>
							        <th><h6>Shipper Name</h6></th>
							          <!--  <th><h6>Shipper Phone</h6></th>
							         <th><h6>Shipper Address</h6></th>
							          <th><h6>Shipper Email</h6></th>-->
							          <th><h6>Shipper City</h6></th>
							       <th><h6>Scanning Date</h6></th>
							  <th><h6>LR No </h6></th>
							     <th><h6>BKG Date</h6></th>
							     <th><h6>Invoice No </h6></th>
							     <th><h6>Customer Name </h6></th>
						         <th><h6>Destinantion</h6></th>
						         	<th><h6>Cases </h6></th>
									<th><h6>Qty </h6></th>
										<th><h6>Part No. </h6></th>
								
								 
								  <th><h6>Without GST Value </h6></th>
								    <th><h6>Invoice Value </h6></th>
								  	 
									
								   <th><h6>Godown Receipt Date </h6></th>
								<th><h6>Type</h6></th>
								<th><h6>Remarks</h6></th>
								<th><h6>My Remarks</h6></th>
								  <th><h6>Delivery date </h6></th>
								    <th><h6>Mode</h6></th>
							    <th><h6>A/Weight </h6></th>
								<th><h6>C/Weight </h6></th>
									<th><h6>E WayBill No. </h6></th>
									<th><h6>E Way Start Date  </h6></th><th><h6>E Way End Date </h6></th>
									<th><h6>Vehicle No. </h6></th>
									<th><h6>Rate </h6></th>
										<th><h6>ODA </h6></th>
											<th><h6>Fright </h6></th>
											<th><h6>GST </h6></th>
								
								<th><h6>Total </h6></th>
								
							</tr>
						</thead> 
						<tbody>
							<?php echo $tr; ?>	
						</tbody>
					</table></div>
					</div><!--end -->

		<?php
		// Add pagination if data is loaded
		if (!empty($date) && !empty($date2) && !empty($id)) {
		    // Convert dates to proper format for count query
		    $Cdate = date('Y-m-d', strtotime($date));
		    $Cdate1 = date('Y-m-d', strtotime($date2));

		    // Count total records for pagination
		    $count_sql = "SELECT COUNT(*) as total FROM (tbl_courier inner join status on tbl_courier.status=status.statusid) join  tbl_city_code a on tbl_courier.s_add=a.Id WHERE tbl_courier.branch_id ='$id' and tbl_courier.book_date between '$Cdate' and '$Cdate1'";
		    $count_result = mysqli_query($con, $count_sql);
		    $count_row = mysqli_fetch_assoc($count_result);
		    $total_records = $count_row['total'];
		    $total_pages = ceil($total_records / $limit);

		    $date_encoded = urlencode($date);
		    $date2_encoded = urlencode($date2);
		    $id_encoded = urlencode($id);

		    $adjacents = 2;
		    $start_loop = ($page > $adjacents) ? $page - $adjacents : 1;
		    $end_loop = ($page < ($total_pages - $adjacents)) ? $page + $adjacents : $total_pages;

		    // Styling
		    echo "<style>
		    .pagination-wrapper {
		        display: flex;
		        justify-content: center;
		        margin: 30px 0;
		    }
		    .pagination {
		        display: flex;
		        flex-wrap: wrap;
		        gap: 5px;
		    }
		    .pagination a {
		        padding: 6px 12px;
		        background-color: white;
		        border: 1px solid rgb(255, 106, 0);
		        color: #f16325;
		        text-decoration: none;
		        border-radius: 4px;
		    }
		    .pagination a.active {
		        background-color: #f16325;
		        color: white;
		        font-weight: bold;
		    }
		    .pagination a:hover {
		        background-color: #f16325;
		        color: white;
		    }
		    </style>";

		    // Output pagination
		    echo "<div class='pagination-wrapper'>";
		    echo "<div class='pagination'>";

		    // First/Previous
		    if ($page > 1) {
		        echo "<a href='?page=1&date2=$date_encoded&date4=$date2_encoded&rep=$id_encoded&limit=$limit'>&laquo;</a>";
		        echo "<a href='?page=" . ($page - 1) . "&date2=$date_encoded&date4=$date2_encoded&rep=$id_encoded&limit=$limit'>Previous</a>";
		    }

		    // Page numbers
		    for ($i = $start_loop; $i <= $end_loop; $i++) {
		        $active_class = ($i == $page) ? 'active' : '';
		        echo "<a href='?page=$i&date2=$date_encoded&date4=$date2_encoded&rep=$id_encoded&limit=$limit' class='$active_class'>$i</a>";
		    }

		    // Next/Last
		    if ($page < $total_pages) {
		        echo "<a href='?page=" . ($page + 1) . "&date2=$date_encoded&date4=$date2_encoded&rep=$id_encoded&limit=$limit'>Next</a>";
		        echo "<a href='?page=$total_pages&date2=$date_encoded&date4=$date2_encoded&rep=$id_encoded&limit=$limit'>&raquo;</a>";
		    }

		    echo "</div>";
		    echo "</div>";

		    // Show pagination info
		    $start_record = ($page - 1) * $limit + 1;
		    $end_record = min($page * $limit, $total_records);
		    echo "<div style='text-align: center; margin: 10px 0; color: #666;'>";
		    echo "Showing $start_record to $end_record of $total_records entries";
		    echo "</div>";
		}
		?>

				</div><!--end span-->

			</div><!--end row-->

		</div><!--end conatiner-->
	
	
<script type="text/javascript">
    function printpage() {
        //Get the print button and put it into a variable
        var printButton = document.getElementById("printpagebutton");
        var back=document.getElementById("backbutton");
        //Set the print button visibility to 'hidden' 
        printButton.style.visibility = 'hidden';
        back.style.visibility = 'hidden';
        //Print the page content
        window.print();
        //Set the print button to 'visible' again 
        //[Delete this line if you want it to stay hidden after printing]
        printButton.style.visibility = 'visible';
         back.style.visibility = 'visible';
    }
</script>
<script type="text/javascript">

function closeWin() {
    back.close();
}
    window.onbeforeunload = function() {
        
    }
</script>
<script> 
/*function abc()
{
var date=document.getElementById("date2").value;
var date2=document.getElementById("date4").value;
var rep=document.getElementById("rep").value;


location.href='customerwise_excel.php?id="+rep+"&date1="+date+"&date2="+date2+"';
*/
</script>
<?php
echo "<script> 
function abc()
{

location.href='branchwise_excel.php?id=".$id."&strdate=".$Cdate."&date2=".$Cdate1."';

}</script>"; 
?>