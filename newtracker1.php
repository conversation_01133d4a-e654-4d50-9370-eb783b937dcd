<?php 
error_reporting(~E_ALL);
session_start();
require 'connection.php';


$tr="";   
$els="";
$tr1="";  
$consign="";
$consign=$_POST['Consignment'];
//$consign=14325;
$cntr=0;
$pod_imag_path="";

$sql22="SELECT * FROM `currentloc` where `consig_no`='$consign'";
$sql22="SELECT `tbl_courier_officers`.`office` as source, `currentloc`.`current_loc` as destin , `currentloc`.`current_date` as date_time , `status`.`statusname` as staus FROM (`currentloc` inner join `tbl_courier_officers` on `currentloc`.`sourcesid`= `tbl_courier_officers`.`cid`)inner join status on `status`.`statusid`=`currentloc`.`currentstatus` where  `currentloc`.`consig_no`= '$consign'";


 $sql = "SELECT cons_no,book1_date,a.city_name as city ,tbl_city_code.city_name , chweight,mode,noofpackage,statusname FROM (tbl_courier inner join status on tbl_courier.status=status.statusid) join tbl_city_code on tbl_city_code.Id= tbl_courier.r_add join tbl_city_code a on tbl_courier.s_add=a.Id where cons_no='$consign'";

$result=mysqli_query($con,$sql);
while($row2=mysqli_fetch_array($result)){
	
$tr1=$tr1."<tr><td><h4><b>".++$count."</b></h4></td><td><h4><b>".$row2['city_name']."</b></h4></td><td><h4><b>".$row2['city']."</b></h4></td><td><h4><b>".$row2['book1_date']."</b></h4></td><td><h4><b>".$row2['statusname']."</b></h4></td></tr>";
 
}

  $sql = "SELECT cons_no,pod_img,book1_date,a.city_name as city ,tbl_city_code.city_name , chweight,mode,weight,noofpackage,ship_name,rev_name,type,dod_cod,dock_charg,qty,
 oda_mis,invi_value,statusname ,pod_imag_path FROM (tbl_courier inner join status on tbl_courier.status=status.statusid) join tbl_city_code on tbl_city_code.Id= tbl_courier.r_add join 
 tbl_city_code a on tbl_courier.s_add=a.Id where cons_no='$consign'";

$result2=mysqli_query($con,$sql);
$no = mysqli_num_rows($result2);
if($no == 1){
while($row2=mysqli_fetch_array($result2))
{
$Cno=$row2['cons_no'];   $reciver=$row2['rev_name'];   $bkdate=$row2['book1_date'];  $invoiceno=$row2['invice_no']; 
$shipper=$row2['ship_name'];   $To=$row2['city_name'];   $tatusss=$row2['statusname'];  $invalu=$row2['invi_value']; 
$from=$row2['city'];   $bkmode=$row2['mode'];   $oda=$row2['oda_mis'];  $Cno=$row2['cons_no']; 
$bktype=$row2['type'];   $weigh=$row2['weight'];   $noqty=$row2['qty'];  $Cno=$row2['cons_no']; 
$docket=$row2['dock_charg'];   $dod=$row2['dod_cod'];   $Cno=$row2['cons_no'];  $Cno=$row2['cons_no']; 
$Cno=$row2['cons_no'];   $Cno=$row2['cons_no'];   $Cno=$row2['cons_no'];  $Cno=$row2['cons_no'];  $podimg=$row2['pod_img'];
$pod_imag_path=$row2['pod_imag_path'];
 $tr=$tr."<tr><td>".++$count."</td><td>".$row2['city']."</td><td>".$row2['city_name']."</td><td>".$row2['book1_date']."</td><td>".$row2['statusname']."</td></tr>";

}
}
else
{ 
   $sql2 = "SELECT cons_no,pod_img,book1_date,chweight,mode,weight,noofpackage,ship_name,rev_name,type,dod_cod,dock_charg,qty,oda_mis,invi_value,statusname 
  ,pod_imag_path FROM tbl_courier inner join status on tbl_courier.status=status.statusid 
   where cons_no='$consign'";

$result23=mysqli_query($con,$sql2);
$no = mysqli_num_rows($result2);

while($row23=mysqli_fetch_array($result23))
{
$Cno=$row23['cons_no'];   $reciver=$row23['rev_name'];   $bkdate=$row23['book1_date'];  $invoiceno=$row23['invice_no']; 
$shipper=$row23['ship_name'];   $To=$row23['city_name'];   $tatusss=$row23['statusname'];  $invalu=$row23['invi_value']; 
$from=$row23['city'];   $bkmode=$row23['mode'];   $oda=$row23['oda_mis'];  $Cno=$row23['cons_no']; 
$bktype=$row23['type'];   $weigh=$row23['weight'];   $noqty=$row23['qty'];  $Cno=$row23['cons_no']; 
$docket=$row23['dock_charg'];   $dod=$row23['dod_cod'];   $Cno=$row23['cons_no'];  $Cno=$row23['cons_no']; 
$Cno=$row23['cons_no'];   $Cno=$row23['cons_no'];   $Cno=$row23['cons_no'];  $Cno=$row23['cons_no'];  $pod_imag_path=$row23['pod_imag_path'];

 $tr=$tr."<tr><td>".++$count."</td><td>".$row23['city']."</td><td>".$row23['city_name']."</td><td>".$row23['book1_date']."</td><td>".$row23['statusname']."</td></tr>";
}
}

$statsql="SELECT statusname from status where statusid='$tatusss'";
$resultstat=mysqli_query($con,$statsql);
while($statrow=mysqli_fetch_array($resultstat)){

   $statu=$statrow['statusname'];
}
?>
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>

	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	<title>Vivanta Logistics</title>
	<meta name="description" content="">
	<meta name="author" content="Ahmed Saeed">
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
	<link rel="shortcut icon" href="images/favicon.html">
	<link rel="apple-touch-icon" href="images/apple-touch-icon.html">
	<link rel="apple-touch-icon" sizes="72x72" href="images/apple-touch-icon-72x72.html">
	<link rel="apple-touch-icon" sizes="114x114" href="images/apple-touch-icon-114x114.html">
</head>
<?php
include("header.php");
?>

	<div class="container">
             
			<div class="row">
             
			</div>		
            <div class="row">					
				<div class="span11">
				       			
					<div class="cart-accordain" id="cart-acc">
                              
						<div class="accordion-group">
                            <div class="accordion-heading">
								<h2 align="center"> <b>&nbsp;&nbsp;&nbsp;&nbsp;Tracker Result&nbsp;&nbsp;&nbsp;&nbsp;</b></h2>
							</div>
							<div class="accordion-heading">
								<a class="accordion-toggle" data-toggle="collapse" data-parent="#cart-acc" href="#gift-voucher1">
									<h2> <i class="icon-caret-right"></i><b>Consignment Details...</b></h2>
								</a>
							</div>
							<div id="gift-voucher1" class="accordion-body collapse in">
								<div class="accordion-inner">
									<form class="form-horizontal">
				<div class="span5">
				       
				        <div class="control-group ">
							    <label class="control-label"> <h4> <b> Consignment Number :  </b></h4> </label>
							    <div class="controls">
								<!--<p><h4><?php echo $Cno;?></h4></p>-->
							     <input type="text"      name="custpan" id="custpan"  value="<?php echo $Cno;?>" readonly>
							    </div>
						</div><!--end control-group  -->
					     
				        <div class="control-group ">
							    <label class="control-label"> <h4><b> Shipper Name :</b></h4> </label>
							    <div class="controls">
								<!--<p ><h4><?php echo $shipper;?></h4></p>-->
							     <input type="text"      name="custpan" id="custpan"  value="<?php echo $shipper;?> " readonly>
							    </div>
						</div><!--end control-group  -->
					     <div class="control-group ">
							    <label class="control-label"> <h4><b> From :</b></h4> </label>
							    <div class="controls">
								<!--<p ><h4> <?php echo $from;?></h4></p>-->
							     <input type="text" name="custpan" id="custpan"  value=" <?php echo $from;?>" readonly>
							    </div>
						</div><!--end control-group  -->
					  
						 <div class="control-group ">
							    <label class="control-label"> <h4><b> Type of Shipment :</b></h4> </label>
							    <div class="controls">
								<!--<p ><h4><?php echo $bktype;?> </h4></p>-->
							     <input type="text"      name="custpan" id="custpan"  value="<?php echo $bktype;?>" readonly>
							    </div>
						</div><!--end control-group  -->
					     
				        <div class="control-group ">
							    <label class="control-label"> <h4><b> Weight : </b></h4> </label>
							    <div class="controls">
								<!--<p ><h4><?php echo $weigh;?></h4></p>-->
							     <input type="text"      name="custpan" id="custpan"  value="<?php echo $weigh;?>" readonly>
							    </div>
						</div><!--end control-group  -->
						 <div class="control-group ">
							    <label class="control-label"> <h4><b> Number of Quntity :</b> </h4> </label>
							    <div class="controls">
								<!--<p ><h4> <?php echo $noqty;?></h4></p>-->
							     <input type="text"      name="custpan" id="custpan"  value="<?php echo $noqty;?>" readonly>
							    </div>
						</div><!--end control-group  -->
					     
				        <div class="control-group ">
							    <label class="control-label"> <h4><b> Status : </b></h4> </label>
							    <div class="controls">
								<!--<p ><h4> <?php echo $tatusss;?></h4></p>-->
							     <input type="text" name="custpan" id="custpan"  value="<?php echo $tatusss;?>" readonly>
							    </div>
						</div><!--end control-group  -->
					     
				</div>
				 <div class="span5">
				            <div class="control-group ">
							    <label class="control-label"> <h4><b> Book Date : </b> </h4> </label>
							    <div class="controls">
								<!--<p ><h4><?php echo $bkdate;?></h4></p>-->
							     <input type="text"      name="custpan" id="custpan"  value="<?php echo $bkdate;?>" readonly>
							    </div>
						</div><!--end control-group  -->  
						  <div class="control-group ">
							    <label class="control-label"> <h4><b> Receiver Name :</b> </h4> </label>
							    <div class="controls">
								<!--<p ><h4><?php echo $reciver;?></h4></p>-->
							     <input type="text" name="custpan" id="custpan"  value="<?php echo $reciver;?>" readonly>
							    </div>
						</div><!--end control-group  -->
					     
				        <div class="control-group ">
							    <label class="control-label"> <h4><b> To :  </b></h4> </label>
							    <div class="controls">
								<!--<p ><h4><?php echo $To;?></h4></p>-->
							     <input type="text"      name="custpan" id="custpan"  value="<?php echo $To;?>" readonly>
							    </div>
						</div><!--end control-group  -->
					     <div class="control-group ">
							    <label class="control-label"> <h4> <b> Book Mode : </b></h4> </label>
							    <div class="controls">
								<!--<p ><h4><?php echo $bkmode;?> </h4></p>-->
							     <input type="text"      name="custpan" id="custpan"  value="<?php echo $bkmode;?> " readonly>
							    </div>
						</div><!--end control-group  -->
					     
				        <div class="control-group ">
							    <label class="control-label"> <h4> <b> Docket Charges  : </b></h4> </label>
							    <div class="controls">
								<!--<p ><h4><?php echo $docket;?></h4></p>-->
							     <input type="text"      name="custpan" id="custpan"  value="<?php echo $docket;?>" readonly>
							    </div>
						</div><!--end control-group  -->
						 <div class="control-group ">
							    <label class="control-label"> <h4> <b> DOD / COD : </b></h4> </label>
							    <div class="controls">
								<!--<p ><h4><?php echo $dod;?></h4></p>-->
							     <input type="text"      name="custpan" id="custpan"  value="<?php echo $dod;?>" readonly>
							    </div>
						</div><!--end control-group  -->
					     
				        <div class="control-group ">
							    <label class="control-label"> <h4> <b> ODA / MIS :</b> </h4> </label>
							    <div class="controls">
								<!--<p ><h4><?php echo $oda;?></h4></p>-->
							     <input type="text"      name="custpan" id="custpan"  value="<?php echo $oda;?>" readonly>
							    </div>
						</div><!--end control-group  -->
						  <div class="control-group ">
						       <div class="controls">
							    <label class="control-label"> <h4><a href ="<?php echo $pod_imag_path; ?>" class="lightbox-image" title="Image Caption Here" target="_blank"><b>View POD Image</b></a> </h4> </label>
							   
				
							    </div>
						</div><!--end control-group  --><!--end control-group  -->
						<!--<div class="control-group ">
							    <label class="control-label"> <h4> <b> Status Date : </b></h4> </label>
							    <div class="controls">
								<p ><h4><?php echo $Cno;?></h4></p>
							     <input type="hidden"      name="custpan" id="custpan"  value="<?php echo $Cno;?>" readonly>
							    </div>
						</div><!-end control-group  -->
				
						  
				</div>
			
									  
									  
									  
									</form>
								</div>
							</div>
						</div><!--end accordion-group-->
						 
						<div class="accordion-group">
							<div class="accordion-heading">
								<a class="accordion-toggle" data-toggle="collapse" data-parent="#cart-acc" href="#gift-voucher">
									<h2> <i class="icon-caret-down"></i><b>&nbsp;&nbsp;&nbsp;&nbsp;More Details...&nbsp;&nbsp;&nbsp;&nbsp;</b></h2>
								</a>
							</div>
							<div id="gift-voucher" class="accordion-body collapse">
								<div class="accordion-inner">
									<form class="form-horizontal">
									  <table class="table">
									  <thead>
									  <tr><h4><b>
									  <th> Sr.No </th>
									  <th>Sources</th>
									  <th>Destination</th>
									  <th>Date_Time</th>
									  <th>Status</th>
									  </h4> </b> </tr></thead>
									  <tr> 
									  <?php echo $tr;?>
									   <?php echo $els;?>
									  </tr>
									  </table>
									</form>
								</div>
							</div>
						</div><!--end accordion-group-->

					</div><!--end cart-accordain-->
				</div><!--end span7-->
               
			</div><!--end row-->

	</div><!--end conatiner-->
	
<?php
include("footer.php");
?>
	