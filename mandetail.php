<?php
session_start();
require_once('database.php');
require_once('library.php');

isUser();

$sql = "SELECT *
		FROM tbl_courier_officers";
$result = dbQuery($sql);		

?>
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>

	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
	
</head>
<script language="JavaScript">
var checkflag = "false";

function check(field) {
if (checkflag == "false")
 {
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll")
	{
	field[i].checked=true;	
	}
	}
	checkflag = "true";
}
else
{
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll")
	{
	field[i].checked=false;
	}
	}
	checkflag = "false";
}

}
function confirmDel(field,msg)
{
	count=0;
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll" && field[i].checked==true)
	{
	count++;
	}
	}
	
	if(count == 0)
	{
		alert("Select any one record to delete!");
		return false;
	}
	else
	{
		return confirm(msg);
	}
}
</script>

<?php include("header.php"); ?>



		<div class="container">

			<div class="row">

				              
                
                <div class="span11">
					<div class="account-list-outer">

						<div class="titleHeader clearfix">
							<h3>View All Manager Details   </h3>
						</div><!--end titleHeader-->

						<table class="table">
						<thead>
							<tr>
								<th><h5>Sr.No </h5></th>
								<th><h5>Manager Name </h5></th>
								<th><h5>Phone Number</h5></th> 
								<th><h5>Email </h5></th>		
								<th><h5>Office Name </h5></th>
								<th><h5>Office Address </h5></th>
								
							</tr>
						</thead>
	<?php
	
	while($data = dbFetchAssoc($result)){
	extract($data);	
	?>
						<tbody>
							<tr>
								<td><!--Sr.No-->
									<?php echo $count; ?>
								</td>
								<td ><!--Manager Name-->
									<?php echo $Manager_name; ?>
								</td>
								<td><!--Contact Number-->
									<?php echo $ph_no; ?>
								</td>
								<td><!--Email-->
								<?php echo $email; ?>
								</td>
								<td ><!--Office Name-->
									<?php echo $office; ?>
								</td>
								<td><!--Office Address-->
									<?php echo $address; ?>
								</td>
								                                
							</tr>
							
						</tbody>
						<?php
	}//while
	?>
					</table>
				

				
					</div><!--end -->
				</div><!--end span-->


				
			</div><!--end row-->



			<!--end row-->


			<!--end row-->


		</div><!--end conatiner-->


<?php include("footer.php"); ?>