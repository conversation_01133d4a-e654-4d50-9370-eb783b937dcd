<?php 
session_start();
require_once('library.php');
$rand = get_rand_id(8);
//echo $rand;
$a=$_SESSION['username'];
$sql="select * from login where username='$a'";
$result = mysqli_query($con, $sql);
$row1 = mysqli_fetch_array($result, MYSQLI_BOTH);
 $userid = ($row1 && isset($row1['rid'])) ? $row1['rid'] : 0;

$query = "SELECT MAX(custid) AS custid FROM custreg ";
    if($result = mysqli_query($con,$query))
    {
  while ($row = mysqli_fetch_assoc($result))
  {
        $count = $row['custid'];
        $count = $count+1;

      $code_no = str_pad($count, 7, "0", STR_PAD_LEFT);
  }
	}

	// Initialize POST variables
	$he = $_POST['cstin'] ?? 0;
	$we = $_POST['custstax'] ?? 0;
	$len = $_POST['custpan'] ?? 0;

	// echo $he;
	// echo $we;
	// echo $len;

	 $tot=($len*$we*$he)/6000;
?>
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>

	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
	
</head>
<?php
include("header.php");
?>



        <div class="container">

			<div class="row">

				<div class="span2">
				
				</div><!--end span8-->
                             
                <div class="span8">
					<div class="register">
						<div class="titleHeader clearfix">
							<h3>Delivery Boy Registration</h3>
						</div><!--end titleHeader-->
						<form action="processDri.php?action=regBoy" method="post" class="form-horizontal" onSubmit="return validate()" name="frmShipment">
						   	<legend>&nbsp;&nbsp;&nbsp;&nbsp;Delivery Boy Information :</legend>

							<div class="control-group ">
							    <label class="control-label">Name :<span class="text-error" >*</span></label>
							    <div class="controls">
							      <input type="text" name="boyname" placeholder="Full Name">  
							     <!-- <span class="help-inline"><i class="icon-ok"></i> Avaliable input!</span>-->
							    </div>
							</div><!--end control-group-->
<div class="control-group ">
							    <label class="control-label">User Name :  <span class="text-error" >*</span></label>
							    <div class="controls">
							      <input type="text" name="uname" placeholder="UserName" onKeyUp="myFunction(this.value)"> 
 <small class="errorText" id="dboyunames" style="color:red"></small><small class="errorText" id="dboyunamea" style="color:green"></small>
							     <!-- <span class="help-inline"><i class="icon-ok"></i> Avaliable input!</span>-->
							    </div>
							</div><!--end control-group--><input type="hidden" name="cid" value="<?php echo $userid;?>">
							<div class="control-group">
							    <label class="control-label" for="boymob">Mobile No : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="boymob" placeholder="Mobile No">  
							    </div>
							</div><!--end control-group-->
							<!--<div class="control-group">
							    <label class="control-label">E-Mail :</label>
							    <div class="controls">
							      <input type="email" name="custemail" placeholder="<EMAIL>">
							    </div>
							</div><!--end control-group-->
							<div class="control-group ">
							    <label class="control-label" for="boyaddress"> Address :<span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="boyaddress" placeholder="Address">  
							     <!--< <span class="help-inline">-->
							    </div>
							</div><!--end control-group   <i class="icon-remove"></i> Invalid input!</span>-->
							
							<div class="control-group">
							    <div class="control-label">States : </div>
							    <div class="controls">
							      <select name="states">
							      	<option value="Select" selected="selected">-- Select States --</option>
							      	<option value="AP">Andhra Pradesh</option>
							      	<option value="GA">Goa</option>
							      	<option value="GU">Gujarat</option>
							      	<option value="KA">Karnataka</option>
							      	<option value="KE">Kerala </option>
<option value="MP">Madhya Pradesh </option>
<option value="MH">Maharashtra  </option>
<option value="TS">Telangana </option>
							      </select>
							    </div>
							</div><!--end control-group-->
							
						    <div class="control-group">
							    <div class="control-label">City : </div>
							    <div class="controls">
							      <select name="city">
								<option selected="selected" value="Select">--Select City--</option>
							      	<option value="Ahm">Ahmedabad</option>
							      	<option value="Bang">Bangalore</option>
							      	<option value="Chen">Chennai</option>
							      	<option value="Delh">Delhi</option>
							      	<option value="Hyd">Hyderabad</option> 
<option value="Ko">Kolkata</option> 
<option value="Mum">Mumbai</option> 
<option value="pune">Pune</option> 
							      </select>
							    </div>
							</div><!--end control-group-->
							<div class="control-group success">
							    <label class="control-label" for="zip"> Zip Code : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="zip" placeholder="Zip Code">  
							      <!--<span class="help-inline"><i class="icon-ok"></i> Avaliable input!</span>-->
							    </div>
							</div><!--end control-group-->
<div class="control-group">
							    <label class="control-label" for="ememobno">Emergency Phoneno : <span class="text-error" id="vati" >*</span>
							    </label>
							    <div class="controls">
							      <input type="text" name="ememobno" placeholder="Emergency Mobile No">  
		                        </div>				
							</div><!--end control-group-->
                            <div class="control-group">
							    <label class="control-label" for="drilin1">Driving License No : </label>
							    <div class="controls">
							      <input type="text" name="drilin1" placeholder="Driving License No" >
							    </div>
							</div><!--end control-group--> 
 <div class="control-group">
							    <label class="control-label" for="ememobno">Password : <span class="text-error" id="vati" >*</span></label>
							    <div class="controls">
							      <input type="password" name="pass" placeholder="Enter Password">  
							    </div>
							</div><!--end control-group-->
							<div class="control-group">
							    <label class="control-label" for="cpass">Confirm Password :<span class="text-error" id="vati" >*</span> </label>
							    <div class="controls">
							      <input type="password" name="cpass" placeholder="Enter Confirm Password">  
							    </div>
							</div><!--end control-group-->
                           	<legend>&nbsp;&nbsp;&nbsp;&nbsp;Vehicle Information :</legend>
                            
							<div class="control-group">
							    <label class="control-label" for="vehicleno">Vehicle No : <span class="text-error" id="vati" >*</span></label>
							    <div class="controls">
							      <input type="text" name="vehicleno" id="vehicleno" placeholder="Vehicle Number">  
							    </div>
							</div><!--end control-group-->
							<div class="control-group">
							    <label class="control-label" for="vehiclename">Vehicle Type : </label>
							    <div class="controls">
							      <input type="text" name="vehiclename" placeholder="Vehicle Type" >
							    </div>
							</div><!--end control-group-->    

							<!--<div class="control-group ">
							    <label class="control-label" for="custpan">PAN No : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="custpan" placeholder="PAN No">
							     <!--< <span class="help-inline">-->
							   <!-- </div>
							</div><!--end control-group   <i class="icon-remove"></i> Invalid input!</span>-->
							<div class="control-group">
							    <div class="controls">
							
									<input name="submit" class="btn btn-primary" type="submit" value="Register" onClick="return validateForm()" id="bute">
									<button type="reset" class="btn ">Clear</button>
							    </div>
							</div><!--end control-group-->
							
																				
						</form><!--end form-->

					</div><!--end register-->
				
				</div><!--end span-->
	
			</div><!--end row-->

        </div><!--end conatiner-->


<script  type="text/javascript">
function myFunction(q)
{
//alert("hii");
$.ajax({

  type: "POST",
  url: "uname.php",
  data: {item:q},
  success:function(data){
 //alert(data);
  document.getElementById("dboyunames").innerHTML=data;
  if(data=="Username Already Exist!!")
  {
  document.getElementById("bute").style.visibility = 'hidden';
  document.getElementById("dboyunamea").style.visibility = 'hidden';
  }else
  {
	document.getElementById("dboyunamea").style.visibility = 'visible';
	document.getElementById("dboyunamea").innerHTML="Username Available!!";
	document.getElementById("bute").style.visibility = 'visible';  
  }
}
  });
}
</script>
	
<script  type="text/javascript">
function validateForm()
{
var x=document.forms["frmShipment"]["boyname"].value;
if (x==null || x=="")
  {
  alert("Name must be filled out");
  return false;
  }

   var uname1=document.forms["frmShipment"]["uname"].value;
if (uname1==null || uname1=="")
  {
  alert("UserName must be filled out");
  return false;
  }

  var phone=document.forms["frmShipment"]["boymob"].value;
if (phone==null || phone=="")
  {
   alert("Mobile No must be 10 digit ");
  return false;
  
  }
  
  var add=document.forms["frmShipment"]["boyaddress"].value;
if (add==null || add=="")
  {
  alert("Address must be filled out");
  return false;
  }

  var zip=document.forms["frmShipment"]["zip"].value;
if (zip==null || zip=="")
  {
  alert("Zip Code must be filled out");
  return false;
  }
 var emg=document.forms["frmShipment"]["ememobno"].value;
if (emg==null || emg=="")
  {
  alert("Emergency number must be filled out");
  return false;
  }

var pass=document.forms["frmShipment"]["pass"].value;
  if (pass==null || pass=="")
  {
  alert("Password must be filled out");
  return false;
  }
  var cpass=document.forms["frmShipment"]["cpass"].value;
  if (cpass==null || cpass=="")
  {
  alert("Confirm Password must be filled out");
  return false;
  }
  if(pass!=cpass)
  {
   alert("Password and Confirm Password Not Matched");
  return false;
  }

  var vehicleno1=document.forms["frmShipment"]["vehicleno"].value;
if (vehicleno1==null || vehicleno1=="")
  {
  alert("Vehical Number must be filled out");
  return false;
  }
  
}
</script>

<script  type="text/javascript">
function validateForm1()
{ 
  var x1=document.forms["frmShipment"]["custphone"].value;
if (!x1>10 || x1=10)
  {
  alert("Mobile No must be 10 digit ");
  return false;
  }
  
 var x2=document.forms["frmShipment"]["custemail"].value;
 var atposition=x2.indexOf("@");  
var dotposition=x2.lastIndexOf(".");  
if (atposition<1 || dotposition<atposition+2 || dotposition+2>=x.length){  
  alert("Please enter a valid e-mail address \n atpostion:"+atposition+"\n dotposition:"+dotposition);  
  return false;  
  }
  
 var x3=document.forms["frmShipment"]["custaddress"].value;
if (x3==null || x3=="")
  {
  alert("Address must be filled out");
  return false;
  }
  
 var x4=document.forms["frmShipment"]["custzip"].value;
if (x4==6 || !x4>6)
  {
  alert("Zip code must be 6 Digit only ");
  return false;
  }
  
 var x5=document.forms["frmShipment"]["states"].value;
if (x5==null || x5=="")
  {
  alert("Please Select States");
  return false;
  }
  
   var x6=document.forms["frmShipment"]["city"].value;
if (x6==null || x6=="")
  {
  alert("Please Select City");
  return false;
  }
  
 var x7=document.forms["frmShipment"]["custin"].value;
if (isNaN(x7)){  
  document.getElementById("vati").innerHTML="Enter Numeric value only";  
  return false;  
}else{  
  return true;  
  }  
  
  var x8=document.forms["frmShipment"]["custstax"].value;
if (isNaN(x8)){  
  document.getElementById("st").innerHTML="Enter Numeric value only";  
  return false;  
}else{  
  return true;  
  }  
  
 
}
</script>		
           

<?php
include("footer.php");
?>