<?php
session_start();
require_once('database.php');
require_once('library.php');
require 'connection.php';
isUser();

$a=$_SESSION['username'];
 $sql="select * from tbl_courier_officers where username='$a'";
$result = mysqli_query($con, $sql);
$row = mysqli_fetch_array($result, MYSQLI_BOTH);
  $id = ($row && isset($row['rid'])) ? $row['rid'] : 0;

$sql="select * from `tbl_courier_officers` where cid='$id'";
$result = mysqli_query($con, $sql);
$row1 = mysqli_fetch_array($result, MYSQLI_BOTH);
$uoffice = ($row1 && isset($row1['office'])) ? $row1['office'] : '';
$uaddress = ($row1 && isset($row1['address'])) ? $row1['address'] : '';

date_default_timezone_set('Asia/Kolkata');
$date = date('d/m/Y h:i:s', time());

// Initialize dropdown variables
$company = $_POST['company'] ?? '';
$loc = $_POST['loc'] ?? '';

$sql1="select * from `tbl_offices` ORDER BY off_name ASC";
 $result2=mysqli_query($con,$sql1);
while($row2=mysqli_fetch_array($result2))
{
	if($company==$row2['id'])
	{
	$loc=$loc."<option value='".$row2['id']."' selected>".$row2['off_name']."</option>";
	}
	else{
	$loc=$loc."<option value='".$row2['id']."' >".$row2['off_name']."</option>";
	}
}


$no=1;
/*$result= mysqli_query($con,"SELECT *,max(billid) as id FROM receipt where type='Invoice'");
while($row = mysqli_fetch_array($result))
  {
  $maxInvid=$row['id'];
 
   }*/

//$result1 = mysqli_query($con,"SELECT * FROM `` ORDER BY `id` ASC ");
 //$sql2="SELECT * FROM receipt where billid=".$maxInvid."";
 /*$sql2="SELECT * FROM receipt";
$result2= mysqli_query($con,$sql2);
while($row = mysqli_fetch_array($result2))
  {
 $maxInvid;
$no=$row['billid'];
 
}*/
 $_SESSION["no"]=$no;
 
?>

<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>
	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
<script language="JavaScript">
</script>
</head>
<?php include("header.php"); ?>
   <div class="container">
			<div class="row">
     			<div class="span2">
				         
				</div><!--end span3-->
                <form action="billConsgIn.php" name="billform" method="post" >
                <div class="span8">
					<div class="account-list-outer">

							<div class="titleHeader clearfix">
							<h3>Consignment Bill Generate</h3> 
						</div>
					     
						<!--<div class="control-group">
                            <label>Branch Location :</label>
							<div class="controls">
	                            <select name="curntloca" id="curntloca" data-rule-required="true">
												<option value="">-- Please select --</option>
												<?php echo $loc; ?>
								</select>
							</div>
					    </div>-->
						<div class="control-group">
                             <div class="controls">
							     <label class="control-label"> Bill No.</label>
							     <input name="prebilno" type="text" value="<?php echo $no;?>" readonly>
						<input type="hidden" name="uoffice" value="<?php echo $uoffice;?>">
<input type="hidden" name="uaddress" value="<?php echo $uaddress;?>">
<input type="hidden" name="cid" value="<?php echo $id;?>">
                                                <input name="uoffadd" type="hidden" value="<?php echo $add;?>"> 
							 </div>
					    </div>
						
						<div class="control-group">
							    <label class="control-label">Consignment No : </label> 
							        <div class="controls">
									   <input name="consgin" id="consgin" type="text" placeholder="Insert Consignment No" > <span class="text-error" >*</span>
	                                </div>
						</div>
					<!--	<div class="control-group">
						<div class="controls">
						     <label class="control-label"> Start Date </label>
							<div class="controls input-append date form_date" data-date="" data-date-format="dd MM yyyy" data-link-field="dtp_input2" data-link-format="yyyy-mm-dd">
										<input name="stdate" id="stdate" type="text" value="" readonly>
										<span class="add-on"><i class="icon-remove"></i></span>
										<span class="add-on"><i class="icon-th"></i></span>
							</div>
										<input type="hidden" id="dtp_input2" value="" />
					    </div>
						</div>
						<div class="control-group">
						<div class="controls">
						     <label class="control-label"> End Date </label>
							<div class="controls input-append date form_date" data-date="" data-date-format="dd MM yyyy" data-link-field="dtp_input2" data-link-format="yyyy-mm-dd">
										<input name="endate" id="endate" type="text" value="" readonly>
										<span class="add-on"><i class="icon-remove"></i></span>
										<span class="add-on"><i class="icon-th"></i></span>
							</div>
										<input type="hidden" id="dtp_input2" value="" />
					    </div>
						</div>-->
						<div class="control-group">
						    <div class="controls"> 
								<input name="Submit" class="btn btn-primary" type="submit" value="SUBMIT" onClick="return validateForm()" >
							&nbsp;&nbsp;&nbsp;<button type="reset" class="btn ">Clear</button>
					        </div>
					    </div>
                   
					  
					   
                    </div><!--end -->
				</div><!--end span6-->
				</form>
			</div><!--end row-->
		</div><!--end conatiner-->
				
<script>
$(document).ready(function ()
  {
   $("#curntloca").change(function () { 
  
   $('#cname').find('option').remove().end().append('<option value="">-- Select Customer --</option>').val('');
    $.ajax({                                      
      url: 'ajaxGetCustName.php?compid='+$('#curntloca').val(),                  //the script to call to get data          
      data: "",                        //you can insert url argumnets here to pass to api.php
                                       //for example "id=5&parent=6"
      dataType: 'json',                //data format      
      success: function(data)          //on recieve of reply
      {
      $.each(data, function(index, data) {
        $('#cname').append( $('<option></option>').val(data.siteid).html(data.SiteName) );
       });
       }
       });       
    });
  }); 
  


</script>	

<link href="./bootstrap/css/bootstrap-datetimepicker.min.css" rel="stylesheet" media="screen">
<script type="text/javascript" src="./jquery/jquery-1.8.3.min.js" charset="UTF-8"></script>
<script type="text/javascript" src="./bootstrap/js/bootstrap.min.js"></script>
<script type="text/javascript" src="./jquery/bootstrap-datetimepicker.js" charset="UTF-8"></script>
<script type="text/javascript" src="./jquery/locales/bootstrap-datetimepicker.pl.js" charset="UTF-8"></script>
<script type="text/javascript">
	$('.form_date').datetimepicker({
        language:  'pl',
        weekStart: 1,
        todayBtn:  1,
		autoclose: 1,
		todayHighlight: 1,
		startView: 2,
		minView: 2,
		forceParse: 0
    });
	$('.form_time').datetimepicker({
        language:  'pl',
        weekStart: 1,
        todayBtn:  1,
		autoclose: 1,
		todayHighlight: 1,
		startView: 1,
		minView: 0,
		maxView: 1,
		forceParse: 0
    });
</script>
	

<script  type="text/javascript">
function validateForm()
{
  var consgin1=document.forms["billform"]["consgin"].value;
if (consgin1==null || consgin1=="")
  {
   alert("Please Fill Consignment No");
  return false;
  
  }
  
   
}
</script>

	
<?php 
include("footer.php"); ?>