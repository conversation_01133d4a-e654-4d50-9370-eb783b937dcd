<?php
//error_reporting(~E_ALL);
session_start();
require_once('database.php');
require_once('library.php');
require 'connection.php';
isUser();

$a=$_SESSION['username'];
$sql="select * from login where username='$a'";
$result = mysqli_query($con, $sql);
$row1 = mysqli_fetch_array($result, MYSQLI_BOTH);
 $userid = ($row1 && isset($row1['rid'])) ? $row1['rid'] : 0;

 $sql="select cid,office,Manager_name from tbl_courier_officers where cid='$userid'";
$result = mysqli_query($con, $sql);
$row1 = mysqli_fetch_array($result, MYSQLI_BOTH);
$uoffice = ($row1 && isset($row1['office'])) ? $row1['office'] : '';
$clerkname = ($row1 && isset($row1['Manager_name'])) ? $row1['Manager_name'] : '';
$id = ($row1 && isset($row1['cid'])) ? $row1['cid'] : 0;

date_default_timezone_set('Asia/Kolkata');
$date = date('d/m/Y h:i:s', time());

// Initialize dropdown variables
$company = $_POST['company'] ?? '';
$loc = $_POST['loc'] ?? '';

$sql1="select * from `tbl_offices` ORDER BY off_name ASC";
 $result2=mysqli_query($con,$sql1);
while($row2=mysqli_fetch_array($result2))
{
	if($company==$row2['id'])
	{
	$loc=$loc."<option value='".$row2['id']."' selected>".$row2['off_name']."</option>";
	}
	else{
	$loc=$loc."<option value='".$row2['id']."' >".$row2['off_name']."</option>";
	}
}


// Initialize customer dropdown variable
$drop1 = $_POST['drop1'] ?? '';

if($a!="admin")
{
$sql = "SELECT * FROM `custreg` WHERE userid='$id' ORDER BY custname ASC";
}
else{
$sql = "SELECT * FROM `custreg` ORDER BY custname ASC";
}
$result2=mysqli_query($con,$sql);
while($row2=mysqli_fetch_array($result2))
{
	if($company==$row2['custid'])
	{
	$drop1=$drop1."<option value='".$row2['custid']."' selected>".$row2['custname']."</option>";
	}
	else{
	$drop1=$drop1."<option value='".$row2['custid']."' >".$row2['custname']."</option>";
	}
}


/*$no=01;
$result= mysqli_query($con,"SELECT *,max(billid) as id FROM receipt where type='Invoice'");
while($row = mysqli_fetch_array($result))
  {
  $maxInvid=$row['id'];
 
   }*/

//$result1 = mysqli_query($con,"SELECT * FROM `` ORDER BY `id` ASC ");
 //$sql2="SELECT * FROM receipt where billid=".$maxInvid."";
 /*$sql2="SELECT * FROM receipt";
$result2= mysqli_query($con,$sql2);
while($row = mysqli_fetch_array($result2))
  {
 $maxInvid;
$no=$row['billid'];

}*/

// Initialize variables
$no = $_SESSION['no'] ?? 1;
$invbil = '';

 $_SESSION["no"]=$no;



$sqlli="SELECT * FROM `invoicebill` ORDER BY `inv_no` ASC";
 $invsr=0;
 $datas=mysqli_query($con,$sqlli);
 while($invrow=mysqli_fetch_array($datas))
 {
	 $invsr++;

	 $invbil=$invbil."<tr><td>".$invsr."</td><td><a href='invoicebil1.php?cust=".$invrow['custid']."&start=".$invrow['sdate']."&end=".$invrow['tdate']."&mode=".$invrow['bokkingmode']."&invoiceno=".$invrow['inv_no']." '>".$invrow['inv_no']."</a></td><td>".$invrow['inv_date']."</td></tr>";
	
 }
 
 
?>

<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>
	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
<script language="JavaScript">
</script>
</head>
<?php include("header.php"); ?>
   <div class="container">
   <form action="getdocket.php" name="billform" method="post" >
			<div class="row">
     			     <div class="titleHeader clearfix">
							<h3>Invoice Bill Generate </h3> 
						</div>
					<div class="span6">
					  
						<!--<div class="control-group"> <form action="invoicebil.php" name="billform" method="post" >
                             <div class="controls">
							     <label class="control-label">Bill No.</label>
							     <input name="bilno" type="text" value="<?php echo $no;?>" readonly>
								<input name="tp" type="hidden" value="Invoice"> 

							 </div>
					    </div>
						<div class="control-group">
                            <label>Branch Location :</label>
							<div class="controls">
	                            <select name="curntloca" id="curntloca" data-rule-required="true">
												<option value="">-- Please select --</option>
												<?php echo $loc; ?>
								</select>
							</div>
					    </div>-->
				<input type="hidden" name="uoffice" value="<?php echo $uoffice;?>">
				<input type="hidden" name="uaddress" value="<?php echo $clerkname;?>">
				<input type="hidden" name="cid" value="<?php echo $id;?>">
				<input name="todat" id="todat" type="hidden" value="<?php echo $date;?>" >		
						<div class="control-group">
							    <label class="control-label">Customer Name: </label> 
							        <div class="controls">
									    <select name="cname" id="cname" required>
									     <option value="">-- Please Select --</option>
									     	<?php echo $drop1; ?>
											
								        </select>
	                                </div>
						</div>
						
						<div class="control-group">
							    <label class="control-label">Booking Mode: </label> 
							        <div class="controls">
									    <select name="mode" id="node" required>
									     <option value="">-- Please Select --</option>
									      <option value="Air">Air</option>
                      <option value="Road">Surface</option>
                      <option value="Train">Train</option>
											
								        </select>
	                                </div>
						</div>
                       <!-- <div class="control-group">
							    <label class="control-label">Fuel Charges (%): </label> 
							        <div class="controls">
									     <input name="fuelcharge" id="fuelcharge" type="text" placeholder="Fuel Charges" >
									</div>
						</div>-->
						<div class="control-group ">
						   <div class="controls">
							<label for="rep"> Start Date : <span class="text-error">*</span></label>
								<div class="controls input-append date form_date" data-date="" data-date-format="dd MM yyyy" data-link-field="dtp_input2" data-link-format="yyyy-mm-dd">
									 <input  type="text" value=""id="date2" name="date2" readonly required>	
										<span class="add-on"><i class="icon-remove"></i></span>
										 <span class="add-on"><i class="icon-th"></i></span>
								</div>
									 <input type="hidden" id="dtp_input2" value="" />
						    </div>
						</div>
						<div class="control-group ">
						   <div class="controls">
							<label class="control-label" for="rep"> End Date : <span class="text-error">*</span></label>
								<div  class="controls input-append date form_date" data-date="" data-date-format="dd MM yyyy" data-link-field="dtp_input2" data-link-format="yyyy-mm-dd">
									<input  type="text" value="" id="date4" name="date4" readonly required>	
										<span class="add-on"><i class="icon-remove"></i></span>
										 <span class="add-on"><i class="icon-th"></i></span>
								</div>
									 <input type="hidden" id="dtp_input2" value="" />
						   </div>
						</div>
									 
                       <div class="control-group">
						    <div class="controls"> 
								<input name="Submit" class="btn btn-primary" type="submit" value="SUBMIT">
							&nbsp;&nbsp;&nbsp;<button type="reset" class="btn ">CLEAR</button>
					        </div>
					    </div>
                    </div>
                    
                    <!-- span6-->
					<div class="span6">
					
						<div class="control-group">
							<label class="control-label"><h3 align="center"><b>Invoice Bill List</b></h3></label> 
							        <div class="controls">
									    <table class="table">
                                    			<thead>
												<th>Sr.No</th>
												<th>Invoice Bill No.</th>
												<th>Invoice Bill Date.</th>
												</thead>	
                                                <tbody>
												<?php echo $invbil;?>
												
												</tbody>												
										</table>										
	                                </div>
						</div>
						
						
						
					
					</div>
					
			</div><!--end row-->
			
			
	</div><!--end span12-->
	
	
		<div id="cmp" style="overflow-x:auto!important;
   height:350px; width: 50%;"></div>
	 </form>
	
<link href="./bootstrap/css/bootstrap-datetimepicker.min.css" rel="stylesheet" media="screen">		
<script type="text/javascript" src="./jquery/jquery-1.8.3.min.js" charset="UTF-8"></script>
<script type="text/javascript" src="./bootstrap/js/bootstrap.min.js"></script>
<script type="text/javascript" src="./jquery/bootstrap-datetimepicker.js" charset="UTF-8"></script>
<!--<script type="text/javascript" src="./jquery/locales/bootstrap-datetimepicker.pl.js" charset="UTF-8"></script>-->
<script type="text/javascript">
   	$('.form_date').datetimepicker({
        language:  'pl',
        weekStart: 1,
        todayBtn:  1,
		autoclose: 1,
		todayHighlight: 1,
		startView: 2,
		minView: 2,
		forceParse: 0
    });
    
    
    
function getRep()
{

var date=document.getElementById("date2").value;
var date2=document.getElementById("date4").value;
var rep=document.getElementById("cname").value;
if (window.XMLHttpRequest)
{
 xmlhttp=new XMLHttpRequest();
 }
else
  {
  xmlhttp=new 
   
  veXObject("Microsoft.XMLHTTP");
  }
xmlhttp.onreadystatechange=function()
  {
  if (xmlhttp.readyState==4 && xmlhttp.status==200)
    {
   // alert (xmlhttp.responseText);
   document.getElementById("cmp").innerHTML=xmlhttp.responseText;
    }
  }
  xmlhttp.open("GET","getdocket.php?date="+date+"&date2="+date2+"&rep="+rep);
xmlhttp.send();

}
    
function checkAll(bx) {
var cbs = document.getElementsByTagName('input');
for(var i=0; i < cbs.length; i++) {
if(cbs[i].type == 'checkbox') {
cbs[i].checked = bx.checked;
}
}
}	
</script>
	

<script  type="text/javascript">
function validateForm()
{

  var cname1=document.forms["billform"]["cname"].value;
if (cname1==null || cname1=="")
  {
   alert("Please Select Customer Name ");
  return false;
  
  }
  
  var fuelcharge1=document.forms["billform"]["fuelcharge"].value;
if (fuelcharge1==null || fuelcharge1=="")
  {
  alert("Fuel Charges must be filled out");
  return false;
  }
  
}
</script>

<?php

 if(isset($_GET['msg']))
 {
	 $msg=$_GET['msg'];
	 if($msg=="yes"){
		 echo "<script> alert('Invoice Bill Submit Successfully...!');</script>";
	 }elseif($msg="yes1"){ 
				echo "<script> alert('Invoice Bill Update Successfully...!');</script>"; 
	 }else{ 
				 echo "<script> alert('Problem Occured...!');</script>"; 
	 }
	 
 }
 
include("footer.php"); ?>