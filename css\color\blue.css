/*============= Global ancher link color style ==========*/
a {
  color:#0069b4;
}
a:hover, a:active {
  color:#0069b4;
  text-decoration:underline;
}
a.invarseColor:link, a.invarseColor:visited {
  color:#666;
}
a.invarseColor:hover, a.invarseColor:active {
  color:#0069b4;
  text-decoration:none;
}
a.active:link, a.active:visited {
  color:#0069b4;
  cursor:default;
}

/*=================== dropdown =============*/
.dropdown-menu li > a:hover,
.dropdown-menu li > a:focus,
.dropdown-submenu:hover > a {
  background:#0069b4;
  background-color:#0069b4;
  filter:#0069b4;
}
.dropdown-menu .active > a,
.dropdown-menu .active > a:hover {
  background:#0069b4;
  background-color:#0069b4;
  filter: #0069b4;
}

/*==================== navbar =================*/
.navbar .nav > li:hover {
  background:#2374b2;
}
/* active link */
.navbar .nav .active a,
.navbar .nav .active a:hover,
.navbar .nav .active a:focus
.navbar .nav > .active > a,
.navbar .nav > .active > a:hover,
.navbar .nav > .active > a:focus,
.navbar .nav > .active > a i,
.navbar .nav > .active > a:focus i,
.navbar .nav > .active > a:hover i,
.navbar .nav > .active > a span,
.navbar .nav > .active > a:focus span,
.navbar .nav > .active > a:hover span {
  background: #2374b2;
  background-color: #2374b2;
  filter:#2374b2;
}
/* nested ul */
.navbar .nav div {
  background: #2374b2;
}


/*========== btn-primary (blue-btn) ==========*/
.btn-primary {
  border:1px solid #2374B2;
  background: #2989d8;
  background: -moz-linear-gradient(top, #2989d8 0%, #2374b2 100%);
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#2989d8), color-stop(100%,#2374b2));
  background: -webkit-linear-gradient(top, #2989d8 0%,#2374b2 100%);
  background: -o-linear-gradient(top, #2989d8 0%,#2374b2 100%);
  background: -ms-linear-gradient(top, #2989d8 0%,#2374b2 100%);
  background: linear-gradient(to bottom, #2989d8 0%,#2374b2 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#2989d8', endColorstr='#2374b2',GradientType=0 );
}
.btn-primary:hover,
.btn-primary:active,
.btn-primary.active,
.btn-primary.disabled,
.btn-primary[disabled] {
  background: #2374B2;
}
.btn-primary:active,
.btn-primary.active {
  background-color: #2989d8;
}