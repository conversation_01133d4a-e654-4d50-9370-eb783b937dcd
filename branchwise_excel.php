<?php
session_start();
require 'connection.php';
require_once('database.php');
require_once('library.php');
ob_start();

// Get parameters from the URL - handle both old and new parameter names
$branch = $_GET['branch'] ?? $_GET['id'] ?? '';
$date1 = $_GET['date1'] ?? $_GET['strdate'] ?? '';
$date2 = $_GET['date2'] ?? '';

// Validate required parameters
if (empty($branch) || empty($date1) || empty($date2)) {
    die('Error: Missing required parameters (branch, date1, date2)');
}

$Cdate = date('Y-m-d', strtotime($date1));
$Cdate1 = date('Y-m-d', strtotime($date2));
$tr="";

// Build the SQL query based on the selected branch (using status name for branch filtering)
$sql = "SELECT userid,cons_no,weight,gtotamt,rev_name,r_add,ship_name,rate,oda_mis,status_date,book_mode,noofpackage,e_waybill,eway_start_date,eway_end_date,status,eway_expdate,gst,partno,remark,freight,book_mode,invi_value,qty,assured_dly_date,book1_date,a.city_name as city
,type,invice_no,chweight,mode,statusname,vehicle FROM (tbl_courier inner join status on tbl_courier.status=status.statusid) join  tbl_city_code a on tbl_courier.s_add=a.Id
WHERE status.statusname = '$branch' and tbl_courier.book_date between '$Cdate' and '$Cdate1' ORDER BY cons_no DESC ";
$result = mysqli_query($con,$sql); 
 
$count=0;
while($row=mysqli_fetch_array($result))
{
    // Initialize variables to avoid undefined variable warnings
    $shipper_name = '';
    $shipper_phone = '';
    $shipper_mail = '';
    $shipper_add = '';
    $shipper_city = '';
    $destination = '';
    $Manager_name = '';
    $empcode = '';
    $statusdate = '';
    $delivereddate = '';
    $withoutgstvalue = '';

    //shipper details
    $sql_s="SELECT * FROM `custreg` where `custname` like '".$row['ship_name']."'" ;
    $result_s = mysqli_query($con,$sql_s);
    if($result_s && $row_s = mysqli_fetch_array($result_s)) {
        $shipper_name = $row_s['custname'] ?? '';
        $shipper_phone = $row_s['custphone'] ?? '';
        $shipper_mail = $row_s['custmail'] ?? '';
        $shipper_add = $row_s['custadd'] ?? '';
        $shipper_c = $row_s['custcity'] ?? '';

        if($shipper_c) {
            $sql_s="SELECT * FROM `city` where ctid=".$shipper_c ;
            $result_s = mysqli_query($con,$sql_s);
            if($result_s && $row_s = mysqli_fetch_array($result_s)) {
                $shipper_city = $row_s['cityname'] ?? '';
            }
        }
    }
    //shipper details end

    // Status date handling
    if($row['status']=='6' || $row['status']=='50' || $row['status']=='57'){
        $statusdate=$row['status_date'];
    }else{
        $statusdate="";
    }

    // Delivered date handling
    if($row['status']=='5'){
        $delivereddate=$row['status_date'];
    }else{
        $delivereddate="";
    }

    // Calculate without GST value (assuming 18% GST)
    if($row['invi_value'] && is_numeric($row['invi_value'])) {
        $withoutgstvalue = round($row['invi_value'] / 1.18, 2);
    }
 
    // Destination handling
    if($row['r_add']==""){
        $destination="";
    }else{
        $sql1="SELECT city_name,r_add from tbl_city_code inner join tbl_courier on tbl_city_code.Id= tbl_courier.r_add
        WHERE tbl_courier.r_add = '".$row['r_add']."' " ;
        $result1 = mysqli_query($con,$sql1);
        if($result1 && $row1 = mysqli_fetch_array($result1)) {
            $destination = $row1['city_name'] ?? '';
        }
    }

    // Manager details
    $sql2="SELECT Manager_name,empcode from tbl_courier_officers inner join tbl_courier on tbl_courier_officers.cid=tbl_courier.userid
    WHERE tbl_courier.userid = '".$row['userid']."' " ;
    $result2 = mysqli_query($con,$sql2);
    if($result2 && $row2 = mysqli_fetch_array($result2)) {
        $Manager_name = $row2['Manager_name'] ?? '';
        $empcode = $row2['empcode'] ?? '';
    }
 
	$count++;
$tr=$tr."<tr><td>".$count."</td><td>".$Manager_name."</td><td>".$empcode."</td><td>".$shipper_name."</td><td>".$shipper_phone."</td><td>".$shipper_add."</td><td>".$shipper_mail."</td><td>".$shipper_city."</td><td>".$row['status_date']."</td><td>".$row['cons_no']."</td><td>".$row['book1_date']."</td><td>".$row['invice_no']."</td>
<td>".$row['rev_name']."</td><td>".$destination."</td><td>".$row['noofpackage']."</td><td>".$row['qty']."</td><td>".$row['partno']."</td><td>".$withoutgstvalue."</td><td>".$row['invi_value']."</td><td>".$statusdate."</td>
<td>".$row['book_mode']."</td><td>".$row['statusname']."</td><td>".$row['remark']."</td><td>".$delivereddate."</td><td>".$row['mode']."</td><td>".$row['weight']."</td><td>".$row['chweight']."</td><td>".$row['e_waybill']."</td><td>".$row['eway_start_date']."</td><td>".$row['eway_end_date']."</td><td>".$row['vehicle']."</td><td>".$row['rate']."</td>
<td>".$row['oda_mis']."</td><td>".$row['freight']."</td><td></td><td></td></tr>";



}

header("Content-Type:   application/vnd.ms-excel; charset=utf-8");
header("Content-type:   application/x-msexcel; charset=utf-8");
header("Content-Disposition: attachment; filename=Customer_Report.xls"); 
header("Expires: 0");
header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
header("Cache-Control: private",false);

	
	
		echo' <table class="table1"  style="width:100%"  border="1">
						<thead>
					 <th><h6>Sr No. </h6></th>
							      <th><h6>Employee Name </h6></th>
							      <th><h6>Emp=ID</h6></th>
							        <th><h6>Shipper Name</h6></th>
							         <th><h6>Shipper Phone</h6></th>
							          <th><h6>Shipper Address</h6></th>
							           <th><h6>Shipper Mail</h6></th><th><h6>Shipper City</h6></th>
							       <th><h6>Scanning Date</h6></th>
							  <th><h6>LR No </h6></th>
							     <th><h6>BKG Date</h6></th>
							     <th><h6>Invoice No </h6></th>
							     <th><h6>Customer Name </h6></th>
						         <th><h6>Destinantion</h6></th>
						         	<th><h6>Cases </h6></th>
									<th><h6>Qty </h6></th>
										<th><h6>Part No. </h6></th>
								
								 
								  <th><h6>Without GST Value </h6></th>
								    <th><h6>Invoice Value </h6></th>
								  	 
									
								   <th><h6>Godown Receipt Date </h6></th>
								<th><h6>Type</h6></th>
								<th><h6>Remarks</h6></th>
								<th><h6>My Remarks</h6></th>
								  <th><h6>Delivery date </h6></th>
								    <th><h6>Mode</h6></th>
							    <th><h6>A/Weight </h6></th>
								<th><h6>C/Weight </h6></th>
									<th><h6>E WayBill No. </h6></th>		<th><h6>E Way Start Date </h6></th>
											<th><h6>E Way End Date </h6></th>
							
									<th><h6>Vehicle No. </h6></th>
									<th><h6>Rate </h6></th>
										<th><h6>ODA </h6></th>
											<th><h6>Fright </h6></th>
											<th><h6>GST </h6></th>
								
								<th><h6>Total </h6></th>
							</tr>
						</thead> <tbody>'.$tr,'</tbody>
					</table>';
	
?>
	
	