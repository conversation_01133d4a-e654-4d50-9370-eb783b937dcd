<?php 
session_start();
require_once('library.php');
$rand = get_rand_id(8);
//echo $rand;
  $userid=$_SESSION['desgn'];
 $a=$_SESSION['username'];
/* $sql="select * from login where type='$a'";
$result = mysqli_query($con, $sql);
$row1 = mysqli_fetch_array($result);
  $userid=$row1['rid'];*/
	
$query = "SELECT MAX(id) AS rcid FROM receiver_reg ";  
    if($result = mysqli_query($con,$query))
    {
  while ($row = mysqli_fetch_assoc($result))
  {
        $count = $row['rcid'];
        $count = $count+1;

      $code_no = str_pad($count, 7, "600100", STR_PAD_LEFT);
  }
	}

	// Initialize POST variables
	$he = $_POST['cstin'] ?? 0;
	$we = $_POST['custstax'] ?? 0;
	$len = $_POST['custpan'] ?? 0;

	// echo $he;
	// echo $we;
	// echo $len;

	 $tot=($len*$we*$he)/6000;

	// Initialize dropdown variables
	$stname = $_POST['stname'] ?? '';
	$statedrop = $_POST['statedrop'] ?? '';

 $statesql="SELECT * FROM stationary_tbl order by Stationary_items ASC";
 $stateresult=mysqli_query($con,$statesql);
 While($staterow=mysqli_fetch_array($stateresult))
{
	if($stname==$staterow['Id'])
	{
	 $statedrop=$statedrop."<option value='".$staterow['Id']."' selected>".$staterow['Stationary_items']."</option>";
	}
	else{
	  $statedrop=$statedrop."<option value='".$staterow['Id']."' >".$staterow['Stationary_items']."</option>";
	}
}
?>
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>

	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	<title>Vivanta Logistics</title>
	<meta name="description" content="">
	<meta name="author" content="Ahmed Saeed">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
<script>
 $(function() {
          
            $( "#datepicker" ).datepicker({dateFormat: 'yy-mm-dd'});
  });

  </script>
 <script language="javascript" type="text/javascript" src="datetimepicker.js">   </script>  	
</head>
<?php
include("header.php");
?>



        <div class="container">

			<div class="row">

				<div class="span2">
				
				</div><!--end span8-->
                             
                <div class="span8">
					<div class="register">
						<div class="titleHeader clearfix">
							<h3>Stationary Registration</h3>
						</div><!--end titleHeader-->
						<form action="process.php?action=add-stationary" method="post" class="form-horizontal" onSubmit="return validate()" name="frmShipment">
						   	<legend>&nbsp;&nbsp;&nbsp;&nbsp;Stationary Information :</legend>
	<div class="control-group ">
							    <label class="control-label">Stationary Item : <span class="text-error" >*</span></label>
							    <div class="controls">
							      <select name="stitems" id="stitems"  onchange="stationary(this.value)">
							          <option value="">Please Select</option>
							          <?php echo $statedrop; ?>
							          </select>
							           </div>
							</div>
							        
							          <div class="control-group " >
							               <div id="description" style="display:none">
							               <label class="control-label">Description : <span class="text-error" >*</span></label>
							    <div class="controls">
							          <input type="text" name="description" id="description"  placeholder="Description "> </div>
							</div></div>
							
							 <div class="control-group " >
							               <div id="from" style="display:none">
							               <label class="control-label">From : <span class="text-error" >*</span></label>
							    <div class="controls">
							          <input type="text" name="fromdocket" id="fromdocket"  placeholder="From "> </div>
							</div></div>
							
							 <div class="control-group " >
							               <div id="to" style="display:none">
							               <label class="control-label">To : <span class="text-error" >*</span></label>
							    <div class="controls">
							          <input type="text" name="todocket" id="todocket"  placeholder="To "> </div>
							</div></div>
							
							
							   
							<div class="control-group ">
							    <label class="control-label">Issued To : <span class="text-error" >*</span></label>
							    <div class="controls">
							      <input type="text" name="emname" placeholder=" Name">
							     <!-- <span class="help-inline"><i class="icon-ok"></i> Avaliable input!</span>-->
							    </div>
							</div><!--end control-group-->
						
							<div class="control-group">
							    <label class="control-label" for="remark">Remark : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="remark" placeholder="Remark ">
							    </div>
							</div><!--end control-group-->
						
							   
							    
							   				    <div class="control-group">
							    <label class="control-label" for="Packupdate"> Date  :</label>
							    <div class="controls">
							      
										 <div class="controls input-append date form_date" data-date="" data-date-format="dd MM yyyy" data-link-field="dtp_input2" data-link-format="yyyy-mm-dd">
										
											<input type="text" name="Packupdate" id="Packupdate"  value=""  readonly>
											<span class="add-on"><i class="icon-remove"></i></span>
											<span class="add-on"><i class="icon-th"></i></span>
										</div>
										     <input type="hidden" id="dtp_input2" value="" />
		
										    
							    </div>
							    </div>
			</div>	
							
							
						<!--end control-group   <i class="icon-remove"></i> Invalid input!</span>-->
						<!--end control-group-->
						
						    
						<!--end control-group-->

<!--end control-group-->    

						



					
							<div class="control-group">
							    <div class="controls">
							
									<input name="Submit" class="btn btn-primary" type="submit" value="Register" onClick="return validateForm()">
									<button type="reset" class="btn ">Clear</button>
							    </div>
							</div><!--end control-group-->
							
																				
						</form><!--end form-->

					</div><!--end register-->
				
				</div><!--end span-->
	
			</div><!--end row-->

        </div><!--end conatiner-->


<script>
function stationary(stitems){
	
	if(stitems=="10")
	{  
	document.getElementById("description").style.display="block";
		
	}
	else{
	document.getElementById("description").style.display="none";
	}
	
		if(stitems=="1")
	{  
	document.getElementById("from").style.display="block";
	document.getElementById("to").style.display="block";
		
	}
	else{
	document.getElementById("from").style.display="none";
	document.getElementById("to").style.display="none";
	}
}
</script>  
<script>
function validate()
{
 var name=$("#Name").val();
 if(name=='')
 {
 	alert("Please Select Name Or User  Id Not set");
 	return false;
 }
 var address=$("#Address").val();
 if(address=='')
 {
 	alert("Please Enter Shipping address");
 	$("#Address").focus();
 	return false;
 }	
}
</script> 
		
<script  type="text/javascript">
function validateForm()
{
var x=document.forms["frmShipment"]["rcname"].value;
if (x==null || x=="")
  {
  alert("Receiver Name must be filled out");
  return false;
  }
  
  var phone=document.forms["frmShipment"]["rcphone"].value;
if (phone==null || phone=="")
  {
   alert("Mobile No must be 10 digit ");
  return false;
  
  }
  
 var custadd=document.forms["frmShipment"]["rcemail"].value;
if (custadd==null || custadd=="")
  {
  alert("Email must be filled out");
  return false;
  }
  var add=document.forms["frmShipment"]["rcaddress"].value;
if (add==null || add=="")
  {
  alert("Address must be filled out");
  return false;
  }
  //
  var x=document.forms["frmShipment"]["rczip"].value;
if (x==null || x=="")
  {
  alert("Zip Code must be filled out");
  return false;
  }
  
  var states1=document.forms["frmShipment"]["states"].selectedIndex;
if (states1==null || states1=="")
  {
   alert("State must be select");
  return false;
  
  }
  
 var city1=document.forms["frmShipment"]["city"].selectedIndex;
if (city1==null || city1=="")
  {
  alert("City must be select");
  return false;
  }
/*  var custin1=document.forms["frmShipment"]["cusvatin"].value;
if (custin1==null || custin1=="")
  {
  alert("VAT Tin must be filled out");
  return false;
  }

   var csttin1=document.forms["frmShipment"]["csttin"].value;
if (csttin1==null || csttin1=="")
  {
  alert("CST Tin must be filled out");
  return false;
  }
  
 var custstax1=document.forms["frmShipment"]["custstax"].value;
if (custstax1==null || custstax1=="")
  {
  alert("Service Tax must be filled out");
  return false;
  }
  var custpan1=document.forms["frmShipment"]["custpan"].value;
if (custpan1==null || custpan1=="")
  {
  alert("PAN No. must be filled out");
  return false;
  }
*/
}
</script>

<script  type="text/javascript">
function validateForm1()
{ 
  var x1=document.forms["frmShipment"]["rcphone"].value;
if (!x1>10 || x1=10)
  {
  alert("Mobile No must be 10 digit ");
  return false;
  }
  
 var x2=document.forms["frmShipment"]["rcemail"].value;
 var atposition=x2.indexOf("@");  
var dotposition=x2.lastIndexOf(".");  
if (atposition<1 || dotposition<atposition+2 || dotposition+2>=x.length){  
  alert("Please enter a valid e-mail address \n atpostion:"+atposition+"\n dotposition:"+dotposition);  
  return false;  
  }
  
 var x3=document.forms["frmShipment"]["rcaddress"].value;
if (x3==null || x3=="")
  {
  alert("Address must be filled out");
  return false;
  }
  
 var x4=document.forms["frmShipment"]["rczip"].value;
if (x4==6 || !x4>6)
  {
  alert("Zip code must be 6 Digit only ");
  return false;
  }
  
 var x5=document.forms["frmShipment"]["states"].value;
if (x5==null || x5=="")
  {
  alert("Please Select States");
  return false;
  }
  
   var x6=document.forms["frmShipment"]["city"].value;
if (x6==null || x6=="")
  {
  alert("Please Select City");
  return false;
  }
  
 var x7=document.forms["frmShipment"]["custin"].value;
if (isNaN(x7)){  
  document.getElementById("vati").innerHTML="Enter Numeric value only";  
  return false;  
}else{  
  return true;  
  }  
  
  var x8=document.forms["frmShipment"]["rcgst"].value;
if (isNaN(x8)){  
  document.getElementById("st").innerHTML="Enter Numeric value only";  
  return false;  
}else{  
  return true;  
  }  
}
</script>
    <script src="https://code.jquery.com/jquery-1.9.1.min.js"></script>
<script>
$(document).ready(function(){ 
         $("#add").click(function(){
        
  var rowCount=$('#tb tr').length;

$("#tb").append('<tr><td>'+rowCount+'</td><td><input name="sources'+rowCount+'" id="sources'+rowCount+'" class="input-small"></td><td><input name="destin'+rowCount+'" id="destin'+rowCount+'" class="input-small"></td><td><select name="mode'+rowCount+'" id="mode'+rowCount+'" class="input-small" ><option name="mode'+rowCount+'" id="mode'+rowCount+'" value="" selected>Select</option><option name="mode'+rowCount+'" id="mode'+rowCount+'" value="Air" >Air</option><option name="mode'+rowCount+'" id="mode'+rowCount+'" value="Road" >Road</option></select> </td><td><input name="fuelc'+rowCount+'" id="fuelc'+rowCount+'" class="input-small"></td><td><select name="ftl'+rowCount+'" id="ftl'+rowCount+'" class="input-small"><option name="ftl'+rowCount+'" id="ftl'+rowCount+'" class="input-small"  value="" selected>Select</option><option name="ftl'+rowCount+'" id="ftl'+rowCount+'" value="ftl" class="input-small">FTL</option><option name="ftl'+rowCount+'" id="ftl'+rowCount+'" value="per/kg" class="input-small" >Per/Kg</option></select> </td><td><input name="rate'+rowCount+'" id="rate'+rowCount+'" class="input-small"></td></tr>');



     $("#ct").val(rowCount);
    });
});
</script>
<script> 
  function del1()
{
rowCount=0;
 rowCount=$('#tb tr').length;
 if((rowCount-1)!=1)
 {
  	$('#hid_count1').val(rowCount-2);
 	if(rowCount!=2)
 	{
	var table = document.getElementById("tb");
 	table.deleteRow(rowCount -1);
  
	}
}
}
</script>
           
<script>
function cityfun() { 
 // alert("hhi");
   $('#city').find('option').remove().end().append('<option value="">----Select City----</option>').val('');
    $.ajax({                                      
      url: 'ajax_getCity.php?type='+$('#states').val(),                  //the script to call to get data          
      data: "",                        //you can insert url argumnets here to pass to api.php
                                       //for example "id=5&parent=6"
      dataType: 'json',                //data format      
      success: function(data)          //on recieve of reply
      {
      $.each(data, function(index, data) {
        $('#city').append( $('<option></option>').val(data.id).html(data.name) );
       });
       }
       });
       
    }
	
</script>
<!--<script  type="text/javascript">
function myFunction(q)
{

$.ajax({

  type: "POST",
  url: "rcode.php",
  data: {item:q},
  success:function(data){
 //alert(data);
  document.getElementById("hubunames").innerHTML=data;
  if(data=="Receiver Code Already Exist!!")
  {
  document.getElementById("bute").style.visibility = 'hidden';
  document.getElementById("hubunamea").style.visibility = 'hidden';
  }else
  {
	document.getElementById("hubunamea").style.visibility = 'visible';
	document.getElementById("hubunamea").innerHTML="Receiver Code Available!!";
	document.getElementById("bute").style.visibility = 'visible';  
  }
}
  });
}
</script> -->
<link href="./bootstrap/css/bootstrap-datetimepicker.min.css" rel="stylesheet" media="screen">
	<script type="text/javascript" src="./jquery/jquery-1.8.3.min.js" charset="UTF-8"></script>
<script type="text/javascript" src="./bootstrap/js/bootstrap.min.js"></script>
<script type="text/javascript" src="./jquery/bootstrap-datetimepicker.js" charset="UTF-8"></script>
<!--<script type="text/javascript" src="./jquery/locales/bootstrap-datetimepicker.pl.js" charset="UTF-8"></script>-->
<script type="text/javascript">
	$('.form_date').datetimepicker({
        language:  'pl',
        weekStart: 1,
        todayBtn:  1,
		autoclose: 1,
		todayHighlight: 1,
		startView: 2,
		minView: 2,
		forceParse: 0
    });
	$('.form_time').datetimepicker({
        language:  'pl',
        weekStart: 1,
        todayBtn:  1,
		autoclose: 1,
		todayHighlight: 1,
		startView: 1,
		minView: 0,
		maxView: 1,
		forceParse: 0
    });
</script>
<link rel="stylesheet" type="text/css" media="all" href="jsDatePick_ltr.min.css" />
<script type="text/javascript" src="jsDatePick.min.1.3.js"></script>
<script type="text/javascript">
	window.onload = function(){
	
		new JsDatePick({
			useMode:2,
			target:"db",
			dateFormat:"%d-%m-%Y"

		});
		
		
	};
</script>
<style>
table {
		overflow:hidden;
		border:1px solid #d3d3d3;
		background:#fefefe;
		width:100%;
		margin:5% auto 0;
		-moz-border-radius:5px; /* FF1+ */
		-webkit-border-radius:5px; /* Saf3-4 */
		border-radius:5px;
		-moz-box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
		-webkit-box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
	}
	
	th, td {padding:18px 28px 18px; text-align:center; }
	
	th {padding-top:22px; text-shadow: 1px 1px 1px #fff; background:#e8eaeb;}
	
	td {border-top:1px solid #e0e0e0; border-right:1px solid #e0e0e0;}
	
	tr.odd-row td {background:#f6f6f6;}
	
	td.first, th.first {text-align:left}
	
	td.last {border-right:none;}
	
	/*
	Background gradients are completely unnecessary but a neat effect.
	*/
	
	td {
		background: -moz-linear-gradient(100% 25% 90deg, #fefefe, #f9f9f9);
		background: -webkit-gradient(linear, 0% 0%, 0% 25%, from(#f9f9f9), to(#fefefe));
	}
	
	tr.odd-row td {
		background: -moz-linear-gradient(100% 25% 90deg, #f6f6f6, #f1f1f1);
		background: -webkit-gradient(linear, 0% 0%, 0% 25%, from(#f1f1f1), to(#f6f6f6));
	}
	
	th {
		background: -moz-linear-gradient(100% 20% 90deg, #e8eaeb, #ededed);
		background: -webkit-gradient(linear, 0% 0%, 0% 20%, from(#ededed), to(#e8eaeb));
	}
	
	/*
	I know this is annoying, but we need additional styling so webkit will recognize rounded corners on background elements.
	Nice write up of this issue: http://www.onenaught.com/posts/266/css-inner-elements-breaking-border-radius
	
	And, since we've applied the background colors to td/th element because of IE, Gecko browsers also need it.
	*/
	
	tr:first-child th.first {
		-moz-border-radius-topleft:5px;
		-webkit-border-top-left-radius:5px; /* Saf3-4 */
	}
	
	tr:first-child th.last {
		-moz-border-radius-topright:5px;
		-webkit-border-top-right-radius:5px; /* Saf3-4 */
	}
	
	tr:last-child td.first {
		-moz-border-radius-bottomleft:5px;
		-webkit-border-bottom-left-radius:5px; /* Saf3-4 */
	}
	
	tr:last-child td.last {
		-moz-border-radius-bottomright:5px;
		-webkit-border-bottom-right-radius:5px; /* Saf3-4 */
	}

</style>

<script>
function insur(insu){
	if(insu=="ys")
	{  
	document.getElementById("insurance").style.display="block";
	}
	else{
	document.getElementById("insurance").style.display="none";
	}
}
</script> 
<?php
											
								$msg = $_GET['msg'] ?? '';
								if($msg=="yes1")
								{
						echo "<script> alert('Stationary Issued Successfully');</script>";
								}
								else if($msg=="no1"){
						echo "<script> alert('Stationary Not Issued Successfully');</script>";
								}
								?>  
<?php
include("footer.php");
?>