<?php
session_start();
require_once('database.php');
require_once('library.php');
require 'connection.php';
isUser();

$a=$_SESSION['username'];
 $sql="select * from login where username='$a'";
$result = mysqli_query($con, $sql);
$row1 = mysqli_fetch_array($result, MYSQLI_BOTH);
 $userid = ($row1 && isset($row1['rid'])) ? $row1['rid'] : 0;

// Initialize dropdown variables
$company = $_POST['company'] ?? '';
$loc = $_POST['loc'] ?? '';

$sql1="select * from `tbl_offices` ORDER BY off_name ASC";
 $result2=mysqli_query($con,$sql1);
while($row2=mysqli_fetch_array($result2))
{
	if($company==$row2['id'])
	{
	$loc=$loc."<option value='".$row2['id']."' selected>".$row2['off_name']."</option>";
	}
	else{
	$loc=$loc."<option value='".$row2['id']."' >".$row2['off_name']."</option>";
	}
}

?>

<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>

	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
	
</head>

<?php
include("header.php");
?>

		<div class="container">

			<div class="row">

				<div class="span2">
				
				</div><!--end span8-->
                
                
                <div class="span8">
					<div class="register">

						<div class="titleHeader clearfix">
							<h3>HUB Office Register</h3>
						</div><!--end titleHeader-->

						<form action="processH.php?action=add-hub" method="post"  class="form-horizontal" name="frmShipment">

                           <legend>&nbsp;&nbsp;&nbsp;&nbsp; New HUB Office Information :</legend>

							<div class="control-group">
							    <label class="control-label" for="huboffname">HUB Office Name : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="huboffname" id="huboffname" placeholder="HUB Office Name">
							      <!--<span class="help-inline"><i class="icon-ok"></i> Avaliable input!</span>-->
							    </div>
							</div><!--end control-group-->
							
							<div class="control-group">
							    <label class="control-label" for="hubuname">User Name : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="hubuname" id="hubuname" placeholder="User Name" onKeyUp="myFunction(this.value)">
								  <small class="errorText" id="hubunames" style="color:red"></small><small class="errorText" id="hubunamea" style="color:green"></small>
							      <!--<span class="help-inline"><i class="icon-ok"></i> Avaliable input!</span>-->
							    </div>
							</div><!--end control-group-->
							
							<div class="control-group">
							    <label class="control-label" for="hubupass">Password: <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="password" name="hubupass" id="hubupass"placeholder="**********">
							    </div>
							</div><!--end control-group-->
<input type="hidden" name="cid" value="<?php echo $userid;?>">
							<div class="control-group">
							    <label class="control-label" for="cpass">Confirm Password: <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="password" name="cpass" id="cpass"  placeholder="">
							    </div>
							</div><!--end control-group-->


							<!--<div class="control-group">
							    <label class="control-label" for="Password">Re-Type Password: <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="password" name="Password" placeholder="**********">
							    </div>
							</div><!--end control-group-->
							
							<div class="control-group">
							    <label class="control-label" for="hubno">Contact No : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="hubno" id="hubno" placeholder="phone no">
							    </div>
							</div><!--end control-group-->
								<div class="control-group">
							    <label class="control-label" for="hubno">Contact Person  : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="pname" id="pname" placeholder="Contact Name">
							    </div>
							</div><!--end control-group-->
								<div class="control-group">
							    <label class="control-label" for="hubno">Mobile No.  : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="mobilno" id="mobilno" placeholder="phone no">
							    </div>
							</div><!--end control-group-->
							<div class="control-group">
							    <label class="control-label" for="hubemail">E-Mail : <span class="text-error"></span></label>
							    <div class="controls">
							      <input type="email" name="hubemail" id="hubemail" placeholder="<EMAIL>">
							    </div>
							</div><!--end control-group-->
							<div class="control-group ">
							    <label class="control-label" for="hubaddress">HUB Address : <span class="text-error">*</span></label>
							    <div class="controls">
							      <textarea type="text" name="hubaddress" id="hubaddress"placeholder="Office Address" ></textarea>
							      <!--<span class="help-inline">-->
							    </div>
							</div><!--end control-group   <i class="icon-remove"></i> Invalid input!</span>-->
														
							<div class="control-group">
  <label class="control-label" for="curntloca">
    Add Branch Offices to Hub: <span class="text-error">*</span>
  </label>
  <div class="controls">
    <select name="curntloca[]" multiple="multiple" id="curntloca">
      <option value="">-- Please select --</option>
      <?php echo $loc; ?>
    </select>
  </div>
</div>


    						<div class="control-group">
							    <div class="controls"><br>
							        <button type="submit" class="btn btn-primary" name="submit" onClick="return validateForm()" id="bute">Register</button>&nbsp;&nbsp;
									<button type="reset" class="btn">Clear</button>
							    </div>
							</div><!--end control-group-->
							
							

						</form><!--end form-->
						
						

					</div><!--end register-->
				
				</div><!--end span-->


				
			</div><!--end row-->



			<!--end row-->


			<!--end row-->


		</div><!--end conatiner-->
<script  type="text/javascript">
function myFunction(q)
{

$.ajax({

  type: "POST",
  url: "uname.php",
  data: {item:q},
  success:function(data){
 //alert(data);
  document.getElementById("hubunames").innerHTML=data;
  if(data=="Username Already Exist!!")
  {
  document.getElementById("bute").style.visibility = 'hidden';
  document.getElementById("hubunamea").style.visibility = 'hidden';
  }else
  {
	document.getElementById("hubunamea").style.visibility = 'visible';
	document.getElementById("hubunamea").innerHTML="Username Available!!";
	document.getElementById("bute").style.visibility = 'visible';  
  }
}
  });
}
</script>

<script  type="text/javascript">
function validateForm()
{
var huboffname1=document.forms["frmShipment"]["huboffname"].value;
if (huboffname1==null || huboffname1=="")
  {
  alert("Hub Office Name must be filled out");
  return false;
  }
  
  var hubuname1=document.forms["frmShipment"]["hubuname"].value;
if (hubuname1==null || hubuname1=="")
  {
  alert("UserName must be filled out");
  return false;
  }
  
  var hubupass1=document.forms["frmShipment"]["hubupass"].value;
if (hubupass1==null || hubupass1=="")
  {
   alert("Password must be filled out");
  return false;
  
  }
  var cpass1=document.forms["frmShipment"]["cpass"].value;
if (cpass1==null || cpass1=="")
  {
  alert("Confiirm Password must be filled out");
  return false;
  }
  if(hubupass1!=cpass1)
  {
   alert("Password and Confirm Password Not Matched");
  return false;
  }

  var hubno1=document.forms["frmShipment"]["hubno"].value;
if (hubno1==null || hubno1=="")
  {
  alert("Mobile NO must be filled out");
  return false;
  }
 var hubemail1=document.forms["frmShipment"]["hubemail"].value;
if (hubemail1==null || hubemail1=="")
  {
  alert("Email Address must be filled out");
  return false;
  }
  var hubaddress1=document.forms["frmShipment"]["hubaddress"].value;
  if (hubaddress1==null || hubaddress1=="")
  {
  alert("Hub Address must be filled out");
  return false;
  }
  var curntloca1=document.forms["frmShipment"]["curntloca"].value;
  if (curntloca1==null || curntloca1=="")
  {
  alert("Add Office To HUb must be filled out");
  return false;
  }
}
</script>

<?php
include("footer.php");
?>