<?php
error_reporting(~E_ALL);
session_start();
require 'connection.php';
require_once('database.php');


 date_default_timezone_set('Asia/Kolkata');
$todate = date('d/m/Y h:i:s', time());


$cono=$_GET['consid'];   
	  


if($cono!="")
{         
  $sql="SELECT * FROM tbl_courier WHERE `tbl_courier`.`cons_no` = '$cono'";
}
else 
{
	$tr5=$tr5.'<tr><td colspan="8" align="center"><font color="red" size="4"> " ..............No Record Found..........."</font></td></tr>';

}
$result1 = mysqli_query($con, $sql);
$row1= mysqli_fetch_array($result1, MYSQLI_BOTH);
$Qnty = $row1['qty']; $Shiptype = $row1['type'];
$ConsignmentNo = $row1['cons_no']; $Shippername = $row1['ship_name']; $Shipperphone = $row1['phone'];$Shipperaddress = $row1['shipadd'];$Shipperemail = $row1['smail'];
$custzip = $row1['custzip']; $custgst = $row1['custgst']; $custstax = $row1['custstax']; $custpan = $row1['custpan'];$bukdate = $row1['book_date'];
$Receivername = $row1['rev_name']; $Receiverphone = $row1['r_phone']; $Receiveremail = $row1['rmail']; $rcgst=$row1['rcgst']; 
$rcstin = $row1['rcsttin'];$Receiveraddress = $row1['recadd']; $rcity = $row1['r_city']; $rstates = $row1['r_states']; $rzip =$row1['r_zip']; 
$shides = $row1['shidesc']; $partno =$row1['partno']; $Weight = $row1['weight'];$chweight = $row1['chweight'];$Invoiceno = $row1['invice_no']; 
$Bookingmod = $row1['book_mode'];$Totalfreight = $row1['freight']; $invalue = $row1['invi_value']; $docharg = $row1['dock_charg'];
$codod = $row1['dod_cod']; $oda = $row1['oda_mis']; $stax = $row1['st'];$Mode = $row1['mode']; $Packupdate = $row1['book1_date'];$ewaybill=$row1['e_waybill'];  
$status = $row1['status'];  $volw= $row1['volumw']; $to= $row1['desti']; $clerkn= $row1['clerkname'];$clerkpho= $row1['clerkcon'];$dph=$row1['aftcharge'];
$frm=$row1['from'];$assured_dly_date=$row1['assured_dly_date'];$handlingcharge=$row1['handlingcharge'];$Deliverycharg=$row1['delivery_charge'];
$rov=$row1['rovcharge'];$Noogpakge=$row1['noofpackage'];$Shipperadd = $row1['s_add'];$Receiveradd=$row1['r_add'];
$VehNo = $row1['vehicle'];

$stotal=  $Totalfreight + $docharg +$codod +$oda +$handlingcharge + $dph + $ess + $rov + $Deliverycharg + $fov + $othercharge;

$gst=($stotal*18)/100;

$gtotal=$stotal + $gst;
 
$sqlquery="select * from tbl_city_code where Id='$Shipperadd' ";
$result2=mysqli_query($con,$sqlquery);
while($row2=mysqli_fetch_array($result2)){
$shipcity=$row2['city_name'];
}

 $sqlquery1="select * from tbl_city_code where Id='$Receiveradd' ";
$result=mysqli_query($con,$sqlquery1);
while($row=mysqli_fetch_array($result)){
 $revcity=$row['city_name'];
}

//set it to writable location, a place for temp generated PNG files
    $PNG_TEMP_DIR = dirname(__FILE__).DIRECTORY_SEPARATOR.'temp'.DIRECTORY_SEPARATOR;
    
    //html PNG location prefix
    $PNG_WEB_DIR = 'temp/';
    include "phpqrcode-master/qrlib.php";    
    
    //ofcourse we need rights to create temp dir
    if (!file_exists($PNG_TEMP_DIR))
        mkdir($PNG_TEMP_DIR);
    
    
    $filename = $PNG_TEMP_DIR.'test.png';
    
    //processing form input
    //remember to sanitize user input in real-life solution !!!
    $errorCorrectionLevel = 'L';
    
    $matrixPointSize = 3;
  
    if (isset($_REQUEST['consgin'])) { 

    $filename = $PNG_TEMP_DIR.'test'.md5($_REQUEST['consgin'].'|'.$errorCorrectionLevel.'|'.$matrixPointSize).'.png';
       QRcode::png($_REQUEST['consgin'], $filename, $errorCorrectionLevel, $matrixPointSize, 3);    
        
    } else {    
    
      
     echo  
        QRcode::png('PHP QR Code :)', $filename, $errorCorrectionLevel, $matrixPointSize, 3);    
        
    }    

mysqli_close($con);
?> 
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>
	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
<script language="JavaScript">
</script>

	<style type = "text/css">
  
     
      @media print {
         #CopyTitle {font-weight:900;
             color:#000;
             text-align: right;
             font-size:16px;
         }
      }
    
</style>
</head>

   
 <div class="container">
   <div class="row">	
				    <div class="span12">
                     <div class="flexslider">
                         
                     <label id="CopyTitle" style="vertical-align:top; float:right;"><h4></h4></label>    
                         
  <tr><td>
  <table style="width:100%; border-collapse: collapse;" >
  <tr>
  <td width="20%" style=text-align:center;><img src="img/logo.png" width="60%" /></td>
  <td width="50%"><h5><b><font size="4">Vivanta Logistics Private. Limited</font></b></h5>
  <b>Registered Address</b>: Bungalow No.-7,samata Hsg.Soc,behind MSEB Colony,Bhosale Nagar, Pune-411007<br>
   Customer Care No.-18003131944<br>
    For Tracking :-www.vivantalogistics.in
</td>
  <td width="20%">
   <b>Vehicle No. <?php echo $VehNo; ?></b><br> 
      PAN No. ********** <br>
  GST No.27**********1Z5<br>
  
</td>

 
 
  <td width="10%"><b>Services</b><br>
  <input name="Air" type="checkbox" value="Air" <?php if($Mode=="Air"){ echo "checked" ; } ?>> Air<br>
  <input name="Sea" type="checkbox" value="Sea" <?php if($Mode=="Sea"){ echo "checked"; } ?>> Sea  <br>
  <input name="Road" type="checkbox" value="Road" <?php if($Mode=="Road") { echo "checked"; } ?>>Surface<br>
  <input name="Train" type="checkbox" value="Train" <?php if($Mode=="Train"){ echo "checked"; }?>> Train<br> 
  <input name="International" type="checkbox" value="International" <?php if($Mode=="International") { echo "checked"; } ?>> International</td>
  </tr></table>   </td>
  </tr>
  
  <table style="width:100%; border-collapse: collapse;">
  
         <tbody>
 
  <tr>
    
   
    <td>Booking Date:  &nbsp; &nbsp;<?php echo $Packupdate; ?> </td>
     <td>Expected Delivery Date: &nbsp; &nbsp;<?php echo $assured_dly_date; ?></td>
     	<td>Docket No.&nbsp;&nbsp;<b><?php echo $ConsignmentNo; ?></b></td>
  </tr> 
   <tr>
    <td>FROM,&nbsp;&nbsp;<b><?php echo $frm; ?><br><?php echo $shipcity; ?></b></td>
	<td>TO,&nbsp;&nbsp;<b><?php echo $revcity; ?></b></td>
	
    <td rowspan>E Way Bill No.<br> <h5><?php echo $ewaybill; ?></h5></td>
  </tr>
         </tbody>
  </table>
  <table style="width:100%; border-collapse: collapse;">
		<tr>
			<td colspan="2"> 
			<b>Consignor Name.:</b> &nbsp;&nbsp;<?php echo $Shippername; ?><br>
			<b>Address.:</b> &nbsp;&nbsp;<?php echo $Shipperaddress; ?><br>
			<b>Pincode.:</b> &nbsp;&nbsp;<?php echo $custzip; ?><br>
			<b>Email.:</b> &nbsp;&nbsp;<?php echo $Shipperemail; ?><br>	
			<b>Phone/Cell.:</b> &nbsp;&nbsp;<?php echo $Shipperphone; ?><br>	
			<b> GST NO.:</b> &nbsp;&nbsp;<?php echo $custgst; ?><br></td>
			<td  colspan="2"><b>Consignee Name.:</b>&nbsp;&nbsp;<?php echo $Receivername; ?><br>
			<b>Address.:</b>&nbsp;&nbsp;<?php echo $Receiveraddress; ?><br>
			<b>Pincode:</b>&nbsp;&nbsp;<?php echo $rzip; ?><br>
			<b>Email:</b>&nbsp;&nbsp;<?php echo $Receiveremail; ?><br>
			<b>Phone/Cell.:</b>&nbsp;&nbsp;<?php echo $Receiverphone; ?><br>
			<b>GST NO.:</b>&nbsp;&nbsp;<?php echo $rcgst; ?><br></td>
		</tr>
		<tr>
			<td >Invoice No.: &nbsp;&nbsp; <?php echo $Invoiceno; ?></td>
			<td>Invoice Value.: &nbsp;&nbsp;<?php echo $invalue; ?></td>
			<td>No. Of Packages: &nbsp;&nbsp;<?php echo $Noogpakge; ?> </td>
			<td>Total Quntity.: &nbsp;&nbsp;<?php echo $Qnty; ?></td>
		</tr> 
	 
 </table>
  <table style="width:100%; border-collapse: collapse;">
		<tr>
		  <td colspan="2">Part No. &nbsp;&nbsp;<?php echo $partno; ?></td>
		
                  <td colspan="2">Type Of Shipment:&nbsp;&nbsp;<?php echo $Shiptype ; ?></td>
                  
                   
                   
		</tr>  
		<tr>
		    <td colspan="2">Volume:&nbsp;&nbsp;<?php  echo $volw; ?></td>
		  
			<td colspan="2" width="50%"> Payment Mode: <?php echo $Bookingmod; ?></td>
        </tr>
        <tr align="center">
             <td>Docket No</td>
			 <td>WEIGHT</td> <td colspan="2">Bill Details </td>
		</tr>
		
		<tr>
			  <td rowspan="4" align="center" ><?php  echo '<img src="'.$PNG_WEB_DIR.basename($filename).'" />';?> <br>
			 <b><?php  echo $ConsignmentNo; ?></b></td>
			  
			  <td rowspan="4" "25%">ACTUAL <br><br> <?php echo $Weight; ?></td>
			  <td width="25%">Particulars  </td>
			  <td  width="25%">Rate</td>
		</tr>  
		<tr>
			  <td>Freight </td>
			  <td align="left"><?php echo $Totalfreight; ?></td>
		</tr> 
			<tr>
			  <td >DPH</td> </td>
			  <td align="left"><?php echo $dph; ?></td>
		</tr>
		<tr>  
			  <td>ROV</td>
			  <td align="left"><?php echo $rov; ?></td>
		</tr> 
			
	
        <tr>
               <td rowspan="7" style="vertical-align:top;"><h4>Description (said to contain)</h4><br><b>Auto Part</b></td>
              
			  <td rowspan="7">CHARGED <br><?php echo $chweight; ?> </td>
			  <td>Docket Charges </td>
			  <td align="left"><?php echo $docharg; ?></td>
		</tr>  
         <tr>  
			  <td>ODA/ESS</td>
			  <td align="left"><?php echo $oda; ?></td>
		</tr> 
		 <tr>
				<td >Handling Charges</td>
				<td align="left"><?php echo $handlingcharge; ?></td>
		</tr> 
		
		<tr>
				<td >Delivery Charges</td>
				<td align="left"><?php echo $Deliverycharg; ?></td>
		</tr>  
		<tr> 
			 
		</tr>
		<tr>
			  <td >FOD/COD Charges</td>
			  <td align="left"><?php echo $codod;?></td>
		</tr>
			<tr>
			  <td >Sub Total</td>
			  <td align="left"><?php echo $stotal;?></td>
		</tr>
		
		<tr >
			<td rowspan="2" colspan="2" >Total In Words :-<b>
				<?php echo convert_number_to_words($gtotal);?> Only </b></td> <!-- -->
			<td>GST (18%)</td>
			<td align="left"><?php echo $gst; ?></td>
		</tr> 
		<tr >
			<td >GRAND TOTAL</td>
			<td align="left"><?php echo $gtotal; ?></td>
		</tr> 
  </table>
<table  style="width:100%; border-collapse: collapse;"><tr><td><p>INTERNAL AUDIT OBSERVATION <br><br>
  Checked by<br><br>Date:  &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; Time: <br></p></td>
<td align="center"><p>I hereby Agree to the Terms & Conditions Printed overleaf <br><br>  Consignors Signature</p> <?php echo $Shippername; ?></td>
<td align="center"><p><br><br>Signature Of Booking Clerk <br> <?php echo $clerkn;?><br><?php echo $frm;?><br><?php echo $uadd;  ?></p></td></tr>
</table>
 </div><!--end flexslider <ul class="slides"></ul>-->
                    </div></div>

<p align="left">  <input type="button" id="printpagebutton"  value="Print"><a id="backbutton" href="billConsig.php"><input type="button" id="backbutton" onClick="closeWin();" value="Close"> </a></p>

</div>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
<script type="text/javascript">
var title=["ACCOUNT COPY"];

      $("#printpagebutton").click(function(){
        

      
      
      
        
           $("#CopyTitle").text(title);
            $("#printpagebutton").hide();
            $("#backbutton").hide();
       
            window.print();
           
       
         
        
          
          
          
          
      });
    
   /* function printpage() {
        //Get the print button and put it into a variable
        var printButton = document.getElementById("printpagebutton");
        var back=document.getElementById("backbutton");
        //Set the print button visibility to 'hidden' 
        printButton.style.visibility = 'hidden';
        back.style.visibility = 'hidden';
        //Print the page content
        window.print();
        //Set the print button to 'visible' again 
        //[Delete this line if you want it to stay hidden after printing]
        printButton.style.visibility = 'visible';
         back.style.visibility = 'visible';
    }*/
</script>
<script type="text/javascript">

function closeWin() {
    back.close();
}
    window.onbeforeunload = function() {
        
    }
</script>

<?php
function convert_number_to_words($number) {

    $hyphen      = '-';
    $conjunction = ' and ';
    $separator   = ', ';
    $negative    = 'negative ';
    $decimal     = ' point ';
    $dictionary  = array(
        0                   => 'Zero',
        1                   => 'One',
        2                   => 'Two',
        3                   => 'Three',
        4                   => 'Four',
        5                   => 'Five',
        6                   => 'Six',
        7                   => 'Seven',
        8                   => 'Eight',
        9                   => 'Nine',
        10                  => 'Ten',
        11                  => 'Eleven',
        12                  => 'Twelve',
        13                  => 'Thirteen',
        14                  => 'Fourteen',
        15                  => 'Fifteen',
        16                  => 'Sixteen',
        17                  => 'Seventeen',
        18                  => 'Eighteen',
        19                  => 'Nineteen',
        20                  => 'Twenty',
        30                  => 'Thirty',
        40                  => 'Fourth',
        50                  => 'Fifty',
        60                  => 'Sixty',
        70                  => 'Seventy',
        80                  => 'Eighty',
        90                  => 'Ninety',
        100                 => 'Hundred',
        1000                => 'Thousand',
        1000000             => 'Million',
        1000000000          => 'Billion',
        1000000000000       => 'Trillion',
        1000000000000000    => 'Quadrillion',
        1000000000000000000 => 'Quintillion'
    );

    if (!is_numeric($number)) {
        return false;
    }

    if (($number >= 0 && (int) $number < 0) || (int) $number < 0 - PHP_INT_MAX) {
        // overflow
        trigger_error(
            'convert_number_to_words only accepts numbers between -' . PHP_INT_MAX . ' and ' . PHP_INT_MAX,
            E_USER_WARNING
        );
        return false;
    }

    if ($number < 0) {
        return $negative . convert_number_to_words(abs($number));
    }

    $string = $fraction = null;

    if (strpos($number, '.') !== false) {
        list($number, $fraction) = explode('.', $number);
    }

    switch (true) {
        case $number < 21:
            $string = $dictionary[$number];
            break;
        case $number < 100:
            $tens   = ((int) ($number / 10)) * 10;
            $units  = $number % 10;
            $string = $dictionary[$tens];
            if ($units) {
                $string .= $hyphen . $dictionary[$units];
            }
            break;
        case $number < 1000:
            $hundreds  = $number / 100;
            $remainder = $number % 100;
            $string = $dictionary[$hundreds] . ' ' . $dictionary[100];
            if ($remainder) {
                $string .= $conjunction . convert_number_to_words($remainder);
            }
            break;
        default:
            $baseUnit = pow(1000, floor(log($number, 1000)));
            $numBaseUnits = (int) ($number / $baseUnit);
            $remainder = $number % $baseUnit;
            $string = convert_number_to_words($numBaseUnits) . ' ' . $dictionary[$baseUnit];
            if ($remainder) {
                $string .= $remainder < 100 ? $conjunction : $separator;
                $string .= convert_number_to_words($remainder);
            }
            break;
    }

    if (null !== $fraction && is_numeric($fraction)) {
        $string .= $decimal;
        $words = array();
        foreach (str_split((string) $fraction) as $number) {
            $words[] = $dictionary[$number];
        }
        $string .= implode(' ', $words);
    }

    return $string;
}

?>
</html>