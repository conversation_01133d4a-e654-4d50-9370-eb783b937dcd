<?php 

session_start();
require_once('library.php');
$rand = get_rand_id(8);
//echo $rand;
$userid=$_SESSION['desgn'];
$a=$_SESSION['username'];
/*$sql="select * from login where type='$a'";
$result = mysqli_query($con, $sql);
$row1 = mysqli_fetch_array($result, MYSQLI_BOTH);


 $userid=$row1['rid'];*/

$conn = mysqli_connect("localhost", "root", "", "rqhmqxnf_vivanta-25");


if (!$conn) {
    die("Connection failed: " . mysqli_connect_error());
}

$query = "SELECT MAX(custid) AS custid FROM custreg";
if ($result = mysqli_query($conn, $query)) {
    while ($row = mysqli_fetch_assoc($result)) {
        $count = $row['custid'];
        $count = $count + 1;
        $code_no = str_pad($count, 7, "9", STR_PAD_LEFT);
    }
    mysqli_free_result($result); // Free the result set
} else {
    echo "Error: " . mysqli_error($conn);
}

$statusid = mysqli_real_escape_string($conn, $_GET['id']);
$sql = "SELECT * FROM `status` WHERE statusid='$statusid'";
$result = mysqli_query($conn, $sql);
if ($result) {
    while($data = mysqli_fetch_assoc($result)) {
        $statusname = $data['statusname'];
    }
    mysqli_free_result($result);
} else {
    echo "Error: " . mysqli_error($conn);
}
	
?>
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>

	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
	
</head>
<?php
include("header.php");
?>



        <div class="container">

			<div class="row">

				<div class="span2">
				
				</div><!--end span8-->
                             
                <div class="span12">
					<div class="register">
						<div class="titleHeader clearfix">
							<h3>Status Registration</h3>
						</div><!--end titleHeader-->
						<form action="process.php?action=edit-status" method="post" class="form-horizontal" onSubmit="return validate()" name="frmShipment">
						   	<legend>&nbsp;&nbsp;&nbsp;&nbsp;Updata Status :</legend>

							<div class="control-group ">
							    <label class="control-label">Status : <span class="text-error" >*</span></label>
							    <div class="controls">
							      <input type="text" name="status" id="status"  placeholder="Status" value="<?php echo $statusname;?>">
							        <input type="hidden" name="statusid" id="statusid"  placeholder="Status" value="<?php echo $_GET['id'];?>">
							     <!-- <span class="help-inline"><i class="icon-ok"></i> Avaliable input!</span>-->
							    </div>
							</div><!--end control-group-->
								
							
							<div class="control-group">
							    <div class="controls">
							
									<input name="Submit" class="btn btn-primary" type="submit" value="Update" onClick="return validateForm()">
									<button type="reset" class="btn ">Clear</button>
							    </div>
							</div><!--end control-group-->
							
																				
						</form>
					
						
								
					</div><!--end register-->
				
				</div><!--end span-->
	
			</div><!--end row-->

        </div><!--end conatiner-->



<script>
function validate()
{
 var name=$("#Name").val();
 if(name=='')
 {
 	alert("Please Select Name Or User  Id Not set");
 	return false;
 }
 var address=$("#Address").val();
 if(address=='')
 {
 	alert("Please Enter Shipping address");
 	$("#Address").focus();
 	return false;
 }	
}
</script> 
		
<script  type="text/javascript">
function validateForm()
{
var x=document.forms["frmShipment"]["custname"].value;
if (x==null || x=="")
  {
  alert("Customer Name must be filled out");
  return false;
  }
  
  var phone=document.forms["frmShipment"]["custphone"].value;
if (phone==null || phone=="")
  {
   alert("Mobile No must be 10 digit ");
  return false;
  
  }
  
 var custadd=document.forms["frmShipment"]["custemail"].value;
if (custadd==null || custadd=="")
  {
  alert("Email must be filled out");
  return false;
  }
  var add=document.forms["frmShipment"]["custaddress"].value;
if (add==null || add=="")
  {
  alert("Address must be filled out");
  return false;
  }
  //
  var x=document.forms["frmShipment"]["custzip"].value;
if (x==null || x=="")
  {
  alert("Zip Code must be filled out");
  return false;
  }
  
  var states1=document.forms["frmShipment"]["states"].selectedIndex;
if (states1==null || states1=="")
  {
   alert("State must be select");
  return false;
  
  }
  
 var city1=document.forms["frmShipment"]["city"].selectedIndex;
if (city1==null || city1=="")
  {
  alert("City must be select");
  return false;
  }
/*  var custin1=document.forms["frmShipment"]["cusvatin"].value;
if (custin1==null || custin1=="")
  {
  alert("VAT Tin must be filled out");
  return false;
  }

   var csttin1=document.forms["frmShipment"]["csttin"].value;
if (csttin1==null || csttin1=="")
  {
  alert("CST Tin must be filled out");
  return false;
  }
  
 var custstax1=document.forms["frmShipment"]["custstax"].value;
if (custstax1==null || custstax1=="")
  {
  alert("Service Tax must be filled out");
  return false;
  }
  var custpan1=document.forms["frmShipment"]["custpan"].value;
if (custpan1==null || custpan1=="")
  {
  alert("PAN No. must be filled out");
  return false;
  }
*/
}
</script>

<script  type="text/javascript">
function validateForm1()
{ 
  var x1=document.forms["frmShipment"]["custphone"].value;
if (!x1>10 || x1=10)
  {
  alert("Mobile No must be 10 digit ");
  return false;
  }
  
 var x2=document.forms["frmShipment"]["custemail"].value;
 var atposition=x2.indexOf("@");  
var dotposition=x2.lastIndexOf(".");  
if (atposition<1 || dotposition<atposition+2 || dotposition+2>=x.length){  
  alert("Please enter a valid e-mail address \n atpostion:"+atposition+"\n dotposition:"+dotposition);  
  return false;  
  }
  
 var x3=document.forms["frmShipment"]["custaddress"].value;
if (x3==null || x3=="")
  {
  alert("Address must be filled out");
  return false;
  }
  
 var x4=document.forms["frmShipment"]["custzip"].value;
if (x4==6 || !x4>6)
  {
  alert("Zip code must be 6 Digit only ");
  return false;
  }
  
 var x5=document.forms["frmShipment"]["states"].value;
if (x5==null || x5=="")
  {
  alert("Please Select States");
  return false;
  }
  
   var x6=document.forms["frmShipment"]["city"].value;
if (x6==null || x6=="")
  {
  alert("Please Select City");
  return false;
  }
  
 var x7=document.forms["frmShipment"]["custin"].value;
if (isNaN(x7)){  
  document.getElementById("vati").innerHTML="Enter Numeric value only";  
  return false;  
}else{  
  return true;  
  }  
  
  var x8=document.forms["frmShipment"]["custstax"].value;
if (isNaN(x8)){  
  document.getElementById("st").innerHTML="Enter Numeric value only";  
  return false;  
}else{  
  return true;  
  }  
}
</script>
    <script src="https://code.jquery.com/jquery-1.9.1.min.js"></script>
<script>
$(document).ready(function(){ 
         $("#add").click(function(){
        
  var rowCount=$('#tb tr').length;

$("#tb").append('<tr><td>'+rowCount+'</td><td><input name="sources'+rowCount+'" id="sources'+rowCount+'" class="input-small"></td><td><input name="destin'+rowCount+'" id="destin'+rowCount+'" class="input-small"></td><td><select name="mode'+rowCount+'" id="mode'+rowCount+'" class="input-small" ><option name="mode'+rowCount+'" id="mode'+rowCount+'" value="" selected>Select</option><option name="mode'+rowCount+'" id="mode'+rowCount+'" value="Air" >Air</option><option name="mode'+rowCount+'" id="mode'+rowCount+'" value="Road" >Road</option></select> </td><td><input name="fuelc'+rowCount+'" id="fuelc'+rowCount+'" class="input-small"></td><td><input name="docketcharge'+rowCount+'" id="docketcharge'+rowCount+'" class="input-small"></td><td><input name="other'+rowCount+'" id="other'+rowCount+'" class="input-small"></td><td><select name="ftl'+rowCount+'" id="ftl'+rowCount+'" class="input-small"><option name="ftl'+rowCount+'" id="ftl'+rowCount+'" class="input-small"  value="" selected>Select</option><option name="ftl'+rowCount+'" id="ftl'+rowCount+'" value="ftl" class="input-small">FTL</option><option name="ftl'+rowCount+'" id="ftl'+rowCount+'" value="per/kg" class="input-small" >Per/Kg</option></select> </td><td><input name="rate'+rowCount+'" id="rate'+rowCount+'" class="input-small"></td></tr>');



     $("#ct").val(rowCount);
    });
});
</script>
<script> 
  function del1()
{
rowCount=0;
 rowCount=$('#tb tr').length;
 if((rowCount-1)!=1)
 {
  	$('#hid_count1').val(rowCount-2);
 	if(rowCount!=2)
 	{
	var table = document.getElementById("tb");
 	table.deleteRow(rowCount -1);
  
	}
}
}
</script>
           
<script>
function cityfun() { 
 // alert("hhi");
   $('#city').find('option').remove().end().append('<option value="">----Select City----</option>').val('');
    $.ajax({                                      
      url: 'ajax_getCity.php?type='+$('#states').val(),                  //the script to call to get data          
      data: "",                        //you can insert url argumnets here to pass to api.php
                                       //for example "id=5&parent=6"
      dataType: 'json',                //data format      
      success: function(data)          //on recieve of reply
      {
      $.each(data, function(index, data) {
        $('#city').append( $('<option></option>').val(data.id).html(data.name) );
       });
       }
       });
       
    }
	
</script>

<script>
function insur(insu){
	if(insu=="ys")
	{  
	document.getElementById("insurance").style.display="block";
	}
	else{
	document.getElementById("insurance").style.display="none";
	}
}
</script> 

 <?php
							if(isset($_GET['msg'])) {
    $msg = $_GET['msg'];
    if($msg == "yes1") {
        echo "<script>alert('Status Registered Successfully');</script>";
    } else if($msg == "no1") {
        echo "<script>alert('Status Not Registered Successfully');</script>";
    }
}
?>

<?php
include("footer.php");
?>
