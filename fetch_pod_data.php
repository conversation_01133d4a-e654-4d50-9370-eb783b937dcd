<?php
// Clean output buffer to prevent any unwanted output
if (ob_get_level()) {
    ob_end_clean();
}
ob_start();

session_start();

// Set JSON header and disable error display
header('Content-Type: application/json');
ini_set('display_errors', 0);
error_reporting(0);

include("connection.php");

// Simple authentication check instead of using library.php
if (!isset($_SESSION['username']) || empty($_SESSION['username'])) {
    echo json_encode(['status' => 'error', 'message' => 'Please login first']);
    exit;
}

// Function to send JSON response
function sendResponse($status, $message, $data = null, $stats = null) {
    // Clean any output that might have been generated
    if (ob_get_level()) {
        ob_clean();
    }

    $response = array(
        'status' => $status,
        'message' => $message
    );
    if ($data !== null) {
        $response['data'] = $data;
    }
    if ($stats !== null) {
        $response['stats'] = $stats;
    }

    // Ensure clean JSON output
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse('error', 'Only POST method allowed');
}

// Verify CSRF token
if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
    sendResponse('error', 'Invalid CSRF token');
}

// Check action
if (!isset($_POST['action']) || $_POST['action'] !== 'fetch_month_data') {
    sendResponse('error', 'Invalid action');
}

// Get and validate input
$month = isset($_POST['month']) ? trim($_POST['month']) : '';
$dataFilter = isset($_POST['data_filter']) ? trim($_POST['data_filter']) : 'delivered';

if (empty($month)) {
    sendResponse('error', 'Month is required');
}

// Validate data filter
if (!in_array($dataFilter, ['delivered', 'all'])) {
    $dataFilter = 'delivered'; // Default to delivered
}

// Parse month (format: YYYY-MM)
$monthParts = explode('-', $month);
if (count($monthParts) !== 2) {
    sendResponse('error', 'Invalid month format');
}

$year = intval($monthParts[0]);
$monthNum = intval($monthParts[1]);

if ($year < 2020 || $year > date('Y') + 1) {
    sendResponse('error', 'Invalid year');
}

if ($monthNum < 1 || $monthNum > 12) {
    sendResponse('error', 'Invalid month');
}

try {
    // Build WHERE clause based on filter
    $whereClause = "WHERE YEAR(book_date) = ? AND MONTH(book_date) = ?";
    if ($dataFilter === 'delivered') {
        $whereClause .= " AND (status = 'Delivered' OR status = 'delivered' OR status = 'DELIVERED'
                              OR status = '6' OR status = 'Complete' OR status = 'Completed'
                              OR LOWER(status) LIKE '%deliver%' OR LOWER(status) LIKE '%complete%')";
    }

    // Get statistics for the month
    $stats_sql = "SELECT
                    COUNT(*) as total_records,
                    COUNT(CASE WHEN (status = 'Delivered' OR status = 'delivered' OR status = 'DELIVERED'
                                    OR status = '6' OR status = 'Complete' OR status = 'Completed'
                                    OR LOWER(status) LIKE '%deliver%' OR LOWER(status) LIKE '%complete%') THEN 1 END) as delivered_records,
                    SUM(CASE WHEN weight IS NOT NULL AND weight != '' THEN CAST(weight AS DECIMAL(10,2)) ELSE 0 END) as total_weight,
                    DATE_FORMAT(MIN(book_date), '%d-%m-%Y') as earliest_date,
                    DATE_FORMAT(MAX(book_date), '%d-%m-%Y') as latest_date
                  FROM tbl_courier
                  $whereClause";
    
    $stmt = mysqli_prepare($con, $stats_sql);
    if (!$stmt) {
        throw new Exception('Database prepare failed: ' . mysqli_error($con));
    }
    
    mysqli_stmt_bind_param($stmt, "ii", $year, $monthNum);
    mysqli_stmt_execute($stmt);
    $stats_result = mysqli_stmt_get_result($stmt);
    
    if (!$stats_result) {
        throw new Exception('Statistics query failed: ' . mysqli_error($con));
    }
    
    $stats = mysqli_fetch_assoc($stats_result);
    mysqli_stmt_close($stmt);
    
    // Format statistics
    $stats['total_weight'] = number_format($stats['total_weight'], 2);
    $stats['date_range'] = $stats['earliest_date'] . ' to ' . $stats['latest_date'];
    
    // Query to get detailed data for the selected month
    $data_sql = "SELECT
                    c.cons_no as ConsignmentNo,
                    DATE_FORMAT(c.book_date, '%d-%m-%Y') as book_date,
                    c.partno as PartNo,
                    c.noofpackage as noofpackages,
                    c.qty as Qnty,
                    c.weight as Weight,
                    c.status,
                    DATE_FORMAT(c.status_date, '%d-%m-%Y') as delivery_date,
                    c.rev_name as receiver_name,
                    c.ship_name as sender_name,
                    c.s_add as sender_address,
                    c.phone as sender_phone,
                    c.r_add as receiver_address,
                    c.r_phone as receiver_phone,
                    c.remark as delivery_remarks,
                    c.invice_no as invoice_no,
                    c.invi_value as invoice_value,
                    c.book_mode,
                    c.freight,
                    c.gtotamt as grand_total,
                    c.e_waybill,
                    c.vehicle,
                    c.clerkname as staff_name,
                    c.clerkcon as staff_contact
                FROM tbl_courier c
                $whereClause
                ORDER BY c.book_date DESC, c.cons_no DESC
                LIMIT 500"; // Limit to prevent memory issues
    
    $stmt2 = mysqli_prepare($con, $data_sql);
    if (!$stmt2) {
        throw new Exception('Database prepare failed: ' . mysqli_error($con));
    }
    
    mysqli_stmt_bind_param($stmt2, "ii", $year, $monthNum);
    mysqli_stmt_execute($stmt2);
    $data_result = mysqli_stmt_get_result($stmt2);
    
    if (!$data_result) {
        throw new Exception('Data query failed: ' . mysqli_error($con));
    }
    
    $data = array();
    while ($row = mysqli_fetch_assoc($data_result)) {
        $data[] = $row;
    }
    
    mysqli_stmt_close($stmt2);
    
    if (empty($data)) {
        sendResponse('error', 'No data found for the selected month');
    }
    
    // Month names for display
    $monthNames = array(
        1 => 'January', 2 => 'February', 3 => 'March', 4 => 'April',
        5 => 'May', 6 => 'June', 7 => 'July', 8 => 'August',
        9 => 'September', 10 => 'October', 11 => 'November', 12 => 'December'
    );
    
    $monthName = $monthNames[$monthNum] . ' ' . $year;
    
    sendResponse('success', 'Data fetched successfully for ' . $monthName, $data, $stats);
    
} catch (Exception $e) {
    error_log('POD Data Fetch Error: ' . $e->getMessage());
    sendResponse('error', 'An error occurred while fetching data: ' . $e->getMessage());
}
?>
