<?php
session_start();
include("connection.php");
require_once('database.php');
require_once('library.php');
isUser();

header('Content-Type: application/json');

// Validate CSRF token
if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
    echo json_encode(['status' => 'error', 'message' => 'Invalid CSRF token']);
    exit;
}

// Validate inputs
$email = filter_var($_POST['email'], FILTER_VALIDATE_EMAIL);
$month = $_POST['month'] ?? '';
$downloadUrl = $_POST['download_url'] ?? '';

if (!$email || empty($month) || empty($downloadUrl)) {
    echo json_encode(['status' => 'error', 'message' => 'Invalid input data']);
    exit;
}

// Month name mapping
$monthNames = [
    '01' => 'January', '02' => 'February', '03' => 'March', '04' => 'April',
    '05' => 'May', '06' => 'June', '07' => 'July', '08' => 'August',
    '09' => 'September', '10' => 'October', '11' => 'November', '12' => 'December'
];

$monthParts = explode('-', $month);
$monthName = $monthNames[$monthParts[1]] . ' ' . $monthParts[0];

// Email subject and body
$subject = "POD Download Link - $monthName - Vivanta Logistics";
$message = "Dear User,\n\n";
$message .= "Your POD download link for $monthName is ready.\n\n";
$message .= "Download here: $downloadUrl\n\n";
$message .= "This link will be valid for 7 days.\n\n";
$message .= "Thank you,\nVivanta Logistics Team";

// Send email using PHPMailer
require_once 'PHPMailer/src/PHPMailer.php';
require_once 'PHPMailer/src/SMTP.php';
require_once 'PHPMailer/src/Exception.php';

$mail = new PHPMailer\PHPMailer\PHPMailer(true);

try {
    // SMTP configuration
    $mail->isSMTP();
    $mail->Host = 'smtp.gmail.com';
    $mail->SMTPAuth = true;
    $mail->Username = '<EMAIL>';
    $mail->Password = 'your-email-password';
    $mail->SMTPSecure = 'tls';
    $mail->Port = 587;

    // Recipients
    $mail->setFrom('<EMAIL>', 'Vivanta Logistics');
    $mail->addAddress($email);

    // Content
    $mail->isHTML(false);
    $mail->Subject = $subject;
    $mail->Body = $message;

    $mail->send();
    echo json_encode(['status' => 'success', 'message' => 'Email sent successfully']);
} catch (Exception $e) {
    echo json_encode(['status' => 'error', 'message' => 'Mailer Error: ' . $mail->ErrorInfo]);
}
?>