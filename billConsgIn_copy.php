<?php
session_start();
require 'connection.php';
require_once('database.php');
require_once('library.php');
isUser();

 date_default_timezone_set('Asia/Kolkata');
$todate = date('d/m/Y h:i:s', time());


if(isset($_POST['Submit']))
{
 $cono=$_POST['consgin'];   $userid=$_POST['cid']; $fuel=$_POST['fuelcharge'];  $uadd=$_POST['uaddress']; $billno=$_POST['prebilno'];
	
// $date=$_POST['stdate'];      $date2=$_POST['endate'];
	 
 $frm=$_POST['uoffname'];      $fradd=$_POST['uoffadd'];
       
	  
if($date2=='')
{
 
$Cdate2=date('Y-m-d',time());
}
else
{
 $Cdate2=date('Y-m-d',strtotime($date2));
}
 $Cdate=date('Y-m-d',strtotime($date));

if($cono!="")
{         
 $sql="SELECT * FROM tbl_courier WHERE `tbl_courier`.`cons_no` = '$cono' and tbl_courier.book_date between '$Cdate' and '$Cdate2'";
//$sql="SELECT * FROM tbl_courier WHERE `tbl_courier`.`cons_no` ='$cname' and tbl_courier.book_date between '$date' and '$date2'";
}
else 
{
	$tr5=$tr5.'<tr><td colspan="8" align="center"><font color="red" size="4"> " ..............No Record Found..........."</font></td></tr>';

}
//$sql="SELECT * FROM tbl_courier WHERE `tbl_courier`.`cons_no` ='*********'";
$result1 = mysqli_query($con, $sql);
$row1= mysqli_fetch_array($result1, MYSQLI_BOTH);
$Qnty = $row1['qty']; $Shiptype = $row1['type'];
  $ConsignmentNo = $row1['cons_no']; $Shippername = $row1['ship_name']; $Shipperphone = $row1['phone'];$Shipperaddress = $row1['s_add'];$Shipperemail = $row1['smail'];$custin = $row1['vattin']; $cstin = $row1['csttin']; $custstax = $row1['custstax']; $custpan = $row1['custpan'];
  $bukdate = $row1['book_date'];
	$Receivername = $row1['rev_name']; $Receiverphone = $row1['r_phone']; $Receiveremail = $row1['rmail']; $rtin=$row1['rtin']; $rcstin = $row1['rcsttin'];$Receiveraddress = $row1['r_add']; $rcity = $row1['r_city']; $rstates = $row1['r_states']; $rzip =$row1['r_zip']; $shides = $row1['shidesc']; $perm =$row1['permitno']; 
 
	  
$Weight = $row1['weight'];$chweight = $row1['chweight'];$Invoiceno = $row1['invice_no']; 
$Bookingmod = $row1['book_mode'];$Totalfreight = $row1['freight']; $invalue = $row1['invi_value']; $docharg = $row1['dock_charg'];
     $codod = $row1['dod_cod']; $oda = $row1['oda_mis']; $stax = $row1['st'];$Mode = $row1['mode']; $Packupdate = $row1['pick_date'];  $status = $row1['status'];  $volw= $row1['volumw']; $to= $row1['desti']; 
    $clerkn= $row1['clerkname'];$clerkpho= $row1['clerkcon'];$frm=$row1['from'];

	 	
	//$Comments = $row1['Comments'];	$custzip = $row1['custzip']; $states = $row1['states']; 
        //$city = $row1['city'];

$stotal=  $Totalfreight + $docharg +$codod +$oda ;

$servicetax= ($stotal*14)/100;

$gtotal=$stotal + $servicetax;
 
	
	
}
mysqli_close($con);
?> 
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>
	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
<script language="JavaScript">
</script>
</head>

   <center>  <?php $_SESSION['b']=b; ?>
    <tr align="center"><?php if(isset($_POST['submit']))  
  {
   echo '<img src="img/logo.png" />';
  }
  else
  {
  echo'<img src="img/logo.png" />';
  
  } ?></tr>
  </center>
 <div class="container">
   <div class="row">	
				    <div class="span12">
                     <div class="flexslider">
  <table border="1" style="width:100%">
  
         <tbody>
  <tr>
    <td>FROM,&nbsp;&nbsp;<b><?php echo $frm; ?><br><?php echo $fradd; ?></b></td>
	<td colspan="2">TO,&nbsp;&nbsp;<b><?php echo $to; ?></b></td>
    <td rowspan="2">Way Bill No.<br> <h5><?php echo $billno; ?></h5></td>
  </tr>
  <tr>
    <td>Head Office Copy </td>
    <td>Booked at Owner's Risk </td>
    <td>Date:  &nbsp; &nbsp;<?php echo $bukdate; ?> </td>
  </tr> 
         </tbody>
  </table>
  <table border="1" style="width:100%">
		<tr>
			<td colspan="2"> <b>Consignor M/s.:</b> &nbsp;&nbsp;<?php echo $Shippername; ?><br>&nbsp;&nbsp;<?php echo $Shipperaddress; ?></td>
			<td  colspan="2">Consignee M/s.:&nbsp;&nbsp;<?php echo $Receivername; ?><br>&nbsp;&nbsp;<?php echo $Receiveraddress; ?></td>
		</tr>
		<tr>
			<td >TIN No.: &nbsp;&nbsp; <?php echo $custin; ?></td>
			<td>Contact No.: &nbsp;&nbsp;<?php echo $Shipperphone; ?></td>
			<td>TIN No.: &nbsp;&nbsp;<?php echo $rtin; ?> </td>
			<td>Contact No.: &nbsp;&nbsp;<?php echo $Receiverphone; ?></td>
		</tr> 
		<tr>
			<td >Service TAX No. : &nbsp;&nbsp;<?php echo $custstax; ?></td>
			<td>PAN No.: &nbsp;&nbsp;<?php echo $custpan; ?></td>
		</tr> 
 </table>
  <table border="1" style="width:100%">
		<tr>
		  <td>Invoice No. &nbsp;&nbsp;<?php echo $Invoiceno; ?></td>
		  <td>Permit No.&nbsp;&nbsp;<?php echo $perm; ?></td>
                  <td>Type Of Shipment:&nbsp;<?php echo $Shiptype ; ?></td>
                   <td>Total Quntity:&nbsp;<?php  echo $Qnty ; ?></td>
		</tr>  
		<tr>
		    <td colspan="2" width="50%"> Declared Value Rs.: <?php echo $invalue ; ?></td>
			<td colspan="2" width="50%"> Payment Mode: <?php echo $Bookingmod ; ?></td>
        </tr>
        <tr align="center">
             <td >Description (said to contain)</td>
			 <td>WEIGHT</td> <td colspan="2">Non Negotiable </td>
		</tr>
		<tr>
			  <td rowspan="7"><?php echo $shides;?></td>
			  <td rowspan="2">ACTUAL <br><br> <?php echo $Weight; ?></td>
			  <td>Particulars  </td>
			  <td >Freight Rs.</td>
		</tr>  
		<tr>
			  <td >Freight </td>
			  <td align="right"><?php echo $Totalfreight; ?></td>
		</tr>  
        <tr>
			  <td rowspan="5">CHARGED <br><?php echo $chweight; ?> </td>
			  <td>Docket Charges </td>
			  <td align="right"><?php echo $docharg; ?></td>
		</tr>  
         <tr>  
			  <td>DOD/COD </td>
			  <td align="right"><?php echo $codod; ?></td>
		</tr> 
		 <tr>
				<td >ODA/Misc</td>
				<td align="right"><?php echo $oda; ?></td>
		</tr>  
		<tr> 
			 
		</tr>
		<tr>
			  <td >SUB TOTAL</td>
			  <td align="right"><?php echo $stotal;?></td>
		</tr>
		<tr >
			<td rowspan="2" colspan="2" >Total In Words :-<b>
				<?php echo convert_number_to_words($gtotal);?> Only </b></td> <!-- -->
			<td>SERVICE TAX (14%)</td>
			<td align="right"><?php echo $servicetax; ?></td>
		</tr> 
		<tr >
			<td >GRAND TOTAL</td>
			<td align="right"><?php echo $gtotal; ?></td>
		</tr> 
  </table>
<table border="1" style="width:100%"><tr><td><p>INTERNAL AUDIT OBSERVATION <br><br>
  Checked by<br><br>Date:  &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; Time: <br></p></td>
<td align="center"><p>I hereby Agree to the Terms & Conditions Printed overleaf <br><br>  Consignors Signature</p> <?php echo $Shippername; ?></td>
<td align="center"><p><br><br>Signature Of Booking Clerk <br> <?php echo $clerkn;?><br><?php echo $frm;?><br><?php echo $uadd;  ?></p></td></tr>
</table>
 </div><!--end flexslider <ul class="slides"></ul>-->
                    </div></div>

<p align="left">  <input type="button" id="printpagebutton" onClick="printpage();" value="Print"><a id="backbutton" href="billConsig.php"><input type="button" id="backbutton" onClick="closeWin();" value="Close"> </a></p>

</div>

<script type="text/javascript">
    function printpage() {
        //Get the print button and put it into a variable
        var printButton = document.getElementById("printpagebutton");
        var back=document.getElementById("backbutton");
        //Set the print button visibility to 'hidden' 
        printButton.style.visibility = 'hidden';
        back.style.visibility = 'hidden';
        //Print the page content
        window.print();
        //Set the print button to 'visible' again 
        //[Delete this line if you want it to stay hidden after printing]
        printButton.style.visibility = 'visible';
         back.style.visibility = 'visible';
    }
</script>
<script type="text/javascript">

function closeWin() {
    back.close();
}
    window.onbeforeunload = function() {
        
    }
</script>

<?php
function convert_number_to_words($number) {

    $hyphen      = '-';
    $conjunction = ' and ';
    $separator   = ', ';
    $negative    = 'negative ';
    $decimal     = ' point ';
    $dictionary  = array(
        0                   => 'Zero',
        1                   => 'One',
        2                   => 'Two',
        3                   => 'Three',
        4                   => 'Four',
        5                   => 'Five',
        6                   => 'Six',
        7                   => 'Seven',
        8                   => 'Eight',
        9                   => 'Nine',
        10                  => 'Ten',
        11                  => 'Eleven',
        12                  => 'Twelve',
        13                  => 'Thirteen',
        14                  => 'Fourteen',
        15                  => 'Fifteen',
        16                  => 'Sixteen',
        17                  => 'Seventeen',
        18                  => 'Eighteen',
        19                  => 'Nineteen',
        20                  => 'Twenty',
        30                  => 'Thirty',
        40                  => 'Fourth',
        50                  => 'Fifty',
        60                  => 'Sixty',
        70                  => 'Seventy',
        80                  => 'Eighty',
        90                  => 'Ninety',
        100                 => 'Hundred',
        1000                => 'Thousand',
        1000000             => 'Million',
        1000000000          => 'Billion',
        1000000000000       => 'Trillion',
        1000000000000000    => 'Quadrillion',
        1000000000000000000 => 'Quintillion'
    );

    if (!is_numeric($number)) {
        return false;
    }

    if (($number >= 0 && (int) $number < 0) || (int) $number < 0 - PHP_INT_MAX) {
        // overflow
        trigger_error(
            'convert_number_to_words only accepts numbers between -' . PHP_INT_MAX . ' and ' . PHP_INT_MAX,
            E_USER_WARNING
        );
        return false;
    }

    if ($number < 0) {
        return $negative . convert_number_to_words(abs($number));
    }

    $string = $fraction = null;

    if (strpos($number, '.') !== false) {
        list($number, $fraction) = explode('.', $number);
    }

    switch (true) {
        case $number < 21:
            $string = $dictionary[$number];
            break;
        case $number < 100:
            $tens   = ((int) ($number / 10)) * 10;
            $units  = $number % 10;
            $string = $dictionary[$tens];
            if ($units) {
                $string .= $hyphen . $dictionary[$units];
            }
            break;
        case $number < 1000:
            $hundreds  = $number / 100;
            $remainder = $number % 100;
            $string = $dictionary[$hundreds] . ' ' . $dictionary[100];
            if ($remainder) {
                $string .= $conjunction . convert_number_to_words($remainder);
            }
            break;
        default:
            $baseUnit = pow(1000, floor(log($number, 1000)));
            $numBaseUnits = (int) ($number / $baseUnit);
            $remainder = $number % $baseUnit;
            $string = convert_number_to_words($numBaseUnits) . ' ' . $dictionary[$baseUnit];
            if ($remainder) {
                $string .= $remainder < 100 ? $conjunction : $separator;
                $string .= convert_number_to_words($remainder);
            }
            break;
    }

    if (null !== $fraction && is_numeric($fraction)) {
        $string .= $decimal;
        $words = array();
        foreach (str_split((string) $fraction) as $number) {
            $words[] = $dictionary[$number];
        }
        $string .= implode(' ', $words);
    }

    return $string;
}

?>
</html>