 
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>
	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
 
<script language="JavaScript">
</script>
</head>

       
  
 <div class="container">
 <div class="row">	
<div class="span12">
 <div class="flexslider">
  <table border="0" style="width:100%; border-collapse: collapse;" >
  <tr><td>
  <table border="1" style="width:100%; border-collapse: collapse;" >
  <tr>
  <td width="20%"><img src="img/logo.png" width="50%" /></td>
  <td><h5><b>Vivanta Logistics Private. Limited</b></h5>
  <b>Registered Address</b>: Bungalow No.-7,samata Hsg.Soc,behind MSEB Colony,Bhosale Nagar, Pune-411007<br>
<b>Office Address</b>: 440,Ghokhale nagar Cross road, Model Colony, Shivaji Nagar, Pune-411016<br>
Ph: 020 4120 0105/ Mobile-7767000120  Email:<EMAIL></td>
  <td>PAN No. ********** <br>
  GST No.27**********1Z5<br>
Docket No. <?php echo $ConsignmentNo; ?> <br></td>

  <td>
  <table border="0" class="no_border"><tr>
  <td>Services:</td>
  <td><input name="Air" type="checkbox" value=""> Air Ex
  <input name="Air" type="checkbox" value=""> Sea  <br>
  <input name="Air" type="checkbox" value="">Surface Ex
  <input name="Air" type="checkbox" value=""> Train 
  <input name="Air" type="checkbox" value=""> International</td>
  </tr></table>   </td>
  </tr>
  </table>
  </td></tr>
  
  
<tr><td>
<table border="1" class="outer" width="100%">
<tr>
<td width="30" colspan="2">From&nbsp;&nbsp;<b><?php echo $Shippername; ?><br><?php echo $shipadd; ?></b></td>
<td width="30" colspan="2">To&nbsp;&nbsp;<b><?php echo $Receivername; ?></b></td>
<td width="40" colspan="2" align="center"><b>Bill Details</b></td>
</tr>

<tr>
<td colspan="2" align="center"><b>CONSIGNOR</b></td>
<td colspan="2" align="center"><b>CONSIGNEE</b></td>
<td width="9%">Particulars</td>
<td>Rate</td>

</tr>

<tr>
<td >Name</td>
<td><?php echo $Shippername; ?></td>
<td>Name</td>
<td><?php echo $Receivername; ?></td>
<td>Frieght</td>
<td><?php echo $Totalfreight; ?></td>

</tr>


<tr>
<td>Address</td>
<td><?php echo $shipadd; ?></td>
<td>Address</td>
<td><?php echo $rcadd; ?></td>
<td>DOD/COD</td>
<td><?php echo $codod; ?></td>

</tr>


<tr>
<td>Pincode</td>
<td><?php echo $_POST['custzip']; ?></td>
<td>Pincode</td>
<td><?php echo $rzip; ?></td>
<td>Docket chg</td>
<td><?php echo $docharg; ?></td>

</tr>


<tr>
<td>Email</td>
<td><?php echo $Shipperemail; ?></td>
<td>Email</td>
<td><?php echo $Receiveremail; ?></td>
<td>ODA</td>
<td><?php echo $oda; ?></td>

</tr>


<tr>
<td>Phone/Cell</td>
<td><?php echo $Shipperphone; ?></td>
<td>Phone/Cell</td>
<td><?php echo $Receiverphone; ?></td>
<td>MISC.</td>
<td><?php echo $misc; ?></td>

</tr>

<tr>
<td>GST</td>
<td><?php echo $custgst; ?></td>
<td>GST</td>
<td><?php echo $rcgst; ?></td>
<td>ESS Charges</td>
<td><?php echo $ess; ?></td>

</tr>

<tr>
<td>Total Quntity</td>
<td>Packing</td>
<td>Volume</td>
<td>Docket No</td>
<td>DPH/ATF</td>
<td><?php echo $atf; ?></td>
</tr>


<tr>
<td rowspan="2">&nbsp;<?php  echo $Qnty; ?></td>
<td rowspan="2">&nbsp;</td>
<td rowspan="5">&nbsp;</td>
<td><h5><?php echo $ConsignmentNo; ?></h5>&nbsp;</td>
<td>Handling Chg</td>
<td><?php echo $handlingcharge; ?></td>

</tr>

<tr>
<td>Actual Wt.</td>
<td>Sub Total</td>
<td><?php echo $stotal;?></td>

</tr>

<tr>
<td>Declared Value</td>
<td>Invoice No.</td>
<td>&nbsp;</td>
<td>GST(18%)</td>
<td><?php echo $gst; ?></td>

</tr>

<tr>
<td rowspan="2">&nbsp;<?php echo $invalue ; ?></td>
<td rowspan="2">&nbsp;</td>
<td>Charged Wt.</td>
<td>Grand Total</td>
<td><?php echo $gtotal; ?></td>

</tr>

<tr>
<td>&nbsp;</td>
<td colspan="3">Amount In Words</td>
</tr>

<tr>
<td>Checked By</td>
<td colspan="2">Description</td>
<td colspan="2" rowspan="2">Consignors Signature</br><?php echo $Shippername; ?></td>
<td colspan="2" rowspan="2">Signature of booking Clerk<br> <?php echo $clerkn; ?><br><?php echo $clerkpho;  ?></td>
</tr>


<tr>
<td>Date</td>
<td colspan="2"><?php echo $shipdescrib ; ?></td>
</tr>
</table>
</td></tr>

  
  
  
  
  
  
   </table>
  
  
  
  
   </div><!--end flexslider <ul class="slides"></ul>-->
                    </div></div>
<p align="left">  <input type="button" id="printpagebutton" onClick="printpage();" value="Print"><a style="text-decoration:none" id="backbutton" href="addCourier.php"><input type="button" id="backbutton" onClick="closeWin();" value="Close"> </a></p>

</div>

<script type="text/javascript">
    function printpage() {
        //Get the print button and put it into a variable
        var printButton = document.getElementById("printpagebutton");
        var back=document.getElementById("backbutton");
        //Set the print button visibility to 'hidden' 
        printButton.style.visibility = 'hidden';
        back.style.visibility = 'hidden';
        //Print the page content
        window.print();
        //Set the print button to 'visible' again 
        //[Delete this line if you want it to stay hidden after printing]
        printButton.style.visibility = 'visible';
         back.style.visibility = 'visible';
    }
</script>
<script type="text/javascript">

function closeWin() {
    back.close();
}
    window.onbeforeunload = function() {
        
    }
</script>


</html>