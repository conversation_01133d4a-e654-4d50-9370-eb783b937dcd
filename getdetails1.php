<?php
session_start();
require 'connection.php';
require_once('database.php');
require_once('library.php');
isUser();

// Set filters from GET (for AJAX calls and pagination)
if (isset($_GET['date']) && isset($_GET['date2']) && isset($_GET['rep'])) {
    $_SESSION['datewise_date'] = $_GET['date'];
    $_SESSION['datewise_date2'] = $_GET['date2'];
    $_SESSION['datewise_rep'] = $_GET['rep'];
}

// Check if session variables exist
if (!isset($_SESSION['datewise_date']) || !isset($_SESSION['datewise_date2']) || !isset($_SESSION['datewise_rep'])) {
    echo "<div class='alert alert-warning'>Please select filters first.</div>";
    exit;
}

$date1 = $_SESSION['datewise_date'];
$date2 = $_SESSION['datewise_date2'];
$statusid = $_SESSION['datewise_rep'];

// Pagination setup
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
if (!in_array($limit, [10, 20, 50, 100])) {
    $limit = 10;
}
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$start_from = ($page - 1) * $limit;
$Cdate=date('Y-m-d',strtotime($date1));
$Cdate1=date('Y-m-d',strtotime($date2));

// Count total records for pagination
if($statusid=='1')
{
    $count_sql = "SELECT COUNT(*) as total FROM tbl_courier inner join status on tbl_courier.status=status.statusid  WHERE tbl_courier.status ='$statusid' and tbl_courier.book_date between '$Cdate' and '$Cdate1'";
    $sql="SELECT userid,cons_no,weight,gtotamt,rev_name,pod_img,ship_name,userid,rate,oda_mis,noofpackage,gst,eway_expdate,book_mode,e_waybill,eway_start_date,eway_end_date,r_add,status,qty,partno,freight,invi_value,status_date,assured_dly_date,book1_date,type,invice_no,chweight,mode,statusname FROM tbl_courier inner join status on tbl_courier.status=status.statusid  WHERE tbl_courier.status ='$statusid' and tbl_courier.book_date between '$Cdate' and '$Cdate1' ORDER BY cons_no DESC LIMIT $start_from, $limit";
}
else
{
    $count_sql = "SELECT COUNT(*) as total FROM tbl_courier inner join status on tbl_courier.status=status.statusid  WHERE tbl_courier.status ='$statusid' and tbl_courier.status_date between '$Cdate' and '$Cdate1'";
    $sql="SELECT userid,cons_no,weight,gtotamt,rev_name,pod_img,ship_name,userid,rate,oda_mis,noofpackage,status,book_mode,eway_expdate,e_waybill,r_add,status_date,qty,gst,partno,freight,invi_value,assured_dly_date,book1_date,type,invice_no,chweight,mode,statusname FROM tbl_courier inner join status on tbl_courier.status=status.statusid  WHERE tbl_courier.status ='$statusid' and tbl_courier.status_date between '$Cdate' and '$Cdate1' ORDER BY cons_no DESC LIMIT $start_from, $limit";
}

$count_result = mysqli_query($con, $count_sql);
$count_row = mysqli_fetch_assoc($count_result);
$total_records = $count_row['total'];
$total_pages = ceil($total_records / $limit);

$count = $start_from; // Start count from the current page offset
$tr = ""; // Initialize table rows variable
$result = mysqli_query($con,$sql);

while($row=mysqli_fetch_array($result))
{
    //shipper details 
     
       $sql_s="SELECT * FROM `custreg` where `custname`  like '".$row['ship_name']."'" ;
  
$result_s = mysqli_query($con,$sql_s);
$row_s = mysqli_fetch_array($result_s);
$shipper_name=$row_s['custname'];
 $shipper_phone=$row_s['custphone']; 
 $shipper_mail=$row_s['custmail'];
    $shipper_add=$row_s['custadd'];  
     $shipper_c=$row_s['custcity'];
       $sql_s="SELECT * FROM `city` where ctid=".$shipper_c ;
  
$result_s = mysqli_query($con,$sql_s);
$row_s = mysqli_fetch_array($result_s);
$shipper_city=$row_s['cityname'];
     /* shipper details end  <td>".$shipper_name."</td><td>".$shipper_phone."</td><td>".$shipper_add."</td><td>".$shipper_mail."</td><td>".$shipper_city."</td>
           <th><h6>Shipper Name</h6></th>
							        <th><h6>Shipper Phone</h6></th>
							         <th><h6>Shipper Address</h6></th>
							          <th><h6>Shipper Email</h6></th>
							          <th><h6>Shipper City</h6></th>
							 */
   
    
     // Initialize status and delivery date variables
     $statusdate = "";
     $delivereddate = "";

     if($row['status']=='6' || $row['status']=='50' || $row['status']=='57'){
         $statusdate=$row['status_date'];
     }

     if($row['status']=='5'){
         $delivereddate=$row['status_date'];
     }
 
 if($row['r_add']==""){
         $destination="";
     }else{
  $sql1="SELECT city_name,r_add from tbl_city_code inner join tbl_courier on tbl_city_code.Id= tbl_courier.r_add 
  WHERE tbl_courier.r_add = '".$row['r_add']."' " ;

$result1 = mysqli_query($con,$sql1);
$row1 = mysqli_fetch_array($result1);
$destination=$row1['city_name'];
     }
     
     $sql2="SELECT Manager_name,empcode from tbl_courier_officers inner join tbl_courier on tbl_courier_officers.cid=tbl_courier.userid
  WHERE tbl_courier.userid = '".$row['userid']."' " ;

$result2 = mysqli_query($con,$sql2);
$row2 = mysqli_fetch_array($result2);
$Manager_name=$row2['Manager_name'];
$empcode=$row2['empcode'];

// Calculate value without GST
$withoutgstvalue = "";
if(isset($row['invi_value']) && $row['invi_value'] > 0 && isset($row['gst']) && $row['gst'] > 0) {
    $withoutgstvalue = $row['invi_value'] / (1 + ($row['gst'] / 100));
    $withoutgstvalue = number_format($withoutgstvalue, 2);
} else {
    $withoutgstvalue = $row['invi_value'];
}

// Handle remark field safely
$remark = isset($row['remark']) ? $row['remark'] : "";

	$count++;
$tr=$tr."<tr><td>".$count."</td><td>".$Manager_name."</td><td>".$empcode."</td> <td>".$shipper_name."</td> <td>".$shipper_city."</td>
        <td>".$row['status_date']."</td><td>".$row['cons_no']."</td><td>".$row['book1_date']."</td><td>".$row['invice_no']."</td>
<td>".$row['rev_name']."</td><td>".$destination."</td><td>".$row['noofpackage']."</td><td>".$row['qty']."</td><td>".$row['partno']."</td><td>".$withoutgstvalue."</td><td>".$row['invi_value']."</td><td>".$statusdate."</td>
<td>".$row['book_mode']."</td><td>".$row['statusname']."</td><td>".$remark."</td><td>".$delivereddate."</td><td>".$row['mode']."</td><td>".$row['weight']."</td><td>".$row['chweight']."</td><td>".$row['e_waybill']."</td><td>".$row['eway_start_date']."</td><td>".$row['eway_end_date']."</td><td>".$row['rate']."</td>
<td>".$row['oda_mis']."</td><td>".$row['freight']."</td><td></td><td></td></tr>";
}
/*
header("Content-Type:   application/vnd.ms-excel; charset=utf-8");
header("Content-type:   application/x-msexcel; charset=utf-8");
header("Content-Disposition: attachment; filename=Statuswise_Report.xls"); 
header("Expires: 0");
header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
header("Cache-Control: private",false);
*/

// Records per page selector will be moved to pagination section


		echo' <table class="table1"  style="width:100%"  border="1">
						<thead>
				 <th><h6>Sr No. </h6></th>
							      <th><h6>Employee Name </h6></th>
							      <th><h6>Emp_ID</h6></th><th><h6>Shipper Name</h6></th>
							            <th><h6>Shipper City</h6></th>
	
							       <th><h6>Scanning Date</h6></th>
							  <th><h6>LR No </h6></th>
							     <th><h6>BKG Date</h6></th>
							     <th><h6>Invoice No </h6></th>
							     <th><h6>Customer Name </h6></th>
						         <th><h6>Destinantion</h6></th>
						         	<th><h6>Cases </h6></th>
									<th><h6>Qty </h6></th>
										<th><h6>Part No. </h6></th>
								
								 
								  <th><h6>Without GST Value </h6></th>
								    <th><h6>Invoice Value </h6></th>
								  	 
									
								   <th><h6>Godown Receipt Date </h6></th>
								<th><h6>Type</h6></th>
								<th><h6>Remarks</h6></th>
								<th><h6>My Remarks</h6></th>
								  <th><h6>Delivery date </h6></th>
								    <th><h6>Mode</h6></th>
							    <th><h6>A/Weight </h6></th>
								<th><h6>C/Weight </h6></th>
									<th><h6>E WayBill No. </h6></th><th><h6>E Way Start Date  </h6></th><th><h6>E Way End Date </h6></th>
									<th><h6>Rate </h6></th>
										<th><h6>ODA </h6></th>
											<th><h6>Fright </h6></th>
											<th><h6>GST </h6></th>
								
								<th><h6>Total </h6></th>
							</tr>
						</thead> <tbody>'.$tr.'</tbody></table>';

// Separator to split table content from pagination
echo '<!--PAGINATION_SEPARATOR-->';

// Add records per page selector and pagination navigation
echo '<div class="row control-group" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 15px;">
    <div class="col-md-4">
        <form method="get" id="entryForm" class="form-inline" style="display: inline-block;">
            <label for="limit" style="margin-right: 5px;">Show</label>
            <select name="limit" id="limit" onchange="loadPage(1, this.value);" class="form-control input-sm">
                <option value="10" ' . ($limit == 10 ? 'selected' : '') . '>10</option>
                <option value="20" ' . ($limit == 20 ? 'selected' : '') . '>20</option>
                <option value="50" ' . ($limit == 50 ? 'selected' : '') . '>50</option>
                <option value="100" ' . ($limit == 100 ? 'selected' : '') . '>100</option>
            </select>
            entries
        </form>
    </div>
</div>';

// Add pagination navigation
$adjacents = 2;
$start_loop = ($page > $adjacents) ? $page - $adjacents : 1;
$end_loop = ($page < ($total_pages - $adjacents)) ? $page + $adjacents : $total_pages;

echo "<style>
.pagination-wrapper {
    display: flex;
    justify-content: center;
    margin: 30px 0;
}
.pagination {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}
.pagination a {
    padding: 6px 12px;
    background-color: white;
    border: 1px solid rgb(255, 106, 0);
    color: #f16325;
    text-decoration: none;
    border-radius: 4px;
    cursor: pointer;
}
.pagination a.active {
    background-color: #f16325;
    color: white;
    font-weight: bold;
}
.pagination a:hover {
    background-color: #f16325;
    color: white;
}
</style>";

// Output pagination
echo "<div class='pagination-wrapper'>";
echo "<div class='pagination'>";

// First/Previous
if ($page > 1) {
    echo "<a onclick='loadPage(1, $limit)'>&laquo;</a>";
    echo "<a onclick='loadPage(" . ($page - 1) . ", $limit)'>Previous</a>";
}

// Page numbers
for ($i = $start_loop; $i <= $end_loop; $i++) {
    if ($i == $page)
        echo "<a class='active' onclick='loadPage($i, $limit)'>$i</a>";
    else
        echo "<a onclick='loadPage($i, $limit)'>$i</a>";
}

// Next/Last
if ($page < $total_pages) {
    echo "<a onclick='loadPage(" . ($page + 1) . ", $limit)'>Next</a>";
    echo "<a onclick='loadPage($total_pages, $limit)'>&raquo;</a>";
}

echo "</div></div>";

// Add record count info
echo "<div style='text-align: center; margin: 10px 0;'>";
echo "<span>Showing " . ($start_from + 1) . " to " . min($start_from + $limit, $total_records) . " of $total_records entries</span>";
echo "</div>";

?>
