<?php 
error_reporting(~E_ALL);
session_start();
require 'connection.php';
require_once('database.php');
$tr="";   
$els="";
$tr1="";  
$consign="";
$consign=$_POST['consign'];
//$consign=14325;
$cntr=0;

// $sql22="SELECT * FROM `currentloc` where `consig_no`='$consign'";
$sql22="SELECT `tbl_courier_officers`.`office` as source, `currentloc`.`current_loc` as destin , `currentloc`.`current_date` as date_time , `status`.`statusname` as staus FROM (`currentloc` inner join `tbl_courier_officers` on `currentloc`.`sourcesid`= `tbl_courier_officers`.`cid`)inner join status on `status`.`statusid`=`currentloc`.`currentstatus` where  `currentloc`.`consig_no`= '$consign'";

$result=mysqli_query($con,$sql22);
while($row2=mysqli_fetch_array($result)){
	
$tr=$tr."<tr><td><h4><b>".++$count."</b></h4></td><td><h4><b>".$row2['source']."</b></h4></td><td><h4><b>".$row2['destin']."</b></h4></td><td><h4><b>".$row2['date_time']."</b></h4></td><td><h4><b>".$row2['staus']."</b></h4></td></tr>";
 
}


$sql = "SELECT * FROM `tbl_courier` where cons_no='$consign'";

$result2=mysqli_query($con,$sql);
$no = mysqli_num_rows($result2);
if($no == 1){
while($row2=mysqli_fetch_array($result2))
{
$Cno=$row2['cons_no'];   $reciver=$row2['rev_name'];   $bkdate=$row2['book1_date'];  $invoiceno=$row2['invice_no']; 
$shipper=$row2['ship_name'];   $To=$row2['r_add'];   $statu=$row2['status'];  $invalu=$row2['invi_value']; 
$from=$row2['s_add'];   $bkmode=$row2['mode'];   $oda=$row2['oda_mis'];  $Cno=$row2['cons_no']; 
$bktype=$row2['type'];   $weigh=$row2['weight'];   $noqty=$row2['qty'];  $Cno=$row2['cons_no']; 
$docket=$row2['dock_charg'];   $dod=$row2['dod_cod'];   $Cno=$row2['cons_no'];  $Cno=$row2['cons_no']; 
$Cno=$row2['cons_no'];   $Cno=$row2['cons_no'];   $Cno=$row2['cons_no'];  $Cno=$row2['cons_no']; 

//echo $tr=$tr."<tr><td>".++$count."</td><td>".$row2['cons_no']."</td><td>".$row2['rev_name']."</td><td>".$row2['mode']."</td><td>".$row2['r_add']."</td></tr>";

}
}
else
{ 
   $els= $els."<table border='0' align='center' width='80%' > <tbody> <tr> <td align='center'><h6>Shipment Number <font color='red' size='5' >'$consign'</font> Not found. Please verify the Number.<br><a href='index.html'>Go Back</a> to Search Again.</h6></td></tr></tbody> </table>" ;
}

$statsql="SELECT statusname from status where statusid='$statu'";
$resultstat=mysqli_query($con,$statsql);
while($statrow=mysqli_fetch_array($resultstat)){

   $tatusss=$statrow['statusname'];
}


?>

<!DOCTYPE html>

<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="user-scalable=no, initial-scale=1.0, maximum-scale=1.0">
<title>Vivanta Logistics Pvt. Ltd.</title>
<meta name="author" content="Themezinho">
<meta name="description" content="Logistic and Transportation Template">
<meta name="keywords" content="logistic, transportation, package, delivery, cargo, carousel, post, moving, caring">

<!-- SOCIAL MEDIA META -->
<meta property="og:description" content="Vivanta Logistics">
<meta property="og:image" content="preview.html">
<meta property="og:site_name" content="Vivanta Logistics">
<meta property="og:title" content="Vivanta Logistics">
<meta property="og:type" content="website">






<!-- CSS FILES -->
<link href="css/main.css" rel="stylesheet">

<!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
<!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
<!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body>
<div class="soft-transition"></div>
<!-- end soft-transition -->
<div class="transition-overlay"></div>
<!-- end transition-overlay -->
<main>
  
  <!-- end side-box -->
  <header class="full-header">
    <nav class="navbar navbar-default">
      <div class="top-bar">
        <div class="container">
          <div class="row">
            
       
          </div>
          <!-- end row --> 
        </div>
        <!-- end container --> 
      </div>
      <!-- end top-bar -->
      <div class="container">
        <div class="navbar-header">
          <div class="row">
            <div class="col-md-3 col-sm-4 col-xs-12">
              <button type="button" class="navbar-toggle toggle-menu menu-left push-body" data-toggle="collapse" data-target="#bs-example-navbar-collapse-1"> <span class="sr-only">Toggle navigation</span> <span class="icon-bar"></span> <span class="icon-bar"></span> <span class="icon-bar"></span> </button>
              <a class="navbar-brand" href="index-2.html"><img src="images/logo.png" alt="Image"></a> </div>
            <!-- end col-5 -->
            <div class="col-md-3 col-sm-4 hidden-xs"> <i class="icon-global"></i>
              <h6>OPENING HOURS<br>
                <span>MON-SAT 09:00 - 18:00 </span></h6>
            </div>
            <!-- end col-2 -->
            <div class="col-md-3 col-sm-4 hidden-xs"> <i class="icon-map-pin"></i>
              <h6>OUR LOCATION<br>
                <span>Maharashtra - India</span></h6>
            </div>
            <!-- end col-2 -->
            <div class="col-md-3 hidden-sm hidden-xs"> <i class="icon-chat"></i>
              <h6>QUICK SUPPORT<br>
                <span><EMAIL></span></h6>
            </div>
            <!-- end col-2 --> 
          </div>
          <!-- end row --> 
        </div>
        <!-- end navbar-header -->
        <div class="collapse navbar-collapse cbp-spmenu cbp-spmenu-vertical cbp-spmenu-left" id="bs-example-navbar-collapse-1">
          <ul class="nav navbar-nav main-menu">
            <li class="active"><a href="index.html" class="transition">HOME</a></li>
            <li><a href="about.html" class="transition">ABOUT US</a></li>
            <li><a href="services.html" class="transition">SERVICES</a></li>
            <li><a href="opportunities.html" class="transition">OPPORTUNITIES</a></li>
            <li><a href="contact.html" class="transition">CONTACT US</a></li>
          </ul>
          <ul class="nav navbar-nav social-nav ">
            <li><a href="#" data-toggle="tooltip" data-placement="top" title="Facebook"><i class="ion-social-facebook"></i></a></li>
            <li><a href="#" data-toggle="tooltip" data-placement="top" title="Twitter"><i class="ion-social-twitter"></i></a></li>
            <li><a href="#" data-toggle="tooltip" data-placement="top" title="Google+"><i class="ion-social-googleplus"></i></a></li>
            <li><a href="#" data-toggle="tooltip" data-placement="top" title="Youtube"><i class="ion-social-youtube"></i></a></li>
           
          </ul>
          
        </div>
        <!-- end navbar-collapse --> 
      </div>
      <!-- end container --> 
    </nav>
  </header>
		
           <div class="row">					
				<div class="span12">
				       			
					<div class="cart-accordain" id="cart-acc">
                                                        <div class="h5"><?php echo $els;?> </div>     
						 	<div class="accordion-group">
							<div class="accordion-heading">
								<h2 align="center" class="h5"> <b>&nbsp;&nbsp;&nbsp;&nbsp;Tracker Result&nbsp;&nbsp;&nbsp;&nbsp;</b></h2>
							</div>
							<div class="accordion-heading">
								<a class="accordion-toggle" data-toggle="collapse" data-parent="#cart-acc" href="#gift-voucher1">
									<h2> <i class="icon-caret-right"></i><b>&nbsp;&nbsp;&nbsp;&nbsp;Consignment Details...&nbsp;&nbsp;&nbsp;&nbsp;</b></h2>
								</a>
							</div>
							<div id="gift-voucher1" class="accordion-body collapse in">
								<div class="accordion-inner">
									<form class="form-horizontal">
						<div class="span6">
				       
				        <div class="control-group ">
							    <label class="control-label"> <h4> <b> Consignment Number :  </b></h4> </label>
							    <div class="controls">
								<!--<p><h4><?php echo $Cno;?></h4></p>-->
							     <input type="text"      name="custpan" id="custpan"  value="<?php echo $Cno;?>" readonly>
							    </div>
						</div><!--end control-group  -->
					     
				        <div class="control-group ">
							    <label class="control-label"> <h4><b> Shipper Name :</b></h4> </label>
							    <div class="controls">
								<!--<p ><h4><?php echo $shipper;?></h4></p>-->
							     <input type="text"      name="custpan" id="custpan"  value="<?php echo $shipper;?> " readonly>
							    </div>
						</div><!--end control-group  -->
					     <div class="control-group ">
							    <label class="control-label"> <h4><b> From :</b></h4> </label>
							    <div class="controls">
								<!--<p ><h4> <?php echo $from;?></h4></p>-->
							     <input type="text" name="custpan" id="custpan"  value=" <?php echo $from;?>" readonly>
							    </div>
						</div><!--end control-group  -->
					  
						 <div class="control-group ">
							    <label class="control-label"> <h4><b> Type of Shipment :</b></h4> </label>
							    <div class="controls">
								<!--<p ><h4><?php echo $bktype;?> </h4></p>-->
							     <input type="text"      name="custpan" id="custpan"  value="<?php echo $bktype;?>" readonly>
							    </div>
						</div><!--end control-group  -->
					     
				        <div class="control-group ">
							    <label class="control-label"> <h4><b> Weight : </b></h4> </label>
							    <div class="controls">
								<!--<p ><h4><?php echo $weigh;?></h4></p>-->
							     <input type="text"      name="custpan" id="custpan"  value="<?php echo $weigh;?>" readonly>
							    </div>
						</div><!--end control-group  -->
						 <div class="control-group ">
							    <label class="control-label"> <h4><b> Number of Quntity :</b> </h4> </label>
							    <div class="controls">
								<!--<p ><h4> <?php echo $noqty;?></h4></p>-->
							     <input type="text"      name="custpan" id="custpan"  value="<?php echo $noqty;?>" readonly>
							    </div>
						</div><!--end control-group  -->
					     
				        <div class="control-group ">
							    <label class="control-label"> <h4><b> Status : </b></h4> </label>
							    <div class="controls">
								<!--<p ><h4> <?php echo $statu;?></h4></p>-->
							     <input type="text"      name="custpan" id="custpan"  value="<?php echo $statu;?>" readonly>
							    </div>
						</div><!--end control-group  -->
					     
				</div>
				 <div class="span5">
				             <div class="control-group ">
							    <label class="control-label"> <h4><b> Book Date : </b> </h4> </label>
							    <div class="controls">
								<!--<p ><h4><?php echo $bkdate;?></h4></p>-->
							     <input type="text"      name="custpan" id="custpan"  value="<?php echo $bkdate;?>" readonly>
							    </div>
						</div><!--end control-group  -->  
						  <div class="control-group ">
							    <label class="control-label"> <h4><b> Receiver Name :</b> </h4> </label>
							    <div class="controls">
								<!--<p ><h4><?php echo $reciver;?></h4></p>-->
							     <input type="text" name="custpan" id="custpan"  value="<?php echo $reciver;?>" readonly>
							    </div>
						</div><!--end control-group  -->
					     
				        <div class="control-group ">
							    <label class="control-label"> <h4><b> To :  </b></h4> </label>
							    <div class="controls">
								<!--<p ><h4><?php echo $To;?></h4></p>-->
							     <input type="text"      name="custpan" id="custpan"  value="<?php echo $To;?>" readonly>
							    </div>
						</div><!--end control-group  -->
					     <div class="control-group ">
							    <label class="control-label"> <h4> <b> Book Mode : </b></h4> </label>
							    <div class="controls">
								<!--<p ><h4><?php echo $bkmode;?> </h4></p>-->
							     <input type="text"      name="custpan" id="custpan"  value="<?php echo $bkmode;?> " readonly>
							    </div>
						</div><!--end control-group  -->
					     
				        <div class="control-group ">
							    <label class="control-label"> <h4> <b> Docket Charges  : </b></h4> </label>
							    <div class="controls">
								<!--<p ><h4><?php echo $docket;?></h4></p>-->
							     <input type="text"      name="custpan" id="custpan"  value="<?php echo $docket;?>" readonly>
							    </div>
						</div><!--end control-group  -->
						 <div class="control-group ">
							    <label class="control-label"> <h4> <b> DOD / COD : </b></h4> </label>
							    <div class="controls">
								<!--<p ><h4><?php echo $dod;?></h4></p>-->
							     <input type="text"      name="custpan" id="custpan"  value="<?php echo $dod;?>" readonly>
							    </div>
						</div><!--end control-group  -->
					     
				        <div class="control-group ">
							    <label class="control-label"> <h4> <b> ODA / MIS :</b> </h4> </label>
							    <div class="controls">
								<!--<p ><h4><?php echo $oda;?></h4></p>-->
							     <input type="text"      name="custpan" id="custpan"  value="<?php echo $oda;?>" readonly>
							    </div>
						</div><!--end control-group  -->
						<!--<div class="control-group ">
							    <label class="control-label"> <h4> <b> Status Date : </b></h4> </label>
							    <div class="controls">
								<p ><h4><?php echo $Cno;?></h4></p>
							     <input type="hidden"      name="custpan" id="custpan"  value="<?php echo $Cno;?>" readonly>
							    </div>
						</div><!--end control-group  -->
				</div>
		</form>
								</div>
							</div>
						</div><!--end accordion-group-->
						 
						  <footer class="dark-footer">
    
    <!-- end footer-content -->
    <div class="sub-footer">
      <div class="container"> <span class="copyright">Copyright © 2017, Vivanta Logistics | Developed By <a href="http://vspsofttech.com/" target="_blank">VSP Softtech</a> </span></div>
      <!-- end container --> 
    </div>
    <!-- end sub-footer --> 
  </footer>
  <!-- end footer --> 
</main>

<!-- JS FILES --> 
<script src="js/jquery.min.js"></script> 
<script src="js/bootstrap.min.js"></script> 
<script src="js/bootstrap-select.js"></script> 
<script src="js/bootstrap-datepicker.js"></script> 
<script src="js/jquery.counterup.min.js"></script> 
<script src="js/jquery.stellar.js"></script> 
<script src="js/jquery.validate.min.js"></script> 
<script src="js/jquery.form.js"></script> 
<script src="js/contact-form.js"></script> 
<script src="js/jquery.fancybox.js"></script> 
<script src="js/waypoints.min.js"></script> 
<script src="js/slick.js"></script> 
<script src="js/masonry.min.js"></script> 
<script src="js/scripts.js"></script>
</body>



</html>