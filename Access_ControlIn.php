<?php
require 'connection.php'; 
require_once('database.php');
require_once('library.php');
ob_start();
session_start();
date_default_timezone_set('Asia/Kolkata');
$date = date('Y/m/d h:i:s', time());
$msg="yes";
if (is_array($_POST['SM']))
{
foreach($_POST['SM'] as $area)    {
            echo '<li>'.$area.'</li>';
            
           echo $sql="insert into  Access_Control (Emp_ID,Sub_ID) values('".$_POST['name']."','".$area."')";
            echo "</br>".$sql."</br>";
 if (!mysqli_query($con,$sql))
 {
  $msg="no"; 
 }
         
            
            }
}
header("location: AccessControl.php?msg=".$msg);
?>