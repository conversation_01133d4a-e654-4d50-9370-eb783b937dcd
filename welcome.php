<?php
session_start();
include("connection.php");
require_once('database.php');
require_once('library.php');
isUser();

$a=$_SESSION['username'];
$sql="select * from login where username='$a'";
$result = mysqli_query($con, $sql);
$row1 = mysqli_fetch_array($result, MYSQLI_BOTH);
$userid = ($row1 && isset($row1['rid'])) ? $row1['rid'] : 0;

// Get selected date or default to today
$selected_date = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');

// Initialize dashboard variables
$booked = 0;
$in_transit = 0;
$stock = 0;
$out_for_delivery = 0;
$delivered = 0;

// Function to get counts based on date and user
function getDashboardCounts($con, $userid, $selected_date, $is_admin) {
    $counts = array(
        'booked' => 0,
        'in_transit' => 0,
        'stock' => 0,
        'out_for_delivery' => 0,
        'delivered' => 0
    );

    // If admin or userid is 0, show all records; otherwise filter by user
    if($is_admin || $userid == 0) {
        $user_condition = "";
    } else {
        $user_condition = "AND userid='$userid'";
    }

    // Booked (Status 1) - use book_date with DATE() function
    $sql = "SELECT COUNT(*) as count_status FROM tbl_courier
            WHERE status = '1' AND DATE(book_date) = '$selected_date' $user_condition";
    $result = mysqli_query($con, $sql);
    if($result && $row = mysqli_fetch_array($result)) {
        $counts['booked'] = (int)$row['count_status'];
    }

    // In Transit (Status 28) - use status_date
    $sql = "SELECT COUNT(*) as count_status FROM tbl_courier
            WHERE status = '28' AND DATE(status_date) = '$selected_date' $user_condition";
    $result = mysqli_query($con, $sql);
    if($result && $row = mysqli_fetch_array($result)) {
        $counts['in_transit'] = (int)$row['count_status'];
    }

    // Stock = Shipments with "stock at %" statuses (like branch wise report)
    // Use status_date for status-based queries (not book_date)
    $sql = "SELECT COUNT(*) as count_status FROM tbl_courier
            INNER JOIN status ON tbl_courier.status = status.statusid
            WHERE LOWER(status.statusname) LIKE 'stock at %' AND DATE(status_date) = '$selected_date' $user_condition";
    $result = mysqli_query($con, $sql);
    if($result && $row = mysqli_fetch_array($result)) {
        $counts['stock'] = (int)$row['count_status'];
    }

    // Out for Delivery - dynamically find status IDs that represent "out for delivery"
    // First get all status IDs that might represent "out for delivery"
    $delivery_status_sql = "SELECT statusid FROM status
                           WHERE statusname LIKE '%delivery%'
                           OR statusname LIKE '%out%'
                           OR statusid IN ('6', '50', '57')";
    $delivery_status_result = mysqli_query($con, $delivery_status_sql);
    $delivery_status_ids = [];

    if($delivery_status_result) {
        while($delivery_row = mysqli_fetch_array($delivery_status_result)) {
            $delivery_status_ids[] = "'" . $delivery_row['statusid'] . "'";
        }
    }

    // If no specific delivery statuses found, fall back to the original IDs
    if(empty($delivery_status_ids)) {
        $delivery_status_ids = ['6', '50', '57'];
    }

    $status_list = implode(',', $delivery_status_ids);
    $sql = "SELECT COUNT(*) as count_status FROM tbl_courier
            WHERE status IN ($status_list) AND DATE(status_date) = '$selected_date' $user_condition";
    $result = mysqli_query($con, $sql);
    if($result && $row = mysqli_fetch_array($result)) {
        $counts['out_for_delivery'] = (int)$row['count_status'];
    }



    // Delivered (Status 5) - use status_date
    $sql = "SELECT COUNT(*) as count_status FROM tbl_courier
            WHERE status = '5' AND DATE(status_date) = '$selected_date' $user_condition";
    $result = mysqli_query($con, $sql);
    if($result && $row = mysqli_fetch_array($result)) {
        $counts['delivered'] = (int)$row['count_status'];
    }

    return $counts;
}

// Check user type and get counts
$data = mysqli_query($con, "SELECT cid, userType FROM tbl_courier_officers WHERE cid='$userid'");
$is_admin = false;
$uidd = $userid;

if($rec = mysqli_fetch_row($data)) {
    $rr = $rec[1];
    if($rr == "admin") {
        $is_admin = true;
    } else {
        $uidd = $rec[0];
    }
}

$counts = getDashboardCounts($con, $uidd, $selected_date, $is_admin);
$booked = $counts['booked'];
$in_transit = $counts['in_transit'];
$stock = $counts['stock'];
$out_for_delivery = $counts['out_for_delivery'];
$delivered = $counts['delivered'];
?>
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>

	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	<title>Vivanta Logistics</title>
	<meta name="description" content="">

	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->

	<!-- Chart.js -->
	<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

	<!-- Dashboard Styles -->
	<style>
        .dashboard-container {
            background: #fff;
            padding: 20px;
            margin: 20px 0;
        }

        .dashboard-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            color: #f16325;
            border-radius: 10px;
            border: 2px solid #f16325;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .dashboard-header h1 {
            color: #f16325;
            margin: 0;
            font-size: 28px;
        }

        .dashboard-header p {
            color: #666;
            margin: 5px 0 0 0;
        }

        .date-selector {
            margin: 20px 0;
            text-align: center;
        }

        .date-selector input[type="date"] {
            padding: 8px 15px;
            border: 2px solid #f16325;
            border-radius: 5px;
            font-size: 16px;
            margin: 0 10px;
        }

        .date-selector button {
            background: #f16325;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }

        .date-selector button:hover {
            background: #d14d1f;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 15px;
            margin: 30px 0;
        }

        .stat-card {
            background: white;
            border: 2px solid #f16325;
            border-radius: 8px;
            padding: 15px 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            min-width: 0;
        }

        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }

        .stat-card h3 {
            color: #f16325;
            margin: 0 0 8px 0;
            font-size: 14px;
            font-weight: 600;
        }

        .stat-number {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin: 8px 0;
        }

        .stat-icon {
            font-size: 20px;
            color: #f16325;
            margin-bottom: 8px;
        }

        .chart-container {
            margin: 30px 0;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .chart-title {
            text-align: center;
            color: #f16325;
            margin-bottom: 20px;
            font-size: 20px;
        }

        #statusChart {
            max-height: 400px;
        }

        .current-date {
            color: #f16325;
            font-weight: bold;
            font-size: 18px;
        }

        .stat-card .btn {
            font-size: 12px;
            padding: 6px 12px;
            margin-top: 8px;
        }

        /* Responsive design for smaller screens */
        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }

            .stat-card {
                padding: 12px 8px;
            }

            .stat-number {
                font-size: 24px;
            }

            .stat-card h3 {
                font-size: 13px;
            }
        }

        @media (max-width: 480px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>

	<!-- Favicons
	================================================== -->
	
</head>
<?php
include("header.php");
?>
		<div class="container">
			<div class="row">
     			<div class="span2">
				</div><!--end span2-->

                <div class="span8" align="center">

<h2> Welcome To </h2>  <h1> Vivanta Logistics </h1>

				</div><!--end span8-->
			</div><!--end row-->

			<!-- Dashboard Section -->
			<div class="dashboard-container">
				<div class="dashboard-header">
					<h1><i class="fa fa-dashboard"></i> Logistics Dashboard</h1>
					<p>Monitor your daily logistics activity</p>
				</div>

				<div class="date-selector">
					<form method="GET" action="">
						<label for="date">Select</label>
						<input type="date" id="date" name="date" value="<?php echo $selected_date; ?>" max="<?php echo date('Y-m-d'); ?>">
						<button type="submit">Update Dashboard</button>
					</form>
					<p class="current-date">Showing data for: <?php echo date('F j, Y', strtotime($selected_date)); ?></p>
				</div>

				<div class="stats-grid">
					<div class="stat-card">
						<div class="stat-icon"><i class="fa fa-plus-circle"></i></div>
						<h3>Booked</h3>
						<div class="stat-number"><?php echo $booked; ?></div>
						<a href="inTransitReport.php?date=<?php echo $selected_date; ?>" class="btn btn-primary btn-small">View Details</a>
					</div>

					<div class="stat-card">
						<div class="stat-icon"><i class="fa fa-truck"></i></div>
						<h3>In Transit</h3>
						<div class="stat-number"><?php echo $in_transit; ?></div>
						<a href="transitReport.php?date=<?php echo $selected_date; ?>" class="btn btn-primary btn-small">View Details</a>
					</div>

					<div class="stat-card">
						<div class="stat-icon"><i class="fa fa-archive"></i></div>
						<h3>Stock</h3>
						<div class="stat-number"><?php echo $stock; ?></div>
						<a href="stockReport.php?date=<?php echo $selected_date; ?>" class="btn btn-primary btn-small">View Details</a>
					</div>

					<div class="stat-card">
						<div class="stat-icon"><i class="fa fa-road"></i></div>
						<h3>Out For Delivery</h3>
						<div class="stat-number"><?php echo $out_for_delivery; ?></div>
						<a href="outfordeliveredReport.php?date=<?php echo $selected_date; ?>" class="btn btn-primary btn-small">View Details</a>
					</div>

					<div class="stat-card">
						<div class="stat-icon"><i class="fa fa-check-circle"></i></div>
						<h3>Delivered</h3>
						<div class="stat-number"><?php echo $delivered; ?></div>
						<a href="deliveredReport.php?date=<?php echo $selected_date; ?>" class="btn btn-primary btn-small">View Details</a>
					</div>
				</div>

				<div class="chart-container">
					<h3 class="chart-title">Daily Logistics Overview</h3>
					<canvas id="statusChart"></canvas>
				</div>
			</div>
		</div><!--end conatiner-->

<script>
// Chart.js configuration for logistics dashboard
const ctx = document.getElementById('statusChart').getContext('2d');
const statusChart = new Chart(ctx, {
    type: 'bar',
    data: {
        labels: ['Booked', 'In Transit', 'Stock', 'Out For Delivery', 'Delivered'],
        datasets: [{
            label: 'Count',
            data: [
                <?php echo $booked; ?>,
                <?php echo $in_transit; ?>,
                <?php echo $stock; ?>,
                <?php echo $out_for_delivery; ?>,
                <?php echo $delivered; ?>
            ],
            backgroundColor: [
                '#f16325',
                '#f4885a',
                '#f69d7a',
                '#f8b29b',
                '#fac7bc'
            ],
            borderColor: [
                '#f16325',
                '#f4885a',
                '#f69d7a',
                '#f8b29b',
                '#fac7bc'
            ],
            borderWidth: 2,
            borderRadius: 5,
            borderSkipped: false,
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            },
            tooltip: {
                backgroundColor: '#f16325',
                titleColor: '#fff',
                bodyColor: '#fff',
                borderColor: '#f16325',
                borderWidth: 1
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1,
                    color: '#666'
                },
                grid: {
                    color: '#e0e0e0'
                }
            },
            x: {
                ticks: {
                    color: '#666'
                },
                grid: {
                    display: false
                }
            }
        },
        animation: {
            duration: 1000,
            easing: 'easeInOutQuart'
        }
    }
});
</script>

<?php
include("footer.php");
?>
