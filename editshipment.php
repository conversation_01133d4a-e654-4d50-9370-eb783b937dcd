<?php
session_start();
require 'connection.php';
require_once('database.php');
require_once('library.php');


$a = isset($_SESSION['username']) ? $_SESSION['username'] : '';
 $sql="select * from login where username='$a'";
$result = mysqli_query($con, $sql);
$row = mysqli_fetch_array($result, MYSQLI_BOTH);
$userid = ($row && isset($row['rid'])) ? $row['rid'] : 0;

$sql="select * from `tbl_courier_officers` where cid='$userid'";
$result = mysqli_query($con, $sql);
$row1 = mysqli_fetch_array($result, MYSQLI_BOTH);
$uoffice = ($row1 && isset($row1['office'])) ? $row1['office'] : '';
$uaddress = ($row1 && isset($row1['address'])) ? $row1['address'] : '';
$clrkname = ($row1 && isset($row1['Manager_name'])) ? $row1['Manager_name'] : '';


/* $statesql="SELECT * FROM state order by statename ASC";
 $stateresult=mysqli_query($con,$statesql);
 While($staterow=mysqli_fetch_array($stateresult))
{
	if($stname==$staterow['stid'])
	{
	 $statedrop=$statedrop."<option value='".$staterow['stid']."' selected>".$staterow['statename']."</option>";
	}
	else{
	  $statedrop=$statedrop."<option value='".$staterow['stid']."' >".$staterow['statename']."</option>";
	}
}*/

// Get cid parameter from GET request
$cid = isset($_GET['cid']) ? (int)$_GET['cid'] : 0;

$sql0="select * from `tbl_courier` where cid='$cid'";
$result0 = mysqli_query($con,$sql0);


  while ($row0 = mysqli_fetch_assoc($result0)){
     
 $shweta=$row0['shipper_code'];
     


     
 }
  
//echo $Bookingmod = $_GET['Bookingmode'];

$query = "SELECT MAX(cid) AS custid FROM tbl_courier";  
    if($result = mysqli_query($con,$query))
    {
  while ($row = mysqli_fetch_assoc($result))
  {
        $count = $row['custid'];
  }
	}

// $cid already defined above
 $sqlx="select * from `tbl_courier` where cid='$cid'";
$resultx = mysqli_query($con,$sqlx);


 while($rowx=mysqli_fetch_array($resultx)){
      $status=$rowx['status'];
      
    $city=$rowx['r_city'];   
  $consno=$rowx['cons_no'];  $shipame=$rowx['ship_name'];  $phone=$rowx['phone'];  $s_add=$rowx['s_add']; 
  $ewayexpdate=$rowx['eway_expdate']; 
  $smail = isset($rowx['smail']) ? $rowx['smail'] : '';
  $custgst = isset($rowx['custgst']) ? $rowx['custgst'] : '';
  $custpan = isset($rowx['custpan']) ? $rowx['custpan'] : '';
  $custzip = isset($rowx['custzip']) ? $rowx['custzip'] : '';
   $rev_name=$rowx['rev_name'];  $r_phone=$rowx['r_phone'];  $rmail=$rowx['rmail'];  $r_add=$rowx['r_add']; 
    $r_zip=$rowx['r_zip'];  $type1=$rowx['type'];  $weight=$rowx['weight'];  $chweight=$rowx['chweight']; 
    $invoice_no=$rowx['invice_no'];  $qty=$rowx['qty']; $frieght=$rowx['freight']; 
     $clerkcon=$rowx['clerkcon'];  $clerkname=$rowx['clerkname'];    $book_mode=$rowx['book_mode'];  $invi_value=$rowx['invi_value']; 
      $shidesc=$rowx['shidesc'];  $mode=$rowx['mode'];$delivery_type=$rowx['delivery_type'];  $book1_date=$rowx['book1_date']; 
       $type=$rowx['colorRadio'];$rate=$rowx['rate']; $delivery_charge=$rowx['delivery_charge'];   
       $fov=$rowx['fov_charge']; $other_charge=$rowx['other_charge']; 
     
       
        $noofpackage=$rowx['noofpackage'];  $dodcod=$rowx['dod_cod'];  $doccharge=$rowx['dock_charg'];  $odamis=$rowx['oda_mis'];   $assureddlydate=$rowx['assured_dly_date'];  $clerkname=$rowx['clerkname']; $shipname=$rowx['ship_name'];
         $partno=$rowx['partno'];  $rccode=$rowx['rccode'];  $ewaybill=$rowx['e_waybill']; $VehNo=$rowx['vehicle']; $rcgst=$rowx['rcgst']; 
 $insurance=$rowx['insurance'];$gtotal=$rowx['gtotamt'];
 $handlingcharge=$rowx['handlingcharge'];$rovcharge =$rowx['rovcharge'];
 $esscharge=$rowx['esscharge'];$dph =$rowx['aftcharge']; $shipadd =$rowx['shipadd'];$recadd =$rowx['recadd'];

// Calculate total from all charges
$total = (float)$dodcod + (float)$frieght + (float)$rovcharge + (float)$dph + (float)$esscharge + (float)$handlingcharge + (float)$doccharge + (float)$delivery_charge + (float)$fov + (float)$other_charge;

 $gst=$rowx['gst'];
 $cgst=$gst/2;
 $sgst=$gst/2;
     
     

	  /*$state1=$rowx['r_states'];
		$sqlstate1="SELECT * FROM `state` where stid=$state1";
		$stateresult1= mysqli_query($con,$sqlstate1);
		$row1=mysqli_fetch_array($stateresult1);*/
	 $state = isset($row1['statename']) ? $row1['statename'] : '';

// Initialize variables to prevent undefined variable warnings
$state1 = isset($rowx['r_states']) ? $rowx['r_states'] : 0;
$statelist = "";

//===================================================
// to populate Selected state in dropdown
		$sqlstate="SELECT * FROM `state`  order by statename ASC";
		$stateresult= mysqli_query($con,$sqlstate);
                //echo $state1;
		while($row34=mysqli_fetch_array($stateresult)){
               // echo "inwhile".$row34['stid'];
		if($state1==$row34['stid']){
			$statelist=$statelist."<option value='".$row34['stid']."' selected>".$row34['statename']."</option>";
		}else{
			 $statelist=$statelist."<option value='".$row34['stid']."'>".$row34['statename']."</option>";
		}
	}

//====================================	

		 /*$sqlcity1="SELECT * FROM `city` where ctid=$city";
		$cityresult1= mysqli_query($con,$sqlcity1);
		$row2=mysqli_fetch_array($cityresult1);*/
   //$city=$row2['cityname'];
		//===================================================
// to populate Selected cITY in dropdown
		$sqlcity="SELECT * FROM `city` where stid='$state1' order by cityname ASC";

                $cityresult= mysqli_query($con,$sqlcity);
                $citylist = ""; // Initialize variable
                //echo $city;
		while($row43=mysqli_fetch_array($cityresult)){
               // echo "inwhile".$row43['ctid'];
		if($city==$row43['ctid']){
			$citylist=$citylist."<option value='".$row43['ctid']."' selected>".$row43['cityname']."</option>";
		}else{
			 $citylist=$citylist."<option value='".$row43['ctid']."'>".$row43['cityname']."</option>";
		}
	}     
   
   
    $code=$rowx['shipper_code'];
		 $sqlcode1="SELECT * FROM `custreg` where custid=$code";  
		$coderesult1= mysqli_query($con,$sqlcode1);
		$row2=mysqli_fetch_array($coderesult1);
		   $custname = (isset($row2['custcode']) ? $row2['custcode'] : '') .
		              (isset($row2['custname']) ? $row2['custname'] : '') .
		              (isset($row2['cityname']) ? $row2['cityname'] : '');
		
	/*	if($a!="admin") {
	$sql = "SELECT * FROM `custreg` where userid='$userid' ORDER BY custname ASC";
}
else{
	$sql = "SELECT * FROM `custreg` ORDER BY custname ASC";
}*/
	$sql = "SELECT * FROM `custreg` ORDER BY custname ASC";
$result2=mysqli_query($con,$sql);
$drop1="";
while($row2=mysqli_fetch_array($result2))
{
	if($code==$row2['custid'])
	{
	$custcode = isset($row2['custcode']) ? $row2['custcode'] : '';
	$custname = isset($row2['custname']) ? $row2['custname'] : '';
	$cityname = isset($row2['cityname']) ? $row2['cityname'] : '';
	$drop1=$drop1."<option value='".$row2['custid']."' selected>".$custcode."-".$custname."-".$cityname."</option>";
	}
	else{
	$custcode = isset($row2['custcode']) ? $row2['custcode'] : '';
	$custname = isset($row2['custname']) ? $row2['custname'] : '';
	$cityname = isset($row2['cityname']) ? $row2['cityname'] : '';
	$drop1=$drop1."<option value='".$row2['custid']."' >".$custcode."-".$custname."-".$cityname."</option>";
	}
}


// tbl_city_code for receiver
 $sqlcity12="SELECT * FROM `tbl_city_code` where Id=$r_add";
		$cityresult12= mysqli_query($con,$sqlcity12);
		$row8=mysqli_fetch_array($cityresult12);
     $city2 = ($row8 && isset($row8['city_name'])) ? $row8['city_name'] : '';
 	 $statesql12="SELECT * FROM tbl_city_code";
 $cityresult2=mysqli_query($con,$statesql12);
 $citydrop2 = ""; // Initialize variable
 while($staterow2=mysqli_fetch_array($cityresult2))
{
    //echo "inwhile".$staterow['Id'];
	if($r_add==$staterow2['Id'])
	{
	 $citydrop2=$citydrop2."<option value='".$staterow2['Id']."' selected>".$staterow2['city_name']."-".$staterow2['city_code']."</option>";
	}
	else{
	  $citydrop2=$citydrop2."<option value='".$staterow2['Id']."' >".$staterow2['city_name']."-".$staterow2['city_code']."</option>";
	}
}

// tbl_city_code for shipper
 $sqlcity1="SELECT * FROM `tbl_city_code` where Id=$s_add";
		$cityresult1= mysqli_query($con,$sqlcity1);
		$row8=mysqli_fetch_array($cityresult1);
     $city1 = ($row8 && isset($row8['city_name'])) ? $row8['city_name'] : '';
 	 $statesql="SELECT * FROM tbl_city_code";
 $cityresult=mysqli_query($con,$statesql);
 $citydrop = ""; // Initialize variable
 while($staterow=mysqli_fetch_array($cityresult))
{
    //echo "inwhile".$staterow['Id'];
	if($s_add==$staterow['Id'])
	{
	 $citydrop=$citydrop."<option value='".$staterow['Id']."' selected>".$staterow['city_name']."-".$staterow['city_code']."</option>";
	}
	else{
	  $citydrop=$citydrop."<option value='".$staterow['Id']."' >".$staterow['city_name']."-".$staterow['city_code']."</option>";
	}
}
		          

		  $rccode=$rowx['rccode'];
		$sqlcode="SELECT * FROM `receiver_reg` where receiver_code=$rccode";  
		$coderesult1= mysqli_query($con,$sqlcode);
		$row2=mysqli_fetch_array($coderesult1);
		$rcode = ($row2 && isset($row2['receiver_code'])) ? $row2['receiver_code'] : '';
		echo $rcode;
		
	/*	if($a!="admin") {
	$sql1 = "SELECT * FROM `receiver_reg` where userid='$userid' ORDER BY receiver_code ASC";
}
else{
	$sql1 = "SELECT * FROM `receiver_reg` ORDER BY receiver_code ASC";
}*/
	$sql1 = "SELECT * FROM `receiver_reg` ORDER BY receiver_code ASC";
$result3=mysqli_query($con,$sql1);
$drop2="";
while($row2=mysqli_fetch_array($result3))
{
	if($rccode==$row2['id'])
	{
	$receiver_code = isset($row2['receiver_code']) ? $row2['receiver_code'] : '';
	$receivername = isset($row2['receivername']) ? $row2['receivername'] : '';
	$cityname = isset($row2['cityname']) ? $row2['cityname'] : '';
	$drop2=$drop2."<option value='".$row2['id']."' selected>".$receiver_code."-".$receivername."-".$cityname."</option>";
	}
	else{
	$receiver_code = isset($row2['receiver_code']) ? $row2['receiver_code'] : '';
	$receivername = isset($row2['receivername']) ? $row2['receivername'] : '';
	$cityname = isset($row2['cityname']) ? $row2['cityname'] : '';
	 $drop2=$drop2."<option value='".$row2['id']."' >".$receiver_code."-".$receivername."-".$cityname."</option>";
	}
}

 }
 
 
 
?>
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>

	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	<title>Vivanta Logistics</title>
	<meta name="description" content="">
	<meta name="author" content="Ahmed Saeed">
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
	
<script type="text/JavaScript">

function MM_findObj(n, d) { //v4.01
  var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {
    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}
  if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];
  for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);
  if(!x && d.getElementById) x=d.getElementById(n); return x;
}

function MM_validateForm() { //v4.0
  var i,p,q,nm,test,num,min,max,errors='',args=MM_validateForm.arguments;
  for (i=0; i<(args.length-2); i+=3) { test=args[i+2]; val=MM_findObj(args[i]);
    if (val) { nm=val.name; if ((val=val.value)!="") {
      if (test.indexOf('isEmail')!=-1) { p=val.indexOf('@');
        if (p<1 || p==(val.length-1)) errors+='- '+nm+' must contain an e-mail address.\n';
      } else if (test!='R') { num = parseFloat(val);
        if (isNaN(val)) errors+='- '+nm+' must contain a number.\n';
        if (test.indexOf('inRange') != -1) { p=test.indexOf(':');
          min=test.substring(8,p); max=test.substring(p+1);
          if (num<min || max<num) errors+='- '+nm+' must contain a number between '+min+' and '+max+'.\n';
    } } } else if (test.charAt(0) == 'R') errors += '- '+nm+' is required.\n'; }
  } if (errors) alert('The following error(s) occurred:\n'+errors);
  document.MM_returnValue = (errors == '');
}
function fun2()
{
var a=document.getElementById("rccode").value;
//alert(a);
obj=new XMLHttpRequest();
obj.open("GET","ajaxgetrcDetails.php?a="+a,true);
obj.send();
obj.onreadystatechange=funca1
}
function fun()
{
var a=document.getElementById("sname").value;
//alert(a);
obj=new XMLHttpRequest();
obj.open("GET","ajaxgetCustDetails.php?a="+a,true);
obj.send();
obj.onreadystatechange=funca
}
function funca()
{
   if(obj.readyState==4)
     {
	vala=obj.responseText;
	//alert(vala);
	var res = vala.split("*");
		//alert(res[0]);
		document.getElementById("Shippername").value=res[1];
		document.getElementById("Shipperphone").value=res[2];
		document.getElementById("Shipperemail").value=res[3];
		document.getElementById("Shipperadd").value=res[4];
		document.getElementById("custzip").value=res[5];
	//	document.getElementById("custin").value=res[8];
	//	document.getElementById("cstin").value=res[9];
		document.getElementById("custgst").value=res[10];
		document.getElementById("custpan").value=res[11];
		
		}	
}

function funca1()
{
   if(obj.readyState==4)
     {
	vala=obj.responseText;
	//alert(vala);
	var res = vala.split("*");
		//alert(res[0]);
		document.getElementById("Receivername").value=res[3];
		document.getElementById("Receiveradd").value=res[6];
		document.getElementById("Receiverphone").value=res[4];
		document.getElementById("Receiveremail").value=res[5];
		document.getElementById("rzip").value=res[7];
		document.getElementById("gstno").value=res[10];
		document.getElementById("rcpan").value=res[11];
		//document.getElementById("custstax").value=res[10];
		//document.getElementById("custpan").value=res[11];
		
		}	
}
function fun1()
{ alert("hiii"); }
</script>

</head>
<?php
include("header.php");
?>
			<?php
	$qs=mysqli_query($con,"select cons_no from tbl_courier where cid='$count'");
	while($re=mysqli_fetch_row($qs))
	{
		$ree=$re[0];
	}
if(isset($_SESSION['sav']))
{
	echo "<div align='center'><h3>Courier is added successfully.
							  Your Registration No is: <b style='color:red'>$count </b>& Con no. is : <b style='color:red'>$ree</b></h3></div>";
	echo "<br>";
	unset($_SESSION['sav']);
}
if(isset($_SESSION['b']))
{
	
	echo "<div align='center'><h3>Courier is added successfully.
							  Your Registration No is: <b style='color:red'>$count </b>& Con no. is : <b style='color:red'>$ree</b></h3></div>";
	echo "<br>";
	unset($_SESSION['b']);
}
?>
<div class="container">
                <div class="span12">
					<div class="register">
						
                         <div class="titleHeader clearfix">
		                    	<h3>Book Shipment </h3>
					     </div>
		<!--<form action="process.php?action=add-cons" method="post" class="form-horizontal" name="bookshipment">-->
<form action="saveBillPrint_update.php" method="post" class="form-horizontal" name="bookshipment">
			 <div class="row">	
				<div class="span6">
						<center><legend>&nbsp;&nbsp;&nbsp;&nbsp;Shipper info :</legend></center>
                <input type="hidden" name="id" id="id" value="<?php echo $cid;?>">
						<div class="control-group">
					         <div class="controls">
                                  <label class="control-label"> <input type="radio" name="colorRadio" value="account" onclick="paymentMode(this);" id="accont" <?php if($type=="account") echo 'checked="checked"';  ?> required > Account Party </label>
                                  <label class="control-label"> <input type="radio" name="colorRadio" value="cash" onclick="paymentMode(this);" id="cash" <?php if($type=="cash") echo 'checked="checked"';  ?> required > Cash Party </label>
							</div>
							<div class="clearfix"></div>
                            <br>
							<br>



	                            
							    <label class="control-label">Shipper Code: <span class="text-error">*</span> </label> 
							        <div class="controls">
									    <select name="sname" id="sname"   onchange="fun(this);to(this);">
									    <option onSelect="fun()" >-- Please Select --</option>
												<?php echo $drop1; ?>
								        </select>
	                                </div>
							     <br>	
						
                      
                        <?php  if($type=='account'){ ?>
                        	<div class="control-group ">
							    <label class="control-label">Payment Mode: <span class="text-error">*</span> </label> 
       						    <div class="controls"> 
	                                <select  name="Bookingmode" id="Bookingmode">
	                                    <option selected="selected" value="">--Select Payment Mode--</option>
									     <option value="TBB" <?php if($book_mode == "TBB") echo "selected"; ?>>TBB</option>
										 
									</select>
	                            </div>	 
							</div>
							  <?php } ?>
                        
                        	</div>
                        	<?php  if($type=='cash'){ ?>
							<div class="control-group ">
							    <label class="control-label">Payment Mode: <span class="text-error">*</span> </label> 
       						    <div class="controls"> 
	                                <select name="Bookingmode" id="Bookingmode">
										 <option selected="selected" value="">--Select Payment Mode--</option>
									     <option value="Paid" <?php if($book_mode == "Paid") echo "selected"; ?> >Paid</option>
									     <option value="ToPay" <?php if($book_mode == "ToPay") echo "selected"; ?>>ToPay</option>
									     <option value="topaycod" <?php if($book_mode == "ToPay&COD") echo "selected"; ?>>ToPay & COD</option>
									     <option value="FOC" <?php if($book_mode == "FOC") echo "selected"; ?>>FOC</option><!---->
									</select>
	                            </div>	 
							</div>
							 <?php } ?>
                             <div class="control-group " id="insurances" >
							    <label class="control-label">Insurance(%): <span class="text-error">*</span></label> 
							        <div class="controls">
										<select name="insuran" id="insuran" onchange="insur(this.value)">
											<option value="Select" selected="selected">-- Select--</option>
											<option value="ys" <?php if($insurance == "ys") echo "selected"; ?>>Yes</option>
											<option value="no"<?php if($insurance == "no") echo "selected"; ?>> No</option>
												<option value="carrierisk" <?php if($insurance == "carrierisk") echo "selected"; ?>>Carrier Risk</option>
											<option value="yor" <?php if($insurance == "yor") echo "selected"; ?>>Your OwnRisk</option>
										</select>
									  <input type="text" name="insurance" id="insurance" style="display:none" placeholder="Insurance in Percent ">
							    </div>
							</div>
							

						<div class="control-group ">
							    <label class="control-label">Shipper Name : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="Shippername" id="Shippername" placeholder="Shipper Name" value="<?php echo $shipname; ?>">
							     <!-- <span class="help-inline"><i class="icon-ok"></i> Avaliable input!</span>-->
							    </div>
							</div><!--end control-group-->
							
								<div class="control-group ">
							    <label class="control-label" for="Shipperaddress">Shipper Address : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="Shipperadd" id="Shipperadd" placeholder="Shipper Address" value="<?php echo $shipadd;?>">
							     <!--< <span class="help-inline">-->
							    </div>
							</div><!--end control-group   <i class="icon-remove"></i> Invalid input!</span>-->
							<div class="control-group ">
							    <label class="control-label" for="Shipperaddress">Shipper City : <span class="text-error">*</span></label>
							    <div class="controls">
							      <select name="Shipperaddress" id="Shipperaddress" value="<?php 
							      echo $city1; ?>" required>
							          <option value="">-- Select--</option>
							     	<?php echo $citydrop; ?>
							     </select>
							    </div>
							</div><!--end control-group   <i class="icon-remove"></i> Invalid input!</span>-->
                           
							      <!--<span class="help-inline"><i class="icon-ok"></i> Avaliable input!</span>-->
							 
							<div class="control-group">
							    <label class="control-label" for="Shipperphone">Contact : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="Shipperphone" id="Shipperphone" placeholder="Mobile No" value="<?php echo $phone;?>">
							    </div>
							</div><!--end control-group-->
							<div class="control-group">
							    <label class="control-label">E-Mail :</label>
							    <div class="controls">
							      <input type="email" name="Shipperemail" id="Shipperemail" placeholder="<EMAIL>" value="<?php echo $smail; ?>">
							    </div>
							</div><!--end control-group-->
						<!--end control-group-->
							
						<!--end control-group-->

                                                       <div class="control-group success">
							    <label class="control-label" for="custzip">Zip Code: </label>
							    <div class="controls">
							      <input type="text" name="custzip"  id="custzip" placeholder="Zip Code" value="<?php echo $custzip; ?>">
							      <!--<span class="help-inline"><i class="icon-ok"></i> Avaliable input!</span>-->
							   </div>
							</div>
							<div class="control-group">
							    <label class="control-label" for="custstax">GST No: </label>
							    <div class="controls">
							      <input type="text" name="custgst" id="custgst" placeholder="GST No"  value="<?php echo $custgst; ?>" >
							    </div>
							</div><!--end control-group-->
						
					</div>  
	
						<center><legend >&nbsp;&nbsp;&nbsp;&nbsp;Receiver info :</legend></center>
                      <div class="span5"> 
                      <div class="control-group ">

		<label class="control-label"> <input type="radio" name="colorRadio" value="walkin" onclick="paymentMode(this);" <?php if($type=="walkin") echo 'checked="checked"';  ?> required> Walk In Customer </label>

					

							    

							</div>
							<br>
							
                      <div class="control-group ">
							    <label class="control-label" for="Receiveraddress">Receiver Code : <span class="text-error">*</span></label>
							    <div class="controls">
							     <select name="rccode" id="rccode" onchange="fun2(this);to(this);"   >
									  <option onSelect="fun2()" value="">-- Please Select --</option>
												<?php echo $drop2; ?>
								        </select>
							     <!--< <span class="help-inline">-->
							    </div>
							</div>
							<div class="control-group success">
							    <label class="control-label" for="Receivername">Receiver Name : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="Receivername" id="Receivername" placeholder="Receiver Name" value="<?php echo $rev_name;?>">
							      <!--<span class="help-inline"><i class="icon-ok"></i> Avaliable input!</span>-->
							    </div>
							</div>
							
							<div class="control-group ">
							    <label class="control-label" for="Receiveraddress">Receiver Address : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="Receiveradd" id="Receiveradd" placeholder="Receiver Address"  value="<?php echo $recadd; ?>">
							     <!--< <span class="help-inline">-->
							    </div>
							</div><!--end control-group-->
								<div class="control-group ">
							    <label class="control-label" for="Receiveraddress">Receiver City : <span class="text-error">*</span></label>
							    <div class="controls">
							      <select name="Receiveraddress" id="Receiveraddress" value="<?php echo $city2; ?>" required><option value="">-- Select--</option>
							          <?php echo $citydrop2; ?>
							          </select>
							     <!--< <span class="help-inline">-->
							    </div>
							</div><!--end control-group-->
							<div class="control-group">
							    <label class="control-label" for="Receiverphone">Contact No: <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="Receiverphone" id="Receiverphone" placeholder="Mobile No"  value="<?php echo $r_phone; ?>">
							    </div>
							</div><!--end control-group-->
							<div class="control-group">
							    <label class="control-label" for="Receiveremail">E-Mail : </label>
							    <div class="controls">
							      <input type="email" name="Receiveremail" id="Receiveremail" placeholder="<EMAIL>" value="<?php echo $rmail;?>">
							    </div>
							</div><!--end control-group-->
							<!--end control-group   <i class="icon-remove"></i> Invalid input!</span>-->
							<div class="control-group">
							<label class="control-label" > States : </label>
							    <div class="controls">
								<select name="rstates" id="rstates" value="<?php echo $state; ?>" onchange="cityfun()"  >
									     <option  value="">-- Please Select --</option>
												<?php echo $statelist; ?>
								</select>
							      
							    </div>
						</div><!--end control-group-->

					

                            <div class="control-group success">
							    <label class="control-label" for="rzip">Zip Code: </label>
							    <div class="controls">
							      <input type="text" name="rzip" id= "rzip" placeholder="Zip Code"  value="<?php echo $r_zip; ?>">
							      <!--<span class="help-inline"><i class="icon-ok"></i> Avaliable input!</span>-->
							    </div>
							</div><!--end control-group-->
                             <!--end control-group-->
 
                       <div class="control-group">
							    <label class="control-label" for="rcstin">GST No: </label>
							    <div class="controls">
							      <input type="gstno" name="gstno" id="gstno" placeholder="GST No"  value="<?php echo $rcgst; ?>" >
							    </div>
							</div><!--end control-group--> 
                </div>  
             </div>
		   
		     <div class="row">	
			          
						<center>	<legend >&nbsp;&nbsp;&nbsp;&nbsp;Shipment info :  </legend></center>








	<div class="control-group">

					         <!--<div class="controls">

						 <label class="control-label"> <input type="radio" name="colorRadio" value="account" onclick="manually(this);" > Manually </label>

						 </div> </div>-->
						 <br>
						 
						 <div class="span12">
						     <div class="control-group">

								    <label class="control-label"> Shipment Type  : <span class="text-error">*</span></label>

								    <div class="controls">

								      <label class="control-label">
								          <input type="radio" name="shipmentType" value="regular" onclick="handleShipmentTypeChange()"> Regular
								      </label>
								      <label class="control-label">
								          <input type="radio" name="shipmentType" value="transfer" onclick="handleShipmentTypeChange()"> Transfer
								      </label>

								    </div>

							</div>
							<div class="clearfix"></div>
							<br>



							
<div id="transferDiv" style="display: none; width: 890px; min-height: 200px; border: 2px solid black; padding: 15px; background-color: #f9f9f9; border-radius: 6px;">
    <!-- Type Dropdown -->
    <div class="control-group" style="margin-bottom: 1element.style5px;">
        <label class="control-label" for="transferTypes">Type: <span class="text-error">*</span></label>
        <div class="controls">
            <select name="transferTypes" id="transferTypes" onchange="handleTransferTypeChange()" style="width: 200px;">
                <option value="" selected="selected">-- Select Type --</option>
                <option value="inward">Inward</option>
                <option value="outward">Outward</option>
            </select>
        </div>
    </div>

    <!-- Inward Part Details Section -->
    <div id="transferInwardSection" style="display: none; margin-top: 20px;">
        <div style="margin-bottom: 20px;">
            <div class="transfer-row-flex" style="display: flex; gap: 10px; align-items: center; margin-bottom: 10px;">
                <label style="min-width: 80px;">Part Details:</label>
                <input type="text" name="transfer_part_no[]" placeholder="Part No" style="width: 200px;">
                <input type="text" name="transfer_quantity[]" placeholder="Quantity" style="width: 200px;">
                <input type="text" name="transfer_packages[]" placeholder="No. of Packages" style="width: 200px;">
            </div>
            <div id="transferRowsContainer"></div>
            <div style="margin-top: 10px;">
                <button type="button" id="addRowBtn" class="btn" onclick="addTransferRow()" style="background-color: white; color: black; padding: 8px 16px; border: none; border-radius: 4px;">Add Row</button>
            </div>
        </div>
    </div>

    <!-- Outward Part Details Section -->
    <div id="transferOutwardSection" style="display: none; margin-top: 20px;">
        <div style="margin-bottom: 20px;">
            <div class="outward-row-flex" style="display: flex; gap: 10px; align-items: center; margin-bottom: 10px;">
                <label style="min-width: 80px;">Part Details:</label>
                <select name="outward_part_no[]" style="width: 200px;" onchange="fetchPartDetails(this.value, this)">
                    <option value="">-- Select Part No --</option>
                    <?php echo $partDropdown; ?>
                </select>
                <input type="text" name="outward_packages[]" placeholder="No. of Packages" style="width: 200px;">
                <input type="text" name="outward_quantity[]" placeholder="Quantity" style="width: 200px;">
            </div>
            <div id="outwardRowsContainer"></div>
            <div style="margin-top: 10px;">
                <button type="button" id="addOutwardRowBtn" class="btn" onclick="addOutwardRow()" style="background-color: white; color: black; padding: 8px 16px; border: none; border-radius: 4px;">Add Row</button>
            </div>
        </div>
    </div>
</div>


	                      <br> 
	                      <br>
						 </div>
						 













                      <div class="span6">        
							   
							 <div class="control-group ">
							    <label class="control-label" for="ConsignmentNo"> Docket No  : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="ConsignmentNo" id="ConsignmentNo" placeholder="Docket No" onblur="consignmen()" value="<?php echo $consno; ?>" readonly>
							      
							    </div>
							</div>
							<!--<div class="control-group">
							     <input type="hidden" name="uoffice1" id="uoffice" value="<?php echo $uoffice;?> ">
						    	<input type="hidden" name="uaddress" value="<?php echo $uaddress;?>">
								<input type="hidden" name="cid" value="<?php echo $userid;?>">  
							    <label class="control-label" for="uoffice">From  : </label>
							    <div class="controls">
								<input type="text" name="uoffice" id="uoffice" value="<?php echo $uoffice;?>,<?php echo $uaddress;?>" readonly>
							      
							      
							    </div>
							</div>-->
							
						
									
								
							
							
							 <div class="control-group ">
							    <label class="control-label" for="PartNo">Part No  : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="PartNo" id="PartNo" placeholder="Part No" onblur="consignmen()" value="<?php echo $partno;?>">
							      
							    </div>
							</div>
							
								 <div class="control-group ">
							    <label class="control-label" for="No. Of Packages"> No. Of Packages  : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="noofpackages" id="noofpackages" placeholder="No. Of Packages" onblur="consignmen()" value="<?php echo $noofpackage;?>">
							      
							    </div>
							</div>
						
						
								  <input type="hidden" name="rate1" id="rate1" placeholder="Rate " >
							 
							
							<!--end control-group-->  
							<!--<div class="control-group" >
							    <label class="control-label" for="desti">Rate / Unit :<span class="text-error">*</span> </label>
							    <div class="controls">
							     <input type="text" class="span2" name="rate" id="rate"  placeholder="rate" >
								<input type="text" class="span1" name="unit1" id="unit1"  onchange ="hideqty(this.value)" placeholder="ftl"  >
								 </div>
								 
							</div>--><!--end control-group-->  

							
							<!--end control-group-->
                                                        
                                                     

                                                         <!--<div class="control-group">
							    <label class="control-label" for="round">Round :</label>
							    <div class="controls">
							        <select name="round" id="round">
									    <option selected="selected" value="">--Select Round--</option>
										<option value="sigl">Single</option>     
										<option value="round">Round</option>   
										
									</select>
			     			    </div>
							</div>--><!--end control-group-->    

							<div class="control-group">
							    <label class="control-label" for="Invoiceno">Invoice no : <span class="text-error">*</span>	</label>
							    <div class="controls">
							      <input type="text" name="Invoiceno" id="Invoiceno" placeholder="invoice No"  value="<?php echo $invoice_no; ?>">
							    </div>
							</div><!--end control-group-->
							<div class="control-group">
							    <label class="control-label" for="invalue">Invoice Value : </label>
							    <div class="controls">
							      <input type="text" name="invalue" id="invalue" placeholder="Invoice Value"   value="<?php echo $invi_value; ?>">
							    </div>
							</div><!--end control-group-->
						   <div class="control-group">
							    <label class="control-label" for="Mode">Booking Mode :<span class="text-error">*</span></label>
							    <div class="controls">
							        <select name="Mode" id="Mode" value="<?php echo $mode; ?>">
									    <option selected="selected" value="">--Select Booking Type--</option>
										<option value="Air" <?php if($mode == "Air") echo "selected"; ?>>Air Express</option>     
										<option value="Road" <?php if($mode == "Road") echo "selected"; ?>>Surface Express</option>   
										<option value="Train" <?php if($mode == "Train") echo "selected"; ?>>Train</option> 
										<option value="International" <?php if($mode == "International") echo "selected"; ?>>International</option>       
										<option value="Sea" <?php if($mode == "Sea") echo "selected"; ?>>Sea</option>       
									</select>
			     			    </div>
							</div><!--end control-group-->
							 <div class="control-group">
							    <label class="control-label" for="delivery">Delivery Type :<span class="text-error">*</span></label>
							    <div class="controls">
							        <select name="delivery" id="delivery">
									    <option selected="selected" value="">-- Select Type --</option>
										<option value="door" <?php if($delivery_type=="door") echo "selected"?>>Door Delivery </option>     
										<option value="gdown" <?php if($delivery_type=="gdown") echo "selected"?>>Godown Delivery</option>   
									</select>
			     			    </div>
							</div><!--end control-group-->
												
							
						
							
							<div class="control-group">
							    <label class="control-label" for="Shiptype">Type of Packing : <span class="text-error">*</span></label>
							    <div class="controls">
							        <select name="Shiptype" id="Shiptype">
									    <option selected="selected" value="">--Please Select--</option>
										<option value="CTN-BOX" <?php if($type1 == "CTN-BOX") echo "selected"; ?>>CTN-BOX</option>     
										<option value="WB-BOX" <?php if($type1 == "WB-BOX") echo "selected"; ?>>WB-BOX</option>   
										<option value="Loose-BOX" <?php if($type1 == "Loose-BOX") echo "selected"; ?>>Loose BOX</option> 
										<option value="Gany-BOX" <?php if($type1 == "Gany-BOX") echo "selected"; ?>>Gany Box</option>       
									   
									</select>
			     			    </div>
							</div><!--end control-group-->
						
							<div class="control-group">
							    <label class="control-label" for="descript">Shipment Description:</label>
							    <div class="controls">
                                   <input type="text" name="descript" id="descript" placeholder="Shipment Description" value="<?php echo $shidesc; ?>">
								  
							        <span class="help-inline">
							       
							    </div>
							</div><!--end control-group-->
						<!--end control-group-->
							<div class="control-group " id="Weight1">
							    <label class="control-label" for="Weight"> Actual Weight : </label>
							    <div class="controls">
                                  <input type="text" name="Weight" id="Weight" placeholder="Actual Weight in KG" onkeyup="getValues1()"  value="<?php echo $weight; ?>"> </div>
							</div>
                                   <div class="control-group " id="cweight1">
							    <label class="control-label" for="Chargeable Weight"> Chargeable Weight : </label>
							    <div class="controls">
							      <input type="text" name="cweight" id="cweight" placeholder="Chargeable Weight in KG" onkeyup="getValues1()" value="<?php echo $chweight; ?>">  
							     
							  
							      <span class="help-inline">
							    </div>
							</div><!--end control-group-->
                           <!--end control-group-->  

                                 <!--<div class="control-group" id="volwem1">  
                                     <label class="control-label" for="Comments">Volumetric Weight :</label>								 
					                <div class="controls-row"  >
										<input type="text" class="span1" name="wei" id="wei"  placeholder="weight" onkeyup="getValues()"/>
										<input type="text" class="span1" name="len" id="len" placeholder="lenght" onkeyup="getValues()"/>
										<input type="text" class="span1" name="hei" id="hei"  placeholder="height" onkeyup="getValues()"/>
										<input type="text" class="span1" name="tot" id="tot" value="0"onkeyup="getValues1()"/>
									</div>
							     </div>-->
							     	<div class="control-group">
								      <label class="control-label" for="Vehicle">Vehicle No.:<span class="text-error">*</span> </label>
							    
							    <div class="controls">
							   <input type="text" name="vehicleNo" id="vehicleNo" placeholder="Vehicle No." Value="<?php echo $VehNo; ?>"/>
							    </div>
							       </div>
	 	<div class="control-group">
							    <label class="control-label" for="clerkname">Staff Name: </label>
							    <div class="controls">
							      <input type="text" name="clerkname" placeholder="Clerk Name " Value="<?php echo $clerkname; ?>" >
							    </div>
							</div><!--end control-group--> 
                             <div class="control-group">
							    <label class="control-label" for="clerkno">Staff Contact No: </label>
							    <div class="controls">
							      <input type="text" name="clerkno" placeholder="Clerk Contact No"  Value="<?php echo $clerkcon; ?>" >
							    </div>
							<!--end control-group--> 
                            </br></br></br></br>	</br></br>		</br></br>		</br></br>	</br></br>	</br></br>	
							       <span class="help-inline">
							</div>
				    <div class="control-group">
							    <label class="control-label" for="Packupdate">Booking Date  :</label>
							    <div class="controlsa">
							      
										 <div class="controls input-append date form_date" data-date="" data-date-format="dd MM yyyy hh:mm:ss" data-link-field="dtp_input2" data-link-format="yyyy-mm-dd">
											<input type="text" name="Packupdate" id="Packupdate"  value="<?php echo $book1_date; ?>"  required readonly>
											<span class="add-on"><i class="icon-remove"></i></span>
											<span class="add-on"><i class="icon-th"></i></span>
										</div>
										     <input type="hidden" id="dtp_input2" value="" />
							    </div>
							    </div>
							    <div class="control-group">
							    <label class="control-label" for="Packupdate">Assured Dly Date  :</label>
							    <div class="controlsa">
							      
										 <div class="controls input-append date form_date" data-date="" data-date-format="dd MM yyyy hh:mm:ss" data-link-field="dtp_input2" data-link-format="yyyy-mm-dd">
											<input type="text"name="Packupdate1" id="Packupdate1" required readonly  Value="<?php echo $assureddlydate; ?>">
											<span class="add-on"><i class="icon-remove"></i></span>
											<span class="add-on"><i class="icon-th"></i></span>
										</div>
										     <input type="hidden" id="dtp_input2" value="" />
							    </div>
							</div>
							
							<div class="control-group">
<label class="control-label" for="amt">User Id </label>
<div class="controls">
<input type="number" name="cid" id="cid" value="<?php echo $_SESSION['desgn']; ?>" readonly>
</div></div>
							
							<!--end control-group-->
			</div>	
			
			
			
			<div class="span4">	
			 <div class="control-group" id="asdf">
							    <label class="control-label" for="E-Way-Bill">E-Way Bill:<span class="text-error">*</span> </label>
							    <div class="controls">
							      <input type="text" name="ewaybill" id="ewaybill" placeholder="E-Way Bill"  Value="<?php echo $ewaybill; ?>" /><br>
								 </div>
								 
							</div></div>
							<div class="span4">	
								<div class="control-group">
								     
							    
							    <div class="controls" id="Div">
							  
							    </div>
							</div></div>




											
<div class="span4">  
  <div class="control-group">
    <label class="control-label" for="startdate">Start Date:<span class="text-error">*</span></label>
    <div class="controls input-append date form_date" data-date="" data-date-format="dd MM yyyy hh:mm:ss" data-link-field="start_input" data-link-format="yyyy-mm-dd">
      <input type="text" name="startdate" id="startdate" value="" readonly required>
      <span class="add-on"><i class="icon-remove"></i></span>
      <span class="add-on"><i class="icon-th"></i></span>
    </div>
  </div>
</div>

<div class="span4">  
  <div class="control-group">
    <label class="control-label" for="enddate">End Date:<span class="text-error">*</span></label>
    <div class="controls input-append date form_date" data-date="" data-date-format="dd MM yyyy hh:mm:ss" data-link-field="end_input" data-link-format="yyyy-mm-dd">
      <input type="text" name="enddate" id="enddate" value="" readonly required>
      <span class="add-on"><i class="icon-remove"></i></span>
      <span class="add-on"><i class="icon-th"></i></span>
    </div>
  </div>
</div>
			



				<div class="span4">		
							<div class="control-group" id="Qnty1">
							    <label class="control-label" for="Qnty">Qnty :</label>
							    <div class="controls">
							      <input type="text" name="Qnty" id="Qnty" placeholder="Qnty" onkeyup="getValues1()"  Value="<?php echo $qty; ?>">
							    </div>
							</div><!--end control-group-->
                             

							<div class="control-group" id="Qnty1">
							    <label class="control-label" for="Rate">Rate:</label> 
							    <div class="controls">
							      <input type="text" name="rate" id="rate" placeholder="Rate" onkeyup="getValues1()" Value="<?php echo $rate; ?>" >
							    </div>
							</div>
							<!--end control-group-->
							
							
							
						
								 <div class="control-group">
							    <label class="control-label" for="Totalfreight">Total Freight :</label>
							    <div class="controls">
							      <input type="text" name="Totalfreight" id="Totalfreight" placeholder="Total freight" Value="<?php echo $frieght;  ?>" onkeyup="getValues2()" >
							    </div>
							</div><!--end control-group-->
								
								
							<div class="control-group" id="volwem1">  
                                     <label class="control-label" for="Comments">DPH Charges :</label>								 
					                <div class="controls-row"  >
										<input type="text" class="span1" name="DPH" id="DPH"  placeholder="DPH" onkeyup="getValues2()"/>
										<input type="text" class="span1" name="dphpercent" id="dphpercent" Value="<?php echo $dph; ?>" onkeyup="getValues2()"/>
									
							     
							    </div>
							</div><!--end control-group-->
							
							
								<div class="control-group">
							    <label class="control-label" for="ROV">ROV Charges: </label>
							    <div class="controls-row"  >
							      <input type="text" class="span1" name="rov"  id="rov" placeholder="ROV"  onkeyup="getValues2()">
							      	<input type="text" class="span1" name="rovpercent" id="rovpercent" Value="<?php echo $rovcharge; ?>" onkeyup="getValues2()"/>
									
							    </div>
							</div><!--end control-group-->
							

						<div class="control-group">
							    <label class="control-label" for="docharg">Docket Charges :</label>
							    <div class="controls">
							      <input name="docharg"  id="docharg" onkeyup="getValues2()"  Value="<?php echo $doccharge; ?>">
							    </div>
							</div>
							
								<div class="control-group">
							    <label class="control-label" for="docharg">Delivery Charges :</label>
							    <div class="controls">
							      <input name="Deliverycharg" value="0" id="Deliverycharg" onkeyup="getValues2()" Value="<?php echo $delivery_charge; ?>" >
							    </div>
							</div>
							
							 <div class="control-group">
							    <label class="control-label" for="oda">ODA /ESS:</label>
							    <div class="controls">
							      <input type="text" name="oda" id="oda" onkeyup="getValues2()"   Value="<?php echo $odamis; ?>">
							    </div>
							</div>
							
								 <div class="control-group">
							    <label class="control-label" for="codod">Handling Charges :</label>
							    <div class="controls">
							      <input type="text" name="handlingcharge" id="handlingcharge" onkeyup="getValues2()"   value="<?php echo $handlingcharge; ?>">
							    </div>
							</div><!--end control-group-->

					 <div class="control-group">
							    <label class="control-label" for="fov">FOV Charges :</label>
							    <div class="controls">
							      <input type="text" name="fov" id="fov" onkeyup="getValues2()"   Value="<?php echo $fov; ?>">
							    </div>
							</div><!--end control-group-->
 <div class="control-group">
							    <label class="control-label" for="Other">Other Charges :</label>
							    <div class="controls">
							      <input type="text" name="othercahrge" id="othercahrge" onkeyup="getValues2()"   Value="<?php echo $other_charge; ?>">
							    </div>
							</div><!--end control-group-->

                            <div class="control-group">
							    <label class="control-label" for="codod">COD / FOD :</label>
							    <div class="controls">
							      <input type="text" name="codod" id="codod" onkeyup="getValues2()"   value="<?php echo $dodcod; ?>">
							    </div>
							</div><!--end control-group-->
							
						
 
							<div class="control-group">
							    <label class="control-label" for="amt">Sub Total </label>
							    <div class="controls">
							      <input type="text" name="amt" id="amt"  readonly value="<?php echo $total; ?>" >
							    </div></div>	<!--end control-group-->

<!--end control-group-->

							<div class="control-group">
							    <label class="control-label" for="CGST">CGST  :</label>
							    <div class="controls">
							      <input name="CGST"  id="CGST" onkeyup="getValues2()" readonly  value="<?php echo $cgst; ?>">
							        
							    </div>
							</div><!--end control-group-->
							
								<div class="control-group">
							    <label class="control-label" for="SGST">SGST  :</label>
							    <div class="controls">
							      <input name="SGST"  id="SGST" onkeyup="getValues2()" readonly  value="<?php echo $sgst; ?>">
							        
							    </div>
							</div><!--end control-group-->
							
							
						
							    
							    	<div class="control-group">
							    <label class="control-label" for="
							    amt">Grand Total </label>
							    <div class="controls">
							      <input type="text" name="grandamt" id="grandamt"    value="<?php echo $gtotal; ?>">
							    </div></div>
					         <input type="hidden" name="status" value="<?php echo $status; ?>">
				</div>	</div><!--end row--></br></br>	</br></br>		 
							<div class="control-group">
							    <div class="controls">
							
									<input name="submit" class="btn btn-primary" type="submit" value="Update & Print" onClick="return validation()">
									<input name="save" class="btn btn-primary" type="submit" value="Update" onClick="return validation()">
									<button type="reset" class="btn ">Clear</button>
							    </div>
							</div><!--end control-group-->
		
		</form><!--end form-->	
</div><!--end conatiner-->
<script>
function validation(){
	var custn= document.forms["bookshipment"]["sname1"].selectedIndex;
	if(custn==null || custn=="")
	{
		alert("Please Select Customer Name");
		return false;
	}
	var bookpm= document.forms["bookshipment"]["Bookingmode"].selectedIndex;
	if(bookpm==null || bookpm=="")
	{
		alert("Please Select Payment Mode");
		return false;
	}
	var insuper= document.forms["bookshipment"]["insuran"].selectedIndex;
	if(insuper==null || insuper=="")
	{
		alert("Please Select Insurance Percentage");
		return false;
	}
	var shipname= document.forms["bookshipment"]["Shiname"].value;
	if(shipname==null || shipname=="")
	{
		alert("Please Enter Shipper Name");
		return false;
	}
	var cont= document.forms["bookshipment"]["Shipperphone"].value;
	if(cont==null || cont=="")
	{
		alert("Please enter Contact number");
		return false;
	}
	var shipadd= document.forms["bookshipment"]["Shipperaddress"].value;
	if(shipadd==null || shipadd=="")
	{
		alert("Please Enter Shipper Address");
		return false;
	}
	var recn= document.forms["bookshipment"]["Receivername"].value;
	if(recn==null || recn=="")
	{
		alert("Please Enter Receiver Name");
		return false;
	}
	var recont= document.forms["bookshipment"]["Receiverphone"].value;
	if(recont==null || recont=="")
	{
		alert("Please Enter Receiver Contact No");
		return false;
	}
	var recadd= document.forms["bookshipment"]["Receiveraddress"].value;
	if(recadd==null || recadd=="")
	{
		alert("Please enter Receiver Address");
		return false;
	}
	var toadd= document.forms["bookshipment"]["asdfg1"].value;
	if(toadd==null || toadd=="")
	{
		alert("Please enter Destination");
		return false;
	}
	var rate11= document.forms["bookshipment"]["rate"].value;
	if(rate11==null || rate11=="")
	{
		alert("Please Enter Rate");
		return false;
	}
	var unit11= document.forms["bookshipment"]["unit1"].value;
	if(unit11==null || unit11=="")
	{
		alert("Please enter unit (ftl/perkg)");
		return false;
	}
	var consignl= document.forms["bookshipment"]["ConsignmentNo"].value;
	if(consignl==null || consignl=="")
	{
		alert("Please Enter Consignment No");
		return false;
	}
	var invice_no1= document.forms["bookshipment"]["Invoiceno"].value;
	if(invice_no1==null || invice_no1=="")
	{
		alert("Please ente Invoice Number");
		return false;
	}
	var invicev= document.forms["bookshipment"]["Shiptype"].value;
	if(invicev==null || invicev=="")
	{
		alert("Please Enter Type of Shipment");
		return false;
	}
/*	var actwe12= document.forms["bookshipment"]["Weight"].value;
	if(actwe12==null || actwe12=="")
	{
		alert("Please enter Actual weight");
		return false;
	}
	var chab= document.forms["bookshipment"]["cweight"].value;
	if(chab==null || chab=="")
	{
		alert("Please enter Chargeable Weight");
		return false;
	}
	var qty1= document.forms["bookshipment"]["Qnty"].value;
	if(qty1==null || qty1=="")
	{
		alert("Please Enter Quantity");
		return false;
	}*/
	var mode1= document.forms["bookshipment"]["Mode"].selectedIndex;
	if(mode1==null || mode1=="")
	{
		alert("Please Select Mode");
		return false;
	}
	var delivery11= document.forms["bookshipment"]["delivery"].value;
	if(delivery11==null || delivery11=="")
	{
		alert("Please Select Delivery type");
		return false;
	}
	/*var actwe112= document.forms["bookshipment"]["Weight"].value;
	if(actwe112==null || actwe112=="")
	{
		alert("Please enter Actual weight");
		return false;
	}*/
	
	
}
</script>




<link href="./bootstrap/css/bootstrap-datetimepicker.min.css" rel="stylesheet" media="screen">
	<script type="text/javascript" src="./jquery/jquery-1.8.3.min.js" charset="UTF-8"></script>
<script type="text/javascript" src="./bootstrap/js/bootstrap.min.js"></script>
<script type="text/javascript" src="./jquery/bootstrap-datetimepicker.js" charset="UTF-8"></script>
<!--<script type="text/javascript" src="./jquery/locales/bootstrap-datetimepicker.pl.js" charset="UTF-8"></script>-->
<script type="text/javascript">
	$('.form_date').datetimepicker({
        language:  'pl',
        weekStart: 1,
        todayBtn:  1,
		autoclose: 1,
		todayHighlight: 1,
		startView: 2,
		minView: 2,
		forceParse: 0
    });
	$('.form_time').datetimepicker({
        language:  'pl',
        weekStart: 1,
        todayBtn:  1,
		autoclose: 1,
		todayHighlight: 1,
		startView: 1,
		minView: 0,
		maxView: 1,
		forceParse: 0
    });
</script>

<script language="JavaScript" type="text/javascript" src="http://ajax.googleapis.com/ajax/libs/jquery/1.11.1/jquery.min.js"></script>

<script>
function getValues(){
	var numVal1 = Number(document.getElementById("hei").value);
	var numVal2 = Number(document.getElementById("wei").value);
	var numVal3 = Number(document.getElementById("len").value);
	//var numVal5 = Number(document.getElementById("freight").value);
	
	var totalValue = (numVal1 * numVal2 * numVal3 )/6000;
	document.getElementById("tot").value = totalValue;
}

function hideqty(id){
	var rat1=document.getElementById("rate").value;
		//alert(rat1);
	if(id=="ftl"){
	//alert("ftl");
		$("#Weight1").hide();
		$("#cweight1").hide();
		$("#volwem1").hide();
		$("#Qnty1").hide();
		
		$("#totfre").val(rat1);
	}
}
</script>
	
<script type="text/javascript" src="http://code.jquery.com/jquery.min.js"></script>
<script type="text/javascript">
$(document).ready(function(){
    $('input[type="radio"]').click(function(){
        if($(this).attr("value")=="account"){
            $(".box").not(".account").hide();
            $(".account").show();
        }
        if($(this).attr("value")=="cash"){
            $(".box").not(".cash").hide();
            $(".cash").show();
        }
      });
});
</script>



<script type="text/javascript">
function consignmen()
{
	var a=document.getElementById("ConsignmentNo").value;
//alert(a);
obja=new XMLHttpRequest();
obja.open("GET","constt.php?a="+a,true);
obja.send();
obja.onreadystatechange=func
}
function func()
{
	//alert("hi");
if(obja.readyState==4)
{
	valar=obja.responseText;
	if(valar!="")
	{
		alert(valar)
	}
}
}


// <!-- <![CDATA[

// Project: Dynamic Date Selector (DtTvB) - 2006-03-16
// Script featured on JavaScript Kit- http://www.javascriptkit.com
// Code begin...
// Set the initial date.
var ds_i_date = new Date();
ds_c_month = ds_i_date.getMonth() + 1;
ds_c_year = ds_i_date.getFullYear();

// Get Element By Id
function ds_getel(id) {
	return document.getElementById(id);
}

// Get the left and the top of the element.
function ds_getleft(el) {
	var tmp = el.offsetLeft;
	el = el.offsetParent
	while(el) {
		tmp += el.offsetLeft;
		el = el.offsetParent;
	}
	return tmp;
}
function ds_gettop(el) {
	var tmp = el.offsetTop;
	el = el.offsetParent
	while(el) {
		tmp += el.offsetTop;
		el = el.offsetParent;
	}
	return tmp;
}

// Output Element
var ds_oe = ds_getel('ds_calclass');
// Container
var ds_ce = ds_getel('ds_conclass');

// Output Buffering
var ds_ob = ''; 
function ds_ob_clean() {
	ds_ob = '';
}
function ds_ob_flush() {
	ds_oe.innerHTML = ds_ob;
	ds_ob_clean();
}
function ds_echo(t) {
	ds_ob += t;
}

var ds_element; // Text Element...

var ds_monthnames = [
'January', 'February', 'March', 'April', 'May', 'June',
'July', 'August', 'September', 'October', 'November', 'December'
]; // You can translate it for your language.

var ds_daynames = [
'Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'
]; // You can translate it for your language.

// Calendar template
function ds_template_main_above(t) {
	return '<table cellpadding="3" cellspacing="1" class="ds_tbl">'
	     + '<tr>'
		 + '<td class="ds_head" style="cursor: pointer" onclick="ds_py();">&lt;&lt;</td>'
		 + '<td class="ds_head" style="cursor: pointer" onclick="ds_pm();">&lt;</td>'
		 + '<td class="ds_head" style="cursor: pointer" onclick="ds_hi();" colspan="3">[Close]</td>'
		 + '<td class="ds_head" style="cursor: pointer" onclick="ds_nm();">&gt;</td>'
		 + '<td class="ds_head" style="cursor: pointer" onclick="ds_ny();">&gt;&gt;</td>'
		 + '</tr>'
	     + '<tr>'
		 + '<td colspan="7" class="ds_head">' + t + '</td>'
		 + '</tr>'
		 + '<tr>';
}

function ds_template_day_row(t) {
	return '<td class="ds_subhead">' + t + '</td>';
	// Define width in CSS, XHTML 1.0 Strict doesn't have width property for it.
}

function ds_template_new_week() {
	return '</tr><tr>';
}

function ds_template_blank_cell(colspan) {
	return '<td colspan="' + colspan + '"></td>'
}

function ds_template_day(d, m, y) {
	return '<td class="ds_cell" onclick="ds_onclick(' + d + ',' + m + ',' + y + ')">' + d + '</td>';
	// Define width the day row.
}

function ds_template_main_below() {
	return '</tr>'
	     + '</table>';
}

// This one draws calendar...
function ds_draw_calendar(m, y) {
	// First clean the output buffer.
	ds_ob_clean();
	// Here we go, do the header
	ds_echo (ds_template_main_above(ds_monthnames[m - 1] + ' ' + y));
	for (i = 0; i < 7; i ++) {
		ds_echo (ds_template_day_row(ds_daynames[i]));
	}
	// Make a date object.
	var ds_dc_date = new Date();
	ds_dc_date.setMonth(m - 1);
	ds_dc_date.setFullYear(y);
	ds_dc_date.setDate(1);
	if (m == 1 || m == 3 || m == 5 || m == 7 || m == 8 || m == 10 || m == 12) {
		days = 31;
	} else if (m == 4 || m == 6 || m == 9 || m == 11) {
		days = 30;
	} else {
		days = (y % 4 == 0) ? 29 : 28;
	}
	var first_day = ds_dc_date.getDay();
	var first_loop = 1;
	// Start the first week
	ds_echo (ds_template_new_week());
	// If sunday is not the first day of the month, make a blank cell...
	if (first_day != 0) {
		ds_echo (ds_template_blank_cell(first_day));
	}
	var j = first_day;
	for (i = 0; i < days; i ++) {
		// Today is sunday, make a new week.
		// If this sunday is the first day of the month,
		// we've made a new row for you already.
		if (j == 0 && !first_loop) {
			// New week!!
			ds_echo (ds_template_new_week());
		}
		// Make a row of that day!
		ds_echo (ds_template_day(i + 1, m, y));
		// This is not first loop anymore...
		first_loop = 0;
		// What is the next day?
		j ++;
		j %= 7;

	}
	// Do the footer
	ds_echo (ds_template_main_below());
	// And let's display..
	ds_ob_flush();
	// Scroll it into view.
	ds_ce.scrollIntoView();
}

// A function to show the calendar.
// When user click on the date, it will set the content of t.
function ds_sh(t) {
	// Set the element to set...
	ds_element = t;
	// Make a new date, and set the current month and year.
	var ds_sh_date = new Date();
	ds_c_month = ds_sh_date.getMonth() + 1;
	ds_c_year = ds_sh_date.getFullYear();
	// Draw the calendar
	ds_draw_calendar(ds_c_month, ds_c_year);
	// To change the position properly, we must show it first.
	ds_ce.style.display = '';
	// Move the calendar container!
	the_left = ds_getleft(t);
	the_top = ds_gettop(t) + t.offsetHeight;
	ds_ce.style.left = the_left + 'px';
	ds_ce.style.top = the_top + 'px';
	// Scroll it into view.
	ds_ce.scrollIntoView();
}

// Hide the calendar.
function ds_hi() {
	ds_ce.style.display = 'none';
}

// Moves to the next month...
function ds_nm() {
	// Increase the current month.
	ds_c_month ++;
	// We have passed December, let's go to the next year.
	// Increase the current year, and set the current month to January.
	if (ds_c_month > 12) {
		ds_c_month = 1; 
		ds_c_year++;
	}
	// Redraw the calendar.
	ds_draw_calendar(ds_c_month, ds_c_year);
}

// Moves to the previous month...
function ds_pm() {
	ds_c_month = ds_c_month - 1; // Can't use dash-dash here, it will make the page invalid.
	// We have passed January, let's go back to the previous year.
	// Decrease the current year, and set the current month to December.
	if (ds_c_month < 1) {
		ds_c_month = 12; 
		ds_c_year = ds_c_year - 1; // Can't use dash-dash here, it will make the page invalid.
	}
	// Redraw the calendar.
	ds_draw_calendar(ds_c_month, ds_c_year);
}

// Moves to the next year...
function ds_ny() {
	// Increase the current year.
	ds_c_year++;
	// Redraw the calendar.
	ds_draw_calendar(ds_c_month, ds_c_year);
}

// Moves to the previous year...
function ds_py() {
	// Decrease the current year.
	ds_c_year = ds_c_year - 1; // Can't use dash-dash here, it will make the page invalid.
	// Redraw the calendar.
	ds_draw_calendar(ds_c_month, ds_c_year);
}

// Format the date to output.
function ds_format_date(d, m, y) {
	// 2 digits month.
	m2 = '00' + m;
	m2 = m2.substr(m2.length - 2);
	// 2 digits day.
	d2 = '00' + d;
	d2 = d2.substr(d2.length - 2);
	// YYYY-MM-DD
	return d2 + '/' + m2 + '/'+ y;
}

// When the user clicks the day.
function ds_onclick(d, m, y) {
	// Hide the calendar.
	ds_hi();
	// Set the value of it, if we can.
	if (typeof(ds_element.value) != 'undefined') {
		ds_element.value = ds_format_date(d, m, y);
	// Maybe we want to set the HTML in it.
	} else if (typeof(ds_element.innerHTML) != 'undefined') {
		ds_element.innerHTML = ds_format_date(d, m, y);
	// I don't know how should we display it, just alert it to user.
	} else {
		alert (ds_format_date(d, m, y));
	}
}

function getSelected(opt)
 {
 
 	var opt=document.frmExport.opt;
            for (var intLoop = 0; intLoop < opt.length; intLoop++)
			 {
			  if (!(opt.options[intLoop].selected))
			   {
			   		alert("Select any one field!");
					return false;
               }
		    }
			return true;           
  }

// And here is the end.

// ]]> 
</script>  

<script>

function to() { 
 // alert("hhi");
   $('#desti').find('option').remove().end().append('<option value="other">----Select Destinantion----</option>').val('');
    $.ajax({                                      
      url: 'ajax_getDestination.php?type='+$('#sname').val(),                  //the script to call to get data          
      data: "",                        //you can insert url argumnets here to pass to api.php
                                       //for example "id=5&parent=6"
      dataType: 'json',                //data format      
      success: function(data)          //on recieve of reply
      {
      $.each(data, function(index, data) {
        $('#desti').append( $('<option></option>').val(data.id).html(data.name) );
       });
       }
       });
       
    }
	
</script>
<script>
function addre()
{
	//document.getElementById("toadd").style.display="none";
}

</script>
<script>
function addres()
{  //alert("addres");
	//document.getElementById("toadd").style.display="block";
}

</script>

<script  type="text/javascript">
function getrate()
{ 
var adest=document.getElementById("desti").value;
//alert(adest); 
obj=new XMLHttpRequest();
obj.open("GET","ajax_getrate1.php?type="+adest,true);
obj.send();
obj.onreadystatechange=funcrate
}
function funcrate()
{
   if(obj.readyState==4)
     {
	getdata=obj.responseText;
	//alert(getdata);
	var destdata = getdata.split("*");
		//alert(destdata[0]);
		document.getElementById("des").value=destdata[0];
		document.getElementById("rate").value=destdata[1];
		document.getElementById("kg").value=destdata[2];
		document.getElementById("ftl").value=destdata[3];
		var uni=destdata[3];
		//alert(uni);
		unit(uni);
		}	
}
function unit(ui)
{
if(ui=="ftl")
{
	var rat1 = Number(document.getElementById("rate").value);
	//alert(rat1);
	document.getElementById("totfre").value = rat1;
	

}
else{
	getValues1();
}

}
</script>
	
<script>
function getValues1(){

    var rate = Number(document.getElementById("rate").value);
  
    var actweight = Number(document.getElementById("Weight").value);
    var chweight = Number(document.getElementById("cweight").value);
  
   // var volumweight = Number(document.getElementById("tot").value);
   // var qtyq = Number(document.getElementById("Qnty").value);
     
  //  var totwe= actweight + chweight + volumweight ;
   
    var totfre1=  rate * chweight;
    
     
     document.getElementById("Totalfreight").value = totfre1;

	



	
}
</script>

<script>
function getValues2(){
	var totfri = Number(document.getElementById("Totalfreight").value);
	var dph = Number(document.getElementById("DPH").value);
	var fov = Number(document.getElementById("fov").value);
	var othercahrge = Number(document.getElementById("othercahrge").value);
	var rov = Number(document.getElementById("rov").value);
	var docha = Number(document.getElementById("docharg").value);
	var oda1 = Number(document.getElementById("oda").value);
    var handling = Number(document.getElementById("handlingcharge").value);
	var cod = Number(document.getElementById("codod").value);
		var dphpercent = Number(document.getElementById("dphpercent").value);
	  var invalue = Number(document.getElementById("invalue").value);
	var rovpercent = Number(document.getElementById("rovpercent").value);
	 var Deliverycharg = Number(document.getElementById("Deliverycharg").value);
	
var dphcharges = totfri*dphpercent/100;
var rovcharges = invalue*rovpercent/100;
document.getElementById("DPH").value=dphcharges;
document.getElementById("rov").value=rovcharges;
	var totall= totfri + docha + cod + oda1 + rov + dph + handling + Deliverycharg + fov + othercahrge;
	var ctot=(totall*9)/100;
	var stot=(totall*9)/100;
var gtotal=ctot+stot+totall;

	
	document.getElementById("amt").value = totall;
	document.getElementById("CGST").value = ctot;
	document.getElementById("SGST").value = stot;
	document.getElementById("grandamt").value = gtotal;
	
	
}
</script>

<script>
function insur(insu){
	
	if(insu=="ys")
	{  
	document.getElementById("insurance").style.display="block";
		
	}
	else{
	document.getElementById("insurance").style.display="none";
	}
}
</script>  

<script>
function insad(insu1){
	//alert(insu1);
	if(insu1=="other")
	{  
	document.getElementById("desti1").style.display="block";
		
	}
	else{
	document.getElementById("desti1").style.display="none";
	}
}

 function asd(ase){
	//alert(ase);
	if(ase!=="")
	{
		$("#asdf").hide();
	}
	
</script>  

<script>





    

   function paymentMode(t){



	var a = ["TBB"];

	var b = ["Paid","ToPay","FOC","ToPay&COD"];

	var c = ["TBB","Paid","ToPay","FOC","ToPay&COD"];

	s = document.getElementById('Bookingmode');

	var sl = s.options.length;

	for(var i = sl-1; i >= 0 ; i--) { s.options[i] = null; }

	if(t.value != 0){

		var z;

		switch (t.value) {

			case 'account' : z = a; break;

			case 'cash' : z = b; break;

			case 'walkin' : z = c; break;

			default : alert('Invalid entry'); break;

		}

		var l = z.length;

		for(i = 0; i < l; i++ ) { s.options[i] = new Option(z[i],z[i],false,false); }

	}

}





	

</script>
      
<script>

$(document).ready(function ()
  {
   $("#sname").change(function () { 
    $('#Shipperphone').val("0");
      $('#Shipperemail').val("");   
   $('#Shipperadd').val("");
       $('#custin').val("0");   
       $('#custgst').val("0"); 
        $('#custpan').val("0");   
    $.ajax({ 
    
                                           
      url: 'ajax_getShipdetails.php?sname='+$('#sname').val(),                  //the script to call to get data          
      data: "",                        //you can insert url argumnets here to pass to api.php
                                       //for example "id=5&parent=6"
      dataType: 'json',                //data format      
      success: function(data)          //on recieve of reply
      {
       
  
      $.each(data, function(index, data) {
         
     $('#Shipperphone').val(data.mob);
      $('#Shipperemail').val(data.email);   
   $('#Shipperadd').val(data.Addr);
       $('#custin').val(data.custtin);   
       $('#custstax').val(data.custtx); 
        $('#custpan').val(data.custpans); 			 	
      
       });
  }
   });
  
    });
  }); 
  
</script>

<script>

function cityfun() { 
 // alert("hhi");
   $('#rcity').find('option').remove().end().append('<option value="">----Select City----</option>').val('');
    $.ajax({                                      
      url: 'ajax_getCity.php?type='+$('#rstates').val(),                  //the script to call to get data          
      data: "",                        //you can insert url argumnets here to pass to api.php
                                       //for example "id=5&parent=6"
      dataType: 'json',                //data format      
      success: function(data)          //on recieve of reply
      {
      $.each(data, function(index, data) {
        $('#rcity').append( $('<option></option>').val(data.id).html(data.name) );
       });
       }
       });
       
    }
	
</script>

<script>
$(document).ready(function() {
    // Initialize Select2 with search functionality
    $('.select2-search').select2({
        placeholder: "-- Please Select --",
        allowClear: true,
        width: '100%',
        dropdownAutoWidth: true,
        theme: 'bootstrap'
    });

    // Fallback: Custom search functionality for regular dropdowns
    function addSearchToSelect(selectElement) {
        var $select = $(selectElement);
        var $wrapper = $('<div class="dropdown-search-wrapper"></div>');
        var $searchInput = $('<input type="text" class="form-control dropdown-search-input" placeholder="Search options..." style="margin-bottom: 5px;">');

        $select.before($wrapper);
        $wrapper.append($searchInput);
        $wrapper.append($select);

        var originalOptions = $select.find('option').clone();

        $searchInput.on('keyup', function() {
            var searchTerm = $(this).val().toLowerCase();
            $select.empty();

            originalOptions.each(function() {
                var optionText = $(this).text().toLowerCase();
                var optionValue = $(this).val().toLowerCase();

                if (optionText.includes(searchTerm) || optionValue.includes(searchTerm) || searchTerm === '') {
                    $select.append($(this).clone());
                }
            });
        });
    }

    // Apply custom search to dropdowns that don't have Select2
    $('select:not(.select2-search)').each(function() {
        if ($(this).find('option').length > 5) { // Only add search if more than 5 options
            addSearchToSelect(this);
        }
    });

    // Alternative: Convert select to input with datalist for better search
    function convertToDatalist(selectElement) {
        var $select = $(selectElement);
        var selectId = $select.attr('id');
        var selectName = $select.attr('name');
        var selectClass = $select.attr('class');
        var isRequired = $select.attr('required');

        var $input = $('<input type="text" list="' + selectId + '_list" placeholder="Type to search or select...">');
        var $datalist = $('<datalist id="' + selectId + '_list"></datalist>');

        // Copy attributes
        if (selectId) $input.attr('id', selectId);
        if (selectName) $input.attr('name', selectName);
        if (selectClass) $input.attr('class', selectClass + ' datalist-input');
        if (isRequired) $input.attr('required', 'required');

        // Copy options to datalist
        $select.find('option').each(function() {
            var $option = $(this);
            if ($option.val() !== '') {
                $datalist.append('<option value="' + $option.text() + '" data-value="' + $option.val() + '">');
            }
        });

        $select.after($input);
        $select.after($datalist);
        $select.hide(); // Hide original select but keep it for form submission

        // Handle input changes
        $input.on('change', function() {
            var inputValue = $(this).val();
            var matchedOption = $datalist.find('option[value="' + inputValue + '"]');

            if (matchedOption.length > 0) {
                $select.val(matchedOption.attr('data-value'));
            } else {
                $select.val('');
            }
        });
    }
});
</script>

<script>
// Show/hide transfer div based on dropdown selection
$(document).ready(function() {
    console.log('Transfer script loaded');
    console.log('Found shipmentType dropdown:', $('#shipmentTypeDropdown').length > 0);

    // Test if div exists
    console.log('Transfer div exists:', $('#transferDiv').length > 0);
});

// Handle shipment type radio button change
function handleShipmentTypeChange() {
    var shipmentType = $('input[name="shipmentType"]:checked').val();
    console.log('Shipment type changed to:', shipmentType);

    if (shipmentType === 'transfer') {
        console.log('Showing transfer div');
        $('#transferDiv').show();
    } else {
        console.log('Hiding transfer div');
        $('#transferDiv').hide();
        // Reset transfer form when hiding
        $('#transferTypes').val('');
        $('#transferInwardSection').hide();
        $('#transferOutwardSection').hide();
    }
}

// Transfer type change handler
function handleTransferTypeChange() {
    var transferType = $('#transferTypes').val();
    console.log('Transfer type changed to:', transferType);

    if (transferType === 'inward') {
        $('#transferInwardSection').show();
        $('#transferOutwardSection').hide();
        // Clear outward rows when switching to inward
        $('#outwardRowsContainer').empty();
        outwardRowCount = 0;
    } else if (transferType === 'outward') {
        $('#transferInwardSection').hide();
        $('#transferOutwardSection').show();
        // Clear inward rows when switching to outward
        $('#transferRowsContainer').empty();
        transferRowCount = 0;
    } else {
        $('#transferInwardSection').hide();
        $('#transferOutwardSection').hide();
        // Clear both when hiding
        $('#transferRowsContainer').empty();
        $('#outwardRowsContainer').empty();
        transferRowCount = 0;
        outwardRowCount = 0;
    }
}

// Inward row functionality
var transferRowCount = 0;

function addTransferRow() {
    transferRowCount++;
    var rowHtml = '<div class="transfer-row-flex" id="transferRow' + transferRowCount + '" style="display: flex; gap: 10px; align-items: center; margin-bottom: 10px;">' +
        '<label style="min-width: 80px;"></label>' +
        '<input type="text" name="transfer_part_no[]" placeholder="Part No" style="width: 200px;">' +
        '<input type="text" name="transfer_quantity[]" placeholder="Quantity" style="width: 200px;">' +
        '<input type="text" name="transfer_packages[]" placeholder="No. of Packages" style="width: 200px;">' +
        '<button type="button" onclick="removeTransferRow(' + transferRowCount + ')" style="background-color: red; color: white; padding: 4px 8px; border: none; border-radius: 4px; margin-left: 10px;">Remove</button>' +
        '</div>';

    $('#transferRowsContainer').append(rowHtml);
}

function removeTransferRow(rowId) {
    $('#transferRow' + rowId).remove();
}

// Outward row functionality
var outwardRowCount = 0;

function addOutwardRow() {
    outwardRowCount++;
    var rowHtml = '<div class="outward-row-flex" id="outwardRow' + outwardRowCount + '" style="display: flex; gap: 10px; align-items: center; margin-bottom: 10px;">' +
        '<label style="min-width: 80px;"></label>' +
        '<select name="outward_part_no[]" style="width: 200px;" onchange="fetchPartDetails(this.value, this)">' +
        '<option value="">-- Select Part No --</option>' +
        '</select>' +
        '<input type="text" name="outward_packages[]" placeholder="No. of Packages" style="width: 200px;">' +
        '<input type="text" name="outward_quantity[]" placeholder="Quantity" style="width: 200px;">' +
        '<button type="button" onclick="removeOutwardRow(' + outwardRowCount + ')" style="background-color: red; color: white; padding: 4px 8px; border: none; border-radius: 4px; margin-left: 10px;">Remove</button>' +
        '</div>';

    $('#outwardRowsContainer').append(rowHtml);
}

function removeOutwardRow(rowId) {
    $('#outwardRow' + rowId).remove();
}

// Placeholder function for part details fetching
function fetchPartDetails(partNo, selectElement) {
    // This function would typically make an AJAX call to fetch part details
    console.log('Fetching details for part:', partNo);
    // Implementation would be similar to addcourier1.php
}
</script>

<?php
include("footer.php");
?>