<?php 
error_reporting(~E_ALL);
session_start();
require 'connection.php';
require_once('database.php');
require_once('library.php');
isUser();

 
echo $cono=$_GET['cna'];

$date=$_GET['sd'];

$date2=$_GET['ed'];

if($date2=='')
{
 
$Cdate2=date('Y-m-d',time());
}
else
{
 $Cdate2=date('Y-m-d',strtotime($date2));
}


 $Cdate=date('Y-m-d',strtotime($date));

if($cono=='' || $cono=='all')
{
$prospectquery="";
}
else
{
$prospectquery=" AND status='".$statusid."' ";

}

$sql="select * from `tbl_courier` where `tbl_courier`.`ship_name` ='$cono'"; 

$result1 = mysqli_query($con, $sql);
$row1 = mysqli_fetch_array($result1, MYSQLI_BOTH);

$date=$_POST['todat'];            $bill=$_GET['billno'];
$ship_name=$row1['ship_name'];     $custpan=$row1['custpan']; 
$s_add=$row1['s_add'];             $custin=$row1['custin']; 
$phone=$row1['phone'];             $custstax=$row1['custstax'];
$custemil=$row1['smail']; 



//echo $sql;
echo "<table  width='100%' bgcolor='#D8D8D8' class='mystyle'><tr ><td>Date: ".$date."</td> <td>Bill No : ".$bill."</td></tr><tr><td>Customer Name : ".$ship_name."</td> <td>PAN No : ".$custpan."</td></tr><tr><td>Address : ".$s_add."</td> <td>TIN No :  ".$custin."</td></tr><tr><td>Contact No : ".$phone."</td> <td>Service Tax No : ".$custstax."</td></tr><tr><td>Email ID : ".$custemil."</td></tr> </table> <table border='1' width='100%' bgcolor='#D8D8D8' class='mystyle'>";

//echo "<tr ><td colspan='12' align='center'><b>New Enquiries</b></td></tr>";
echo "<tr align='center'><td><b>Sr.No.</b></td><td><b>C/N No</b></td><td><b>Date</b></td><td><b>To</b></td><td><b>Weight </b></td><td><b>Type </b></td><td><b>Docket</b></td><td><b>Freight</b></td></tr>";
$result = mysqli_query($con,$sql);
$cntr=0;
while($row = mysqli_fetch_array($result)) 
 {
$cntr=$cntr+1;  
//echo $cono=$row['cons_no'];

echo "<tr><td>".$cntr."</td><td>".$row['cons_no']."</td><td>".$row['book_date']."</td><td>".$row['desti']."</td><td>".$we[]=$row['weight']."</td><td>".$row['type']."</td><td>".$doc[]=$row['dock_charg']."</td><td>".$tot[]=$row['gtotamt']."</td></tr>";
  }
 
 echo "<tr><td colspan='4' align='center'>Total </td><td>".array_sum($we)."</td><td colspan='1'></td><td>".array_sum($doc)."</td><td>".array_sum($tot)."</td></tr>";
   if($cntr==0)
  {
  echo "<tr><td colspan='12' align='center'><font color='red'> No Record Found...........</font></td></tr>";
  }
  echo "<table border='1' width='100%' bgcolor='#D8D8D8' class='mystyle'><tr><td><p>INTERNAL AUDIT OBSERVATION <br><br><br><br>
  Checked by<br><br>Date:  &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; Time: <br></p></td><td align='center'><p>I hereby Agree to the Terms & Conditions Printed overleaf <br><br><br><br>  Consignor's Signature<br> ".$ship_name."</p></td><td  align='center'><p><br><br><br><br>Signature Of Booking Clerk </p></td></tr>
  </table>";
 
 echo "</table>";
mysqli_close($con);
?> 