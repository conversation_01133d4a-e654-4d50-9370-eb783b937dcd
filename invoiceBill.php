<?php
error_reporting(~E_ALL);
session_start();
require 'connection.php';
require_once('database.php');
require_once('library.php');
isUser();

 date_default_timezone_set('Asia/Kolkata');
$todate = date('d/m/Y h:i:s', time());


if(isset($_POST['Submit']))
 {
	 $cname=$_POST['cname'];   $bill=$_POST['bilno']; $userid=$_POST['cid']; $frm=$_POST['uoffice'];$clerkname=$_POST['uaddress'];$fuel=$_POST['fuelcharge'];
	
// $date=$_POST['stdate']; $date2=$_POST['endate'];  
	// 
       
if($date2=='')
{
 
$Cdate2=date('Y-m-d',time());
}
else
{
 $Cdate2=date('Y-m-d',strtotime($date2));
}
 $Cdate=date('Y-m-d',strtotime($date));

if($cname!="")
{
	//$sql="select * from `tbl_courier` where `tbl_courier`.`ship_name` ='$cono'"; 
$sql="SELECT * FROM tbl_courier WHERE `tbl_courier`.`ship_name` ='$cname' and tbl_courier.book_date between '$Cdate' and '$Cdate2'";
//$sql="SELECT * FROM tbl_courier WHERE `tbl_courier`.`ship_name` ='$cname' and tbl_courier.book_date between '$date' and '$date2'";
}
else 
{
	$tr5=$tr5.'<tr><td colspan="8" align="center"><font color="red" size="4"> " ..............No Record Found..........."</font></td></tr>';

}

//$sql="select * from `tbl_courier` where `tbl_courier`.`ship_name` ='asd'"; 
$result = mysqli_query($con,$sql);
$cntr=0;

while($row = mysqli_fetch_array($result)) 
 {
$cntr=$cntr+1;  
 $tr=$tr.'<tr><td >'.$cntr.'</td><td>'.$row['cons_no'].'</td><td>'.$row['book_date'].'</td><td>'.$row['desti'].'</td><td>'.$we[]=$row['weight'].'</td><td>'.$row['type'].'</td><td>N/A</td><td>'.$tot[]=$row['gtotamt'].'</td></tr>';
 }
 
$tr1=$tr1.'<tr><td colspan="7" align="right">Sub Total </td><td>'.$subtot=array_sum($tot).'</td></tr>';

$sertax= $subtot*14 /100;
//$fuelrs=$fuel;
$totf= $subtot+$sertax+$fuel;
$tr2=$tr2.'<tr><td colspan="7" align="right">Service Tax (14%) </td><td>'.$sertax.'</td></tr>';
$tr3=$tr3.'<tr><td colspan="7" align="right">Fuel Charges </td><td>'.$fuel.'</td></tr>';

$tr4=$tr4.'<tr><td colspan="4" align="center">Total </td><td>'.$totw=array_sum($we).'</td><td colspan="1"></td><td>N/A</td><td>'.$totf.'</td></tr>';
 
 
$result1 = mysqli_query($con, $sql);
$row1 = mysqli_fetch_array($result1, MYSQLI_BOTH);
          
$ship_name=$row1['ship_name'];     $custpan=$row1['custpan']; 
$s_add=$row1['s_add'];             $custin=$row1['custin']; 
$phone=$row1['phone'];             $custstax=$row1['custstax'];
$custemil=$row1['smail']; 



}
mysqli_close($con);
?> 
<html>

<head>
</head>	

    <center>  <?php $_SESSION['b']=b; ?>
    <tr ><?php if(isset($_POST['submit']))  
  {
   echo '<img src="img/bill.png" />';
  }
  else
  {
  echo'<img src="img/bill.png" />';
  
  } ?></tr>
  </center>
  
  <table border='1' width='100%' bgcolor='#D8D8D8' class='mystyle'>
  <tr > 
      <td>Date: &nbsp; &nbsp;<?php echo $todate; ?> </td> 
	  <td>Bill No : &nbsp; &nbsp;<?php echo $bill; ?> </td>
  </tr>
  <tr>
	<td>Customer Name : &nbsp; &nbsp;<?php echo $ship_name; ?> </td> <td>PAN No :&nbsp; &nbsp;<?php echo $custpan; ?> </td>
  </tr>
  <tr>
	<td>Address :&nbsp; &nbsp;<?php echo $s_add; ?> </td> <td>TIN No : &nbsp; &nbsp;<?php echo $custin; ?> </td>
  </tr>
  <tr>
	<td>Contact No : &nbsp; &nbsp;<?php echo $phone; ?> </td> <td>Service Tax No :&nbsp; &nbsp;<?php echo $custstax; ?> </td>
  </tr>
  <tr>
    <td>Email ID : &nbsp; &nbsp;<?php echo $custemil; ?> </td>
  </tr> 
  </table > 
  <table border='1' width='100%' bgcolor='#D8D8D8' class='mystyle'> 
   <tr align='center'>
   <th><b>Sr.No.</b></th><th><b>C/N No</b></th><th><b>Date</b></th><th><b>To</b></th><th><b>Weight </b></th><th><b>Type </b></th><th><b>Dock/Non-Dock</b></th><th><b>Freight</b></th>
   </tr>
    <tr>
	<?php echo $tr;?>
	</tr>
     <tr>
	<?php echo $tr1;?>
	</tr>
	 <tr>
	<?php echo $tr2;?>
	</tr>
	 <tr>
	<?php echo $tr3;?>
	</tr>
	 <tr>
	<?php echo $tr4;?>
	</tr>
	 <tr>
	<?php echo $tr5;?><td colspan="8" align="center">Total In Words :-<b>
	<?php echo convert_number_to_words($totf);?> Only </b>
	</tr>
  </table> 
   <table border='1' width='100%' bgcolor='#D8D8D8' class='mystyle'>
   <tr><td><p>INTERNAL AUDIT OBSERVATION <br><br><br><br>
  Checked by<br><br>Date:  &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; Time: <br></p></td><td align="center"><p>I hereby Agree to the Terms & Conditions Printed overleaf <br><br><br><br>  Consignors Signature</p><?php echo $cname; ?></td><td  align="center"><p><br><br><br><br>Signature Of Booking Clerk </p><?php echo $clerkname;?><br><?php echo $frm;  ?></td></tr>
  </table>
  
<p align="left">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>



<p align="left">  <input type="button" id="printpagebutton" onclick="printpage();" value="Print"><a id="backbutton" href="invoicebillPrint.php"><input type="button" id="backbutton" onclick="closeWin();" value="Close"> </a></p>

</div>

<script type="text/javascript">
    function printpage() {
        //Get the print button and put it into a variable
        var printButton = document.getElementById("printpagebutton");
        var back=document.getElementById("backbutton");
        //Set the print button visibility to 'hidden' 
        printButton.style.visibility = 'hidden';
        back.style.visibility = 'hidden';
        //Print the page content
        window.print();
        //Set the print button to 'visible' again 
        //[Delete this line if you want it to stay hidden after printing]
        printButton.style.visibility = 'visible';
         back.style.visibility = 'visible';
    }
</script>
<script type="text/javascript">

function closeWin() {
    back.close();
}
    window.onbeforeunload = function() {
        
    }
</script>
<?php
function convert_number_to_words($number) {

    $hyphen      = '-';
    $conjunction = ' and ';
    $separator   = ', ';
    $negative    = 'negative ';
    $decimal     = ' point ';
    $dictionary  = array(
        0                   => 'Zero',
        1                   => 'One',
        2                   => 'Two',
        3                   => 'Three',
        4                   => 'Four',
        5                   => 'Five',
        6                   => 'Six',
        7                   => 'Seven',
        8                   => 'Eight',
        9                   => 'Nine',
        10                  => 'Ten',
        11                  => 'Eleven',
        12                  => 'Twelve',
        13                  => 'Thirteen',
        14                  => 'Fourteen',
        15                  => 'Fifteen',
        16                  => 'Sixteen',
        17                  => 'Seventeen',
        18                  => 'Eighteen',
        19                  => 'Nineteen',
        20                  => 'Twenty',
        30                  => 'Thirty',
        40                  => 'Fourth',
        50                  => 'Fifty',
        60                  => 'Sixty',
        70                  => 'Seventy',
        80                  => 'Eighty',
        90                  => 'Ninety',
        100                 => 'Hundred',
        1000                => 'Thousand',
        1000000             => 'Million',
        1000000000          => 'Billion',
        1000000000000       => 'Trillion',
        1000000000000000    => 'Quadrillion',
        1000000000000000000 => 'Quintillion'
    );

    if (!is_numeric($number)) {
        return false;
    }

    if (($number >= 0 && (int) $number < 0) || (int) $number < 0 - PHP_INT_MAX) {
        // overflow
        trigger_error(
            'convert_number_to_words only accepts numbers between -' . PHP_INT_MAX . ' and ' . PHP_INT_MAX,
            E_USER_WARNING
        );
        return false;
    }

    if ($number < 0) {
        return $negative . convert_number_to_words(abs($number));
    }

    $string = $fraction = null;

    if (strpos($number, '.') !== false) {
        list($number, $fraction) = explode('.', $number);
    }

    switch (true) {
        case $number < 21:
            $string = $dictionary[$number];
            break;
        case $number < 100:
            $tens   = ((int) ($number / 10)) * 10;
            $units  = $number % 10;
            $string = $dictionary[$tens];
            if ($units) {
                $string .= $hyphen . $dictionary[$units];
            }
            break;
        case $number < 1000:
            $hundreds  = $number / 100;
            $remainder = $number % 100;
            $string = $dictionary[$hundreds] . ' ' . $dictionary[100];
            if ($remainder) {
                $string .= $conjunction . convert_number_to_words($remainder);
            }
            break;
        default:
            $baseUnit = pow(1000, floor(log($number, 1000)));
            $numBaseUnits = (int) ($number / $baseUnit);
            $remainder = $number % $baseUnit;
            $string = convert_number_to_words($numBaseUnits) . ' ' . $dictionary[$baseUnit];
            if ($remainder) {
                $string .= $remainder < 100 ? $conjunction : $separator;
                $string .= convert_number_to_words($remainder);
            }
            break;
    }

    if (null !== $fraction && is_numeric($fraction)) {
        $string .= $decimal;
        $words = array();
        foreach (str_split((string) $fraction) as $number) {
            $words[] = $dictionary[$number];
        }
        $string .= implode(' ', $words);
    }

    return $string;
}

?>
</html>