/*=====================
 Customize bootstrap
 made by <PERSON>
 <EMAIL>
==========================*/

/*=============== clearfix ===========*/
}
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
  margin:0;
  padding:0;
  height:0;
  line-height: 0;
}
.clearfix:after {
  clear: both;
}

table, table tr, table tr td{
    border: solid 1px #000;
}

/*============= Global ancher link color style ==========*/
a {
  color:#f16325;
}
a:hover, a:active {
  color:#f16325;
  text-decoration:underline;
}
a.invarseColor:link, a.invarseColor:visited {
  color:#666;
}
a.invarseColor:hover, a.invarseColor:active {
  color:#f16325;
  text-decoration:none;
}
a.active:link, a.active:visited {
  color:#f16325;
  cursor:default;
}

/*=========== placeholder ============*/
/* for old firfox */
input::-moz-placeholder,
textarea::-moz-placeholder {
  color: #999999;
  font-family: 'Open Sans', Arial sans-serif;
  font-size: 10px;
}
input:-moz-placeholder,
textarea:-moz-placeholder {
  color: #999999;
  font-family: 'Open Sans', Arial sans-serif;
  font-size: 10px;
}
/* IE 10 */
input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  color: #999999;
  font-family: 'Open Sans', Arial sans-serif;
  font-size: 10px;
}
input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: #999999;
  font-family: 'Open Sans', Arial sans-serif;
  font-size: 10px;
}
.placeholder {
  color: #999999;
  font-family: 'Open Sans', Arial sans-serif;
  font-size: 10px;
}

/*=============== inputs ==================*/
label,
input,
button,
select,
textarea {
  font-family: 'Open Sans', Arial sans-serif;
  font-size: 12px;
  font-weight: normal;
  line-height: 20px;
  color:#000;
}
textarea {
  height:120px;
}
select {
  -webkit-border-radius:2px;
  -moz-border-radius:2px;
  border-radius:2px;
  background:#f7f7f7;
}
select option {
  background:#f7f7f7;
}
select:focus {
  outline:none;
}

select, textarea,
input[type="text"],
input[type="password"],
input[type="datetime"],
input[type="datetime-local"],
input[type="date"],
input[type="month"],
input[type="time"],
input[type="week"],
input[type="number"],
input[type="email"],
input[type="url"],
input[type="search"],
input[type="tel"],
input[type="color"],
.uneditable-input {
  font-size: 13px;
}

textarea,
input[type="text"],
input[type="password"],
input[type="datetime"],
input[type="datetime-local"],
input[type="date"],
input[type="month"],
input[type="time"],
input[type="week"],
input[type="number"],
input[type="email"],
input[type="url"],
input[type="search"],
input[type="tel"],
input[type="color"],
.uneditable-input {
  -webkit-box-shadow: 0;
  -moz-box-shadow: 0;
  box-shadow: 0;
}


textarea:focus,
input[type="text"]:focus,
input[type="password"]:focus,
input[type="datetime"]:focus,
input[type="datetime-local"]:focus,
input[type="date"]:focus,
input[type="month"]:focus,
input[type="time"]:focus,
input[type="week"]:focus,
input[type="number"]:focus,
input[type="email"]:focus,
input[type="url"]:focus,
input[type="search"]:focus,
input[type="tel"]:focus,
input[type="color"]:focus,
.uneditable-input:focus {
  outline: none;
  -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 6px rgba(0,0,0,.3);
  -moz-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 6px rgba(0,0,0,.3);
  box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 6px rgba(0,0,0,.3);
  border-color: #aaa;
}

/*========= dropdown-menu =============*/
.dropdown-menu {
  background-color: #f0f0f0;
}
.dropdown-menu .divider {
  margin: 2px 1px;
  *margin: -5px 0 5px;
  background-color: #e0e0e0;
  border-bottom: 1px solid #fff;
}
.dropdown-menu li > a:hover,
.dropdown-menu li > a:focus,
.dropdown-submenu:hover > a {
  text-decoration: none;
  color: #fff;
  background:#f16325;
  background-color:#f16325;
  filter:#f16325;
}
.dropdown-menu .active > a,
.dropdown-menu .active > a:hover {
  color: #ffffff;
  text-decoration: none;
  outline: 0;
  background:#f16325;
  background-color:#f16325;
  filter: #f16325;
}

/*============== btn-group =============*/
.btn-group {
  position: relative;
  display: inline-block;
  *display: inline;
  /* IE7 inline-block hack */
  *zoom: 1;
  font-size: 0;
  vertical-align: middle;
  white-space: nowrap;
  *margin-left: .3em;
}
.btn-group:first-child {
  *margin-left: 0;
}
.btn-group + .btn-group {
  margin-left: 5px;
}
.btn-toolbar {
  font-size: 0;
  margin-top: 0;
  margin-bottom: 0;
}
.btn-toolbar > .btn + .btn,
.btn-toolbar > .btn-group + .btn,
.btn-toolbar > .btn + .btn-group {
  margin-left: 5px;
}
.btn-group > .btn {
  position: relative;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
.btn-group > .btn + .btn {
  margin-left: -1px;
}
.btn-group > .btn,
.btn-group > .dropdown-menu,
.btn-group > .popover {
  font-size: 12px;
}
.btn-group > .btn-mini {
  font-size: 10.5px;
}
.btn-group > .btn-small {
  font-size: 11.9px;
}
.btn-group > .btn-large {
  font-size: 17.5px;
}
.btn-group > .btn:first-child {
  margin-left: 0;
  -webkit-border-top-left-radius: 4px;
  -moz-border-radius-topleft: 4px;
  border-top-left-radius: 4px;
  -webkit-border-bottom-left-radius: 4px;
  -moz-border-radius-bottomleft: 4px;
  border-bottom-left-radius: 4px;
}
.btn-group > .btn:last-child,
.btn-group > .dropdown-toggle {
  -webkit-border-top-right-radius: 4px;
  -moz-border-radius-topright: 4px;
  border-top-right-radius: 4px;
  -webkit-border-bottom-right-radius: 4px;
  -moz-border-radius-bottomright: 4px;
  border-bottom-right-radius: 4px;
}
.btn-group > .btn.large:first-child {
  margin-left: 0;
  -webkit-border-top-left-radius: 6px;
  -moz-border-radius-topleft: 6px;
  border-top-left-radius: 6px;
  -webkit-border-bottom-left-radius: 6px;
  -moz-border-radius-bottomleft: 6px;
  border-bottom-left-radius: 6px;
}
.btn-group > .btn.large:last-child,
.btn-group > .large.dropdown-toggle {
  -webkit-border-top-right-radius: 6px;
  -moz-border-radius-topright: 6px;
  border-top-right-radius: 6px;
  -webkit-border-bottom-right-radius: 6px;
  -moz-border-radius-bottomright: 6px;
  border-bottom-right-radius: 6px;
}
.btn-group > .btn:hover,
.btn-group > .btn:focus,
.btn-group > .btn:active,
.btn-group > .btn.active {
  z-index: 2;
}
.btn-group .dropdown-toggle:active,
.btn-group.open .dropdown-toggle {
  outline: 0;
}
.btn-group > .btn + .dropdown-toggle {
  padding-left: 8px;
  padding-right: 8px;
  -webkit-box-shadow: inset 1px 0 0 rgba(255,255,255,.125), inset 0 1px 0 rgba(255,255,255,.2), 0 1px 2px rgba(0,0,0,.05);
  -moz-box-shadow: inset 1px 0 0 rgba(255,255,255,.125), inset 0 1px 0 rgba(255,255,255,.2), 0 1px 2px rgba(0,0,0,.05);
  box-shadow: inset 1px 0 0 rgba(255,255,255,.125), inset 0 1px 0 rgba(255,255,255,.2), 0 1px 2px rgba(0,0,0,.05);
  *padding-top: 5px;
  *padding-bottom: 5px;
}
.btn-group > .btn-mini + .dropdown-toggle {
  padding-left: 5px;
  padding-right: 5px;
  *padding-top: 2px;
  *padding-bottom: 2px;
}
.btn-group > .btn-small + .dropdown-toggle {
  *padding-top: 5px;
  *padding-bottom: 4px;
}
.btn-group > .btn-large + .dropdown-toggle {
  padding-left: 12px;
  padding-right: 12px;
  *padding-top: 7px;
  *padding-bottom: 7px;
}

/*=================== navbar ================*/
.navbar {
  margin:0;
  width:100%;
  height: 40px;
  padding-left: 0;
  padding-right: 0;
  background-color: #e7e7e7;
  background-image:none;
  filter:none;
  border:none;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
.navbar select {
  display: none;
}
.navbar .nav {
  margin: 0 auto 0;
}
.navbar .nav > li {
  line-height:40px;
  border-right:1px solid #c7c7c7;
  position:relative;
  -webkit-transition:background .1s linear;
  -moz-transition:background .1s linear;
  transition:background .1s linear;
}
.navbar .nav > li:hover {
  background:#f16325;
  color:#fff !important;
}
.navbar .nav > li:first-child {
	border-left:1px solid #c7c7c7;
}
.navbar .nav > li:first-child a {
	font-size: 16px;
  padding:0 16px;
}
.navbar .nav li span {
  display: none;
  color:#333;
}
.navbar .nav > li > a {
  padding: 0 18px;
  color: #505050;
  text-shadow: 0 1px 0 #ffffff;
  -moz-text-shadow: 0 1px 0 #ffffff;
  -webkit-text-shadow: 0 1px 0 #ffffff;
  text-transform: uppercase;
  font-size: 12px;
  font-weight: 600;
}
.navbar .nav > li:hover > a:focus,
.navbar .nav > li:hover > a:hover,
.navbar .nav > li:hover > a:focus i,
.navbar .nav > li:hover > a:hover i,
.navbar .nav > li:hover > a:focus span,
.navbar .nav > li:hover > a:hover span {
  color:#fff;
}
.navbar .nav > li:hover a{
  color:#fff;
  -webkit-text-shadow:0 1px 0 rgba(0,0,0,.1);
  -moz-text-shadow:0 1px 0 rgba(0,0,0,.1);
  text-shadow:0 1px 0 rgba(0,0,0,.1);
}
.navbar .nav .active a,
.navbar .nav .active a:hover,
.navbar .nav .active a:focus
.navbar .nav > .active > a,
.navbar .nav > .active > a:hover,
.navbar .nav > .active > a:focus,
.navbar .nav > .active > a i,
.navbar .nav > .active > a:focus i,
.navbar .nav > .active > a:hover i,
.navbar .nav > .active > a span,
.navbar .nav > .active > a:focus span,
.navbar .nav > .active > a:hover span {
  color: #fff;
  text-decoration: none;
  background: #f16325;
  background-color: #f16325;
  filter:#f16325;
  -webkit-text-shadow:0 1px 0 rgba(0,0,0,.1);
  -moz-text-shadow:0 1px 0 rgba(0,0,0,.1);
  text-shadow:0 1px 0 rgba(0,0,0,.1);
  cursor:default;
}

/* ul nested inside ul */
.navbar .nav div {
  position: absolute;
  left:-1px;
  background: #f16325;
  z-index:100;
  border:1px solid #c7c7c7;
  border-top:none;
  -webkit-border-radius:0 0 4px 4px;
  -moz-border-radius:0 0 4px 4px;
  border-radius:0 0 4px 4px;
  display: none;
}
.navbar .nav div ul {
  list-style: none;
  margin:0;
  padding:0;
  display: table-cell;
}
.navbar .nav div ul li {
  margin:0;
  padding:0;
  color:#fff;
  border-top:1px solid rgba(255,255,255,.1);
  border-bottom:1px solid rgba(0,0,0,.1);
}
.navbar .nav div ul li:first-child {
  border-top:none;
}
.navbar .nav div ul li a {
  color:#fff;
  white-space: nowrap;
  padding:6px 18px;
  display: block;
  font-size:12px;
  font-weight: 600;
  text-transform: uppercase;
}
.navbar .nav div ul li a:hover {
  text-decoration: none;
  background:rgba(0,0,0,.1);
}
.navbar .nav li:hover > div {
  display: block;
}

/*============== btn ==============*/
.btn {
  font-size: 14px;
  font-weight:600;
}
/*========== btn-primary (orange-btn) ==========*/
.btn-primary {
  border:1px solid #f16325;
  background: #f16325;
  background: -moz-linear-gradient(top, #f4885a 0%, #f16325 100%);
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#f4885a), color-stop(100%,#f16325));
  background: -webkit-linear-gradient(top, #f4885a 0%,#f16325 100%);
  background: -o-linear-gradient(top, #f4885a 0%,#f16325 100%);
  background: -ms-linear-gradient(top, #f4885a 0%,#f16325 100%);
  background: linear-gradient(to bottom, #f4885a 0%,#f16325 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}
.btn-primary:hover,
.btn-primary:active,
.btn-primary.active,
.btn-primary.disabled,
.btn-primary[disabled] {
  background: #f16325;
}
.btn-primary:active,
.btn-primary.active {
  background-color: #f46f35;
}


/*================= pagination ==================*/
.pagination {
  clear:both;
  margin: 42px 0 0;
}
.pagination ul > .active > a,
.pagination ul > .active > a:hover,
.pagination ul > .active > span {
  color: #999999;
  cursor: default;
  background:#eee;
}


/*===================== carousel ========================*/
.carousel {
  position: relative;
  line-height: 1;
  margin:0;
  padding:4px;
  background:#fff;
  border:1px solid #e0e0e0;
  -moz-box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
  -webkit-box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
}
.carousel-control {
  position: absolute;
  top: 50%;
  left: 15px;
  width: 20px;
  height: 20px;
  margin-top: -10px;
  font-size: 30px;
  font-weight: 100;
  line-height: 15px;
  color: #fff;
  text-align: center;
  background: #222222;
  border: 3px solid #ffffff;
  -webkit-border-radius: 23px;
  -moz-border-radius: 23px;
  border-radius: 23px;
  opacity: 0.5;
  filter: alpha(opacity=50);
}


/*==================== .table ===============*/
.table {
  margin:0;
}
.table th,
.table td {
  padding: 18px 0;
  text-align: center;
  vertical-align: middle;
  border:none;
  background:url('../img/dottedBorder.png') repeat-x left bottom;
}
.table th {
  padding:8px 0;
  background: none;
}
.table thead {
  border:1px solid #ddd;
  background: #e7e7e7;
  background: -moz-linear-gradient(top, #f7f7f7 0%, #e7e7e7 100%);
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#f7f7f7), color-stop(100%,#e7e7e7));
  background: -webkit-linear-gradient(top, #f7f7f7 0%,#e7e7e7 100%);
  background: -o-linear-gradient(top, #f7f7f7 0%,#e7e7e7 100%);
  background: -ms-linear-gradient(top, #f7f7f7 0%,#e7e7e7 100%);
  background: linear-gradient(to bottom, #f7f7f7 0%,#e7e7e7 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f7f7f7', endColorstr='#e7e7e7',GradientType=0 );
}
.table td img {
  padding: 3px;
  border: 1px solid #e0e0e0;
  background-color:#fff;
  -moz-box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
  -webkit-box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
  box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
  background-color:#fff;
}
.table td img:hover {
  border: 1px solid #d0d0d0;
}

/* td.desc */
.table td.desc,
.table th.desc {
  text-align: left;
}
.table td.desc .rating {
  list-style: none;
  padding:0;
  margin:6px 0;
}
.table td.desc .rating li {
  margin:0;
  padding:0;
  float: left;
}
.table td.desc .rating li i {
  margin:0;
  padding:0;
  font-size: 14px;
}
.table td.desc .rating i.star-on {
  width:17px;
  height:17px;
  display: block;
  background:url('../img/star-on.png') no-repeat left top;
}
.table td.desc .rating i.star-off {
  width:17px;
  height:17px;
  display: block;
  background:url('../img/star-off.png') no-repeat left top;
}
/* quantity */
.table td.quantity input {
  width:25px;
  text-align: center;
}
/* sub-price */
.table td.sub-price {
  font-size: 18px;
  font-weight: 600;
}


/*==================== accoradain ================*/
.cart-accordain {
  margin-top:60px;
  margin-bottom:0;
}
.accordion-group {
  margin-bottom: 2px;
  border: 1px solid #ddd;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius:0;
}
.accordion-heading {
  background: #e7e7e7;
  background: -moz-linear-gradient(top, #f7f7f7 0%, #e7e7e7 100%);
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#f7f7f7), color-stop(100%,#e7e7e7));
  background: -webkit-linear-gradient(top, #f7f7f7 0%,#e7e7e7 100%);
  background: -o-linear-gradient(top, #f7f7f7 0%,#e7e7e7 100%);
  background: -ms-linear-gradient(top, #f7f7f7 0%,#e7e7e7 100%);
  background: linear-gradient(to bottom, #f7f7f7 0%,#e7e7e7 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f7f7f7', endColorstr='#e7e7e7',GradientType=0 );

}
.accordion-heading .accordion-toggle {
  padding: 5px 14px;
  color:#444;
  font-family: 'Sanchez', serif;
  font-weight: 13px;
}
.accordion-heading .accordion-toggle:hover,
.accordion-heading .accordion-toggle:active {
  text-decoration: none;
}
.accordion-inner {
  padding: 14px;
  border-top: 1px solid #d6d6d6;
}


/*==================== .form-horizontal .control-group ====================*/
.form-horizontal .control-group {
  margin-bottom: 14px;
  *zoom: 1;
}
.form-horizontal .control-label {
  width: 140px;
  color:#777;
}
.form-horizontal .controls {
  margin-left: 160px;
}
.controlsa {
  margin-left: -15px;
}

/*========================== nav-tabs =====================*/
.nav-tabs > li:first-child {
  margin-left:8px;
}
.nav-tabs {
  border-bottom: 1px solid #c4c4c4;
}
.nav-tabs > li > a {
  color:#444;
  border: 1px solid #d2d2d2;
  background: #e9e9e9;
  background: -moz-linear-gradient(top, #e9e9e9 0%, #e2e2e2 100%);
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#e9e9e9), color-stop(100%,#e2e2e2));
  background: -webkit-linear-gradient(top, #e9e9e9 0%,#e2e2e2 100%);
  background: -o-linear-gradient(top, #e9e9e9 0%,#e2e2e2 100%);
  background: -ms-linear-gradient(top, #e9e9e9 0%,#e2e2e2 100%);
  background: linear-gradient(to bottom, #e9e9e9 0%,#e2e2e2 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#e9e9e9', endColorstr='#e2e2e2',GradientType=0 );
}
.nav-tabs > li > a:hover {
  border-color: #d2d2d2 #d2d2d2 #d2d2d2;
  background: #e0e0e0;
}
.nav-tabs > li > a:active {
  border-color: #ddd #ddd #ddd;
  background: #e5e5e5;
  -webkit-box-shadow: inset 0 3px 3px rgba(0,0,0, .05);
  -moz-box-shadow: inset 0 3px 3px rgba(0,0,0, .05);
  box-shadow: inset 0 3px 3px rgba(0,0,0, .05);
}
.nav-tabs > .active > a,
.nav-tabs > .active > a:hover {
  background: #fff;
  filter: #fff;
}

/* opened dropdown in nav-tabs */
.nav-tabs .open > a {
  background:#555;
  background-color:#555;
  filter: #555;
}


/*====================== media-list ==================*/
.media-list .media img {
  padding: 3px;
  border: 1px solid #e0e0e0;
  background-color:#fff;
  -moz-box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
  -webkit-box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
  box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
}
.media-list .media img:hover {
  border: 1px solid #aaa;
}


/*==================== well ===================*/
.well {
  padding:20px;
  margin:0;
  background-color: #f2f2f2;
  border: 1px solid #e7e7e7;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}



/* #Media Queries
================================================== */
@media only screen and (min-width: 768px) and (max-width: 979px) {
  /* navbar */
  .navbar .nav > li:first-child a {
    padding:0 6px;
  }
  .navbar .nav > li a {
    padding:0 9px;
  }
  /*.nav-tabs*/
  .blog-tab .nav-tabs > li > a {
    padding-left:8px !important;
    padding-right:8px !important;
  }
}
@media only screen and (max-width: 767px) {
  /* navbar */
  .navbar ul.nav {
    display: none;
  }
  .navbar select {
    display: block;
    width:90%;
    margin:8px auto 0;
    padding-right:10px;
    padding-left:10px;
    line-height: 40px;
  }
}
@media (max-width: 480px) {}