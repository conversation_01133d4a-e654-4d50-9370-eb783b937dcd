<?php
ob_start();
session_start();
require 'connection.php';
require_once('database.php');

$action = $_GET['action'];

switch($action) {
	case 'add-cons':
		addCons();
	break;
	
	case 'add-cust':
		addCust();
	break;
	
		case 'add-status':
		addstatus();
	break;
	
		case 'edit-status':
		editstatus();
	break;
	
		case 'del-status':
		deletestatus();
	break;
	
		case 'add-city-code':
		addcitycode();
	break;
	
		case 'updatecity-code':
		updatecitycode();
	break;
	
		case 'delcity-code':
		delcitycode();
	break;
	case 'add-receiver':
		addreceiver();
	break;
	
		case 'update-receiver':
		udatereceiver();
	break;
	
        case 'add-hub':
		addHub();
	break;
	
	case 'delivered':
		markDelivered();
	break;
	
	case 'add-office':
		addNewOffice();
	break;
	
	case 'add-manager':
		addManager();
	break;
	
	case 'update-status':
		updateStatus();
	break;
	
		case 'add-stationary':
		addstationary();
	break;
	
	case 'add-branch':
		addBranch();
	break;

	case 'update-branch':
		editBranch();
	break;

	case 'del-branch':
		deleteBranch();
	break;

	case 'change-pass':
		changePass();
	break;

	case 'logOut':
		logOut();
	break;


}//switch

function addCons(){

	$ConsignmentNo = $_POST['ConsignmentNo']; $Shippername = $_POST['Shippername']; $Shipperphone = $_POST['Shipperphone'];$Shipperaddress = $_POST['Shipperaddress'];$Shipperemail = $_POST['Shipperemail'];$custin = $_POST['custin']; $cstin = $_POST['cstin']; $custstax = $_POST['custstax']; $custpan = $_POST['custpan'];
	
	$Receivername = $_POST['Receivername']; $Receiverphone = $_POST['Receiverphone']; $Receiveremail = $_POST['Receiveremail']; $rtin=$_POST['rtin']; $rcstin = $_POST['rcstin'];$Receiveraddress = $_POST['Receiveraddress']; $rcity = $_POST['rcity']; $rstates = $_POST['rstates']; $rzip =$_POST['rzip']; 
 
	$Shiptype = $_POST['Shiptype'];$Weight = $_POST['Weight'];$chweight = $_POST['cweight'];$Invoiceno = $_POST['Invoiceno']; $Qnty = $_POST['Qnty'];$Bookingmod = $_POST['Bookingmode'];$Totalfreight = $_POST['Totalfreight']; $invalue = $_POST['invalue']; $docharg = $_POST['docharg'];
     $codod = $_POST['codod']; $oda = $_POST['oda']; $stax = $_POST['stax'];$Mode = $_POST['Mode']; $Packupdate = $_POST['Packupdate'];  $status = $_POST['status'];  $volw= $_POST['tot']; $to= $_POST['desti'];$clerkn= $_POST['clerkname'];$clerkpho= $_POST['clerkno'];$roun= $_POST['round'];
$colorRadio = $_POST['colorRadio'];$insuran = $_POST['insuran'];$delivery = $_POST['delivery'];
	 	
	//$Comments = $_POST['Comments'];	$custzip = $_POST['custzip']; $states = $_POST['states']; 
        //$city = $_POST['city']; 
		
         $sql="INSERT INTO `tbl_courier` (`cons_no`, `ship_name`, `phone`, `s_add`, `smail`, `vattin`, `csttin`, `custstax`, `custpan`, `rev_name`, `r_phone`, `rmail`, `rtin`, `rcsttin`, `r_add`, `r_city`, `r_states`, `r_zip`, `type`, `weight`, `chweight`, `invice_no`, `qty`, `book_mode`, `freight`, `invi_value`, `dock_charg`, `dod_cod`, `oda_mis`, `st`, `mode`, `book1_date`, `status`, `volumw`,`desti`, `clerkname`, `clerkcon`,`gtotamt`,`round`,`book_date`) VALUES ('$ConsignmentNo', '$Shippername', '$Shipperphone', '$Shipperaddress', '$Shipperemail', '$custin', '$cstin', '$custstax', '$custpan', '$Receivername', '$Receiverphone', '$Receiveremail', '$rtin', '$rcstin', '$Receiveraddress', '$rcity', '$rstates', '$rzip', '$Shiptype', '$Weight', '$chweight', '$Invoiceno', '$Qnty', '$Bookingmod', '$Totalfreight', '$invalue', '$docharg', '$codod', '$oda', '$stax', '$Mode', '$Packupdate', '$status', '$volw','$to', '$clerkn', '$clerkpho', '
$gtotal','$roun', NOW())"; 
	mysqli_query($con,$sql);

	
if(isset($_POST['submit']))
 {
$message=' Hello '.$_POST['Shippername'].' ,<br>
Welcome to Vivanta Logistics Pvt. Ltd.<br>
Your Booked Consignment  Details as follows:
<table><th>Your Consignment No:'.$_POST['ConsignmentNo'].' </th><tr><td><tr><td>Shipper Name: '.$_POST['Shippername'].' </td><td>Reciver Name:'.$_POST['Receivername'].' </td></tr> </td> 
<tr><td>Shipper Contact No: '.$_POST['Shipperphone'].' </td><td>Reciver Contact No:'.$_POST['Receiverphone'].' </td></tr>
<tr><td>Shipper Address: '.$_POST['Shipperaddress'].' </td><td>Reciver Address:'.$_POST['Receiveraddress'].' </td></tr>
<tr><td>Booking Type: '.$_POST['Shiptype'].' </td><td>Booking Mode:'.$_POST['Mode'].' </td></tr>
</tr></table>
';

$my_email = "<EMAIL>";
$headers = "MIME-Version: 1.0" . "\r\n";
$headers .= "Message-ID: <".gettimeofday()." TheSystem@Runanubandh>\r\n";
$headers .= "X-Mailer: PHP v Balaji\r\n";
date_default_timezone_set( 'Asia/Calcutta' );
$headers .= "BCC: <EMAIL>,".$_POST['Shipperemail'].",".$_POST['Receiveremail']."\r\n";
$headers .= "Content-type: text/html\r\n"; 
//$headers .= "BCC:<EMAIL>, <EMAIL>\r\n";
$headers .= "BCC:<EMAIL>\r\n";
$headers .= "Content-type: text/html\r\n"; 

$subject = "Vivanta Logistics Pvt. Ltd. Consignment Booking Successfully..";
//mail($my_email,$subject,$message,$headers);

}$_SESSION['a']=a;
	//header('Location: courier-add-success.php'); 
header('Location: saveBillPrint.php'); 
	
}//addCons

function markDelivered() {
	global $con; // Add this line to access the global $con variable
	$cid = (int)$_GET['cid']; // Cast the cid to an integer to prevent SQL injection
	$sql = "UPDATE tbl_courier
			SET status = 'Delivered'
			WHERE cid= $cid";
	mysqli_query($con,$sql);
	header('Location: delivered-success.php'); 
			
}//markDelivered();

function addCust(){
	global $con; // Add this line to access the global $con variable

	// Safely get POST values with default empty values
	$uid = $_POST['cid'] ?? '';
	$Shippername = $_POST['custname'] ?? '';
	$Shipperphone = $_POST['custphone'] ?? '';
	$Shipperaddress = $_POST['custaddress'] ?? '';
	$Shipperemail = $_POST['custemail'] ?? '';
	$custzip = $_POST['custzip'] ?? '';
	$states = $_POST['states'] ?? '';
	$city = $_POST['city'] ?? '';
	$custin = $_POST['cusvatin'] ?? ''; // Fixed: was 'cusvatin', should match form field
	$custstax = $_POST['custstax'] ?? '';
	$cpan = $_POST['custpan'] ?? '';
	$cstin = $_POST['csttin'] ?? '';
	$insu = $_POST['insurance'] ?? '';
	$Shippercode = $_POST['custcode'] ?? '';
	$cphone = $_POST['cphone'] ?? '';
	$conpname = $_POST['conpname'] ?? '';

	// Check if email already exists
	$check_email = mysqli_query($con, "SELECT custmail FROM custreg WHERE custmail = '$Shipperemail'");
	if(mysqli_num_rows($check_email) > 0) {
		$msg = "email_exists";
	} else {
		// Escape strings to prevent SQL injection
		$Shippername = mysqli_real_escape_string($con, $Shippername);
		$Shipperphone = mysqli_real_escape_string($con, $Shipperphone);
		$Shipperaddress = mysqli_real_escape_string($con, $Shipperaddress);
		$Shipperemail = mysqli_real_escape_string($con, $Shipperemail);

		$sql1 = "INSERT INTO custreg (`userid`,`custname`, `custphone`, `custmail`, `custadd`, `custzip`, `custsts`,`custcity`,`vattin`, `csttin`, `custgst`,`custpan`,`insurn`,`book_date`,`custcode`,`contact_person`,`phoneno`) VALUES('$uid','$Shippername','$Shipperphone','$Shipperemail','$Shipperaddress', '$custzip', '$states', '$city','$custin','$cstin','$custstax','$cpan','$insu', NOW(),'$Shippercode','$conpname','$cphone')";

		if(mysqli_query($con,$sql1)) {
			$msg="yes1";
		} else {
			$msg="no1";
		}
	}
$dna=mysqli_query($con,"select max(custid) from custreg");

$ct=$_POST['ct'];
for($i=1;$i<=$ct;$i++)
{
     $ghkl=$_POST['other'.$i];
 $qrsr="INSERT INTO `custrate` (`crsource`, `crdesti`, `crrate`, `fuelc`, `perkgftp`, `mode`,`doc_charge`,`other`) VALUES ('".$_POST['sources'.$i]."', '".$_POST['destin'.$i]."', '".$_POST['rate'.$i]."','".$_POST['fuelc'.$i]."', '".$_POST['ftl'.$i]."','".$_POST['mode'.$i]."','".$_POST['docketcharge'.$i]."','".$_POST['other'.$i]."')";
	mysqli_query($con,$qrsr);
 }
	
	

	

if(isset($_POST['Submit']))
 {
$message=' Hello '.$_POST['custname'].' ,<br>
Welcome to Vivanta Logistics Pvt. Ltd.<br>
Please see below your Registration Details<br>
Your Name: '.$_POST['custname'].'<br>
Your Contact No: '.$_POST['custphone'].'<br>
Your Email ID: '.$_POST['custemail'].' ';

//$my_email = "<EMAIL>";
$headers = "MIME-Version: 1.0" . "\r\n";
$headers .= "Message-ID: <".gettimeofday()." TheSystem@Runanubandh>\r\n";
$headers .= "X-Mailer: PHP v Balaji\r\n";
//date_default_timezone_set( 'Asia/Calcutta' );
$headers .= "BCC: <EMAIL>,".$_POST['custemail']."\r\n";
$headers .= "Content-type: text/html\r\n"; 
$headers .= "BCC:<EMAIL>\r\n";

$subject = "Vivanta Logistics Customer Registration Successfully..";
mail($my_email,$subject,$message,$headers);

} 

//$_SESSION['a']=a;

 header("location: custReg.php?msg=".$msg);
	
}//addCust


function addreceiver(){
	global $con; // Add this line to access the global $con variable


	 $uid= mysqli_real_escape_string($con, $_POST['cid']);
	 $receivercode = mysqli_real_escape_string($con, $_POST['rccode']);
	 $receivername = mysqli_real_escape_string($con, $_POST['rcname']);
	 $receiverphone = mysqli_real_escape_string($con, $_POST['rcphone']);
	 $receiveraddress = mysqli_real_escape_string($con, $_POST['rcaddress']);
	$rcpan = $_POST['rcpan']; $gst = $_POST['rcgst'];
		
  $sql1 = "INSERT INTO receiver_reg (`userid`,`receiver_code`,`receivername`, `receiverphone`, `receiveremail`, `receiveradd`, `receiverzip`, `receiversts`, `receivercity`,`rcgst`,`rcpan`,`contact_per_name`) VALUES('$uid',$receivercode,'$receivername','$receiverphone','$receiveremail','$receiveraddress', '$receiverzip', '$states', '$city','$gst','$rcpan','$cphone')";	
 $sql1;
	
	if(mysqli_query($con,$sql1))
	
 {
    $msg="yes1";	
  }
  
 
  else
  {
   $msg="no1"; 
  }
	 header("location: receiver_reg.php?msg=".$msg);

 }	
 
 
  function addstatus(){
	global $con; 

	echo $status= $_POST['status'];
		
 $sql1 = "INSERT INTO status(`statusname`) VALUES('$status')";	

	
	if(mysqli_query($con,$sql1))
	
 {
    $msg="yes1";	
  }
  
 
  else
  {
   $msg="no1"; 
  }
	 header("location: add_status.php?msg=".$msg);

 }	
 
 function editstatus(){
	global $con; // Add this line to access the global $con variable

	 $status= mysqli_real_escape_string($con, $_POST['status']);
	 $statusid= mysqli_real_escape_string($con, $_POST['statusid']);

 echo $sql1 = "UPDATE `status` SET `statusname`='$status'  WHERE statusid='$statusid'";

	if(mysqli_query($con,$sql1))
	
 {
    $msg="yes";	
  }
  
 
  else
  {
   $msg="no"; 
  }
	 header("location: add_status.php?msg=".$msg);

 }	
 
 function deletestatus(){
	global $con; // Add this line to access the global $con variable


	 $sql1="DELETE FROM `status` WHERE statusid='".$_GET['id']."'";

	
	if(mysqli_query($con,$sql1))
	
 {
    $msg="yes2";	
  }
  
 
  else
  {
   $msg="no2"; 
  }
	 header("location: add_status.php?msg=".$msg);

 }	
 
 function addcitycode(){
global $con; 
	echo $citycode= $_POST['citycode'];$cityname = $_POST['cityname'];
		
 $sql1 = "INSERT INTO tbl_city_code(`city_name`,`city_code`) VALUES('$cityname','$citycode')";	

	
	if(mysqli_query($con,$sql1))
	
 {
    $msg="yes1";	
  }
  
 
  else
  {
   $msg="no1"; 
  }
	 header("location: addcitycode.php?msg=".$msg);

 }	
 
 
 
 function updatecitycode(){
global $con; // Add this line to access the global $con variable

		
 echo $sql1 = "UPDATE `tbl_city_code` SET `city_name`='".$_POST['cityname']."',`city_code`='".$_POST['citycode']."'  WHERE Id='".$_POST['cityid']."'";
	
	if(mysqli_query($con,$sql1))
	
 {
    $msg="yes";	
  }
  
 
  else
  {
   $msg="no"; 
  }
	 header("location: addcitycode.php?msg=".$msg);

 }	
 
 function delcitycode(){
global $con; // Add this line to access the global $con variable
 
	 $sql1="DELETE FROM `tbl_city_code` WHERE Id='".$_GET['id']."'";

	
	if(mysqli_query($con,$sql1))
	
 {
    $msg="yes2";	
  }
  
 
  else
  {
   $msg="no2"; 
  }
	 header("location: addcitycode.php?msg=".$msg);

 }	

function addHub(){
global $con; // Add this line to access the global $con variable

	$huboffname = $_POST['huboffname']; $hubuname = $_POST['hubuname'];$hubupass = $_POST['hubupass'];$hubno = $_POST['hubno'];$hubemail = $_POST['hubemail']; $hubaddress = $_POST['hubaddress']; $OfficeName = $_POST['curntloca'];   $conpass = $_POST['cpass']; $mobilno = $_POST['mobilno']; $pname = $_POST['pname'];$uid=$_POST['cid'];
		
	$sql1 = "INSERT INTO hubreg (`userid`,`hoffname`, `uname`, `pass`, `cont`, `hmail`, `haddress`, `offhub`, `regdate`,`perid`,`contact_person`,`mobilno`) VALUES('$uid','$huboffname','$hubuname','$hubupass', '$hubno','$hubemail','$hubaddress', '$OfficeName', NOW(),'','$pname','$mobilno')";	
	mysqli_query($con,$sql1);
	
	 $data=mysql_query("select Max(hubid) from hubreg");
 while($rec=mysql_fetch_row($data))
 {
	 $idd=$rec[0];
 }
 
 $sqlt="INSERT INTO `login` ( `username`, `password`, `cpassword`, `type`, `rid`) VALUES ('$hubuname', '$hubupass', '$conpass', 'admin', '$idd')";
 mysqli_query($con,$sqlt);
 
 if(isset($_POST['submit']))
 {
$message=' Hello '.$_POST['hubuname'].',<br>
Welcome to Vivanta Logistics Pvt. Ltd.<br>
Please see below your Registration Details<br>

Your User Name: '.$_POST['hubuname'].'<br>
Your Password : '.$_POST['cpass'].' <br>
Click here to login http://rcplexpress.com/rcpl/login.php
<br>
Your Office Name: '.$_POST['huboffname'].' <br>
Your Contact No: '.$_POST['hubno'].'<br>
Your Email ID: '.$_POST['hubemail'].'<br>
Your Office Address: '.$_POST['hubaddress'].'

';

$my_email = "<EMAIL>";
$headers = "MIME-Version: 1.0" . "\r\n";
$headers .= "Message-ID: <".gettimeofday()." TheSystem@Runanubandh>\r\n";
$headers .= "X-Mailer: PHP v Balaji\r\n";
date_default_timezone_set( 'Asia/Calcutta' );
$headers .= "BCC:<EMAIL> ,".$_POST['hubemail']."\r\n";
$headers .= "Content-type: text/html\r\n"; 
$headers .= "BCC:<EMAIL>,".$_POST['hubemail']."\r\n";

$subject = "Vivanta Logistics Pvt. Ltd. HUB Office Registration Successfully..";
//mail($my_email,$subject,$message,$headers);

}
 
	header('Location:hubregisuccess.php'); 
	
}//addHub


function addNewOffice() {
	global $con; // Add this line to access the global $con variable

	$OfficeName ="";
	$OfficeAddress ="";
	$City ="";
	$PhoneNo ="";
	$OfficeEmail ="";
	$OfficeTiming ="";
	$ContactPerson ="";
        $hubtoadd="";
	$uid="";

	$OfficeName = $_POST['OfficeName'];
	$OfficeAddress = $_POST['OfficeAddress'];
	$City = $_POST['City'];
	$PhoneNo = $_POST['PhoneNo'];
	$OfficeEmail = $_POST['OfficeEmail'];
	$OfficeTiming = $_POST['OfficeTiming'];
	$ContactPerson = $_POST['ContactPerson'];
        $hubtoadd=$_POST['addhub'];
	$uid=$_POST['userid'];	$mobno=$_POST['mobno'];	$pername=isset($_POST['pername']) ? $_POST['pername'] : '';

	$sql = "INSERT INTO tbl_offices (off_name, address, city, ph_no, officemail,office_time, contact_person,addhub,userid,phoneno,cont_pername)
			VALUES ('$OfficeName', '$OfficeAddress', '$City', '$PhoneNo', '$OfficeEmail','$OfficeTiming', '$ContactPerson','$hubtoadd','$uid','$mobno','$pername')";
	mysqli_query($con,$sql);

if(isset($_POST['submit']))
 {
$message=' Hello '.$_POST['ContactPerson'].',<br>
Welcome to Vivanta Logistics Pvt. Ltd.<br>
Please see below your Registration Details<br>
Your Office Name: '.$_POST['OfficeName'].' <br>
Your Contact No: '.$_POST['PhoneNo'].'<br>
Your Email ID: '.$_POST['OfficeEmail'].'<br>
Your Office Address: '.$_POST['OfficeAddress'].'
';

$my_email = "<EMAIL>";
$headers = "MIME-Version: 1.0" . "\r\n";
$headers .= "Message-ID: <".gettimeofday()." TheSystem@Runanubandh>\r\n";
$headers .= "X-Mailer: PHP v Balaji\r\n";
date_default_timezone_set( 'Asia/Calcutta' );
$headers .= "BCC:<EMAIL> ,".$_POST['OfficeEmail']."\r\n";
$headers .= "Content-type: text/html\r\n"; 
$headers .= "BCC:<EMAIL>,".$_POST['OfficeEmail']."\r\n";

$subject = "Vivanta Logistics Pvt. Ltd. Office Registration Successfully..";
//mail($my_email,$subject,$message,$headers);

}

	header('Location: office-add-success.php');
}//addNewOffice

function addManager() {
global $con; // Add this line to access the global $con variable   	
	$ManagerName = $_POST['ManagerName'];
	$uname = $_POST['uname'];
	$fpass = $_POST['fPassword'];
	$Confpass = $_POST['Password'];
	$Address = $_POST['Address'];
	$Address1 = $_POST['Address1'];
	$Empcode = $_POST['Emcode'];
	$Email = $_POST['Email'];
	$PhoneNo = $_POST['PhoneNo'];
    $desgn = $_POST['desgn'];
	$bloodgroup = $_POST['bloodgroup'];
	$OfficeName = $_POST['OfficeName'];
	$userid = $_POST['cid'];
	
	// Handle file upload safely
	$uploaded_file = '';
	if(isset($_FILES['emp_images']) && $_FILES['emp_images']['error'] == 0) {
		$target_path = "emp_images/";
		$target_path = $target_path . basename($_FILES['emp_images']['name']);

		if(move_uploaded_file($_FILES['emp_images']['tmp_name'], $target_path)) {
			$uploaded_file = $_FILES['emp_images']['name'];
			echo "<h4>The file ". basename($_FILES['emp_images']['name']). " has been uploaded Successfully.</h4>";
		} else {
			echo "Sorry there was an error uploading the file, please try again!";
		}
	}

	// Use the correct file field name consistently
	$sql = "INSERT INTO tbl_courier_officers (Manager_name, username, pass, address, email, ph_no, office, userType, reg_date,address1, blood_grp, desgn, images,empcode)
			VALUES ('$ManagerName','$uname', '$fpass', '$Address', '$Email', '$PhoneNo', '$OfficeName', '$userid', NOW(),'$Address1','$bloodgroup','$desgn','$uploaded_file','$Empcode')";
	$msg = ""; // Initialize the $msg variable to an empty string to store the message to be displayed on the success page. This will be used to determine the message to display based on the result of the query.
	// Check if username already exists
	$check_username = mysqli_query($con, "SELECT username FROM login WHERE username = '$uname'");
	if(mysqli_num_rows($check_username) > 0) {
		$msg = "username_exists"; // Set the $msg variable to a specific value to indicate that the username already exists.
		header('Location: manager-add-success.php?msg='.$msg);
		exit();
	}

	if(mysqli_query($con,$sql)) {
		$data=mysqli_query($con,"select Max(cid) from tbl_courier_officers");
		if($data && mysqli_num_rows($data) > 0) {
			while($rec=mysqli_fetch_row($data)) {
				$idd=$rec[0];
			}

			$sqlt="INSERT INTO `login` (`username`, `password`, `cpassword`, `type`, `rid`) VALUES ('$uname', '$fpass', '$Confpass', '$desgn', '$idd')";
			if(mysqli_query($con,$sqlt)) {
				$msg = "success";
			} else {
				$msg = "login_error";
				echo "Login table error: " . mysqli_error($con);
			}
		} else {
			$msg = "data_error";
			echo "Error getting max cid: " . mysqli_error($con);
		}
	} else {
		$msg = "error";
		echo "Manager insert error: " . mysqli_error($con);
	}
	
if(isset($_POST['submit']))
 {


$message=' Hello '.$_POST['ManagerName'].',<br>
Welcome to Vivanta Logistics.<br>
Please see below your Registration Details
<br>
Your User Name: '.$_POST['uname'].'<br>
Your Password : '.$_POST['Password'].' <br>

Click here to login https://vivantalogistics.in/vivanta/index.php
';

$my_email = "<EMAIL>";
$headers = "MIME-Version: 1.0" . "\r\n";
$headers .= "Message-ID: <".gettimeofday()." TheSystem@Runanubandh>\r\n";
$headers .= "X-Mailer: PHP v Balaji\r\n";
date_default_timezone_set( 'Asia/Calcutta' );
$headers .= "BCC: <EMAIL>,<EMAIL> \r\n";
$headers .= "Content-type: text/html\r\n"; 
$headers .= "BCC:<EMAIL>,".$_POST['Email']."\r\n";
//$from="<EMAIL>";
$subject = "Vivanta Logistics Manager Registration Successfully..";
 mail($my_email,$subject,$message,$headers);

}
header('Location: manager-add-success.php?msg='.$msg); // Redirect to the success page with the message parameter set to the value of $msg

}//addNewOffice

function updateStatus() {
global $con; // Add this line to access the global $con variable
$date = date('Y/m/d h:i:s', time()); // Get the current date and time in MySQL format
	
	 $OfficeName = $_POST['OfficeName'];
	 $status = $_POST['status'];
	 $comments = $_POST['comments'];
	 $cid = (int)$_POST['cid'];
	 $cons_no1 = $_POST['cons_no'];
	  $userid = $_POST['user'];
	   $from = $_POST['offic'];
			
	//$sql = "INSERT INTO tbl_courier_track (cid, cons_no, current_city, status, comments, bk_time)
	//	VALUES ($cid, '$cons_no1', '$OfficeName', '$status', '$comments', NOW())";
//	dbQuery($sql);
	$target_path = "PHP-API/images/";

$target_path = $target_path.$_FILES['photo']['name']; 
//print_r($_FILES['photo']);exit;

if(move_uploaded_file($_FILES['photo']['tmp_name'], $target_path))
{
  "<h4>The file ". basename($_FILES['photo']['name']). " has been uploaded Successfully.</h4>";
}
else
{
	 "Sorry there was an error uploading the file, please try again!";
} 


 $sql_1 = "UPDATE tbl_courier SET status= '$status',pod_img='".$_FILES['photo']['name']."', status_date = NOW() ,remark='$comments' WHERE cons_no = '$cons_no1'";
mysqli_query($con,$sql_1);

	
$sql2 = "INSERT INTO `currentloc` (`consig_no`, `userid`, `sourcesid`, `current_loc`, `current_date`, `currentstatus`, `remark`, `book_date`,`pod_img`) VALUES ('$cons_no1', '$userid', '$from', '$OfficeName', NOW(), '$status', '$comments', NOW(),'".$_FILES['photo']['name']."')";
	 
mysqli_query($con,$sql2);


 $insertquery="INSERT INTO `status_history_tbl`(`Cons_Id`, `status`, `Date`, `remark`) VALUES ('$cons_no1','$status','$date','$comments')";
mysqli_query($con,$insertquery);		header('Location: update-success.php');

}//addNewOffice

function addstationary(){
global $con; // Add this line to access the global $con variable
$stitems= $_POST['stitems'];$description = $_POST['description'];
$remark= $_POST['remark'];$Packupdate = $_POST['Packupdate'];$emname=$_POST['emname'];$fromdocket= $_POST['fromdocket'];$todocket= $_POST['todocket'];
$sql1 = "INSERT INTO stationary_isssue(`stitems`,`description`,`fromdocket`,`todocket`,`emname`,`remark`,`Packupdate`) VALUES('$stitems','$description','$fromdocket','$todocket','$emname','$remark','$Packupdate')";	

	
	if(mysqli_query($con,$sql1))
	
 {
    $msg="yes1";	
  }
  
 
  else
  {
   $msg="no1"; 
  }
	 header("location: stationaruReg.php?msg=".$msg);
	 ob_end_clean();

 }	

 function addBranch(){
    global $con; // Add this line to access the global $con variable
    $address = mysqli_real_escape_string($con, $_POST['branch_address']);
    $branchname = mysqli_real_escape_string($con, $_POST['branch_name']);

    $sql1_branch = "INSERT INTO branches(`name`,`address`) VALUES('$branchname','$address')";

    if(mysqli_query($con,$sql1_branch)) {
        $msg="yes1";
    } else {
        $msg="no1";
    }
    header("location: addBranch.php?msg=".$msg);
}

function editBranch(){
    global $con; // Add this line to access the global $con variable
    $address = mysqli_real_escape_string($con, $_POST['branch_address']);
    $branchname = mysqli_real_escape_string($con, $_POST['branch_name']);
    $id = mysqli_real_escape_string($con, $_POST['id']);

    $sql1 = "UPDATE `branches` SET `name`='$branchname' ,`address`='$address' WHERE id='$id'";

    if(mysqli_query($con,$sql1)) {
        $msg="yes";
    } else {
        $msg="no";
    }
    header("location: updateBranch.php?msg=".$msg);
}	

function deleteBranch(){
    global $con; // Add this line to access the global $con variable
    $id = mysqli_real_escape_string($con, $_GET['id']);
    $sql1="DELETE FROM `branches` WHERE Id='$id'";

    if(mysqli_query($con,$sql1)) {
        $msg="yes2";
    } else {
        $msg="no2";
    }
    header("location: addBranch.php?msg=".$msg);
}

function udatereceiver(){
	global $con; // Add this line to access the global $con variable

	$uid = mysqli_real_escape_string($con, $_POST['cid']);
	$receivercode = mysqli_real_escape_string($con, $_POST['rccode']);
	$receivername = mysqli_real_escape_string($con, $_POST['rcname']);
	$receiverphone = mysqli_real_escape_string($con, $_POST['rcphone']);
	$receiveraddress = mysqli_real_escape_string($con, $_POST['rcaddress']);
	$receiveremail = mysqli_real_escape_string($con, $_POST['rcemail']);
	$receiverzip = mysqli_real_escape_string($con, $_POST['rczip']);
	$states = mysqli_real_escape_string($con, $_POST['states']);
	$city = mysqli_real_escape_string($con, $_POST['city']);
	$rcpan = $_POST['rcpan'];
	$gst = $_POST['rcgst'];
	$cphone = $_POST['cphone'];
	$id = $_POST['id'];

	$sql1 = "UPDATE receiver_reg SET
			userid='$uid',
			receiver_code='$receivercode',
			receivername='$receivername',
			receiverphone='$receiverphone',
			receiveremail='$receiveremail',
			receiveradd='$receiveraddress',
			receiverzip='$receiverzip',
			receiversts='$states',
			receivercity='$city',
			rcgst='$gst',
			rcpan='$rcpan',
			contact_per_name='$cphone'
			WHERE id='$id'";

	if(mysqli_query($con,$sql1)) {
		$msg="yes1";
	} else {
		$msg="no1";
	}
	header("location: receiver_reg.php?msg=".$msg);
}

function changePass(){
	global $con; // Add this line to access the global $con variable

	$username = mysqli_real_escape_string($con, $_POST['username']);
	$oldpass = mysqli_real_escape_string($con, $_POST['oldpass']);
	$newpass = mysqli_real_escape_string($con, $_POST['newpass']);
	$confirmpass = mysqli_real_escape_string($con, $_POST['confirmpass']);

	// Check if new password and confirm password match
	if($newpass != $confirmpass) {
		header("location: change_password.php?msg=nomatch");
		return;
	}

	// Verify old password
	$sql_check = "SELECT * FROM login WHERE username='$username' AND password='$oldpass'";
	$result = mysqli_query($con, $sql_check);

	if(mysqli_num_rows($result) > 0) {
		// Update password
		$sql_update = "UPDATE login SET password='$newpass', cpassword='$confirmpass' WHERE username='$username'";

		if(mysqli_query($con, $sql_update)) {
			$msg = "yes";
		} else {
			$msg = "no";
		}
	} else {
		$msg = "wrongpass";
	}

	header("location: change_password.php?msg=".$msg);
}

function logOut(){
	if(isset($_SESSION['user_name'])){
		unset($_SESSION['user_name']);
	}
	if(isset($_SESSION['user_type'])){
		unset($_SESSION['user_type']);
	}
	session_destroy();
	header('Location: index.php');
}//logOut

?>