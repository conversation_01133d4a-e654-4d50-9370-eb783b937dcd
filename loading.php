<?php
session_start();
require_once('database.php');
require_once('library.php');
require 'connection.php';
isUser();

$a=$_SESSION['username'];
 $sql="select * from tbl_courier_officers where username='$a'";
$result = mysqli_query($con, $sql);
$row1 = mysqli_fetch_array($result, MYSQLI_BOTH);
 $uoffice = ($row1 && isset($row1['office'])) ? $row1['office'] : '';
 $uaddress = ($row1 && isset($row1['address'])) ? $row1['address'] : '';
 $id = ($row1 && isset($row1['cid'])) ? $row1['cid'] : 0;


?>
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>
	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
<script language="JavaScript">
var checkflag = "false";
 //---- Checks consignment is selected or not script start-----
function check(field) {
if (checkflag == "false")
 {
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll")
	{
	field[i].checked=true;	
	}
	}
	checkflag = "true";
}
else
{
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll")
	{
	field[i].checked=false;
	}
	}
	checkflag = "false";
}

} //---- Checks consignment is selected or not script end-----
//---- Delete the consignment script start-----
function confirmDel(field,msg)
{
	count=0;
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll" && field[i].checked==true)
	{
	count++;
	}
	}
	
	if(count == 0)
	{
		alert("Select any one record to delete!");
		return false;
	}
	else
	{
		return confirm(msg);
	}
}
//---- Delete the consignment script end-----
</script>
</head>
<?php include("header.php"); ?>
		<div class="container">
			<div class="row">
               <div class="span11">
					<div class="account-list-outer">

						<div class="titleHeader clearfix">
							<h3> Loading Sheet   </h3>
							<h5 align="center"> Source :<b><?php echo $uoffice;?> </b></h5>
                                                         
						</div><!--end titleHeader <form  method="post" >   -->
        <form  action="loadingIn.php" method="post" name="loading" >
				    	<!--<div class="row" >
     			    <div class="span3">	
					    <div class="control-group">
                            <div class="controls">
							 <label class="control-label"> <input type="radio" name="huback" id="huback" value="HUB" onchange="filterbytype('HUB_Office');"> HUB Office</label>
							 </div>
					    </div>
					</div>	
					<div class="span3">	
					    <div class="control-group">
                            <div class="controls">
					        <label class="control-label"> <input type="radio" name="huback" id="huback" value="Branch" onchange="filterbytype('Branch_Office');"> Branch Office </label>
						 </div>
					    </div>
					</div>	
					<div class="span2">	
					    <div class="control-group">
                            <div class="controls">
					           <label class="control-label">  <input type="radio" name="huback" id="huback" value="De" onchange="filterbytype('Deli_Vehicle');"> Delivery Vehicle </label>
						    </div>
					    </div>
					</div>	
					<div class="span2">	
					    <div class="control-group">
                            <div class="controls">
								<label class="control-label"> <input type="radio" name="huback" id="huback" value="#" onchange="filterbytype('Deli_Boy');"> Delivery Boy </label>
					           </div>
					    </div>
					</div>	
				</div> -->
				<div class="row" >
     			    <div class="span5" >
					     <div class="control-group">
                            <label>Source </label>
							<div class="controls">
	                           <input type="text" name="source" id="source" value="<?php echo $uoffice;?>" readonly>
							 <input type="hidden" name="offic" id="offic" value="<?php echo $id;?>">
							<input type="hidden" name="stas" id="stas"  value="2">
							<input type="hidden" name="user" id="user" value="<?php echo $id;?>">
							
							</div>
					    </div>
					</div>	
					<div class="span5" >	
					   <div class="control-group">
					   <label>Destination</label>
						<div class="controls"><input type="text" name="mloca" id="mloca" value="" >
	                            <!--<select name="mloca" id="mloca" value="">
												<option value="">-- Please select --</option>
												<!--<?php echo $loc; ?>-->
								<!--</select>-->
						</div>
				       </div>
				    </div>	
				</div>
	
				<div class="row"><!--<th><input type="checkbox" name="check_all" id="check_all" onClick="checkAll(this)"> All </th>-->
				<table  id="tb" class="table">
      				<thead> <tr >
      					    	<th>Sr.No</th>
								<th>C/N No</th>
								<th>Zipcode</th>
								<th>Qnty</th>
								<th>Weight</th>
								<th>Invoice No</th>
                                                                <th>Invoice Value</th> 
								<th>Pay_mode</th>
								<th>Fright</th>
								<th>Mode</th>
								<th>ODA</th>
					</tr> </thead>
				</table>
<input type="hidden" name="ct" id="ct" value="1" >
<table border="0"><tr><td>
<input class="btn btn-primary" value="Add Another" type="button" id="add" onClick="add1()">
</td><td> 
<input class="btn btn-primary" value="Delete" type="button" id="del" onClick="del1()">
	</td> </tr></table>	
			</div>
				
   				<div class="row">
				    <div class="span 3">
						<div class="control-group">
						   <label>Total Consignment</label>
							<div class="controls"> 
								 <input type="text" name="totcons" id="totcons"class="input-small"  value="0" readonly>
								 
							</div>
						</div>
					</div>	
					<div class="span 3">	
						<div class="control-group">
						   <label>Total Quantity</label>
							<div class="controls"> 
								 <input type="text" name="totqty" id="totqty" class="input-small" value="0" readonly>
								
							</div>
						</div>
				    </div>
					<div class="span 3">	
						<div class="control-group">
						   <label>Total Weight</label>
							<div class="controls"> 
								 <input type="text" name="totwei" id="totwei" class="input-small" value="0" readonly>
								 
							</div>
						</div>
					</div>
					<div class="span 3">	
							<div class="control-group">
								<label>Total Freight</label>
								<div class="controls"> 
									<input type="text" name="totfrigh" id="totfrigh" class="input-small" value="0" readonly>
								
								</div>
							</div>
					</div>
                                         <div class="span 3">	
							<div class="control-group">
								<label>Total Invoice Value</label>
								<div class="controls"> 
									<input type="text" name="totinvo" id="totinvo" class="input-small" value="0" readonly>
									
								</div>
							</div>
					</div>
					
			</div>
								
				<div class="control-group">
						<div class="controls"> 
							<input type="hidden" name="uaddress" value="<?php echo $uaddress;?>">
							
					    </div>
				</div>
				
			<div class="row">
				    <div class="span5">
						<div class="control-group">
						   <label>Vehicle Number</label>
							<div class="controls"> 
								 <input type="text" name="vehino" id="vehino" onChange="vehi(this.value);">
								 
							</div>
						</div>
						<div class="control-group">
						   <label>Driver Name</label>
							<div class="controls"> 
								 <input type="text" name="dname" id="dname" value="" readonly >
								
							</div>
						</div>
						 
					</div>	
					<div class="span5">	
						<div class="control-group">
						   <label>Vehicle Type</label>
							<div class="controls"> 
								 <input type="text" name="vehitype" id="vehitype" value="" readonly>
								
							</div>
						</div>
						<div class="control-group">
						   <label>Driver Contact No :</label>
							<div class="controls"> 
								 <input type="text" name="dcont" id="dcont" value="" readonly >
								
							</div>
						</div>
					</div>
				</div>	
					<div class="row">
						<div class="span 4">					
							<div class="control-group">
							   <label><B>Total Amount(Rs) :</B></label>
								<div class="controls"> 
									 <input type="text" name="totamnt" id="totamnt" value="" onKeyUp="getValues1()">
								
																</div>
							</div>
						</div>
						<div class="span 4">					
							<div class="control-group">
							   <label><B>Advance (Rs) :</B></label>
								<div class="controls"> 
									 <input type="text" name="advan" id="advan" value="" onKeyUp="getValues1()">
									
								</div>
							</div>
						</div>
						<div class="span 4">					
							<div class="control-group">
							   <label><B>Balances (Rs) :</B></label>
								<div class="controls"> 
									 <input type="text" name="ban" id="ban" value="" readonly >
								</div>
							</div>
						</div>
					</div>
					
			
			<div class="row" align="center">	
			    <div class="span8">
				    <div class="control-group">
						<div class="controls"> 
 <input name="submit" class="btn btn-primary" type="submit" value="Save" onClick="return valid()" >&nbsp;&nbsp;&nbsp;<button type="reset" id="backbutton" class="btn ">Clear</button> &nbsp;&nbsp; <input type="button" class='btn btn-primary' id="printpagebutton" onClick="printpage();" value="Print">
					    </div>
					</div>
				</div>
			</div>			
						</form>
					</div><!--end -->
				</div><!--end span-->
			</div>

<script>
function add1(){ 
        //alert("hii");
  var rowCount=$('#tb tr').length;

$("#tb").append('<tr class="table"><td>'+rowCount+'</td><td><input name="consign'+rowCount+'" id="consign'+rowCount+'" class="input-small" onchange="to(this.value,'+rowCount+'); "></td><td><input name="zip'+rowCount+'" id="zip'+rowCount+'" class="span1" onKeyup="calstageamt(this.value,'+rowCount+');" readonly></td><td><input name="qty'+rowCount+'" id="qty'+rowCount+'" class="span1" readonly value="0"></td><td><input name="wegh'+rowCount+'" id="wegh'+rowCount+'" value="0" class="input-small" readonly></td><td><input name="inno'+rowCount+'" id="inno'+rowCount+'" class="input-small" readonly></td><td><input name="inval'+rowCount+'" id="inval'+rowCount+'" value="0" class="input-small" readonly></td><td><input name="type'+rowCount+'" id="type'+rowCount+'" class="span1" readonly></td><td><input name="frig'+rowCount+'" id="frig'+rowCount+'" value="0" class="input-small" readonly></td><td><input name="mode'+rowCount+'" id="mode'+rowCount+'" class="span1" readonly></td><td><input name="oda'+rowCount+'" id="oda'+rowCount+'" class="span1" readonly></td></tr>');

     $("#ct").val(rowCount);
    $("#totcons").val(rowCount);
	

    }

</script>

<script>
function to(idaa,str) { 
  //static frighttot=0;
        var count=0;
  //$('#tb tr:not(:first)').remove();
    $.ajax({                                      
      url: 'ajaxgetConsigDetail.php?cons='+idaa,                  //the script to call to get data          
      data: "",                        //you can insert url argumnets here to pass to api.php
                                       //for example "id=5&parent=6"
      dataType: 'json',                //data format      
      success: function(data)          //on recieve of reply
      {
   // alert(data);
            $.each(data, function(index, data) { 
       $('#zip'+str).val(data.zips);
	   $('#qty'+str).val(data.qtys);
	   $('#wegh'+str).val(data.wegs);
	   $('#inno'+str).val(data.innos);
	   $('#type'+str).val(data.types);
	   $('#mode'+str).val(data.modes);
	   $('#oda'+str).val(data.odas);
	   $('#frig'+str).val(data.frigs);
           $('#inval'+str).val(data.invals);
	   
	
	     });
      $('#count').val(count);
	   }
       });
}
</script>
		
<script >
function calstageamt(dfg,str)
{ 
   rowCount=($('#tb tr').length);
rowCount1=rowCount-1;
alert("Conform Consignment_" +rowCount1);
    var qt=0; var we=0; var fr=0; var iv=0;
		
  for(i=1;i<rowCount;i++)
  {   
      var ww=$("#wegh"+i).val();
      var qq=$("#qty"+i).val(); 
	  var ff=$("#frig"+i).val(); 
	  var ii=$("#inval"+i).val(); 
    
     if(ww=="")
	 { ww=0;}
      if(qq=="")
	 { qq=0;}
		if(ff=="")
	 { ff=0;}
		if(ii=="")
	 { ii=0;}
   qt=qt+parseFloat(qq);   
   we=we+parseFloat(ww);
   fr=fr+parseFloat(ff);
   iv=iv+parseFloat(ii);
  }
 document.getElementById("totqty").value = qt;
 document.getElementById("totwei").value = we;
 document.getElementById("totfrigh").value = fr;
 document.getElementById("totinvo").value = iv;
 }
</script>
	
<script> 
 function del1()
{ 
 rowCount=0;
 rowCount=$('#tb tr').length;
 
var table = document.getElementById("tb");
 table.deleteRow(rowCount -1);

 rowCount1=$('#tb tr').length;
// alert(rowCount1);
 rowCount=rowCount1-1;
 //alert(rowCount);
 
    document.getElementById("totcons").value = rowCount;
  var qqa=$("#qty"+rowCount).val(); 
  var wwa=$("#wegh"+rowCount).val();
  var ffa=$("#frig"+rowCount).val(); 
  var iia=$("#inval"+rowCount).val(); 
   
  if(rowCount==0)
  {
	  document.getElementById("totqty").value=0;
	  document.getElementById("totqty").value=0;
	  document.getElementById("totwei").value = 0;
	  document.getElementById("totfrigh").value = 0;
	  document.getElementById("totinvo").value = 0;
	   }
	   else{
		     var qq1=document.getElementById("totqty").value;
			 var ww1=document.getElementById("totwei").value;
			 var ff1=document.getElementById("totfrigh").value;
			 var ii1=document.getElementById("totinvo").value;
			 
			 var totqq=qq1-qqa;
			 var totww=ww1-wwa;
			 var totff=ff1-ffa;
			 var totii=ii1-iia;
			 
		    document.getElementById("totqty").value=totqq;
		    document.getElementById("totwei").value = totww;
			document.getElementById("totfrigh").value = totff;
			document.getElementById("totinvo").value = totii;
		  }
  }
</script>

<script  type="text/javascript">
function filterbytype(type)
{
//alert("type="+type);
$('#huback').val(type);

$('#mloca').find('option').remove().end().append('<option value="">--- Select '+type+'---</option>').val('');
	// Ajax post
	jQuery.ajax({
	type: "POST",
	url: "ajaxfiltertype.php?type="+type,
	dataType: 'json',
	data: {
		
	},
	//cache: false,	
	success: function(data) { 
	
	   		if (data)
			{  
				$.each(data, function(index, data) 
				{
				$('#mloca').append( $('<option></option>').val(data.id).html(data.name) );	

			} );
			
			} 
		} 
		
	});
$('#status').find('option').remove().end().append('<option value="">--- Select Status---</option>').val('');
	// Ajax post
	jQuery.ajax({
	type: "POST",
	url: "ajax_getstatus.php?type="+type,
	dataType: 'json',
	data: {
		
	},
	//cache: false,	
	success: function(data) { 
	
	   		if (data)
			{  
				$.each(data, function(index, data) 
				{
				$('#status').append( $('<option></option>').val(data.id).html(data.name) );	

			} );
			
			} 
		} 
	});
}
</script>

<script>
function vehi(a)
{ 
//alert(a);
$.ajax({                                      
      url: 'ajaxgetConsigDet.php?vehinoa='+a,                  //the script to call to get data          
      data: "",                        //you can insert url argumnets here to pass to api.php
                                       //for example "id=5&parent=6"
      dataType: 'json',                //data format      
      success: function(data)          //on recieve of reply
      {
   // alert(data);
            $.each(data, function(index, data) { 
      // alert(data);
	  $("#vehitype").val(data.vehity);
	  $("#dname").val(data.vehidr);
	  $("#dcont").val(data.drcon);
	       
       });
     }
       });
}
</script>	
		
<script>
function getValues1(){
	var totamt = Number(document.getElementById("totamnt").value);
	var advances = Number(document.getElementById("advan").value);
		
	var totwe= totamt - advances ;
	
	document.getElementById("ban").value = totwe;
}
</script>	

<script type="text/javascript">
    function printpage() {
        //Get the print button and put it into a variable
        var printButton = document.getElementById("printpagebutton");
        var back=document.getElementById("backbutton");
        //Set the print button visibility to 'hidden' 
        printButton.style.visibility = 'hidden';
        back.style.visibility = 'hidden';
        //Print the page content
        window.print();
        //Set the print button to 'visible' again 
        //[Delete this line if you want it to stay hidden after printing]
        printButton.style.visibility = 'visible';
         back.style.visibility = 'visible';
    }
</script>
<script type="text/javascript">

function closeWin() {
    back.close();
}
    window.onbeforeunload = function() {
        
    }
</script>


     <!--<?php			
								/*$msg=$_GET['msg'];
								if($msg=="yes1")
								{
						echo "<script> alert('Customer Updated Successfully');</script>";
								}
								else if($msg=="no1"){
						echo "<script> alert('Customer Not Updated Successfully');</script>";
								}
								else if($msg=="yes"){
						echo "<script> alert('Consignment Move Successfully');</script>";
								}
								else if($msg=="no"){
						echo "<script> alert('Consignment Moved unsuccessfully');</script>";
								}*/
								?>       --> 


<?php include("footer.php"); ?>
