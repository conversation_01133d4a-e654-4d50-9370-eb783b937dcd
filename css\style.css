/*==== pull open sans & sanchez fonts from gooogle server ===*/
@import url(https://fonts.googleapis.com/css?family=Sanchez:400italic,400|Open+Sans:400italic,600italic,700italic,400,600);
/*
font-family: 'Sanchez', serif;
font-family: 'Open Sans', sans-serif;
*/

/*=========== body =============*/
body {
	color:#3e3e3e;
	font-size: 12px;
	line-height:auto;
	font-family: 'Open Sans', Tahoma, Arial sans-serif;
	font-style: normal;
	font-weight: normal;
	background:#321f1f;
}


/*==== heading ====*/
h1, h2, h3, h4, h5, h6 {
	margin:0; padding:0;
	color:#000000;
	font-family: 'Sanchez', serif;
	font-weight: 400;
}
h2 { font-size: 16px; text-transform: uppercase; line-height:22px; }
h3 { font-size:14px; text-transform: uppercase; line-height:22px; }
h4 { font-size: 14px; }

/*===== title header h3 =========*/
.titleHeader {
	background: none;
}
.titleHeader h3 {
	background:#fff;
	padding-right:8px;
	margin-bottom:10px;
	font-size: 20px;
	text-align: center;
	line-height: 24px;
}
.titleHeader .pagers {
	float:right;
	background:#fff;
	padding-left:8px;
}


/*=================== mainContainer ==============*/
#mainContainer {
	width:100%;
	display: block;
	margin:0 auto;
	background:#fff;
}
/*====== header style ======*/
header {
	margin-bottom:60px;
	padding:5px 0 0;
	width:100%;
}
	header p {
		line-height: 14px;
	}
/* upperHeader */
	.upperHeader {
		/*padding-top:4px;*/
		border-bottom:1px solid #eaeaea;
	}
	.upperHeader select.upper-nav {
		display: none;
	}
	.upperHeader .inline {
		margin:-2px 0 0 0;
		padding:0;
	}
	.upperHeader .inline li {
		margin:5px 0 5px 5px;
		padding:0;
	}
	.upperHeader .inline li a {
		font-size:13px;
		font-weight:700;
		line-height: 13px;
		color:#0033FF;
	}
	.upperHeader > p, header p {
		font-size:11px; 
	}
/* middleHeader */
	.middleHeader .middleContainer {
		padding:30px 0;
		background: none;
	}
/* logo */
	.middleHeader .siteLogo {
		float:left;
		margin-top:-10px;
	}
	.middleHeader .siteLogo a {
		width:400px;
		height:140px;
		display: block;
		font:0/0 a;
		background:url('../img/logo.png') no-repeat left top;
	}


/* search */
	.middleHeader .siteSearch {
		margin-left:10px;
	}
/* currency */
	.middleHeader .currency {
		min-width: 55px;
	}
/* language */
	.middleHeader .language {
		min-width: 65px;
	}
/* mainNav */
	.mainNav {
		background:#e7e7e7;
		border-top: 1px solid #d7d7d7;
	    border-bottom: 1px solid #d7d7d7;
	}


/*==================== cart-content ================*/
.cart-content {
	margin:0;
	padding:0;
	min-width: 350px;
}
	/*table-cart*/
	.cart-content .table-cart {
		width:100%;
	}
	/* tr */
	.cart-content .table-cart tbody tr {
		border-bottom:1px solid #dcdcdc;
	}
	/* td */
	.cart-content .table-cart td {
		margin:0;
		padding:12px 6px;
		vertical-align: top;
	}
	
	
	/* cart-product-info */
	.cart-content .table-cart .cart-product-info {
		text-align: left;
	}
	.cart-content .table-cart .cart-product-info img {
		float: left;
		margin-right:8px;
		padding: 4px;
		background-color:#fff;
	  	border: 1px solid #e0e0e0;
	  	background-color:#fff;
	  	-moz-box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
	  	-webkit-box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
	  	box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
	}
	.cart-content .table-cart td img:hover {
		border:1px solid #aaa;
	}
	/*cart-product-desc*/
	.cart-content .table-cart .cart-product-desc {
		width:200px;
	}
	.cart-content .table-cart td.cart-product-info p {
		margin-bottom:5px;
	}
	.cart-content .table-cart td.cart-product-info p a {
		white-space:pre-line;
		font-weight: 600;
		line-height: 18px;
	}
	.cart-content .table-cart td.cart-product-info .unstyled,
	.cart-content .table-cart td.cart-product-info .unstyled li {
		margin:0;
		padding:0;
		color:#888;
	}
	/*cart-product-setting*/
	.cart-content .table-cart td.cart-product-setting {
		text-align: right;
	}
	.cart-content .table-cart td.cart-product-setting p {
		margin-bottom:3px;
	}
	.cart-content .table-cart td.cart-product-setting a.remove-pro {
		font-size: 18px;
		color:#555;
		line-height: auto;
	}
	.cart-content .table-cart td.cart-product-setting a.remove-pro:hover {
		text-decoration: none;
	}
	
	
	


/*================  ===============*/
#featuredItems, #latestItems, #aboutUs, #twitterFeed, #facebookFeed, #brands {
	margin-top:72px;
}


/*===================== about us ======================*/
#aboutUs p {
	margin:24px 0 0 0;
	padding:0 10px 0 0;
	line-height: 24px;
}


/*============== .hProductItems ============*/
.hProductItems {
	list-style:none;
	*zoom: 1;
	margin:0;
	padding:0;
}
	.hProductItems li {
		margin-top:36px;

	}
	/* img */
	.hProductItems .thumbnail {
		display: block;
	  	padding: 4px;
	  	border: 1px solid #e0e0e0;
	  	background-color:#fff;
	  	-moz-box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
	  	-webkit-box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
	  	box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
	}
	.hProductItems .thumbnail:hover {
		border: 1px solid #aaa;
	}
	.hProductItems .thumbnail img {
		min-width: 100%;
	}
	/* thumbSetting */
	.hProductItems .thumbSetting {
		padding-left:0;
		padding-right:0;
	}
	.hProductItems .thumbSetting div {
		margin-top:10px;
	}
	/* title */
	.hProductItems .thumbSetting .thumbTitle a {
		line-height:20px;
		font-weight: 600;
	}
	/* price */
	.hProductItems .thumbSetting .thumbPrice {
		display: block;
		background: url('../img/dottedBorder.png') repeat-x 50% 70%;
	}
	.hProductItems .thumbSetting .thumbPrice span {
		background:#fff;
		font-weight: 700;
		font-size: 15px;
		color:#666;
		padding-right:8px;
	}
	.hProductItems .thumbSetting .thumbPrice span .strike-through {
		color:#999;
		text-decoration: line-through;
	}
	/* bttons */
	.hProductItems .thumbSetting .thumbButtons {
		margin:10px 0 0 0;
		float:left;
	}
	.hProductItems .thumbSetting .thumbButtons .btn {
		line-height:24px;
		font-size: 12px
	}
	/* rating */
	.hProductItems .thumbSetting .rating {
		float:right;
		list-style: none;
		margin:16px 0 0 0;
		padding:0;
	}
	.hProductItems .thumbSetting .rating li {
		margin:0;
		padding:0;
		float: left;
	}
	.hProductItems .thumbSetting .rating i.star-on {
		width:17px;
		height:17px;
		display: block;
		background:url('../img/star-on.png') no-repeat left top;
	}
	.hProductItems .thumbSetting .rating i.star-off {
		width:17px;
		height:17px;
		display: block;
		background:url('../img/star-off.png') no-repeat left top;
	}


/*============== .hProductItems ============*/
.listProductItems {
	list-style:none;
	*zoom: 1;
	margin:0;
	padding:0;
	width:100%;
}
	.listProductItems li {
		margin-top:36px;
	}
	/* img */
	.listProductItems .thumbnail {
		display: block;
	  	padding: 4px;
	  	border: 1px solid #e0e0e0;
	  	background-color:#fff;
	  	-moz-box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
	  	-webkit-box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
	  	box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
	}
	.listProductItems .thumbnail:hover {
		border: 1px solid #aaa;
	}
	.listProductItems .thumbnail img {
		width: 100%;
	}
	/* thumbSetting */
	.listProductItems .thumbSetting > div {
		margin:0;
		padding:12px 0;
		border-bottom: 1px dotted #ccc;
	}
	.listProductItems .thumbSetting div:first-child {
		padding-top:0;
	}
	.listProductItems .thumbSetting div:last-child {
		border:none;
	}
	/* title */
	.listProductItems .thumbSetting .thumbTitle a {
		line-height:20px;
		font-family: 'Sanchez', serif;
		font-size: 14px;
		font-weight: normal;
	}
	/* price */
	.listProductItems .thumbSetting .thumbPriceRate {
		display: block;
	}
	.listProductItems .thumbSetting .thumbPriceRate span {
		float: left;
		background:#fff;
		font-weight: 700;
		font-size: 18px;
		color:#666;
		padding-right:8px;
	}
	/* rating */
	.listProductItems .thumbSetting .thumbPriceRate .rating {
		float:left;
		list-style: none;
		margin:0 12px 0;
		padding:0;
	}
	.listProductItems .thumbSetting .thumbPriceRate .rating li {
		margin:0;
		padding:0;
		float: left;
	}
	.listProductItems .thumbSetting .thumbPriceRate .rating i.star-on {
		width:17px;
		height:17px;
		display: block;
		background:url('../img/star-on.png') no-repeat left top;
	}
	.listProductItems .thumbSetting .thumbPriceRate .rating i.star-off {
		width:17px;
		height:17px;
		display: block;
		background:url('../img/star-off.png') no-repeat left top;
	}
	.listProductItems .thumbSetting p {
		margin:0;
		padding:0;
	}
	/* bttons */
	.listProductItems .thumbSetting .thumbButtons {
		margin:0;
		float:left;
	}
	.listProductItems .thumbSetting .thumbButtons .btn {
		line-height:24px;
		font-size: 12px
	}
	



/*============== .vProductItems ============*/
.vProductItems {
	*zoom: 1;
	margin:0;
	padding:0;
	list-style: none;
	overflow: hidden;
}
	.vProductItems li {
		margin:36px 0 0 0;
		padding:0;
		overflow: hidden;
	}
	.vProductItems .thumbImage {
		float:left;
		margin-right:6px;
		padding: 4px;
	  	border: 1px solid #e0e0e0;
	  	border-radius: 4px;
	  	-webkit-border-radius: 4px;
	  	-moz-border-radius: 4px;
	  	-o-border-radius: 4px;
	  	background-color:#fff;
	  	-moz-box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
	  	-webkit-box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
	  	box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
	  -webkit-transition: all 0.2s ease-in-out;
	  -moz-transition: all 0.2s ease-in-out;
	  -o-transition: all 0.2s ease-in-out;
	  transition: all 0.2s ease-in-out;
	}
	.vProductItems .thumbImage:hover {
		border: 1px solid #aaa;
	}
	.vProductItems .thumbImage img {
		min-width: 100%;
	}
	/* thumbSetting */
	.vProductItems .thumbSetting {
		width:60%;
		float:left;
	}
	.vProductItems .thumbSetting div {
		margin-bottom:10px;
	}
	.vProductItems .thumbSetting div:last-child {
		margin-bottom:0;
	}
	.vProductItems .thumbSetting .thumbTitle a {
		line-height:20px;
		font-weight: 600;
	}
	.vProductItems .thumbSetting .thumbPrice {
		display: block;
		background: url('../img/dottedBorder.png') repeat-x 50% 70%;
	}
	.vProductItems .thumbSetting .thumbPrice span {
		background:#fff;
		font-weight: 700;
		font-size: 14px;
		color:#666;
		padding-right:8px;
	}
	/* rating */
	.vProductItems .thumbSetting .rating {
		list-style: none;
		margin:0;
		padding:0;
	}
	.vProductItems .thumbSetting .rating li {
		margin:0;
		padding:0;
		float: left;
	}
	.vProductItems .thumbSetting .rating i.star-on {
		width:17px;
		height:17px;
		display: block;
		background:url('../img/star-on.png') no-repeat left top;
	}
	.vProductItems .thumbSetting .rating i.star-off {
		width:17px;
		height:17px;
		display: block;
		background:url('../img/star-off.png') no-repeat left top;
	}



/*============== .vProductItems ============*/
.special {
	margin-top:60px;
}
.vProductItemsTiny {
	*zoom: 1;
	margin:0;
	padding:0;
	list-style: none;
	overflow: hidden;
}
	.vProductItemsTiny li {
		margin:24px 0 0 0;
		padding:0;
	}
	.vProductItemsTiny .thumbImage {
		float:left;
		margin-right:6px;
		padding: 3px;
	  	border: 1px solid #e0e0e0;
	  	border-radius: 4px;
	  	-webkit-border-radius: 4px;
	  	-moz-border-radius: 4px;
	  	-o-border-radius: 4px;
	  	background-color:#fff;
	  	-moz-box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
	  	-webkit-box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
	  	box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
	  -webkit-transition: all 0.2s ease-in-out;
	  -moz-transition: all 0.2s ease-in-out;
	  -o-transition: all 0.2s ease-in-out;
	  transition: all 0.2s ease-in-out;
	}
	.vProductItemsTiny .thumbImage:hover {
		border: 1px solid #aaa;
	}
	.vProductItemsTiny .thumbImage img {
		width: 100%;
	}
	/* thumbSetting */
	.vProductItemsTiny .thumbSetting {
		width:40%;
		float:left;
		word-spacing: pre-line;
	}
	.vProductItemsTiny .thumbSetting div {
		margin-bottom:10px;
		word-spacing: pre-line;
	}
	.vProductItemsTiny .thumbSetting div:last-child {
		margin-bottom:0;
	}
	.vProductItemsTiny .thumbSetting .thumbTitle a {
		line-height:20px;
		font-weight: 600;
		word-spacing: pre-line;
	}
	.vProductItemsTiny .thumbSetting .thumbPrice {
		display: block;
		background: url('../img/dottedBorder.png') repeat-x 50% 70%;
	}
	.vProductItemsTiny .thumbSetting .thumbPrice span {
		background:#fff;
		font-weight: 700;
		font-size: 14px;
		color:#666;
		padding-right:8px;
	}
	/* rating */
	.vProductItemsTiny .thumbSetting .rating {
		list-style: none;
		margin:0;
		padding:0;
	}
	.vProductItemsTiny .thumbSetting .rating li {
		margin:0;
		padding:0;
		float: left;
	}
	.vProductItemsTiny .thumbSetting .rating i.star-on {
		width:17px;
		height:17px;
		display: block;
		background:url('../img/star-on.png') no-repeat left top;
	}
	.vProductItemsTiny .thumbSetting .rating i.star-off {
		width:17px;
		height:17px;
		display: block;
		background:url('../img/star-off.png') no-repeat left top;
	}



/*===================== product-details ===================*/

.product-details .product-title {
	margin-bottom:12px;
}
.product-set > div {
	padding:10px 0;
	background: url('../img/dottedBorder.png') repeat-x left bottom;
}
.product-set .product-price span {
	background:#fff;
	font-weight: 700;
	font-size: 14px;
	color:#666;
	padding-right:8px;
}
.product-set .product-price .strike-through {
	color:#999;
	text-decoration: line-through;
}
/* rating */
.product-set .product-rate span {
	margin-left:10px;
}
.product-set .product-rate .rating {
	list-style: none;
	margin:0;
	padding:0;
}
.product-set .product-rate .rating li {
	margin:0;
	padding:0;
	float: left;
}
.product-set .product-rate .rating i.star-on {
	width:17px;
	height:17px;
	display: block;
	background:url('../img/star-on.png') no-repeat left top;
}
.product-set .product-rate .rating i.star-off {
	width:17px;
	height:17px;
	display: block;
	background:url('../img/star-off.png') no-repeat left top;
}
/* product-info*/
.product-set .product-info .dl-horizontal {
	margin:0;
	padding:0;
}
.product-set .product-info .dl-horizontal dt {
	width:auto;
}
.product-set .product-info .dl-horizontal dd {
	float:left;
		margin-left: 20px;
}
/* product-inputs */
.product-set .product-inputs {
	padding:14px 0;
	background: none;
}
.product-set .product-inputs .controls-row {
	background:none;
	padding:0;
}
.product-set .product-inputs .input-append {
	background:none;
}



/*================== product-details ===================*/
.product-details .product-img {
	padding: 3px;
  	border: 1px solid #e0e0e0;
  	border-radius: 4px;
  	-webkit-border-radius: 4px;
  	-moz-border-radius: 4px;
  	-o-border-radius: 4px;
  	background-color:#fff;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
.product-details .product-img:hover {
	border: 1px solid #aaa;
}

.product-details .product-img-thumb img {
	margin:8px 3px 0 3px;
	padding: 2px;
  	border: 1px solid #e0e0e0;
  	border-radius: 4px;
  	-webkit-border-radius: 4px;
  	-moz-border-radius: 4px;
  	-o-border-radius: 4px;
  	background-color:#fff;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
.product-details .product-img-thumb img:hover {
	border: 1px solid #aaa;
}




/*========================= product-tab ==================*/
.product-tab {
	clear:both;
	margin-top:48px;
}
	/* read-review */
	.product-tab #read-review .single-review {
		padding-bottom:14px;
		margin-bottom:14px;
		background:url('../img/dottedBorder.png') repeat-x left bottom;
	}
	.product-tab #read-review .single-review:last-child {
		padding-bottom:none;
		margin-bottom:none;
		background-image:none;
	}
	.product-tab #read-review .single-review .review-header {
		margin-bottom:8px;
	}
	/* rating */
	.product-tab #read-review .single-review .rating {
		float:right;
		list-style: none;
		margin:0;
		padding:0;
	}
	.product-tab #read-review .single-review .rating li {
		margin:0;
		padding:0;
		float: left;
	}
	.product-tab #read-review .single-review .rating i.star-on {
		width:17px;
		height:17px;
		display: block;
		background:url('../img/star-on.png') no-repeat left top;
	}
	.product-tab #read-review .single-review .rating i.star-off {
		width:17px;
		height:17px;
		display: block;
		background:url('../img/star-off.png') no-repeat left top;
	}



/*================== related-product ===============*/
.related-product {
	margin-top:60px;
}


/*================= aside-inner ================*/
.aside-inner {
	margin-top:-60px;
}


/*===================== brandList =================*/
.brandList {
	border:1px solid #e0e0e0;
	margin:30px 0 0 0;
	padding:0;
	list-style: none;
}
	.brandList li {
		height:90px;
		border-right:1px solid #e0e0e0;
		margin:0;
		float:left;
		width: 233px;
		text-align: center;
		border-right:1px solid #e0e0e0;
	}
	.brandList li:last-child {
		border:none;
	}
	.brandList li a {
		display: block;
		width:100%;
		line-height:90px;
	}
	.brandList li img {
		margin:0 auto;

	 }



/*======================= pro-range-slider =======================*/
.pro-range-slider {
	margin-top:54px;
}
	.pro-range-slider .price-range {
		margin-top:24px;
	}
	.pro-range-slider .price-range p {
		margin:0 0 5px 0;
	}
	.pro-range-slider .price-range label {
		float:left;
		cursor:default; 
	}
	.pro-range-slider .price-range input {
		float:left;
		width:80px;
		height:auto;
		padding:0;
		margin:0 0 0 8px;
		color:#666;
		font-weight:600;
		border:0;
		font-size: 12px;
	}
	.pro-range-slider .price-range input:focus {
		-webkit-box-shadow:none;
		-moz-box-shadow:none;
		box-shadow:none;
	}
	.pro-range-slider .ui-slider {
		margin:0;
		height:8px;
		background:#ddd;
		border:1px solid #c5c5c5;
	}
	.pro-range-slider .ui-widget-header {
		background-image: none;
		background-color: #666;
	}
	.pro-range-slider .ui-state-default,
	.pro-range-slider .ui-widget-content .ui-state-default,
	.pro-range-slider .ui-widget-header .ui-state-default{
		background-image: none;
		background-color:#c2c2c2;
		border-radius: 40px;
		-webkit-border-radius:40px;
		border-radius:40px;
		-webkit-box-shadow:inset 0 1px 3px #9f9f9f;
		outline: none;
		border:none;
	}




/*===================== productFilter ===================*/
.productFilter {
	margin:36px 0 0 0;
	padding: 5px 10px;
	background:#f0f0f0;
	border-top:1px dotted #b6b6b6;
	border-bottom:1px dotted #b6b6b6;
}
	.productFilter div {
		margin:0 12px 0 0;
		padding:0;
	}
	.productFilter div:last-child {
		margin-right:0;
	}
	.productFilter select {
		width:auto;
		height:auto;
		margin:0 0 0 2px;
	}



/*===================== blog-article =================*/
.blog-article {
	padding-bottom:36px;
	margin-bottom:36px;
	background:url('../img/dottedBorder.png') repeat-x left bottom;
}
.blog-article:last-child {
	padding-bottom:0;
	margin-bottom:0;
	background:none;
}
	/* blog img */
	.blog-article .blog-img img {
		padding: 3px;
	  	border: 1px solid #e0e0e0;
	  	border-radius: 4px;
	  	-webkit-border-radius: 4px;
	  	-moz-border-radius: 4px;
	  	-o-border-radius: 4px;
	  	background-color:#fff;
	  	-moz-box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
	  	-webkit-box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
	  	box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
	  -webkit-transition: all 0.2s ease-in-out;
	  -moz-transition: all 0.2s ease-in-out;
	  -o-transition: all 0.2s ease-in-out;
	  transition: all 0.2s ease-in-out;
	}
	.blog-article .blog-img img:hover {
		border: 1px solid #aaa;
	}
	/* blog content */
	.blog-article .blog-content div {
		padding:8px 0;
		background:url('../img/dottedBorder.png') repeat-x left bottom;
	}
	/* title */
	.blog-article .blog-content .blog-content-title h2 {
		text-transform:none;
		font-size: 17px;
	}
	/* entry */
	.blog-article .blog-content .blog-content-entry {
		background:none; 
	}
	.blog-article .blog-content .blog-content-entry p {
		margin:0 0 12px 0;
		padding:0;
		line-height: 24px;
	}
	/* date */
	.blog-article .blog-content .blog-content-date li {
		margin-right:10px;
	}


/*================= about-author ================*/
.about-author img {
	margin-right:12px;
	padding: 3px;
  	border: 1px solid #e0e0e0;
  	border-radius: 4px;
  	-webkit-border-radius: 4px;
  	-moz-border-radius: 4px;
  	-o-border-radius: 4px;
  	background-color:#fff;
  	-moz-box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
  	-webkit-box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
  	box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
.about-author img:hover {
	border: 1px solid #aaa;
}


/*======================== user-comments ===================*/
.user-comments {
	margin-top:60px;
}
	.user-comments .media-list {
		margin-top:24px;
	}


/*======================= make-comment ==================*/
.make-comment {
	margin-top:60px;
}
	.make-comment form {
		margin-top:24px;
	}
	.make-comment form textarea {
		height:100px;
	}




/*================ blog-tab ===============*/
.blog-tab {
	margin-top:60px;
}
	/* tab links */
	.blog-tab .nav-tabs > li > a  {
		line-height:10px;
	}
	.blog-tab .nav-tabs > li > a  {
		line-height:14px;
		font-size: 14px;
	}



/*================== blog-category ===============*/
.blog-category {
	margin-top:60px;
}

/*================= blog-adds ==================*/
.blog-adds {
	margin-top:60px;
}
	.blog-adds img {
		padding: 3px;
	  	border: 1px solid #e0e0e0;
	  	border-radius: 4px;
	  	-webkit-border-radius: 4px;
	  	-moz-border-radius: 4px;
	  	-o-border-radius: 4px;
	  	background-color:#fff;
	  	-moz-box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
	  	-webkit-box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
	  	box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
	  -webkit-transition: all 0.2s ease-in-out;
	  -moz-transition: all 0.2s ease-in-out;
	  -o-transition: all 0.2s ease-in-out;
	  transition: all 0.2s ease-in-out;
	}
	.blog-adds img:hover {
		border: 1px solid #aaa;
	}


/*================= blog-twitter ===================*/
.blog-twitter {
	margin-top:60px;
}




/*=========================== table-compare ===================*/

.table-compare th,
.table-compare td{
  margin:0;
  padding:12px 14px;
}
.table-compare td {
  border-left:1px dotted #d2d2d2;
}
.table-compare tr:last-child td  {
	background-image:none;
  /*background:none;*/
}
.table-compare td.aligned-color {
  background-color:#f1f1f1;
  text-align: right;
  border-left:none;
}
/* price */
.table-compare td.price {
	font-weight: 700;
	font-size: 14px;
	color:#666;
}
.table-compare td.price .strike-through {
	color:#999;
	text-decoration: line-through;
	margin-right:3px;
}
/* rate */
.table-compare td .rating {
  list-style: none;
  margin:0;
  padding:0;
}
.table-compare td .rating li {
  margin:0 -2px;
  padding:0;
  display: inline-block;
}
.table-compare td .rating i.star-on {
  width:17px;
  height:17px;
  display: block;
  background:url('../img/star-on.png') no-repeat left top;
}
.table-compare td .rating i.star-off {
  width:17px;
  height:17px;
  display: block;
  background:url('../img/star-off.png') no-repeat left top;
}



/*==================== my-account ===================*/
.my-account {
	margin-top:24px;
	list-style: none;
}
.my-account li {
	border-bottom:1px dotted #d6d6d6;
}
.my-account li a {
	font-weight: 600;
	padding:6px 0 6px 6px;
	display: block;
}
.my-account li a:hover {
	background-color:#f0f0f0;
}
.my-account li a:active {
	background-color:#e7e7e7;
}
.my-account li a.active {
	color:#f16325;
	cursor: default;
}




/*================= categories ================*/
.categories ul.unstyled {
	margin-top:24px;
}
.categories ul.unstyled li {
	border-bottom:1px dotted #d6d6d6;
}
.categories ul.unstyled li:last-child {
	border:none;
}
.categories ul.unstyled li a {
	font-weight: 600;
	padding:6px 0 6px 6px;
	display: block;
}
.categories ul.unstyled li a:hover{
	background-color:#f0f0f0;
}
.categories ul.unstyled li a:active {
	background-color:#e7e7e7;
}
.categories ul.unstyled li a:hover.active {
	background: none !important;
}
/* submenu */
.categories ul.unstyled ul.submenu {
	padding:12px;
	margin:0 0 6px 0;
	list-style-type:square;
	background-color: #f0f0f0;
	border:1px solid #e7e7e7;
}
.categories ul.unstyled ul.submenu li {
	margin:0 0 0 18px;
	padding:0;
}
.categories ul.unstyled ul.submenu li a {
	padding:4px 0;
	font-weight: 400;
	font-size: 11px;
}
.categories ul.unstyled ul.submenu li a.active:hover {
	background-color: none;
}




/*========================= checkout-outer ======================*/
.checkout-outer {
	margin-bottom:6px;
	border:1px solid #e0e0e0;
}
	.checkout-outer hr {
		padding:0;
		border:0;
		height:1px;
		margin:18px 0;
		background:url('../img/dottedBorder.png') repeat-x left top; 
	}

	/* checkout-header */
	.checkout-outer .checkout-header {
		padding:6px 0 6px 14px;
		border-bottom:1px solid #e0e0e0;
		background: #f3f3f3;
		background: -moz-linear-gradient(top, #f3f3f3 0%, #e9e9e9 100%);
		background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#f3f3f3), color-stop(100%,#e9e9e9));
		background: -webkit-linear-gradient(top, #f3f3f3 0%,#e9e9e9 100%);
		background: -o-linear-gradient(top, #f3f3f3 0%,#e9e9e9 100%);
		background: -ms-linear-gradient(top, #f3f3f3 0%,#e9e9e9 100%);
		background: linear-gradient(to bottom, #f3f3f3 0%,#e9e9e9 100%);
		filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f3f3f3', endColorstr='#e9e9e9',GradientType=0 );
	}
	/* checkout-content */
	.checkout-outer .checkout-content {
		padding:18px;
	}





/*================= twitter ===================*/
.tweet ul {
	margin:0 0 0 0;
	padding:0;
	list-style: none;
}
	.tweet ul li {
		margin:24px 0 0 0;
		padding:0 0 0 36px;
		background:url('../img/tweetIcon.png') no-repeat left 5px;
	}




/*================= table-receipt =================*/
.table-receipt {
	margin:60px 0 0 0;
}
	.table-receipt td {
		padding:8px 0;
	}
	.table-receipt td.alignRight {
		text-align: right;
		padding-right:12px;
	}
	.table-receipt td.alignLeft {
		text-align: left;
		padding-left:12px;
	}




/*=================== account-list-outer ==================*/
.account-list {
	margin:24px 0;
	padding:0;
	border:1px solid #e2e2e2;
	background:#f3f3f3;
	list-style: none;
}
	.account-list li {
		margin:0;
		padding:0;
	}
	.account-list li a {
		padding:7px 18px;
		display: block;
		border-bottom:1px dotted #d7d7d7;
	}
	.account-list li:last-child a {
		border:none;
	}
	.account-list li a:hover,
	.account-list li a:active {
		background-color:#efefef;
	}

	.account-list-outer form {
		margin:24px 0 0;
		padding:18px;
		border:1px solid #e2e2e2;
		background:#f3f3f3;
	}



/*===================== register =====================*/
.register .form-horizontal {
	margin-top:24px;
	padding:0 0 12px;
	border:1px solid #e0e0e0;
	border-top:none;
}
	.register .form-horizontal legend {
	    padding: 6px 0;
	    margin-bottom: 14px;
	    color: #404040;
	    background-color:#f0f0f0;
	    border: 0;
	    border-top: 1px solid #e0e0e0;
	    border-bottom: 1px solid #e0e0e0;
	    font-family: 'Sanchez', serif;
	    font-size:14px;
	    text-transform: capitalize;
	    line-height:22px;
	}
	.register .form-horizontal hr {
		padding:0;
		border:0;
		height:1px;
		margin:30px 0 18px;
		background:url('../img/dottedBorder.png') repeat-x left top; 
	}



/*=================== login =================*/
.login {
	border:1px solid #e0e0e0;
}
	.login table td {
		padding:24px;
	}
	.login table td {
		border-right:1px solid #e0e0e0;
		vertical-align: top;
	}
	.login table td h3 {
		margin-bottom:14px;
	}
	.login table td p {
		margin-bottom:18px;
	}



/*====================== contact info ================*/
	.contact-info address {
		margin:18px 0;
		padding-bottom:24px;
		background:url('../img/dottedBorder.png') repeat-x left bottom;
	}
	.contact-info address h3 {
		margin-bottom:8px;
	}
	.contact-info address h4 {
		margin-bottom:6px;
	}



/*===================== google-map ===============*/
	.google-map {
		padding: 3px;
	  	border: 1px solid #e0e0e0;
	  	border-radius: 4px;
	  	-webkit-border-radius: 4px;
	  	-moz-border-radius: 4px;
	  	-o-border-radius: 4px;
	  	background-color:#fff;
	  	-moz-box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
	  	-webkit-box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
	  	box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.05);
	}
	.google-map iframe {
		border:none;
		margin:0;
		padding:0;
	}



/*=================== contact-us-form =================*/
.contact-us-form {
	margin-top:60px;
}
	.contact-us-form form {
		margin-top:24px;
	}
	.contact-us-form form textarea {
		height:120px;
	}



/*====================== search ================*/
.search form {
	margin-top:24px;
}



/*==================== footer style ================*/
footer {margin:82px 0 0 0;}
.footerOuter {
	padding:10px 0;
	margin-bottom:12px;
	background:#e7e7e7;
	border-top:1px dotted #b4b4b4;
	border-bottom:1px dotted #b4b4b4;
	text-align:center;
}
/*===== footer title header h3 =========*/
	footer .titleHeader {
		background: url('../img/dottedBorder.png') repeat-x 50% 70%;
		margin:0;
	}
	footer .titleHeader h3 {
		float:left;
		background:#e7e7e7;
		padding-right:8px;
	}
	footer .titleHeader .pagers {
		float:right;
		background:#e7e7e7;
		padding-left:8px;
	}

/* usefullLinks */
	footer .usefullLinks .unstyled {
		margin-top:14px;
	}
	footer .usefullLinks .unstyled li {
		border-bottom:1px dotted #b4b4b4;
	}
	footer .usefullLinks .unstyled li:last-child {
		border-bottom:none;
	}
	footer .usefullLinks .unstyled li a {
		padding:6px 0;
		display: block;
		font-weight: 600;
	}
	footer .usefullLinks .unstyled li a:hover,
	footer .usefullLinks .unstyled li a:active {
		background:#e0e0e0;
	}
	footer .usefullLinks .unstyled li i {
		font-size: 18px;
	}

/* contactInfo */
	footer .contactInfo {
		margin-top:14px;
	}
	footer .contactInfo ul li {
		border-bottom:1px dotted #b4b4b4;
		padding:7px 0;
		display:block;
		font-weight: 600;
	}
	footer .contactInfo ul li:hover,
	footer .contactInfo ul li:active {
		background:#e0e0e0;
	}
	footer .contactInfo ul li:last-child {
		border-bottom:none;
	}
	footer .contactInfo ul li button {
		height:30px;
		width:30px;
		padding:0;
		margin-right:5px;
		text-align: center;
		-webkit-border-radius: 5000px;
	  -moz-border-radius: 500px;
	  border-radius: 5000px;
	  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.75);
	  background-color: #f5f5f5;
	  background-image: -moz-linear-gradient(top, #ffffff, #e6e6e6);
	  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#ffffff), to(#e6e6e6));
	  background-image: -webkit-linear-gradient(top, #ffffff, #e6e6e6);
	  background-image: -o-linear-gradient(top, #ffffff, #e6e6e6);
	  background-image: linear-gradient(to bottom, #ffffff, #e6e6e6);
	  background-repeat: repeat-x;
	  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffffff', endColorstr='#ffe6e6e6', GradientType=0);
	  border-color: #e6e6e6 #e6e6e6 #bfbfbf;
	  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
	  *background-color: #e6e6e6;
	  /* Darken IE7 buttons by default so they stand out more given they won't have borders */
	  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
	  border: 1px solid #bbbbbb;
	  *border: 0;
	  border-bottom-color: #a2a2a2;
	  -webkit-box-shadow: inset 0 1px 0 rgba(255,255,255,.2), 0 1px 2px rgba(0,0,0,.05);
	  -moz-box-shadow: inset 0 1px 0 rgba(255,255,255,.2), 0 1px 2px rgba(0,0,0,.05);
	  box-shadow: inset 0 1px 0 rgba(255,255,255,.2), 0 1px 2px rgba(0,0,0,.05);
	}
	footer .contactInfo ul li button i {
		font-size: 16px;
	}

/* newslatter */
	footer .newslatter form {
		margin-top:24px;
	}

/* payment */
	footer .payments li {
		width:37px;
		height:26px;
		margin:0;
		display: inline-block;
		background: url('../img/payment.png') no-repeat;
	}
	footer .payments li.visia {
		background-position: 0 0;
	}
	footer .payments li.paypal {
		background-position: 0 -35px;
	}
	footer .payments li.electron {
		background-position: 0 -70px;
	}
	footer .payments li.discover {
		background-position: 0 -105px;
	}
/*====================== end footer ====================*/



/*========================= switcher =========================*/
.switcher {
    position:fixed;
    top:60px;
    left:-170px;/*left:-166px;*/
    z-index:999999;
}
	.switcher h3 {
		width:140px;
		padding:0 15px;
		height:30px;
		line-height:30px;
		background:#555;
		color:#fff;
	}
	/* the ancher link */
	a.Widget-toggle-link {
	    position: absolute;
	    top:0;
	    right:-27px;
	    text-align: center;
	    line-height:26px;
	    height:30px;
	    width:30px;
	    display: block;
	    color:#fff;
	    font-size: 16px;
	    font-weight:600;
	    cursor:pointer;
	    background:#555;
	    -webkit-border-radius:0 6px 6px 0;
	    -moz-border-radius:0 6px 6px 0;
	    border-radius:0 4px 4px 0;
	}
	a.Widget-toggle-link:hover {
		text-decoration:none;
		color:#fff;
	}

	/* switcher-content */
	.switcher-content {
		position:relative;
	    width:140px;
	    background:#fefefe;
	    padding:14px;
	    border:1px solid #e7e7e7;
	    -webkit-box-shadow:0 0 4px 0 rgba(0,0,0,0.2);
	    -moz-box-shadow:0 0 4px 0 rgba(0,0,0,0.2);
	    box-shadow:0 0 4px 0 rgba(0,0,0,0.2);
	}
	.switcher-content h4 {
	    margin-bottom:12px;
	}
	/* layout-switch */
	.switcher-content .layout-switch {
		margin-bottom:24px;
	}
	/* color-switch */
	.switcher-content .color-switch {
		margin-bottom:18px;
	}
	.switcher-content .color-switch .color-switch-link {
		width:22px;
		height:22px;
		font:0/0 a;
		display: block;
		float:left;
		margin:0 6px 6px 0;
		cursor:pointer;
		background-color:transparent;
	}
	.switcher-content .color-switch .color-switch-link:last-child {
		margin-right:0;
	}
	.switcher-content .color-switch .color-switch-link.active {
		cursor:default;
	}
	.switcher-content .color-switch .color-switch-link#orange-color {
		background-color:#f16325;
	}
	.switcher-content .color-switch .color-switch-link#blue-color {
		background-color:#206EA3;
	}
	.switcher-content .color-switch .color-switch-link#green-color {
		background-color:#009640;
	}
	.switcher-content .color-switch .color-switch-link#brown-color {
		background-color:#5d514b;
	}
	.switcher-content .color-switch .color-switch-link#red-color {
		background-color:#e81863;
	}
	/* pattern-switch */
	.switcher-content .pattern-switch a {
	    font:0/0 a;
	    width:22px;
	    height:22px;
	    display:block;
	    float:left;
	    margin:0 6px 6px 0;
	    cursor:pointer;
	}
	.switcher-content .pattern-switch a:hover {
	    opacity: .7;
	    -moz-opacity: .7;
	    filter:alpha(opacity=70);  
	}



/*========================================================================
======================== Begain the media query ==========================
========================================================================*/
@media (max-width: 979px) {
}

@media (min-width: 768px) and (max-width: 979px) {

	/*vProductItems & vProductItemsTiny*/
	.vProductItems li .thumbImage,
	.vProductItemsTiny li .thumbImage {
		width:70px;
	}

	

	/*brandList*/
	.brandList li {
		width:170px;
	}



	/*footer*/
	footer .contactInfo button {
		display:none;
	}
}

@media (max-width: 767px) {
	/*body*/
	body {
    	padding:0 20px;
    	background:#fff !important;
	}
	#mainContainer {
		-webkit-box-shadow:none !important;
		-moz-box-shadow:none !important;
		box-shadow:none !important;
	}
	/*upperHeader*/
	.upperHeader select.upper-nav {
		display: block;
		margin-top:-23px;
		margin:5px auto 10px;
	}
	.upperHeader p {
		text-align:center;
	}
	.upperHeader .inline {
		display: none;
		margin:0 auto;
	}

	/* middleHeader */
	.middleHeader .middleContainer {
		padding:0;
		background-position:50% 50%;
	}
	.middleHeader .siteLogo {
		float:none;
		margin:10px 0;
	}
	.middleHeader .siteLogo h1 a {
		margin:0 auto;
	}
	.middleHeader .pull-right {
		float:none;
		padding:4px 0;
		border-top:1px solid #e0e0e0;
		border-bottom:1px solid #e0e0e0;
		margin:0 auto 10px;
		text-align: center;
		background:#eee;
	}
	.middleHeader .pull-right form {
		margin: 0;
	}
	/* cat-content */
	.middleHeader .pull-right.cart-content {
		right:-80%;
	}

	/*brandList*/
	.brandList li {
		width:160px;
	}




}

@media (max-width: 480px) {
	/*brandList*/
	.brandList li {
		display: block;
		border-right:0;
		float: none;
		text-align: center;
	}
	.brandList li a {
		margin:0 auto;
		text-align: center;
	}

	/*product-details*/
	.product-details .product-inputs form .input-append .span1 {
		width:60px;
	}

	/*product-tab*/
	.product-tab .nav-tabs > li {
		margin-right:1px;
	}
	.product-tab .nav-tabs > li > a {
		padding-left:6px;
		padding-right:6px;
	}
	.product-tab .nav-tabs > li.dropdown > .dropdown-menu {
		left:-100% !important;
	}

	/* checkout */
	.checkout-content .login,
	.login {
		border:none !important;
	}
	.checkout-content table td,
	.login table td {
		padding:14px 0;
		display: block;
		text-align: center;
		border:none;
		width:90%;
		border-bottom:1px solid #e0e0e0;
	}
	.checkout-content table td:last-child,
	.login table td:last-child {
		border-bottom:none;
	}

	/* form-horizontal */
	form.form-horizontal .control-label{
		display: block;
		width: auto;
		float: none;
		margin-left:14px;
	}
	form.form-horizontal .controls{
		margin-left:14px;
		float: left;
	}

}
