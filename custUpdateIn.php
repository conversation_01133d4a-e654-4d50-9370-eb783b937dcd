<?php
session_start();
require 'connection.php';
ob_start();

//require_once('database.php');
//require_once('library.php');
//isUser();
//echo $uid=$_POST['sname']; 
$msg="yes";

 // echo $id=$_GET['id']; 

if($_POST['delete']){
    $sql="DELETE FROM `custreg` WHERE  custid='".$_POST['sname']."'";
  mysqli_query($con,$sql);
    
}else{
 $sql="UPDATE `custreg` SET `custname`='".$_POST['custname']."',`custphone`='".$_POST['custphone']."',`custmail`='".$_POST['custemail']."',`custadd`='".$_POST['custaddress']."',`custzip`='".$_POST['custzip']."',`vattin`='".$_POST['cusvatin']."',`csttin`='".$_POST['csttin']."',`custgst`='".$_POST['custgst']."',`custpan`='".$_POST['custpan']."',`userid`='".$_POST['userid']."' WHERE custid='".$_POST['sname']."'";
  
  if(mysqli_query($con,$sql))
 {
    $msg="yes1";	
  }
  echo $ct=$_POST['ct'];
for($i=1;$i<=$ct;$i++)
{
  // $sql1="UPDATE `custrate` SET `crsource`='".$_POST['sources'.$i]."',`crdesti`='".$_POST['destin'.$i]."',`crrate`='".$_POST['rate'.$i]."',`perkgftp`='".$_POST['fuelc'.$i]."',`fuelc`='".$_POST['ftl'.$i]."' WHERE crid='".$_POST['sname']."'";
$dna="select max(custid) as maxid from custreg where custid='".$_POST['sname']."'";
$result=mysqli_query($con,$dna);
while($row=mysqli_fetch_array($result)){
  $shipid=$row['maxid'];  
}

     echo $qrsr="INSERT INTO `custrate` (`crid`,`crsource`, `crtype`,`crdesti`, `crrate`, `fuelc`, `perkgftp`, `mode`,`doc_charge`,`other`,`miniweight`) VALUES ($shipid,'".$_POST['sources'.$i]."', '','".$_POST['destin'.$i]."', '".$_POST['rate'.$i]."','".$_POST['fuelc'.$i]."', '".$_POST['ftl'.$i]."','".$_POST['mode'.$i]."','".$_POST['docketcharge'.$i]."','".$_POST['other'.$i]."','".$_POST['weight'.$i]."')";
 if(mysqli_query($con,$qrsr))
 {
    $msg="yes1";	
  }
  else
  {
   $msg="no1"; 
  }
}
  // echo $sql;	
  
}
   header("location: custUpdate.php?msg=".$msg);

?>
