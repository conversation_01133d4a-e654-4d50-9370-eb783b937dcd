<?php
session_start();
require_once('database.php');
require_once('library.php');
isUser();
$cid= $_POST['Consignment'];

$sql = "SELECT *
		FROM tbl_courier
		WHERE cons_no = '$cid'";
$sql_1 = "SELECT DISTINCT(off_name)
		FROM tbl_offices";
$result = dbQuery($sql);		
$result_1 = dbQuery($sql_1);
while($data = dbFetchAssoc($result)) {
extract($data);
?>
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>
	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
	
</head>
<?php
include("header.php");
?>
      
		<div class="container">
			<div class="row">
				<div class="span2">
				</div><!--end span8-->

                <div class="span8">
					<div class="account-list-outer">

						<div class="titleHeader clearfix">
							<h3>Search Result. </h3>
						</div><!--end titleHeader-->
	<table  cellpadding="2" cellspacing="2" width="100%" border="1"> 
            <tbody>     <tr> 
							    <td >

        <table border="0" cellpadding="1" cellspacing="1" width="80%">
			<tbody>
				<tr>
					<td width="55%">Shipper Name :</td>
					<td width="45%"><?php echo $ship_name; ?></td>
				</tr>
				<tr>
					<td>Shipper Phone :</td>
					<td> <?php echo $phone; ?>  </td>
				</tr>
				<tr>
					<td>Shipper Address :</td>
					<td><?php echo $s_add; ?></td>
				</tr>
			</tbody>
		</table>
                             </td >
							 <td >

        <table border="0" cellpadding="1" cellspacing="1" width="80%">
			<tbody>
				<tr>
					<td width="55%">Receiver Name :</td>
					<td width="45%"><?php echo $rev_name; ?></td>
				</tr>
				<tr>
					<td>Receiver Phone :</td>
					<td> <?php echo $r_phone; ?>  </td>
				</tr>
				<tr>
					<td>Receiver Address :</td>
					<td><?php echo $r_add; ?></td>
				</tr>
			</tbody>
		</table>
                             </td >
									</tr> 
	<tr>
      <td >&nbsp;</td>
      <td >&nbsp;</td>
    </tr>
	<tr> 
      <td align="right">Consignment No  : </td> 
      <td ><?php echo $cons_no; ?>&nbsp;</td> 
    </tr> 
    <tr>
      <td align="right">Ship Type  :</td>
      <td><?php echo $type; ?>&nbsp;</td>
    </tr>
    <tr>
      <td align="right">Weight :</td>
      <td><?php echo $weight; ?>&nbsp;kg</td>
    </tr>
    <tr>
      <td align="right">Invoice no  :</td>
      <td><?php echo $invice_no; ?>&nbsp;</td>
    </tr>
    <tr>
      <td align="right">Booking Mode :</td>
      <td><?php echo $book_mode; ?>&nbsp;</td>
    </tr>
    <tr>
      <td align="right">Total freight : </td>
      <td><?php echo $freight; ?>&nbsp;Rs.</td>
    </tr>
    <tr>
      <td align="right">Mode : </td>
      <td><?php echo $mode; ?></td>
    </tr> 
    <tr> 
      <td align="right">Pickup Date/Time  :</td> 
      <td><?php echo $pick_date; ?> -<span class="gentxt"><?php echo $pick_time; ?></span> </td> 
    </tr> 
    <tr> 
      <td align="right">Status :</td> 
      <td>&nbsp;<?php echo $status; ?></td> 
    </tr> 
    <tr> 
      <td align="right">Comments :</td> 
      <td>&nbsp;<?php echo $comments; ?></td> 
    </tr> 
			</tbody>
		</table> 						 
	<form action="process.php?action=update-status" method="post" name="frmShipment" id="frmShipment"> 
         <table cellpadding="2" cellspacing="2" align="center" width="75%">	
		    <tbody> 
			    <tr>
    				<td colspan="3" align="right">UPDATE STATUS </td>
				</tr>
				 <tr>
					<td colspan="3" align="right"></td>
				</tr>
				<tr>
					<td align="right" width="16%">New Location:</td>
					<td colspan="2">
						<select name="OfficeName">
							<?php 
								while($data = dbFetchAssoc($result_1)){
							?>
						<option value="<?php echo $data['off_name']; ?>"><?php echo $data['off_name']; ?></option>
							<?php 
								}//while
							?>
						</select>
					</td>
				</tr>
				<tr>
					<td align="right">New Status: </td>
					<td width="26%">
						<select name="status">
							<option value="In Transit">In Transit</option>
							<option value="Landed">Landed</option>
							<option value="Delayed">Delayed</option>
							<option value="Completed">Completed</option>
							<option value="Onhold">Onhold</option>
						</select>
					</td>
					 <td width="58%"><div align="center">
						 <a href="process.php?action=delivered&cid=<?php echo $cid; ?>">Marked this shipment as to be <span>DELIVERED </span></a><span class="style1"></span></div>
					 </td>
				</tr>
				<tr>
					<td align="right"><span class="Partext1">Comments:</span></td>
					<td colspan="2">
						<textarea name="comments" cols="40" rows="3" id="comments"></textarea>
					</td>
				</tr>
				<tr>
					 <td align="right">&nbsp; </td>
					 <td colspan="2">
						<input name="submit" value="Submit" type="submit">
						<input name="cid" id="cid" value="<?php echo $cid; ?>" type="hidden">
						<input name="cons_no" id="cons_no" value="<?php echo $cons_no; ?>" type="hidden">   
					 </td>
				</tr>
				
			</tbody>
		</table> <br>
    </form>		
					</div><!--end -->
				</div><!--end span6-->
			</div><!--end row-->
		</div><!--end conatiner-->
		
<?php
include("footer.php");
?>   
<?php } 
?>