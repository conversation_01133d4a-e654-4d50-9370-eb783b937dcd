
<?php 
session_start();
error_reporting(E_ALL ^ E_NOTICE);
$from_email ="";
$my_email = "<EMAIL>";
$continue = "http://www.rcplexpress.com/rcpl/welcome.php";


//customeraddsuccess
if(!(isset($_SERVER['HTTP_REFERER']) && !empty($_SERVER['HTTP_REFERER']) && stristr($_SERVER['HTTP_REFERER'],$_SERVER['HTTP_HOST'])))
{
	$errors[] = "You must enable referrer logging to use the form";
}

if(!defined("PHP_EOL"))
{
	define("PHP_EOL", strtoupper(substr(PHP_OS,0,3) == "WIN") ? "\r\n" : "\n");
}
function build_message($request_input)
{
	if(!isset($message_output))
	{
		$message_output ="        ";
	}
	if(!is_array($request_input))
	{
		$message_output = $request_input;
	}
	else
	{
		foreach($request_input as $key => $value)
		{
			if(!empty($value))
			{
			if(!is_numeric($key))
				{
					$message_output .= str_replace("_"," ",ucfirst($key)).": ".build_message($value).PHP_EOL.PHP_EOL;
				}
				else
				{
					$message_output .= build_message($value).", ";
				}
			}
		}
	}
	return rtrim($message_output,", ");
}
$message = build_message($_REQUEST);
$message='
Hello '.$_POST['custname'].',
You have Successfully Registered in RCPL Express. Your Customer Registration ID is:'.$_POST['custname'].'.     .
';

$message = $message . PHP_EOL.PHP_EOL."-- ".PHP_EOL."Thank you ";
$message = stripslashes($message);
$subject = "Customer Registration Successfully...";
$subject = stripslashes($subject);
$headers="BCC: <EMAIL>,'.$_POST['custemail'].'";

mail($my_email,$subject,$message,$headers);

//----mail---
?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<title></title>
</head>
<body>
<div>
<center>
<b>Thank you</b>
<br><p>You have Successfully Registered in RCPL Express. Your Registration ID is:</p>
<p><a href="<?php print $continue; ?>">Click here to continue</a></p>
</center>
</div>
</body>
</html>