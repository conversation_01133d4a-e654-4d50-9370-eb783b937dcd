<?php
require 'vendor/autoload.php';

use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

$mail = new PHPMailer(true);

try {
    // Server settings
    $mail->isSMTP();
    $mail->Host       = 'smtp.gmail.com';              
    $mail->SMTPAuth   = true;
    $mail->Username   = '<EMAIL>';         // Change this
    $mail->Password   = 'your-app-password';            // Use App Password from Gmail
    $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
    $mail->Port       = 587;

    // Recipients
    $mail->setFrom('<EMAIL>', 'Vivanta Logistics');
    $mail->addAddress('<EMAIL>');           // Change this

    // Content
    $mail->isHTML(true);
    $mail->Subject = 'Test Mail';
    $mail->Body    = 'This is a test mail using PHPMailer with Composer.';

    $mail->send();
    echo 'Message has been sent successfully.';
} catch (Exception $e) {
    echo "Message could not be sent. Mailer Error: {$mail->ErrorInfo}";
}
?>
