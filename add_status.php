<?php 
session_start();
require_once('library.php');
$rand = get_rand_id(8);
//echo $rand;
$userid=$_SESSION['desgn'];
$a=$_SESSION['username'];
/*$sql="select * from login where type='$a'";
$result = mysqli_query($con, $sql);
$row1 = mysqli_fetch_array($result, MYSQLI_BOTH);
 $userid=$row1['rid'];*/

$query = "SELECT MAX(custid) AS custid FROM custreg ";  
    if($result = mysqli_query($con,$query))
    {
  while ($row = mysqli_fetch_assoc($result))
  {
        $count = $row['custid'];
        $count = $count+1;

      $code_no = str_pad($count, 7 ,"9", STR_PAD_LEFT);
  }
	}

$sql = "SELECT * FROM `status` order by statusname";
$result = mysqli_query($con,$sql);
	
?>
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>

	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
	
</head>
<?php
include("header.php");
?>



        <div class="container">

			<div class="row">

				<div class="span2">
				
				</div><!--end span8-->
                             
                <div class="span12">
					<div class="register">
						<div class="titleHeader clearfix">
							<h3>Status Registration</h3>
						</div><!--end titleHeader-->
						<form action="process.php?action=add-status" method="post" class="form-horizontal" onSubmit="return validate()" name="frmShipment">
						   	<legend>&nbsp;&nbsp;&nbsp;&nbsp;Add Status :</legend>

							<div class="control-group ">
							    <label class="control-label">Status : <span class="text-error" >*</span></label>
							    <div class="controls">
							      <input type="text" name="status" placeholder="Status">
							     <!-- <span class="help-inline"><i class="icon-ok"></i> Avaliable input!</span>-->
							    </div>
							</div><!--end control-group-->
								
							
							<div class="control-group">
							    <div class="controls">
							
									<input name="Submit" class="btn btn-primary" type="submit" value="Register" onClick="return validateForm()">
									<button type="reset" class="btn ">Clear</button>
							    </div>
							</div><!--end control-group-->
							
																				
						</form>
					
						
								<table class="table">
							<thead>
							<tr>
								<th><h5>Sr. No. </h5></th>
									 &nbsp;  &nbsp;  &nbsp;  &nbsp; 
							<th><h5>Status Name</h5></th>
							 &nbsp;  &nbsp;  &nbsp;  &nbsp; 
							
								<th><h5>Action</h5></th>
							</tr>
						</thead>	
						<?php
						$count=0;
							 while($data = mysqli_fetch_array($result)){
							 extract($data);	
							 $count++;
						 ?>
						<tbody>
							<tr>
								<td class="desc">
									 &nbsp;  &nbsp;  &nbsp;  &nbsp; 
								 &nbsp;  &nbsp;  &nbsp;  &nbsp; 
								<?php echo $count; ?>
								</td>
								<td>
									<?php echo $data['statusname']; ?>
								</td>
							
							
                                <td><a href="statusupdate.php?id=<?php echo $data['statusid']; ?>"></i>
									<button class="btn btn-small btn-primary" data-title="To Edit" data-placement="top" rel="tooltip" value="Edit" ><i class="">Update</button></a>
									
								
								
								 <a href="process.php?id=<?php echo $data['statusid']; ?> & action=del-status"></i>
									<button class="btn btn-small btn-primary" data-title="To Delete" data-placement="top" rel="tooltip" value="Delete" ><i class="">Delete</button></a>
									
								
								</td>
							</tr>
						</tbody>
						<?php	}//while ?>
					</table>	<!--end form-->

					</div><!--end register-->
				
				</div><!--end span-->
	
			</div><!--end row-->

        </div><!--end conatiner-->




 <?php
											
								$msg = $_GET['msg'] ?? '';
								if($msg=="yes1")
								{
						echo "<script> alert('Status Registered Successfully');</script>";
								}
								else if($msg=="no1"){
						echo "<script> alert('Status Not Registered Successfully');</script>";
								}else if($msg=="yes"){
								    	echo "<script> alert('Status Updated Successfully');</script>";
								}else if($msg=="no"){
						echo "<script> alert('Status Not Updated Successfully');</script>";
								
								}else if($msg=="yes2"){
								    	echo "<script> alert('Status Deleted Successfully');</script>";
								}else if($msg=="no2"){
						echo "<script> alert('Status Not Deleted Successfully');</script>";
								
								}
								
								?>    <!--   --> 

<?php
/*$sql = "SELECT * FROM `status` order by statusname"; 
$rs_result = mysqli_query($con,$sql); //run the query
$total_records = mysqli_num_rows($rs_result);  //count number of records
$total_pages = ceil($total_records / $num_rec_per_page); 

echo "<a href='add_status.php?page=1'>".'|<'."</a> "; // Goto 1st page  

for ($i=1; $i<=$total_pages; $i++) { 
            echo "<a href='add_status.php?page=".$i."'>".$i."</a> "; 
}; */
include("footer.php");
?>