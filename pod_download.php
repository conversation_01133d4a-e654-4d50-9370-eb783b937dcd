<?php
session_start();
require_once('database.php');
require_once('library.php');
isUser();
check_session();

$PNG_TEMP_DIR = dirname(__FILE__).DIRECTORY_SEPARATOR.'QRC_DOWNLOAD_2'."_".date("d") ."_".date("m") ."_".date("Y").  "_".DIRECTORY_SEPARATOR;

// Create temp directory if not exists
if (!file_exists($PNG_TEMP_DIR)) {
    mkdir($PNG_TEMP_DIR, 0777, true);
}

$date = date("Y/m/d");
$date_old = date('Y-m-d', strtotime('-31 day', strtotime($date)));
$sql = 'SELECT * FROM `tbl_courier` WHERE book1_date BETWEEN DATE('.$date.') AND DATE('.$date_old.')';
$result = mysqli_query($con,$sql);

// Create CSV file
$csvFilename = $PNG_TEMP_DIR . 'qr_codes_data_' . date('Y-m-d') . '.csv';
$csvFile = fopen($csvFilename, 'w');
fputcsv($csvFile, ['Consignment No', 'QR Code File']);

while($data = mysqli_fetch_assoc($result)) {
    $filename = $PNG_TEMP_DIR.'test_'.$data['cons_no'].'_'.md5($data['cons_no'].'|L|3').'.png';
    QRcode::png($data['cons_no'], $filename, 'L', 3, 3);
    fputcsv($csvFile, [$data['cons_no'], basename($filename)]);
}

fclose($csvFile);

// Create ZIP archive
$zipFilename = $PNG_TEMP_DIR . 'qr_codes_' . date('Y-m-d') . '.zip';
$zip = new ZipArchive();
if ($zip->open($zipFilename, ZipArchive::CREATE) === TRUE) {
    // Add all PNG files
    foreach (glob($PNG_TEMP_DIR."*.png") as $file) {
        $zip->addFile($file, basename($file));
    }
    // Add CSV file
    $zip->addFile($csvFilename, basename($csvFilename));
    $zip->close();
    
    // Offer download
    header('Content-Type: application/zip');
    header('Content-Disposition: attachment; filename="'.basename($zipFilename).'"');
    header('Content-Length: ' . filesize($zipFilename));
    readfile($zipFilename);
    
    // Cleanup (optional)
    array_map('unlink', glob($PNG_TEMP_DIR."*.png"));
    unlink($csvFilename);
    unlink($zipFilename);
    rmdir($PNG_TEMP_DIR);
} else {
    die('Failed to create ZIP file');
}
?>