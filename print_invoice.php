<?php
session_start();
include("connection.php");
require('database.php');
require_once('library.php');
$userid=$_SESSION['desgn'];
isUser();
$date=$_POST['sdate'];
$date2=$_POST['edate'];
//$custid=$_POST['sname'];
$Cdate=date('Y-m-d',strtotime($date));
$Cdate2=date('Y-m-d',strtotime($date2));
$invoice=$_POST['invoce'];
//$sname=$_POST['sname'];
$sname=$_SESSION["sname"];
$sql = "SELECT * FROM custreg  WHERE custreg.custid ='$sname'";
$result = mysqli_query($con,$sql);
while($row = mysqli_fetch_array($result)) 
 {
      $custname=$row['custname'];
     $custadd=$row['custadd'];
     $custgst=$row['custgst'];
     //$custname=$row['custname'];
 }

//$totcons=$_POST['cnt']; 
$bookmode=$_SESSION["booking_mode"];
 $totcons=$_SESSION["cnt"];
$cgst=$_SESSION["cgst"];
if(isset($_POST['submit'])){
  $sql1="INSERT INTO `invoicebill` (`custid`, `sdate`, `tdate`, `inv_no`, `inv_date`, `inv_duedate`, `tot_consign`, `grandtot`, `userid`, `edate`,`bokkingmode`) 
 VALUES ('".$sname."', '".$Cdate."', '".$Cdate2."', '".$_POST['invoce']."', '".$Cdate."', '".$Cdate2."', '".$totcons."', '".$_POST['grand']."', '".$userid."', now(),'$bookmode')";

if(!mysqli_query($con,$sql1)){
   $msg="no";
}else{  $msg="yes";}
for($i=1;$i<=$totcons;$i++){
$docketno=$_POST['docketno'.$i];
$rate=$_POST['rate'.$i];
$freight=$_POST['freight'.$i];
$vehiclecharge=$_POST['vehiclecharge'.$i];
$oda=$_POST['oda'.$i];
$othercharge=$_POST['othercharge'.$i];
 $sqlquery="update `tbl_courier` set `freight`='$freight',`oda_mis`='$oda',`handlingcharge`='$vehiclecharge',`rate`='$rate',`other_charge`='$othercharge',
`billing_status`='billed' where cons_no='$docketno' ";
mysqli_query($con,$sqlquery);
}
}

elseif(isset($_POST['update'])){
	 

for($i=1;$i<=$totcons;$i++){
$docketno=$_POST['docketno'.$i];
$rate=$_POST['rate'.$i];
$freight=$_POST['freight'.$i];
$vehiclecharge=$_POST['vehiclecharge'.$i];
$oda=$_POST['oda'.$i];
$othercharge=$_POST['othercharge'.$i];
 $sqlquery="update `tbl_courier` set `freight`='$freight',`oda_mis`='$oda',`handlingcharge`='$vehiclecharge',`rate`='$rate',`other_charge`='$othercharge',
`billing_status`='billed' where cons_no='$docketno' ";
mysqli_query($con,$sqlquery);
}
 }
?>
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>
	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	
	
<style type = "text/css">
.row{margin-left:0px!important;}
table{border:none!important;}
table tr, table tr td, table tr td table tr, table tr td table tr td{border-collapse: collapse;}
@media print {
.row{margin-left:0px!important;}
.remove {margin-left: 0px;}}

.remove {margin-left: 10px; }
.remove:hover{cursor: pointer;}
body{margin:0 auto; 
    width:980px;
}
</style>


</head>
    
<body>
 <div style="margin-top:20px;">
   <div>
       <center>
          <form action="invoice_excel.php" method="post" name="myform">     
<table style="margin:0 auto; width:100%; border:1px solid #000; border-collapse:collapse;">

        
<!--header--->
<tr><td colspan="3"> <center>Tax Invoice</center></td></tr>    

<tr>
<td width="20%"><center><img src="img/logo.png" width="60%" /></center></td>   
<td width="60%" style="text-align:center;">
     <h5><b><font size="4">Vivanta Logistics Private. Limited</font></b> </h5>
  <b>CIN: U74999PN2017PTC172759</b></br>
  <b>Registered Address</b>: Bungalow No.-7,samata Hsg.Soc,behind MSEB Colony,Bhosale Nagar,</br> Pune-411007
   Customer Care No.-18003131944
</td>
<td width="20%">Invoice <?php echo $invoice;?></td>
</tr>

<tr><td colspan="3" width="100%"> <center>Billing Details</center></br>
<p>Name:-<?php echo $custname; ?></p>
<p>Address:-<?php echo $custadd; ?></p>
<p>GSTIN:-<?php echo $cgst; ?></p>
<p>Booking Mode:-<?php echo $bookmode;?></p>
</td></tr>



<tr><td colspan="3"> 
<table style="margin:0 auto; width:100%; border-collapse:collapse;"  id="Table1" border="1">
<thead>
   
    <tr>
           <th>Sr. No.</th>
           
           <th>Docket No.</th>
           <th>Date</th>
           <th>Destin.</th>
           <th><span id="Weight" class="remove remove-col">Weight</span></th>
           <th><span id="Rate" class="remove remove-col">Rate</span></th>
           <th><span id="Freight" class="remove remove-col">Freight</span> </th>
           <th><span id="invalue" class=" remove remove-col">Specail Veh.Charges</span></th>
           <th><span id="oda" class="remove remove-col">ODA/Other</span></th>
           <th><span id="othercharges" class=" remove remove-col">Total Frieght</span> </th>
          
          
          
          <th><span id="cgst" class="remove remove-col">SGST</span></th>
                      <th><span id="cgst" class="remove remove-col">CGST</span></th>
           <th><span id="total" class="remove remove-col">Total</span></th>
           
</tr>   
</thead>     
<tbody>
   <?php

  
for($i=1;$i<=$totcons;$i++) {
$cnt=$_POST['cnt'.$i];
$docketno=$_POST['docketno'.$i];
$bookdate=$_POST['bookdate'.$i];
$destination=$_POST['destination'.$i];
$chweight=$_POST['chweight'.$i];
$rate=$_POST['rate'.$i];
$freight=$_POST['freight'.$i];
$vehiclecharge=$_POST['vehiclecharge'.$i];
$oda=$_POST['oda'.$i];
$totfreght=$_POST['totfreght'.$i];
$sgst=$_POST['sgst'.$i];
$cgst=$_POST['cgst'.$i];
$total=$_POST['total'.$i];

$totalvehiclecharges=$totalvehiclecharges+$vehiclecharge;
$totaloda=$totaloda+$oda;
$totalfrieght=$totalfrieght+$totfreght;
$totalcgst=$totalcgst+$cgst;
$totalsgst=$totalsgst+$sgst;
$sumtotal=$sumtotal+$total;



echo "<tr>
<td>".$cnt."</td>
<td>".$docketno."<input type='hidden' onkeypress='validate(event)' size='20px;' id='docketno".$i."' name='docketno".$i."' value='".$docketno."' ></td>
<td>".$bookdate."<input type='hidden' onkeypress='validate(event)' size='20px;' id='bookdate".$i."' name='bookdate".$i."' value='".$bookdate."' ></td>
<td>".$destination."<input type='hidden' onkeypress='validate(event)' size='20px;' id='destination".$i."' name='destination".$i."' value=".$destination." ></td>
<td>".$chweight."<input type='hidden' onkeypress='validate(event)' size='20px;' id='chweight".$i."' name='chweight".$i."' value='".$chweight."' ></td>
<td>".$rate."<input type='hidden' onkeypress='validate(event)' size='20px;' id='rate".$i."' name='rate".$i."' value='".$rate."' ></td>
<td>".$freight."<input type='hidden' onkeypress='validate(event)' size='20px;' id='freight".$i."' name='freight".$i."' value='".$freight."' onchange='myfunction()'></td>
<td>".$vehiclecharge."<input type='hidden' onkeypress='validate(event)' size='15px;' id='vehiclecharge".$i."' name='vehiclecharge".$i."' value='".$vehiclecharge."' onchange='myfunction()'></td>
<td>".$oda."<input type='hidden' onkeypress='validate(event)' size='15px;' id='oda".$i."' name='oda".$i."' value='0' onchange='myfunction()'></td>
<td>".$totfreght."<input type='hidden' onkeypress='validate(event)' size='15px;' id='totfreght".$i."' name='totfreght".$i."' value='".$totfreght."' ></td>
<td>".$sgst."<input type='hidden' onkeypress='validate(event)' size='15px;' id='sgst".$i."' name='sgst".$i."' value='".$sgst."' ></td>
<td>".$cgst."<input type='hidden' onkeypress='validate(event)' size='15px;' id='cgst".$i."' name='cgst".$i."' value='".$cgst."' ></td>

<td>".$total."<input type='hidden' onkeypress='validate(event)' size='15px;' id='total".$i."' name='total".$i."' value='".$total."' ></td>
</tr>";			

}?>	  				
		
<input type='hidden' name='count' id='count' value='<?php echo $totcons; ?>' >		  
<input type='hidden' name='sname' id='sname' value='<?php echo $sname; ?>'>		  		    
 <input type="hidden" name="sdate" id="sdate" value="<?php echo $date;?>">
<input type="hidden" name="edate" id="edate" value="<?php echo $date2;?>">
</tbody>
<tr><td colspan="6">Total Amount</td><td colspan="1"><?php echo $totfreight;?></td><td><?php echo $totalvehiclecharges;?></td><td><?php echo $totaloda;?></td><td><?php echo $totalfrieght;?></td>
<td><?php echo $totalsgst;?></td><td><?php echo $totalcgst;?></td><td><?php echo $sumtotal;?></td></tr>

</table>
</td></tr> 


<tr><td colspan="3"> 
Notes:-</br>
  1.  Please Pay as per due date given in this Logistics Services Invoice.:-</br>
  2.  Please pay by cheque only in favour of "Vivanta Logistics Private Limited"</br>
  3.  Permanent Account Number(PAN):-**********</br>
  4.  GSTN:-27**********1Z5</br>
  5.  Corporate Identity Number:-U74999PN2017PTC172759</br>
  6.  Invoice Queries,please mail <NAME_EMAIL></br>
  7.  Request you to please pay on time to avoid disruption in service/late payment fees.</br>
  8.  All disputes are subject to pune jirisdiction.</br>
  9.  TDS to be deducted as per provision of section 194C</br>
  10.  Please email TDS <NAME_EMAIL></br>
</td></tr>


<tr><td colspan="3"> 
<b>Bank Details:-</b></br>
 <b>Company Name:</b>Vivanta Logistics Private Limited</br>
 <b>Bank Name:</b>AU Small Finance Bank</br>
 <b>Branch:</b>FC Road, Pune -411016</br>
 <b>A/C No:</b>****************</br>
 <b>IFSC Code:</b>AUBL0002354</br>
 <b>A/C Type:</b>OD Account
 
</td></tr>

  <!--end header---> 



</table>


	<div class="control-group">
							    <div class="controls">
							 
 

									
								
									<input name="export" id="export" class="btn btn-primary" type="submit" value="Export">
						<input type="button" id="printpagebutton"  value="Print" class="btn btn-primary" onClick="printpage();">
						<a id="backbutton" href="invoice1.php">&nbsp;<input type="button" class="btn btn-primary" id="backbutton" onClick="closeWin();" value="Close"> </a>
							    </div>
							</div><!--end control-group-->

</form>
</center>
</div>
</div>
<link href="./bootstrap/css/bootstrap-datetimepicker.min.css" rel="stylesheet" media="screen">		
<script type="text/javascript" src="./jquery/jquery-1.8.3.min.js" charset="UTF-8"></script>
<script type="text/javascript" src="./bootstrap/js/bootstrap.min.js"></script>
<script type="text/javascript" src="./jquery/bootstrap-datetimepicker.js" charset="UTF-8"></script>
<script type="text/javascript" src="./jquery/locales/bootstrap-datetimepicker.pl.js" charset="UTF-8"></script>

<script type="text/javascript">
    function printpage() {
        //Get the print button and put it into a variable
        var printButton = document.getElementById("printpagebutton");
        var back=document.getElementById("backbutton");
	  var exportbt=document.getElementById("export");
	
        //Set the print button visibility to 'hidden' 
        printButton.style.visibility = 'hidden';
        back.style.visibility = 'hidden';
        exportbt.style.visibility = 'hidden';

        //Print the page content
        window.print();
        //Set the print button to 'visible' again 
        //[Delete this line if you want it to stay hidden after printing]
        printButton.style.visibility = 'visible';
         back.style.visibility = 'visible';
        exportbt.style.visibility = 'visible';

    }
</script>
<script type="text/javascript">

function closeWin() {
    back.close();
}
    window.onbeforeunload = function() {
        
    }
</script>
<?php
echo "<script> 
function abc()
{

location.href='invoice_excel.php';

}</script>"; 
 
function convert_number_to_words($number) {

    $hyphen      = '-';
    $conjunction = ' and ';
    $separator   = ', ';
    $negative    = 'negative ';
    $decimal     = ' point ';
    $dictionary  = array(
        0                   => 'Zero',
        1                   => 'One',
        2                   => 'Two',
        3                   => 'Three',
        4                   => 'Four',
        5                   => 'Five',
        6                   => 'Six',
        7                   => 'Seven',
        8                   => 'Eight',
        9                   => 'Nine',
        10                  => 'Ten',
        11                  => 'Eleven',
        12                  => 'Twelve',
        13                  => 'Thirteen',
        14                  => 'Fourteen',
        15                  => 'Fifteen',
        16                  => 'Sixteen',
        17                  => 'Seventeen',
        18                  => 'Eighteen',
        19                  => 'Nineteen',
        20                  => 'Twenty',
        30                  => 'Thirty',
        40                  => 'Fourth',
        50                  => 'Fifty',
        60                  => 'Sixty',
        70                  => 'Seventy',
        80                  => 'Eighty',
        90                  => 'Ninety',
        100                 => 'Hundred',
        1000                => 'Thousand',
        1000000             => 'Million',
        1000000000          => 'Billion',
        1000000000000       => 'Trillion',
        1000000000000000    => 'Quadrillion',
        1000000000000000000 => 'Quintillion'
    );

    if (!is_numeric($number)) {
        return false;
    }

    if (($number >= 0 && (int) $number < 0) || (int) $number < 0 - PHP_INT_MAX) {
        // overflow
        trigger_error(
            'convert_number_to_words only accepts numbers between -' . PHP_INT_MAX . ' and ' . PHP_INT_MAX,
            E_USER_WARNING
        );
        return false;
    }

    if ($number < 0) {
        return $negative . convert_number_to_words(abs($number));
    }

    $string = $fraction = null;

    if (strpos($number, '.') !== false) {
        list($number, $fraction) = explode('.', $number);
    }

    switch (true) {
        case $number < 21:
            $string = $dictionary[$number];
            break;
        case $number < 100:
            $tens   = ((int) ($number / 10)) * 10;
            $units  = $number % 10;
            $string = $dictionary[$tens];
            if ($units) {
                $string .= $hyphen . $dictionary[$units];
            }
            break;
        case $number < 1000:
            $hundreds  = $number / 100;
            $remainder = $number % 100;
            $string = $dictionary[$hundreds] . ' ' . $dictionary[100];
            if ($remainder) {
                $string .= $conjunction . convert_number_to_words($remainder);
            }
            break;
        default:
            $baseUnit = pow(1000, floor(log($number, 1000)));
            $numBaseUnits = (int) ($number / $baseUnit);
            $remainder = $number % $baseUnit;
            $string = convert_number_to_words($numBaseUnits) . ' ' . $dictionary[$baseUnit];
            if ($remainder) {
                $string .= $remainder < 100 ? $conjunction : $separator;
                $string .= convert_number_to_words($remainder);
            }
            break;
    }

    if (null !== $fraction && is_numeric($fraction)) {
        $string .= $decimal;
        $words = array();
        foreach (str_split((string) $fraction) as $number) {
            $words[] = $dictionary[$number];
        }
        $string .= implode(' ', $words);
    }

    return $string;
}

?>



</script>








</body>    
</html>