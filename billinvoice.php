<?php
error_reporting(~E_ALL);
session_start();
require 'connection.php';
require_once('database.php');
require_once('library.php');
isUser();

 date_default_timezone_set('Asia/Kolkata');
$todate = date('d/m/Y ', time());

 $cname=$_GET['cust']; 
 $Cdate=$_GET['start']; 
 $Cdate2=$_GET['end']; 

 if(isset($_GET['cust'])){
 $invsqlli="select * from invoicebill where `custid`='$cname'and `sdate`='$Cdate'and`tdate`='$Cdate2'";
 
 $datainv=mysqli_query($con,$invsqlli);
 
 $invup=mysqli_fetch_array($datainv);
 $no=$invup['inv_no'];
  $dat=$invup['inv_date'];
  $due=$invup['inv_duedate'];
  $invid=$invup['id']; 
 }
 
 
 
if(isset($_POST['Submit']))
 {
	 $cname=$_POST['cname'];    $userid=$_POST['cid']; $frm=$_POST['uoffice'];$clerkname=$_POST['uaddress']; 
	 $fuel=$_POST['fuelcharge'];
	
 $date=$_POST['date2'];  $date2=$_POST['date4']; // $bill=$_POST['bilno'];
	// 
       
if($date2=='')
{
 
$Cdate2=date('Y-m-d',time());
}
else
{
 $Cdate2=date('Y-m-d',strtotime($date2));
}
 $Cdate=date('Y-m-d',strtotime($date));
 
 }
if($cname!="")
{
	//$sql="SELECT * FROM custreg INNER JOIN tbl_courier ON `custreg`.`custname`=`tbl_courier`.`ship_name` WHERE `custreg`.`custid`= '$cname' and tbl_courier.book_date between '$Cdate' and '$Cdate2'";
$sql = "SELECT cons_no,weight,gtotamt,rev_name,ship_name,rate,noofpackage,gst,partno,freight,invi_value,qty,assured_dly_date,book1_date,a.city_name as city ,tbl_city_code.city_name,type,invice_no,chweight,mode,statusname FROM (tbl_courier inner join status on tbl_courier.status=status.statusid) join tbl_city_code on tbl_city_code.Id= tbl_courier.r_add join tbl_city_code a on tbl_courier.s_add=a.Id WHERE tbl_courier.shipper_code ='$custid' and tbl_courier.book1_date between '$date' and '$date2' ORDER BY cons_no DESC ";


$count=0;
$result = dbQuery($sql);	

}

$result = mysqli_query($con,$sql);
$cntr=0;

while($row = mysqli_fetch_array($result)) 
 {
	 $cntr=$cntr+1;  
 $inno=$row["invice_no"];     $cname1=$row["custname"];     $ccity=$row["custcity"];      $ccstin=$row["csttin"];
$indate=$row["book_date"];     $cadd=$row["custadd"];     $cstats=$row["custsts"];      $cpan=$row["custpan"];
//$inno1=$row["invice_no"];     $desti=$row["desti"];     $czip=$row["custzip"];
$cctax=$row["custstax"];     $cmail=$row["custmail"];     $cvattin=$row["vattin"];

 $tot=$row['dock_charg']+$row['dod_cod']+$row['oda_mis']+$row['freight'];
 
$rto=$rto.'<div class="titleHeader clearfix"><tr align="center"><td>'.$cou[]=$cntr.'</td><td>'.$row['cons_no'].'</td><td>'.$row['book_date'].'</td><td>'.$row['from'].'</td><td>'.$row['desti'].'</td><td>'.$row['chweight'].'</td><td>'.$row['qty'].'</td><td>'.$row['mode'].'</td><td>'.$row['freight'].'</td><td>'.$row['dock_charg'].'</td><td>'.$row['dod_cod'].'</td><td>'.$row['oda_mis'].'</td><td><b>'.$towe[]=$tot.'</b></td></tr></div>';
 
 
 }
 $cc=count($cou);
 //echo "<table>";
  $pagetot=$pagetot.'<tr><td colspan="6" rowspan="2"><b><h4>Remark :</h4></b></td><td colspan="6"><b><h4>Total </h4></b></td><td><b>'.$totwe=array_sum($towe).'</b></td></tr>';
   $taxes=$totwe*14/100;
    $swach=$totwe*0.50/100;
	
	$gtot=$totwe-$taxes-$swach;
	$word=convert_number_to_words($gtot);
	
  $tax=$tax.'<tr><td colspan="6"><b><h4>Service Tax @ 14%</h4></b></td><td><b>'.$taxes.'</b></td></tr>';
   $tax1=$tax1.'<tr><td colspan="6" rowspan="2" ><b><h4> Amount In Words :</h4></b><h5>'.$word.'</h5> </td><td colspan="6"><b><h4>Swachh Bharat Cess@0.50% </h4></b></td><td><b>'.$swach.'</b></td></tr>';
   $gtots=$gtots.'<tr><td colspan="6"><b><h4>Grand Total</h4></b></td><td><b>'.$gtot.'</b></td></tr>';
 // $row1 = mysqli_fetch_array($result);
  // echo "</table>";



mysqli_close($con);
?> 
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>
	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
<style>
.table-content {
  padding: 20px;
}
.remove {
  margin-left: 10px;
  color: red;
}
.remove:hover {
  cursor: pointer;
}
.form-control {
  width: 90px;
}

</style>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.0/jquery.min.js"></script>


</head>

    <center>  <?php $_SESSION['b']=b; ?>
    <tr ></tr>
  </center>

   <div class="container">
   <div class="row">
          <form action="invoicebillIn.php" method="post" name="myform">  
				    <div class="span12">
                     <div class="flexslider">
                       
					 <tr><td>
  <table style="width:100%; border-collapse: collapse;" >
       <tr>
    <td colspan="3" align="center" ><h2><b>&nbsp;Tax&nbsp; Invoice &nbsp;</b></h2></td>
  </tr>
  <tr>
  <td width="20%" style=text-align:center;><img src="img/logo.png" width="60%" /></td>
  <td width="50%"  style=text-align:center;><h5><b><font size="4">Vivanta Logistics Private. Limited</font></b></h5>
  <b>CIN: U74999PN2017PTC172759</b></br>
  <b>Registered Address</b>: Bungalow No.-7,samata Hsg.Soc,behind MSEB Colony,Bhosale Nagar,<br> Pune-411007
   Customer Care No.-18003131944
</td>
  <td width="20%">
     
  GSTN.&nbsp;27AAGC1179G1Z5<br>
  <p>Tax is payable on Reverse Charge:No</p>
  
  Invoice No:&nbsp;</br>
  Invoice Date:&nbsp;
 </td>

 	<tr>
 	   
			<td colspan="3"> 
			 <h3 style=text-align:center;><b >&nbsp;Billing Details  &nbsp;</b></h3>
			<b>Consignor Name:</b> &nbsp;&nbsp;<?php echo $Shippername; ?><br>
			<b>Address:</b> &nbsp;&nbsp;<?php echo $shipadd; ?><br>
		    <b> GSTN:</b> &nbsp;&nbsp;<?php echo $custgst; ?><br></td>
			
			
		
	
</tr></table></td></tr>
 
   <table style="width:100%; border-collapse: collapse;"  border="1">
	
						<thead><tr>
		   
           <th>Date</th>
           <th><span id="removespan" class="pull-left remove remove-col">x</span>Docket No.</th>
           <th><span id="removespan1" class="pull-left remove remove-col">x</span>Destin.</th>
           <th><span id="removespan2" class="pull-left remove remove-col">x</span>Weight</th>
           <th><span id="removespan3" class="pull-left remove remove-col">x</span>Rate</th>
           <th><span id="removespan4" class="pull-left remove remove-col">x</span>Freight </th>
           <th><span id="removespan5" class="pull-left remove remove-col">x</span>Invoice Value</th>
           <th><span id="removespan6" class="pull-left remove remove-col">x</span>Invoice No.</th>
           <th><span id="removespan7" class="pull-left remove remove-col">x</span>Other Charges </th>
           <th><span id="removespan8" class="pull-left remove remove-col">x</span>Delivery Charges</th>
           <th><span id="removespan9" class="pull-left remove remove-col">x</span>ToPay Charges</th>
           <th><span id="removespan10" class="pull-left remove remove-col">x</span>Rov</th>
           <th><span id="removespan11" class="pull-left remove remove-col">x</span>FOV </th>
           <th><span id="removespan12" class="pull-left remove remove-col">x</span>ODA</th>
           <th><span id="removespan13" class="pull-left remove remove-col">x</span>IGST 18%</th>
           <th><span id="removespan14" class="pull-left remove remove-col">x</span>SGST</th>
           <th><span id="removespan15" class="pull-left remove remove-col">x</span>CGST</th>
           <th><span id="removespan16" class="pull-left remove remove-col">x</span>Total</th>
		</tr></thead>
		<?php
							 while($data = dbFetchAssoc($result)){
							 extract($data);
							 	$count++;
						 ?>
						
						<tbody>
						  <tr>
          
          
          <td><?php echo $book1_date; ?></td>
          <td><?php echo $cons_no; ?></td>
		  <td><?php echo $city; ?></td>
		  <td><?php echo $chweight; ?></td>
		  <td><?php echo $rate; ?></td>
		  <td><?php echo $freight; ?></td>
		  <td><?php echo $invi_value; ?></td>
		  <td><?php echo $invice_no; ?>	</td>
		  <td><?php echo $other_charges; ?></td>				
		  <td><?php echo $delivery_charge; ?></td>					
		  <td><?php echo $topay_charge; ?></td>
		  <td><?php echo $aftcharge; ?>	</td>
		  <td><?php echo $cons_no; ?></td>
		   <td><?php echo $cons_no; ?></td>
		    <td><?php echo $cons_no; ?></td>
	       <td><?php echo $esscharge; ?></td> 	
			<td><?php echo $cons_no; ?>	</td>
			<td><?php echo $cons_no; ?></td>
        </tr> 
						</tbody>
						<?php } ?>
				</table>
				  <table style="width:100%; border-collapse: collapse;" >
       <tr>
   <td>
       &nbsp;<p><b>Notes</b>:-</br>
        &nbsp;&nbsp;<b>1. &nbsp; Please Pay as per due date given in this Logistics Services Invoice.</b>:-</br>
        &nbsp;&nbsp;<b>2. &nbsp;Please pay by cheque only in favour of "Vivanta Logistics Private Limited"</b></br>
        &nbsp;&nbsp;<b>3. &nbsp;Permanent Account Number(PAN):-**********</b></br>
        &nbsp;&nbsp;<b>4. &nbsp;GSTN:-27**********1Z5</b></br>
        &nbsp;&nbsp;<b>5. &nbsp;Corporate Identity Number:-U74999PN2017PTC172759</b></br>
        &nbsp;&nbsp;<b>6. &nbsp;Invoice Queries,please mail <NAME_EMAIL></b></br>
        &nbsp;&nbsp;<b>7. &nbsp;Request you to please pay on time to avoid disruption in service/late payment fees.</b></br>
        &nbsp;&nbsp;<b>8. &nbsp;All disputes are subject to pune jirisdiction.</b></br>
        &nbsp;&nbsp;<b>9. &nbsp;TDS to be deducted as per provision of section 194C</b></br>
        &nbsp;&nbsp;<b>10. &nbsp;Please email TDS <NAME_EMAIL></b></br>
       </p>
   </td></tr>
	<tr><td>
       <p><b>Bank Details</b>:-</br>
        <b>Company name</b>:- Vivanta Logistics Private Limited</br>
        <b>Bank Name</b>:- Kotak Mahndra Bank</br>
        <b>Branch</b>Model Colony,Pune-411016 </br>
        <b>A/C No.</b>:- **********</br>
        <b>IFSC Code</b>:-KKBK0001759</br>
        <b>Account Type</b>:-Current Account</br></p>
        <p style="text-align:right;" ><b>Authorised Signtory</b></p>
   </td>
			
			
		
	
</tr></table>

		</div>		
  				
			
														
							
														
											
							
							
							
								
							
								
								
							
							
								
							
  
 



							<div class="control-group">
							    <div class="controls">
							
									
									<?php  if(isset($_GET['cust'])){ ?><input name="update" class="btn btn-primary" type="submit" value="Update" onClick="return validation()"> <?php }else {?><input name="submit" class="btn btn-primary" type="submit" value="Submit" onClick="return validation()"> <?php }?>
									<input name="export" class="btn btn-primary" type="submit" value="Export">
									<input type="button" id="printpagebutton" onClick="printpage();" value="Print" class="btn btn-primary">
									<a id="backbutton" href="invoice1.php">&nbsp;<input type="button" class="btn btn-primary" id="backbutton" onClick="closeWin();" value="Close"> </a>
							    </div>
							</div><!--end control-group-->
										
   
  
<p align="left">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>



<p align="left">  </p>

</div></div>	</form>
                    </div></div>	
<link href="./bootstrap/css/bootstrap-datetimepicker.min.css" rel="stylesheet" media="screen">		
<script type="text/javascript" src="./jquery/jquery-1.8.3.min.js" charset="UTF-8"></script>
<script type="text/javascript" src="./bootstrap/js/bootstrap.min.js"></script>
<script type="text/javascript" src="./jquery/bootstrap-datetimepicker.js" charset="UTF-8"></script>
<script type="text/javascript" src="./jquery/locales/bootstrap-datetimepicker.pl.js" charset="UTF-8"></script>
<script type="text/javascript">
   	$('.form_date').datetimepicker({
        language:  'pl',
        weekStart: 1,
        todayBtn:  1,
		autoclose: 1,
		todayHighlight: 1,
		startView: 2,
		minView: 2,
		forceParse: 0
    });
	
</script>
<script type="text/javascript">
    function printpage() {
        //Get the print button and put it into a variable
        var printButton = document.getElementById("printpagebutton");
        var back=document.getElementById("backbutton");
        var removespan=document.getElementById("removespan");
        var removespan1=document.getElementById("removespan1");
        var removespan2=document.getElementById("removespan2");
        var removespan3=document.getElementById("removespan3");
        var removespan4=document.getElementById("removespan4");
        var removespan5=document.getElementById("removespan5");
        var removespan6=document.getElementById("removespan6"); 
        var removespan7=document.getElementById("removespan7"); 
        var removespan8=document.getElementById("removespan8");
        var removespan9=document.getElementById("removespan9");
        var removespan10=document.getElementById("removespan10");
        var removespan11=document.getElementById("removespan11");
        var removespan12=document.getElementById("removespan12");
        var removespan13=document.getElementById("removespan13");
        var removespan14=document.getElementById("removespan14"); 
        var removespan15=document.getElementById("removespan15"); 
        var removespan16=document.getElementById("removespan16");

        //Set the print button visibility to 'hidden' 
      
        removespan.style.visibility = 'hidden';
        removespan1.style.visibility = 'hidden';
        removespan2.style.visibility = 'hidden';
        removespan3.style.visibility = 'hidden';
        removespan4.style.visibility = 'hidden';
        removespan5.style.visibility = 'hidden'; 
        removespan6.style.visibility = 'hidden';
        removespan7.style.visibility = 'hidden';
        removespan8.style.visibility = 'hidden';
        removespan9.style.visibility = 'hidden';
        removespan10.style.visibility = 'hidden'; 
        removespan11.style.visibility = 'hidden';
        removespan12.style.visibility = 'hidden';
        removespan13.style.visibility = 'hidden';
        removespan14.style.visibility = 'hidden';
        removespan15.style.visibility = 'hidden';
        removespan16.style.visibility = 'hidden';
        printButton.style.visibility = 'hidden';
        back.style.visibility = 'hidden';
        //Print the page content
        window.print();
        //Set the print button to 'visible' again 
        //[Delete this line if you want it to stay hidden after printing]
        printButton.style.visibility = 'visible';
         back.style.visibility = 'visible';
    }
</script>



<script>
// we're binding a lot of different click event-handlers to this element
// there's no point looking it up every time we do so:
var body = $('body');

// binding the click event for the add-row button:
body.on('click', 'button.add-row', function() {
  // getting the relevant <table>:
  var table = $(this).closest('div.table-content'),
    // and the <tbody> and <thead> elements:
    tbody = table.find('tbody'),
    thead = table.find('thead');

  // if the <tbody> has children:
  if (tbody.children().length > 0) {
    // we find the last <tr> child element, clone it, and append
    // it to the <tbody>:
    tbody.find('tr:last-child').clone().appendTo(tbody);
  } else {
    // otherwise, we create the basic/minimum <tr> element:
    var trBasic = $('<tr />', {
        'html': '<td><span class="remove remove-row">x</span></td><td><input type="text" class="form-control" /></td>'
      }),
      // we find the number of columns that should exist, by
      // looking at the last <tr> element of the <thead>,
      // and finding out how many children (<th>) elements it has:
      columns = thead.find('tr:last-child').children().length;

    // a for loop to iterate over the difference between the number
    // of children in the created trBasic element and the current
    // number of child elements of the last <tr> of the <thead>:
    for (var i = 0, stopWhen = columns - trBasic.children.length; i < stopWhen; i++) {
      // creating a <td> element:
      $('<td />', {
        // setting its text:
        'text': 'static element'
          // appending that created <td> to the trBasic:
      }).appendTo(trBasic);
    }
    // appending the trBasic to the <tbody>:
    tbody.append(trBasic);
  }
});

body.on('click', 'span.remove-row', function() {
  $(this).closest('tr').remove();
});

body.on('click', 'span.remove-col', function() {
  // getting the closest <th> ancestor:
  var cell = $(this).closest('th'),
    // getting its index with jQuery's index(), though
    // cell.prop('cellIndex') would also work just as well,
    // and adding 1 (JavaScript is zero-based, CSS is one-based):
    index = cell.index() + 1;
  // finding the closest <table> ancester of the <th> containing the
  // clicked <span>:
  cell.closest('table')
    // finding all <td> and <th> elements:
    .find('th, td')
    // filtering that collection, keeping only those that match
    // the same CSS-based, using :nth-child(), index as the <th>
    // containing the clicked <span>:
    .filter(':nth-child(' + index + ')')
    // removing those cells:
    .remove();
});

body.on('click', 'button.add-col', function() {
  // finding the table (because we're using it to find both
  // the <thead> and <tbody>:
  var table = $(this).closest('div.table-content').find('table'),
    thead = table.find('thead'),
    // finding the last <tr> of the <thead>:
    lastTheadRow = thead.find('tr:last-child'),
    tbody = table.find('tbody');

  // creating a new <th>, setting its innerHTML to the string:
  $('<th>', {
    'html': '<input type="text" class="form-control pull-left" value="Property" /> <span class="pull-left remove remove-col">x</span>'
      // appending that created <th> to the last <tr> of the <thead>:
  }).appendTo(lastTheadRow);
  // creating a <td>:
  $('<td>', {
    // setting its text:
    'text': 'static element'
      // inserting the created <td> after every <td> element
      // that is a :last-child of its parent:
  }).insertAfter('td:last-child');
});
</script>
<script type="text/javascript">

function closeWin() {
    back.close();
}
    window.onbeforeunload = function() {
        
    }
</script>

<?php

 
function convert_number_to_words($number) {

    $hyphen      = '-';
    $conjunction = ' and ';
    $separator   = ', ';
    $negative    = 'negative ';
    $decimal     = ' point ';
    $dictionary  = array(
        0                   => 'Zero',
        1                   => 'One',
        2                   => 'Two',
        3                   => 'Three',
        4                   => 'Four',
        5                   => 'Five',
        6                   => 'Six',
        7                   => 'Seven',
        8                   => 'Eight',
        9                   => 'Nine',
        10                  => 'Ten',
        11                  => 'Eleven',
        12                  => 'Twelve',
        13                  => 'Thirteen',
        14                  => 'Fourteen',
        15                  => 'Fifteen',
        16                  => 'Sixteen',
        17                  => 'Seventeen',
        18                  => 'Eighteen',
        19                  => 'Nineteen',
        20                  => 'Twenty',
        30                  => 'Thirty',
        40                  => 'Fourth',
        50                  => 'Fifty',
        60                  => 'Sixty',
        70                  => 'Seventy',
        80                  => 'Eighty',
        90                  => 'Ninety',
        100                 => 'Hundred',
        1000                => 'Thousand',
        1000000             => 'Million',
        1000000000          => 'Billion',
        1000000000000       => 'Trillion',
        1000000000000000    => 'Quadrillion',
        1000000000000000000 => 'Quintillion'
    );

    if (!is_numeric($number)) {
        return false;
    }

    if (($number >= 0 && (int) $number < 0) || (int) $number < 0 - PHP_INT_MAX) {
        // overflow
        trigger_error(
            'convert_number_to_words only accepts numbers between -' . PHP_INT_MAX . ' and ' . PHP_INT_MAX,
            E_USER_WARNING
        );
        return false;
    }

    if ($number < 0) {
        return $negative . convert_number_to_words(abs($number));
    }

    $string = $fraction = null;

    if (strpos($number, '.') !== false) {
        list($number, $fraction) = explode('.', $number);
    }

    switch (true) {
        case $number < 21:
            $string = $dictionary[$number];
            break;
        case $number < 100:
            $tens   = ((int) ($number / 10)) * 10;
            $units  = $number % 10;
            $string = $dictionary[$tens];
            if ($units) {
                $string .= $hyphen . $dictionary[$units];
            }
            break;
        case $number < 1000:
            $hundreds  = $number / 100;
            $remainder = $number % 100;
            $string = $dictionary[$hundreds] . ' ' . $dictionary[100];
            if ($remainder) {
                $string .= $conjunction . convert_number_to_words($remainder);
            }
            break;
        default:
            $baseUnit = pow(1000, floor(log($number, 1000)));
            $numBaseUnits = (int) ($number / $baseUnit);
            $remainder = $number % $baseUnit;
            $string = convert_number_to_words($numBaseUnits) . ' ' . $dictionary[$baseUnit];
            if ($remainder) {
                $string .= $remainder < 100 ? $conjunction : $separator;
                $string .= convert_number_to_words($remainder);
            }
            break;
    }

    if (null !== $fraction && is_numeric($fraction)) {
        $string .= $decimal;
        $words = array();
        foreach (str_split((string) $fraction) as $number) {
            $words[] = $dictionary[$number];
        }
        $string .= implode(' ', $words);
    }

    return $string;
}

?>
</html>
