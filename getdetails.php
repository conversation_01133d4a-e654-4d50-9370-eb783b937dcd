<?php
session_start();
require 'connection.php';
require_once('database.php');
require_once('library.php');
isUser();

$date=$_GET['date'];

 $date2=$_GET['date2'];

$statusid=$_GET['rep'];

//$prospect=$_GET['prospect'];
if($statusid=='all' || $statusid==all)
{
$prospectquery="";
}
else
{
$prospectquery=" AND status='".$statusid."' ";

}

//echo $prospectquery;

if($date2=='')
{
 
$Cdate2=date('Y-m-d',time());
}
else
{
$Cdate2=date('Y-m-d',strtotime($date2));
}



$Cdate=date('Y-m-d',strtotime($date));

if($statusid!="All")
{
$sql="SELECT * FROM tbl_courier WHERE tbl_courier.status ='$statusid' and tbl_courier.book_date between '$Cdate' and '$Cdate2'   ".$prospectquery;
}
else 
{
$sql="SELECT * FROM tbl_courier WHERE tbl_courier.book_date between '$Cdate' and '$Cdate2' ";
}
//echo $sql;
echo "<table border='1' align='center' bgcolor='#D8D8D8' class='mystyle'>";

//echo "<tr ><td colspan='12' align='center'><b>New Enquiries</b></td></tr>";
echo "<tr align='center'><td><b>Sr.No.</b></td><td><b>C/N No</b></td><td><b>Invoice No</b></td><td><b>Invocie Value</b></td><td><b>Qty </b></td><td><b>Weight</b></td><td><b>Type </b></td><td><b>Mode</b></td><td><b>Pickup Date</b></td><td><b>Status </b></td></tr>";
$result = mysqli_query($con,$sql);
$cntr=0;
while($row = mysqli_fetch_array($result))
 {
$cntr=$cntr+1;  
echo "<tr><td>".$cntr."</td><td>".$row['cons_no']."</td><td>".$row['invice_no']."</td><td>".$row['invi_value']."</td><td>".$row['qty']."</td><td>".$row['weight']."</td><td>".$row['type']."</td><td>".$row['mode']."</td><td>".$row['pick_date']."</td><td>".$row['status']."</td></tr>";
  }
  if($cntr==0)
  {
  echo "<tr><td colspan='10' align='center'><font color='red'> No Record Found...........</font></td></tr>";
  }
 echo "</table>";
mysqli_close($con);
?> 