<?php 
require "conn.php";
$docket=$_GET['docketno'];
$sql = "SELECT tbl_city_code.Id,city_name FROM `tbl_city_code` inner join status_history_tbl on tbl_city_code.Id=status_history_tbl.current_loc where Cons_Id='$docket' union SELECT Id,city_name FROM `tbl_city_code`
";

$r = mysqli_query($con,$sql);
$result = array();
while($row = mysqli_fetch_array($r)){
 array_push($result,array(
     'city_id'=>$row['Id'],
'city_name'=>$row['city_name']
    ));
}
echo json_encode(array('result'=>$result));
mysqli_close($con);
?>