<?php
session_start();
require_once('database.php');
require_once('library.php');
require 'connection.php';
isUser();

$a=$_SESSION['username'];
$sql="select * from login where username='$a'";
$result = mysqli_query($con, $sql);
$row1 = mysqli_fetch_array($result, MYSQLI_BOTH);
 $userid=$row1['rid']; 
 
 $sql="select cid,office,Manager_name from tbl_courier_officers where cid='$userid'";
$result = mysqli_query($con, $sql);
$row1 = mysqli_fetch_array($result, MYSQLI_BOTH);
$uoffice=$row1['office'];  $clerkname=$row1['Manager_name'];
$id=$row1['cid'];

date_default_timezone_set('Asia/Kolkata');
$date = date('d/m/Y h:i:s', time());

$sql1="select * from `tbl_offices` ORDER BY off_name ASC";
 $result2=mysqli_query($con,$sql1);
while($row2=mysqli_fetch_array($result2))
{
	if($company==$row2['id'])
	{
	$loc=$loc."<option value='".$row2['id']."' selected>".$row2['off_name']."</option>";
	}
	else{
	$loc=$loc."<option value='".$row2['id']."' >".$row2['off_name']."</option>";
	}
}


if($a!="admin")
{
$sql = "SELECT * FROM `custreg` WHERE userid='$id' ORDER BY custname ASC";
}
else{
$sql = "SELECT * FROM `custreg` ORDER BY custname ASC";
}
$result2=mysqli_query($con,$sql);
while($row2=mysqli_fetch_array($result2))
{
	if($company==$row2['custid'])
	{
	$drop1=$drop1."<option value='".$row2['custid']."' selected>".$row2['custname']."</option>";
	}
	else{
	$drop1=$drop1."<option value='".$row2['custid']."' >".$row2['custname']."</option>";
	}
}


$no=01;
$result= mysqli_query($con,"SELECT *,max(billid) as id FROM receipt where type='Invoice'");
while($row = mysqli_fetch_array($result))
  {
  $maxInvid=$row['id'];
 
   }

//$result1 = mysqli_query($con,"SELECT * FROM `` ORDER BY `id` ASC ");
 //$sql2="SELECT * FROM receipt where billid=".$maxInvid."";
 $sql2="SELECT * FROM receipt";
$result2= mysqli_query($con,$sql2);
while($row = mysqli_fetch_array($result2))
  {
 $maxInvid;
$no=$row['billid'];
 
}
 $_SESSION["no"]=$no;
 
?>

<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>
	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
<script language="JavaScript">
</script>
</head>
<?php include("header.php"); ?>
   <div class="container">
   <form action="invoicebil.php" name="billform" method="post" >
			<div class="row">
     			     <div class="titleHeader clearfix">
							<h3>Invoice Bill Generate </h3> 
						</div>
					<div class="span6">
					  
						<!--<div class="control-group">
                             <div class="controls">
							     <label class="control-label">Bill No.</label>
							     <input name="bilno" type="text" value="<?php echo $no;?>" readonly>
								<input name="tp" type="hidden" value="Invoice"> 

							 </div>
					    </div>
						<div class="control-group">
                            <label>Branch Location :</label>
							<div class="controls">
	                            <select name="curntloca" id="curntloca" data-rule-required="true">
												<option value="">-- Please select --</option>
												<?php echo $loc; ?>
								</select>
							</div>
					    </div>-->
				<input type="hidden" name="uoffice" value="<?php echo $uoffice;?>">
<input type="hidden" name="uaddress" value="<?php echo $clerkname;?>">
<input type="hidden" name="cid" value="<?php echo $id;?>">
<input name="todat" id="todat" type="hidden" value="<?php echo $date;?>" >		
						<div class="control-group">
							    <label class="control-label">Customer Name: </label> 
							        <div class="controls">
									    <select name="cname" id="cname" >
									     <option value="">-- Please Select --</option>
												<?php echo $drop1; ?>
								        </select>
	                                </div>
						</div>
                       <!-- <div class="control-group">
							    <label class="control-label">Fuel Charges (%): </label> 
							        <div class="controls">
									     <input name="fuelcharge" id="fuelcharge" type="text" placeholder="Fuel Charges" >
									</div>
						</div>-->
										 
                       <div class="control-group">
						    <div class="controls"> 
								<input name="Submit" class="btn btn-primary" type="submit" value="SUBMIT" onClick="return validateForm()" >
							&nbsp;&nbsp;&nbsp;<button type="reset" class="btn ">CLEAR</button>
					        </div>
					    </div>
                    </div><!-- span6-->
					<div class="span6">
						<div class="control-group ">
						   <div class="controls">
							<label for="rep"> Start Date : <span class="text-error">*</span></label>
								<div class="controls input-append date form_date" data-date="" data-date-format="dd MM yyyy" data-link-field="dtp_input2" data-link-format="yyyy-mm-dd">
									 <input  type="text" value=""id="date2" name="date2" readonly>	
										<span class="add-on"><i class="icon-remove"></i></span>
										 <span class="add-on"><i class="icon-th"></i></span>
								</div>
									 <input type="hidden" id="dtp_input2" value="" />
						    </div>
						</div>
						<div class="control-group ">
						   <div class="controls">
							<label class="control-label" for="rep"> End Date : <span class="text-error">*</span></label>
								<div  class="controls input-append date form_date" data-date="" data-date-format="dd MM yyyy" data-link-field="dtp_input2" data-link-format="yyyy-mm-dd">
									<input  type="text" value="" id="date4" name="date4" readonly>	
										<span class="add-on"><i class="icon-remove"></i></span>
										 <span class="add-on"><i class="icon-th"></i></span>
								</div>
									 <input type="hidden" id="dtp_input2" value="" />
						   </div>
						</div>
					
					</div>
					
			</div><!--end row-->
				<div class="row">	
				    <div class="span12">
                     <div class="flexslider">
						
							<table border="1" width="100%">
							<tbody>
						     <tr>
							<td width="50%"> To,<b>ReliablePlus Cargo</b>
							</td>
							<td width="50%">  
							<table border="1" width="100%">
									<tr><td align="left">Invoice Period </td><td align="right">0</td></tr>
									<tr><td align="left">Invoice No</td><td align="right">0</td></tr>
									<tr><td align="left">Invoice Date</td><td align="right">0</td></tr>
									<tr><td align="left">T/S Amount</td><td align="right">0</td></tr>
									<tr><td align="left">Service Charges</td><td align="right">0</td></tr>
									<tr><td align="left">Fuel Surcharge</td><td align="right">0</td></tr>
									<tr><td align="left">Sub Total</td><td align="right">0</td></tr>
									<tr><td align="left">Service Tax@14% </td><td align="right">0</td></tr>
									<tr><td align="left">Edu-Cess@</td><td align="right">0</td></tr>
									<tr><td align="left">SHEC@</td><td align="right">0</td></tr>
									<tr><td align="left">Grand Total</td><td align="right">0</td></tr>
							</table>  </td>
						</tr>
						<tr><td align="left" colspan="2"> Amount in words :<b>Zero </b></td> </tr>
						<tr><td colspan="2" align="center">-------------------------------------------------------------------------------------------------------</td></tr>
						<tr> <td align="center" colspan="2"> ReliablePlus Cargo </td></tr>
						<tr> <td  align="center" colspan="2"> Payment Advice (Please detach and return with your payment) </td></tr>
						<tr> <td>Invoice No:    Date:</td><td>Client Code:  </td></tr>
										
							</tbody>
							</table>
							<table border="1" width="100%">
<tr><td colspan="3">From </td><td colspan="3">To</td></tr>
<tr><td>Name of the Bank </td><td>Cheque / DD Number</td><td>Cheque / DD Date</td><td>Invoice Amount (RS.)</td><td>TDS (Rs.)</td><td>Net Amount (Rs.)</td></tr>
<tr><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr>
<tr><td colspan="6">Amount in words : </td></tr>
<tr><td colspan="6" align="center">Please make crossed Cheque or DD in favour of "ReliablePlus Cargo"</td></tr>
<tr><td colspan="2">Name: </td><td colspan="2">Signature : </td><td colspan="2">Date: </td></tr>
<tr><td colspan="6" align="center">--------------------------------------------------------------------------------------------------------</td></tr>
<tr><td colspan="6" align="center">ReliablePlus Cargo </td></tr>
<tr><td colspan="6" align="center">Bill Acknowledgement </td></tr>
<tr><td colspan="6" >Client Code: Invoice No: Invoice Date: Net Amount(Rs.) </td></tr>
<tr><td colspan="6" >Client Name:        Due Date:    </td></tr>
<tr><td colspan="3" >Name of the Receiver    Receiver Date:	</td><td colspan="3">Sign & Seal </td></tr>

</table>
					</div><!--end flexslider <ul class="slides"></ul>-->
				  </div><!--end span12-->
				</div><!--end row-->
<h6>Page 2</h6>
			<div class="row">	
				    <div class="span12">
						<div class="flexslider">	
				     
						<table border="1" width="100%"> <tbody><table width="100%" height="141" border="1">
  <tr>
  <td colspan="7"><strong>Customer Code : Invoice No :  Invoice Date :  Customer Name :  Branch : </strong></td></tr>
  
  <tr>
    <td height="33">SLNO</td>
    <td >MFNO</td>
    <td>MF DATE</td>
    <td >Cn No City Exp</td>
    <td>Total Normal</td>
    <td>Total Weight</td>
    <td> Amount (Rs.)</td>
  </tr>
  <tr>
    <td height="33" colspan="7">&nbsp;</td>
  </tr>
  <tr>
    <td height="33" colspan="5"><strong>Page Total</strong></td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
  </tr>
  <tr>
    <td height="33" colspan="5"><strong>Sub Total</strong></td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
  </tr>
</table>
</tr>
<p>&nbsp;</p><tr>
<table width="100%" height="143" border="1">
  <tr>
    <th colspan="8" scope="col">CONSIGNMENT DETAILS(VAS &amp; MIS CHARGES)</th>
  </tr>
  <tr>
    <td >Sr.No</td>
    <td >Consignment No</td>
    <td >Product </td>
    <td >Transhipment</td>
    <td >Services Charge</td>
    <td >Risk Surcharge</td>
    <td >Misc. Charge</td>
    <td>Total</td>
  </tr>
  <tr>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
  </tr>
  <tr>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
  </tr>
  <tr>
    <td colspan="8"><div align="left">* OTH ( Other Product )</div></td>
  </tr>
</table></tr>
<p>&nbsp;</p><tr>
<table width="100%" border="1">
  <tr>
    <th colspan="13" scope="col">CONSIGNMENT DETAILS(WEIGHT / MODE &amp; DESTINATION MISMATCH)</th>
  </tr>
  <tr>
    <td height="53"><strong>CN no</strong></td>
    <td><strong>BKG. Date</strong></td>
    <td ><strong>Dest Billed</strong></td>
    <td><strong>Product</strong></td>
    <td><strong>Bill wt</strong></td>
    <td><strong>Bill Amnt</strong></td>
    <td ><strong>Inv. No</strong></td>
    <td ><strong>Inv.Date</strong></td>
    <td><strong>Act. Dest</strong></td>
    <td><strong>Act Wt</strong></td>
    <td><strong>Act Amnt</strong></td>
    <td><strong>Diff. Amt.Bill</strong></td>
    <td><strong>Changes Narration</strong></td>
  </tr>
  <tr>
    <td height="25" colspan="13">&nbsp;</td>
  </tr>
  <tr>
    <td height="52" colspan="4"><strong>Total</strong></td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td colspan="3">&nbsp;</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td colspan="2">&nbsp;</td>
  </tr>
</table></tr>
<p>&nbsp;</p><tr>
<table width="100%" border="1">
  <tr>
    <th colspan="7" scope="col">Reverse RTO Charges Details</th>
  </tr>
  <tr>
    <td height="40">Sr.No</td>
    <td >Consignment</td>
    <td>Date</td>
    <td >Origin</td>
    <td>Destination</td>
    <td >Weight</td>
    <td>Amount(Rs.)</td>
  </tr>
  <tr>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
  </tr>
</table></tr>
<p>&nbsp;</p><tr>
<table width="100%" border="1">
  <tr>
    <th colspan="10" scope="col">GEC Consignment Charges Details</th>
  </tr>
  <tr>
    <td>Sr.No</td>
    <td>C/N No</td>
    <td>T/S Amt</td>
    <td>Service Charge</td>
    <td>Risk Charge</td>
    <td>ESS Charge</td>
    <td>Warai Charge</td>
    <td>Extra Charges for Box</td>
    <td>Other Charges</td>
    <td>Total Amount</td>
  </tr>
  <tr>
    <td colspan="10">&nbsp;</td>
  </tr>
</table></tr>
<p>&nbsp;</p>
<tr>
<table width="100%" border="1">
  <tr>
    <th colspan="3" scope="col">Expire Consignment Charges Details</th>
  </tr>
  <tr>
    <td>Rate per C/N</td>
    <td colspan="2">Total Amount</td>
  </tr>
  <tr>
    <td colspan="3">&nbsp;</td>
  </tr>
</table></tr> </tbody></table>
					 
					 
						</div><!--end flexslider <ul class="slides"></ul>-->
				    </div><!--end span12-->
			</div><!--end row-->
			 </form>
		</div><!--end conatiner-->
<link href="./bootstrap/css/bootstrap-datetimepicker.min.css" rel="stylesheet" media="screen">		
<script type="text/javascript" src="./jquery/jquery-1.8.3.min.js" charset="UTF-8"></script>
<script type="text/javascript" src="./bootstrap/js/bootstrap.min.js"></script>
<script type="text/javascript" src="./jquery/bootstrap-datetimepicker.js" charset="UTF-8"></script>
<script type="text/javascript" src="./jquery/locales/bootstrap-datetimepicker.pl.js" charset="UTF-8"></script>
<script type="text/javascript">
   	$('.form_date').datetimepicker({
        language:  'pl',
        weekStart: 1,
        todayBtn:  1,
		autoclose: 1,
		todayHighlight: 1,
		startView: 2,
		minView: 2,
		forceParse: 0
    });
	
</script>
	

<script  type="text/javascript">
function validateForm()
{

  var cname1=document.forms["billform"]["cname"].value;
if (cname1==null || cname1=="")
  {
   alert("Please Select Customer Name ");
  return false;
  
  }
  
  var fuelcharge1=document.forms["billform"]["fuelcharge"].value;
if (fuelcharge1==null || fuelcharge1=="")
  {
  alert("Fuel Charges must be filled out");
  return false;
  }
  
}
</script>

	
<?php 
include("footer.php"); ?>