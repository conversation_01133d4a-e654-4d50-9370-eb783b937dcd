<!DOCTYPE html>
<html>
<head>
    <title>Simple Search Test</title>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
</head>
<body>
    <h2>Simple Search Test</h2>
    
    <label>Test Dropdown:</label>
    <select id="testSelect" style="width: 300px;">
        <option value="">-- Please Select --</option>
        <option value="__SEARCH__">🔍 Search...</option>
        <option value="1">ABC-Company Name-Mumbai</option>
        <option value="2">XYZ-Another Company-Delhi</option>
        <option value="3">DEF-Third Company-Bangalore</option>
        <option value="4">GHI-Fourth Company-Chennai</option>
        <option value="5">JKL-Fifth Company-Kolkata</option>
    </select>

    <br><br>
    <button onclick="testSearch()">Manual Test Search</button>

    <script>
    $(document).ready(function() {
        console.log('Simple test page loaded');
        
        var $select = $('#testSelect');
        
        // Store original options
        var originalOptions = [];
        $select.find('option').each(function() {
            var val = $(this).val();
            var text = $(this).text();
            if (val && val !== '__SEARCH__' && text !== '-- Please Select --') {
                originalOptions.push({value: val, text: text});
            }
        });
        
        console.log('Original options:', originalOptions);
        
        // Initialize Select2
        $select.select2({
            placeholder: "-- Please Select --",
            allowClear: true,
            width: '100%'
        });
        
        // Handle selection change
        $select.on('change', function() {
            var selectedValue = $(this).val();
            console.log('Selected:', selectedValue);
            
            if (selectedValue === '__SEARCH__') {
                console.log('Opening search input...');
                showSearchInput($select, originalOptions);

                setTimeout(function() {
                    $select.val('').trigger('change');
                }, 100);
            }
        });
    });
    
    function testSearch() {
        var $select = $('#testSelect');
        var originalOptions = [
            {value: '1', text: 'ABC-Company Name-Mumbai'},
            {value: '2', text: 'XYZ-Another Company-Delhi'},
            {value: '3', text: 'DEF-Third Company-Bangalore'},
            {value: '4', text: 'GHI-Fourth Company-Chennai'},
            {value: '5', text: 'JKL-Fifth Company-Kolkata'}
        ];
        showSearchInput($select, originalOptions);
    }
    
    function showSearchInput($select, originalOptions) {
        console.log('showSearchInput called with:', originalOptions);

        // Hide the select dropdown
        $select.select2('close');

        // Create search input container
        var searchContainer = $('<div id="searchContainer" style="position: relative; display: inline-block; width: 100%; margin-top: 5px;"></div>');

        // Create search input
        var searchInput = $('<input type="text" placeholder="🔍 Type to search and press Enter..." style="width: 100%; padding: 8px 12px; border: 2px solid #007bff; border-radius: 4px; font-size: 14px; box-sizing: border-box; outline: none;">');

        // Create results dropdown
        var resultsDropdown = $('<div style="position: absolute; top: 100%; left: 0; right: 0; background: white; border: 1px solid #ddd; border-top: none; border-radius: 0 0 4px 4px; max-height: 200px; overflow-y: auto; z-index: 1000; display: none;"></div>');

        // Add elements to container
        searchContainer.append(searchInput);
        searchContainer.append(resultsDropdown);

        // Insert after the select element
        $select.parent().append(searchContainer);

        // Focus on input
        searchInput.focus();

        // Handle input events
        searchInput.on('input', function() {
            var searchTerm = $(this).val().toLowerCase();

            if (searchTerm.length === 0) {
                resultsDropdown.hide();
                return;
            }

            var resultsHtml = '';
            var matchCount = 0;

            for (var i = 0; i < originalOptions.length; i++) {
                var option = originalOptions[i];
                if (option.value && option.text && option.text.toLowerCase().indexOf(searchTerm) > -1) {
                    resultsHtml += '<div class="search-result-item" data-value="' + option.value + '" style="padding: 8px 12px; cursor: pointer; border-bottom: 1px solid #eee;">' + option.text + '</div>';
                    matchCount++;
                }
            }

            if (matchCount === 0) {
                resultsHtml = '<div style="padding: 8px 12px; color: #666;">No matches found</div>';
            }

            resultsDropdown.html(resultsHtml);
            resultsDropdown.show();

            // Handle result clicks
            $('.search-result-item').off('click').on('click', function() {
                var selectedValue = $(this).data('value');
                var selectedText = $(this).text();

                console.log('Selected:', selectedText, 'value:', selectedValue);

                // Set the value in the original select
                $select.val(selectedValue).trigger('change');

                // Remove search container
                searchContainer.remove();
            });

            // Add hover effects
            $('.search-result-item').hover(
                function() { $(this).css('background-color', '#f8f9fa'); },
                function() { $(this).css('background-color', 'white'); }
            );
        });

        // Handle Enter key
        searchInput.on('keydown', function(e) {
            if (e.key === 'Enter') {
                var firstResult = resultsDropdown.find('.search-result-item').first();
                if (firstResult.length > 0) {
                    firstResult.click();
                }
            } else if (e.key === 'Escape') {
                searchContainer.remove();
            }
        });

        // Handle click outside to close
        $(document).on('click.searchInput', function(e) {
            if (!searchContainer.is(e.target) && searchContainer.has(e.target).length === 0) {
                searchContainer.remove();
                $(document).off('click.searchInput');
            }
        });
    }
    </script>
</body>
</html>
