<?php
//session_start();
 //require_once('database.php');
// require_once('library.php');
//isUser();
// check_session();
 
 
 
 

  
 //Code to delete
 echo $year_delete=date("Y", strtotime("-6 months", strtotime(date("Y/m/d"))));
 echo $month_delete=date("m", strtotime("-6 months", strtotime(date("Y/m/d"))));
echo "<br>Today is " . date("Y.m.d");
  $path_delete = "images/".$year_delete."/".$month_delete."";


echo $tmp_file = 'myzip'.$month_delete.'_'.$year_delete.'.zip';

    $zip = new ZipArchive;
    if ($zip->open($tmp_file,  ZipArchive::CREATE)) {
  
  
 
  
     $php_files = glob("images/".$year_delete."/".$month_delete."/*.jpg");
     echo "images/".$year_delete."/".$month_delete."/*.jpg";
   $j=1;
   print_r($php_files);
   
  foreach($php_files as $path) {
     echo $file = basename($path);
       $zip->addFile("$path", 'qr_'.$j.'.jpg');
       $j++;
   
    
  }
  }
  $zip->close();
 
?>


    