<?php
error_reporting(~E_ALL);
session_start();
require 'connection.php';
require_once('database.php');
require_once('library.php');
isUser();

 date_default_timezone_set('Asia/Kolkata');
$todate = date('d/m/Y ', time());

 $mode=$_GET['mode'];

  $invoiceno=$_GET['invoiceno'];
 $cname=$_GET['cust']; 
 $Cdate=$_GET['start']; 
 $Cdate2=$_GET['end']; 


$sql = "SELECT * FROM custreg  WHERE custreg.custid ='$cname'";
$result = mysqli_query($con,$sql);
while($row = mysqli_fetch_array($result)) 
 {
     $custname=$row['custname'];
     $custadd=$row['custadd'];
     $custgst=$row['custgst'];
     //$custname=$row['custname'];
 }

 if(isset($_GET['cust'])){
 $invsqlli="select * from invoicebill where `custid`='$cname'and `sdate`='$Cdate'and`tdate`='$Cdate2'";
 
 $datainv=mysqli_query($con,$invsqlli);
 
 $invup=mysqli_fetch_array($datainv);
 $no=$invup['inv_no'];
  $dat=$invup['inv_date'];
  $due=$invup['inv_duedate'];
  $invid=$invup['id']; 
 }
 
 
 
if(isset($_POST['Submit']))
 {
	 $cname=$_POST['cname'];    $userid=$_POST['cid']; $frm=$_POST['uoffice'];$clerkname=$_POST['uaddress']; 
	 $fuel=$_POST['fuelcharge'];
	
 $date=$_POST['date2'];  $date2=$_POST['date4']; // $bill=$_POST['bilno'];
	// 
       
if($date2=='')
{
 
$Cdate2=date('Y-m-d',time());
}
else
{
 $Cdate2=date('Y-m-d',strtotime($date2));
}
 $Cdate=date('Y-m-d',strtotime($date));
 
 }

$cntr=0;
$word=convert_number_to_words($gtot);
	
 



mysqli_close($con);
?> 
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>
	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	
<script language="JavaScript">
</script>
</head>

 <div style="margin-top:20px;">
   <div>
       <center>
          <form action="print_invoice.php" method="post" name="myform">     
<table style="margin:0 auto; width:100%; border:1px solid #000; border-collapse:collapse;">

        
<!--header--->
<tr><td colspan="3"> <center>Tax Invoice</center></td></tr>    

<tr>
<td width="20%"><center><img src="img/logo.png" width="60%" /></center></td>   
<td width="60%" style="text-align:center;">
     <h5><b><font size="4">Vivanta Logistics Private. Limited</font></b> </h5>
  <b>CIN: U74999PN2017PTC172759</b></br>
  <b>Registered Address</b>: Bungalow No.-7,samata Hsg.Soc,behind MSEB Colony,Bhosale Nagar,</br> Pune-411007
   Customer Care No.-18003131944
</td>
<td width="20%">Invoice <input type='text' name='invoce' id='invoce' readonly value='<?php echo $invoiceno; ?>' ></td>
</tr>

<tr><td colspan="3" width="100%"> <center>Billing Details</center></br>
<p>Name:-<?php echo $custname; ?></p>
<p>Address:-<?php echo $custadd; ?></p>
<p>GSTIN:-<?php echo $custgst; ?></p>
<p>Boking Mode:-<?php echo $mode;?></p>
</td></tr>



<tr><td colspan="3"> 
<table style="margin:0 auto; width:100%; border-collapse:collapse;"  id="Table1" border="1">
<thead>
   
   <tr>
           <th>Sr. No.</th>
           
           <th>Docket No.</th>
           <th>Date</th>
           <th>Destin.</th>
           <th><span id="Weight" class="remove remove-col">Weight</span></th>
           <th><span id="Rate" class="remove remove-col">Rate</span></th>
           <th><span id="Freight" class="remove remove-col">Freight</span> </th>
           <th><span id="invalue" class=" remove remove-col">Specail Veh.Charges</span></th>
           <th><span id="oda" class="remove remove-col">ODA/Other Charges</span></th>
           <th><span id="othercharges" class=" remove remove-col">Total Frieght</span> </th>
          
          
          
           <th><span id="cgst" class="remove remove-col">SGST</span></th>
                      <th><span id="cgst" class="remove remove-col">CGST</span></th>

           <th><span id="total" class="remove remove-col">Total</span></th>
           
</tr>   
</thead>     
<tbody>
   <?php
  require 'connection.php';
 $cname=$_GET['cust']; 
 $Cdate=$_GET['start']; 
 $Cdate2=$_GET['end']; 
 $mode=$_GET['mode'];
if($cname!="")
{
 $sql="SELECT * FROM tbl_courier  WHERE tbl_courier.shipper_code ='$cname' and mode='$mode' and tbl_courier.book_date between '$Cdate' and '$Cdate2' and  `tbl_courier`.`billing_status` ='billed'";

}

$result=mysqli_query($con,$sql);

while($row=mysqli_fetch_array($result)){
  
   $sql1="SELECT city_name,r_add from tbl_city_code inner join tbl_courier on tbl_city_code.Id= tbl_courier.r_add 
  WHERE tbl_courier.r_add = '".$row['r_add']."' " ;

$result1 = mysqli_query($con,$sql1);
$row1 = mysqli_fetch_array($result1);
 $destination=$row1['city_name'];
     
     
    
$sgst=$row['freight']*(9/100);
$cgst=$row['freight']*(9/100);
$freight=$rate*$row['chweight'];

$cgstround=round($cgst);
$total=$freight+$sgst+$cgst;
$freight=$rate*$row['chweight'];
 $totfreight=$totfreight+$freight;
$total=$row['freight']+$sgst+$cgst;

    $count=$count+1;   
    echo "<tr>
<td>".$count."<input type='hidden' onkeypress='validate(event)' size='20px;' id='cnt".$count."' name='cnt".$count."' value=".$count." ></td>
<td>".$row['cons_no']."<input type='hidden' onkeypress='validate(event)' size='20px;' id='docketno".$count."' name='docketno".$count."' value='".$row['cons_no']."' ></td>
<td>".$row['book1_date']."<input type='hidden' onkeypress='validate(event)' size='20px;' id='bookdate".$count."' name='bookdate".$count."' value='".$row['book1_date']."' ></td>
<td>".$destination."<input type='hidden' onkeypress='validate(event)' size='20px;' id='destination".$count."' name='destination".$count."' value='".$destination."' ></td>
<td> ".$row['chweight']."<input type='hidden' onkeypress='validate(event)' size='20px;' id='chweight".$count."' name='chweight".$count."' value=".$row['chweight']." onchange='myfunction()'></td>
<td> ".$row['rate']."<input type='hidden' onkeypress='validate(event)' size='20px;' id='rate".$count."' name='rate".$count."' value='".$row['rate']."' onchange='myfunction()'></td>
<td> ".$row['freight']."<input type='hidden' onkeypress='validate(event)' size='20px;' id='freight".$count."' name='freight".$count."' value='".$row['freight']."' onchange='myfunction()'></td>
<td><input type='text' onkeypress='validate(event)' size='15px;' id='vehiclecharge".$count."' name='vehiclecharge".$count."' value='".$row['handlingcharge']."' onchange='myfunction()'></td>
<td><input type='text' onkeypress='validate(event)' size='15px;' id='oda".$count."' name='oda".$count."' value='".$row['oda_mis']."'onchange='myfunction()'></td>
<td><input type='text' onkeypress='validate(event)' size='15px;' id='totfreght".$count."' name='totfreght".$count."' value='".round($row['freight'])."' onchange='myfunction()'></td>
<td><input type='text' onkeypress='validate(event)' size='15px;' id='sgst".$count."' name='sgst".$count."' value='".$sgst."' onchange='myfunction()'></td>
<td><input type='text' onkeypress='validate(event)' size='15px;' id='cgst".$count."' name='cgst".$count."' value='".$cgst."' onchange='myfunction()'></td>
<td><input type='text' onkeypress='validate(event)' size='15px;' id='total".$count."' name='total".$count."' value='".round($total)."' onchange='myfunction()'></td>

</tr>";			
}
?>	  				
		
<input type='hidden' name='count' id='count' value='<?php echo $count; ?>' onchange='myfunction()'>		  
<input type='hidden' name='sname' id='sname' value='<?php echo $cname; ?>'>		  		    
 <input type="hidden" name="sdate" id="sdate" value="<?php echo $date;?>">
<input type="hidden" name="edate" id="edate" value="<?php echo $date2;?>">
</tbody>
</table>
</td></tr> 


<tr><td colspan="3"> 
Notes:-</br>
  1.   Please Pay as per due date given in this Logistics Services Invoice.:-</br>
  2.  Please pay by cheque only in favour of "Vivanta Logistics Private Limited"</br>
  3.  Permanent Account Number(PAN):-**********</br>
  4.  GSTN:-27**********1Z5</br>
  5.  Corporate Identity Number:-U74999PN2017PTC172759</br>
  6.  Invoice Queries,please mail <NAME_EMAIL></br>
  7.  Request you to please pay on time to avoid disruption in service/late payment fees.</br>
  8.  All disputes are subject to pune jirisdiction.</br>
  9.  TDS to be deducted as per provision of section 194C</br>
  10.  Please email TDS <NAME_EMAIL></br>
</td></tr>

<tr><td colspan="3"> 
<b>Bank Details:-</b></br>
 <b>Company Name:</b>Vivanta Logistics Private Limited</br>
 <b>Bank Name:</b>Bamk of Baroda</br>
 <b>Branch:</b>Senapati Bapat Road,Pune -411016.</br>
 <b>A/C No:</b>**************</br>
 <b>IFSC Code:</b>BARB0POOSEN</br>
 <b>A/C Type:</b>Current Account
 
</td></tr>

  <!--end header---> 



</table>


<div class="control-group">
							    <div class="controls">
								
									<input name="update" class="btn btn-primary" type="submit" value="Update" onClick="return validation()"> 
								
									<a id="backbutton" href="invoice1.php">&nbsp;
									<input type="button" class="btn btn-primary" id="backbutton" onClick="closeWin();" value="Close"> </a>
							    </div>
							</div><!--end control-group-->

</form>
</center>
</div>	
<link href="./bootstrap/css/bootstrap-datetimepicker.min.css" rel="stylesheet" media="screen">		
<script type="text/javascript" src="./jquery/jquery-1.8.3.min.js" charset="UTF-8"></script>
<script type="text/javascript" src="./bootstrap/js/bootstrap.min.js"></script>
<script type="text/javascript" src="./jquery/bootstrap-datetimepicker.js" charset="UTF-8"></script>
<script type="text/javascript" src="./jquery/locales/bootstrap-datetimepicker.pl.js" charset="UTF-8"></script>
<script type="text/javascript">
   	$('.form_date').datetimepicker({
        language:  'pl',
        weekStart: 1,
        todayBtn:  1,
		autoclose: 1,
		todayHighlight: 1,
		startView: 2,
		minView: 2,
		forceParse: 0
    });
	
</script>
<script type="text/javascript">
    function printpage() {
        //Get the print button and put it into a variable
        var printButton = document.getElementById("printpagebutton");
        var back=document.getElementById("backbutton");
        //Set the print button visibility to 'hidden' 
        printButton.style.visibility = 'hidden';
        back.style.visibility = 'hidden';
        //Print the page content
        window.print();
        //Set the print button to 'visible' again 
        //[Delete this line if you want it to stay hidden after printing]
        printButton.style.visibility = 'visible';
         back.style.visibility = 'visible';
    }
</script>
<script type="text/javascript">

function closeWin() {
    back.close();
}
    window.onbeforeunload = function() {
        
    }
    
    
    function myfunction(){
var srn=parseInt(document.getElementById('count').value);
//alert(srn);
 for(var i=1;i<=srn;i++)
{ 

var sgst=$('#sgst'+i).val();
//alert(rate);
var cgst=$('#cgst'+i).val();
var oda=$('#oda'+i).val();
//var othercharges=$('#othercharge'+i).val();
var spevehcharge=$('#vehiclecharge'+i).val();
var freght=$('#freight'+i).val();

var totfreight=parseFloat(oda)+parseFloat(spevehcharge)+parseFloat(freght);
var totalfrieght=Math.round(totfreight)
$('#totfreght'+i).val(totalfrieght);
var sgstround=totalfrieght*9/100;
var cgstround=totalfrieght*9/100;

$('#sgst'+i).val(Math.round(sgstround));
$('#cgst'+i).val(Math.round(cgstround));

$('#total'+i).val(totalfrieght+parseFloat(sgst)+parseFloat(cgst));
}
 
//document.getElementById("total").value=parseInt(dispencertot);
///document.getElementById("totaljar").value=parseInt(totjar);
//var total1=parseInt(document.getElementById("totaljar").value);

}
    
</script>
<?php

 
function convert_number_to_words($number) {

    $hyphen      = '-';
    $conjunction = ' and ';
    $separator   = ', ';
    $negative    = 'negative ';
    $decimal     = ' point ';
    $dictionary  = array(
        0                   => 'Zero',
        1                   => 'One',
        2                   => 'Two',
        3                   => 'Three',
        4                   => 'Four',
        5                   => 'Five',
        6                   => 'Six',
        7                   => 'Seven',
        8                   => 'Eight',
        9                   => 'Nine',
        10                  => 'Ten',
        11                  => 'Eleven',
        12                  => 'Twelve',
        13                  => 'Thirteen',
        14                  => 'Fourteen',
        15                  => 'Fifteen',
        16                  => 'Sixteen',
        17                  => 'Seventeen',
        18                  => 'Eighteen',
        19                  => 'Nineteen',
        20                  => 'Twenty',
        30                  => 'Thirty',
        40                  => 'Fourth',
        50                  => 'Fifty',
        60                  => 'Sixty',
        70                  => 'Seventy',
        80                  => 'Eighty',
        90                  => 'Ninety',
        100                 => 'Hundred',
        1000                => 'Thousand',
        1000000             => 'Million',
        1000000000          => 'Billion',
        1000000000000       => 'Trillion',
        1000000000000000    => 'Quadrillion',
        1000000000000000000 => 'Quintillion'
    );

    if (!is_numeric($number)) {
        return false;
    }

    if (($number >= 0 && (int) $number < 0) || (int) $number < 0 - PHP_INT_MAX) {
        // overflow
        trigger_error(
            'convert_number_to_words only accepts numbers between -' . PHP_INT_MAX . ' and ' . PHP_INT_MAX,
            E_USER_WARNING
        );
        return false;
    }

    if ($number < 0) {
        return $negative . convert_number_to_words(abs($number));
    }

    $string = $fraction = null;

    if (strpos($number, '.') !== false) {
        list($number, $fraction) = explode('.', $number);
    }

    switch (true) {
        case $number < 21:
            $string = $dictionary[$number];
            break;
        case $number < 100:
            $tens   = ((int) ($number / 10)) * 10;
            $units  = $number % 10;
            $string = $dictionary[$tens];
            if ($units) {
                $string .= $hyphen . $dictionary[$units];
            }
            break;
        case $number < 1000:
            $hundreds  = $number / 100;
            $remainder = $number % 100;
            $string = $dictionary[$hundreds] . ' ' . $dictionary[100];
            if ($remainder) {
                $string .= $conjunction . convert_number_to_words($remainder);
            }
            break;
        default:
            $baseUnit = pow(1000, floor(log($number, 1000)));
            $numBaseUnits = (int) ($number / $baseUnit);
            $remainder = $number % $baseUnit;
            $string = convert_number_to_words($numBaseUnits) . ' ' . $dictionary[$baseUnit];
            if ($remainder) {
                $string .= $remainder < 100 ? $conjunction : $separator;
                $string .= convert_number_to_words($remainder);
            }
            break;
    }

    if (null !== $fraction && is_numeric($fraction)) {
        $string .= $decimal;
        $words = array();
        foreach (str_split((string) $fraction) as $number) {
            $words[] = $dictionary[$number];
        }
        $string .= implode(' ', $words);
    }

    return $string;
}

?>
</html>
