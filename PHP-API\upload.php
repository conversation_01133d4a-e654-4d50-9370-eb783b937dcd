<?php
require 'connection.php';
$date1= date('Y-m-d');	 
$id=$_POST['id'];
$qrcode=$_POST['qrcode'];
$status=$_POST['status'];
 
//$target_path = "images/";
//$target_path = $target_path.$_FILES['image']['name']; 
 
//$compressedImage = compressImage($imageTemp, $imageUploadPath, 75); 
// new code start
 $year = date("Y");
 $path = "images/".$year;
  if(!file_exists($path))
  {
 mkdir($path);
}
$month = date("m");
 $target_path = "images/".$year."/".$month;

 if(!file_exists($target_path))
  {
 mkdir($target_path);
      
  }
//  new code end
  
 //$target_path = "images/";

echo $target_path;
if(move_uploaded_file($_FILES['image']['tmp_name'], $target_path))
{
 echo "<h4>The file ". basename($_FILES['image']['name']). " has been uploaded Successfully.</h4>";
}
else
{
	echo "Sorry there was an error uploading the file, please try again!";
} 

//$Sql_Query = "UPDATE `tbl_courier` SET status='$status',book_date='$date1',userid='$id',pod_img='".$_FILES['photo']['name']."' where cons_no='$qrcode' ";
$Sql_Query = "UPDATE `tbl_courier` SET status='$status',book_date='$date1',userid='$id',pod_img='".$_FILES['image']['name']."' where cons_no='$qrcode' ";


 
if(mysqli_query($conn,$Sql_Query))
{
 
echo 'Data Updated Successfully';
  }
else{
 
 echo 'Try Again';
 
 }
 mysqli_close($conn);
 
?>