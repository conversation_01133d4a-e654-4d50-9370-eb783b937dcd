<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<?php
error_reporting(~E_ALL);
session_start();
require_once('library.php');
require 'connection.php';

$a=$_SESSION['username'];
$sql="select * from login where username='$a'";
$result = mysqli_query($con, $sql);
$row1 = mysqli_fetch_array($result);
 $userid=$row1['rid']; 
$msg="";

 $date=$_POST['date2'];  $date2=$_POST['date4']; // $bill=$_POST['bilno'];
	// 
       
if($date2=='')
{
 $Cdate2=date('Y-m-d',time());
}
else
{
 $Cdate2=date('Y-m-d',strtotime($date2));
}
 $Cdate=date('Y-m-d',strtotime($date));




if(isset($_POST['export']))
{
header("Content-Type:   application/vnd.ms-excel; charset=utf-8");
header("Content-type:   application/x-msexcel; charset=utf-8");
header("Content-Disposition: attachment; filename=customerwiseReport.xls"); 
header("Expires: 0");
header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
header("Cache-Control: private",false);

echo '<table border="1">';
   echo '<tr><td align="center"><h2> ReliablePlus Cargo Express</h2><h3>We Carry Your Faith <br>Shop No 8. Springfield Plaza, L & T Phata, Sanaswadi,<br>Tal. Shirur, Dist. Pune - 412208(Maharashtra)<br>Call : +(91)-2137-615019<br> Email : <EMAIL></h3></td></tr>'; 
   
   echo '<tr><td align="center" ><h2><b>&nbsp;Invoice &nbsp;</b></h2></td></tr>'; 
  // echo ''; 
         
	echo '<tr><td align="center"><table border="1"><tr><td rowspan="3"><b><h4>Customer Name :&nbsp;'.$_POST['custname'].'</b><br><b>'.$_POST['custadd'].'</h4></b></td><td><b><h4>Invoice No. &nbsp;</h4></b></td><td><b>'.$_POST['invoiceno'].'</b></td></tr><tr><td><b><h4>Invoice Date &nbsp;</h4></b></td><td><b>'.$_POST['date2'].'</b></td></tr><tr><td><b><h4>Due Date&nbsp;</h4></b></td><td><b>'.$_POST['date4'].'</b></td></tr></table></td></tr>';	 
		 
		 /* echo '<tr><td ><div class="titleHeader clearfix"><table  border="1"><tr><td colspan="2"></td></tr><tr><td rowspan="2"></b></td><td></td></tr>
   <tr><td colspan="2"></td><td></td></tr>'; 
      
  echo '</table>';
*/
  
echo '<tr><td ><table border="1"><thead><tr>
								<th>Sr.No</th>
								<th>Docket No</th>
								<th>Bkg Date</th>
								<th>Bkg Stn</th>
								<th>Dly Stn</th>
								<th>Charged Weight</th>
								<th>Pkgs &nbsp;</th>
								<th>Mode &nbsp;</th>
								<th>Freight</th>
								<th>Docket Charge</th>
								<th>COD Charge</th>
								<th>ODA_MISC Charge</th>
								<th>Total Charge</th>
							</tr>
						</thead>';
echo '<tbody>';

$sql="SELECT * FROM custreg INNER JOIN tbl_courier ON `custreg`.`custname`=`tbl_courier`.`ship_name` WHERE `custreg`.`custid`= '".$_POST['cust']."' and tbl_courier.book_date between '".$_POST['sdate']."' and '".$_POST['tdate']."'";

$result = mysqli_query($con,$sql);
$cntr=0;
while($row = mysqli_fetch_array($result)) 
 {
	 $cntr++;  
 $tot=$row['dock_charg']+$row['dod_cod']+$row['oda_mis']+$row['freight'];
 
$rto=$rto.'<div class="titleHeader clearfix"><tr align="center"><td>'.$cntr.'</td><td>'.$row['cons_no'].'</td><td>'.$row['book_date'].'</td><td>'.$row['from'].'</td><td>'.$row['desti'].'</td><td>'.$row['chweight'].'</td><td>'.$row['qty'].'</td><td>'.$row['mode'].'</td><td>'.$row['freight'].'</td><td>'.$row['dock_charg'].'</td><td>'.$row['dod_cod'].'</td><td>'.$row['oda_mis'].'</td><td><b>'.$towe[]=$tot.'</b></td></tr></div>';
   
 }
echo $rto;
 echo $pagetot=$pagetot.'<tr><td colspan="6" rowspan="2"><b><h4>Remark :</h4></b></td><td colspan="6"><b><h4>Total </h4></b></td><td><b>'.$totwe=array_sum($towe).'</b></td></tr>';
   
   $taxes=$totwe*14/100;
    $swach=$totwe*0.50/100;
	
	$gtot=$totwe-$taxes-$swach;
	$word=convert_number_to_words($gtot);
	
 echo $tax=$tax.'<tr><td colspan="6"><b><h4>Service Tax @ 14%</h4></b></td><td><b>'.$taxes.'</b></td></tr>';
  echo $tax1=$tax1.'<tr><td colspan="6" rowspan="2" ><b><h4> Amount In Words :</h4></b><h5>'.$word.'</h5> </td><td colspan="6"><b><h4>Swachh Bharat Cess@0.50% </h4></b></td><td><b>'.$swach.'</b></td></tr>';
  echo $gtots=$gtots.'<tr><td colspan="6"><b><h4>Grand Total</h4></b></td><td><b>'.$gtot.'</b></td></tr>';


	echo '</tbody> </table></td>	</tr>';
  echo '</table>'; 
   
}
mysqli_close($con);

function convert_number_to_words($number) {

    $hyphen      = '-';
    $conjunction = ' and ';
    $separator   = ', ';
    $negative    = 'negative ';
    $decimal     = ' point ';
    $dictionary  = array(
        0                   => 'Zero',
        1                   => 'One',
        2                   => 'Two',
        3                   => 'Three',
        4                   => 'Four',
        5                   => 'Five',
        6                   => 'Six',
        7                   => 'Seven',
        8                   => 'Eight',
        9                   => 'Nine',
        10                  => 'Ten',
        11                  => 'Eleven',
        12                  => 'Twelve',
        13                  => 'Thirteen',
        14                  => 'Fourteen',
        15                  => 'Fifteen',
        16                  => 'Sixteen',
        17                  => 'Seventeen',
        18                  => 'Eighteen',
        19                  => 'Nineteen',
        20                  => 'Twenty',
        30                  => 'Thirty',
        40                  => 'Fourth',
        50                  => 'Fifty',
        60                  => 'Sixty',
        70                  => 'Seventy',
        80                  => 'Eighty',
        90                  => 'Ninety',
        100                 => 'Hundred',
        1000                => 'Thousand',
        1000000             => 'Million',
        1000000000          => 'Billion',
        1000000000000       => 'Trillion',
        1000000000000000    => 'Quadrillion',
        1000000000000000000 => 'Quintillion'
    );

    if (!is_numeric($number)) {
        return false;
    }

    if (($number >= 0 && (int) $number < 0) || (int) $number < 0 - PHP_INT_MAX) {
        // overflow
        trigger_error(
            'convert_number_to_words only accepts numbers between -' . PHP_INT_MAX . ' and ' . PHP_INT_MAX,
            E_USER_WARNING
        );
        return false;
    }

    if ($number < 0) {
        return $negative . convert_number_to_words(abs($number));
    }

    $string = $fraction = null;

    if (strpos($number, '.') !== false) {
        list($number, $fraction) = explode('.', $number);
    }

    switch (true) {
        case $number < 21:
            $string = $dictionary[$number];
            break;
        case $number < 100:
            $tens   = ((int) ($number / 10)) * 10;
            $units  = $number % 10;
            $string = $dictionary[$tens];
            if ($units) {
                $string .= $hyphen . $dictionary[$units];
            }
            break;
        case $number < 1000:
            $hundreds  = $number / 100;
            $remainder = $number % 100;
            $string = $dictionary[$hundreds] . ' ' . $dictionary[100];
            if ($remainder) {
                $string .= $conjunction . convert_number_to_words($remainder);
            }
            break;
        default:
            $baseUnit = pow(1000, floor(log($number, 1000)));
            $numBaseUnits = (int) ($number / $baseUnit);
            $remainder = $number % $baseUnit;
            $string = convert_number_to_words($numBaseUnits) . ' ' . $dictionary[$baseUnit];
            if ($remainder) {
                $string .= $remainder < 100 ? $conjunction : $separator;
                $string .= convert_number_to_words($remainder);
            }
            break;
    }

    if (null !== $fraction && is_numeric($fraction)) {
        $string .= $decimal;
        $words = array();
        foreach (str_split((string) $fraction) as $number) {
            $words[] = $dictionary[$number];
        }
        $string .= implode(' ', $words);
    }

    return $string;
}

?>
</html>