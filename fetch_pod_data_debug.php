<?php
// Clean output buffer
if (ob_get_level()) {
    ob_end_clean();
}
ob_start();

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

ini_set('display_errors', 0);
error_reporting(0);

// Clean any previous output
ob_clean();

// Function to send JSON response
function sendResponse($status, $message, $data = null, $stats = null) {
    if (ob_get_level()) {
        ob_clean();
    }
    
    $response = array(
        'status' => $status,
        'message' => $message
    );
    if ($data !== null) {
        $response['data'] = $data;
    }
    if ($stats !== null) {
        $response['stats'] = $stats;
    }
    
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse('error', 'Only POST method allowed');
}

// Include database connection
include("connection.php");

if (!$con) {
    sendResponse('error', 'Database connection failed: ' . mysqli_connect_error());
}

// Get parameters (skip authentication for debugging)
$month = isset($_POST['month']) ? trim($_POST['month']) : '2025-07';
$dataFilter = isset($_POST['data_filter']) ? trim($_POST['data_filter']) : 'all';

// Parse month
$monthParts = explode('-', $month);
if (count($monthParts) !== 2) {
    sendResponse('error', 'Invalid month format. Expected: YYYY-MM');
}

$year = intval($monthParts[0]);
$monthNum = intval($monthParts[1]);

try {
    // Simple test query first
    $test_sql = "SELECT COUNT(*) as total FROM tbl_courier WHERE YEAR(book_date) = $year AND MONTH(book_date) = $monthNum";
    $test_result = mysqli_query($con, $test_sql);
    
    if (!$test_result) {
        sendResponse('error', 'Test query failed: ' . mysqli_error($con));
    }
    
    $test_data = mysqli_fetch_assoc($test_result);
    
    if ($test_data['total'] == 0) {
        sendResponse('error', 'No records found for the selected month');
    }
    
    // Get sample data
    $sample_sql = "SELECT 
                    cons_no as ConsignmentNo,
                    DATE_FORMAT(book_date, '%d-%m-%Y') as book_date,
                    partno as PartNo,
                    ship_name as sender_name,
                    rev_name as receiver_name,
                    weight as Weight,
                    status
                   FROM tbl_courier 
                   WHERE YEAR(book_date) = $year AND MONTH(book_date) = $monthNum
                   ORDER BY book_date DESC 
                   LIMIT 5";
    
    $sample_result = mysqli_query($con, $sample_sql);
    
    if (!$sample_result) {
        sendResponse('error', 'Sample query failed: ' . mysqli_error($con));
    }
    
    $data = array();
    while ($row = mysqli_fetch_assoc($sample_result)) {
        $data[] = $row;
    }
    
    $stats = array(
        'total_records' => $test_data['total'],
        'delivered_records' => 0,
        'total_weight' => '0.00',
        'date_range' => 'Debug mode'
    );
    
    sendResponse('success', 'Debug data fetched successfully', $data, $stats);
    
} catch (Exception $e) {
    sendResponse('error', 'Exception: ' . $e->getMessage());
}
?>
