/**
 * German translation for bootstrap-datetimepicker
 * <PERSON> <<EMAIL>>
 */
;(function($){
	$.fn.datetimepicker.dates['de'] = {
		days: ["Sonntag", "Mont<PERSON>", "Die<PERSON><PERSON>", "<PERSON>tt<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Freitag", "Samstag", "Sonntag"],
		daysShort: ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"],
		daysMin: ["So", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Fr", "Sa", "So"],
		months: ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "April", "<PERSON>", "<PERSON><PERSON>", "Juli", "August", "September", "Ok<PERSON><PERSON>", "November", "Dezember"],
		monthsShort: ["Jan", "Feb", "<PERSON>ä<PERSON>", "Apr", "<PERSON>", "Jun", "Jul", "Aug", "Sep", "Okt", "Nov", "Dez"],
		today: "Heute",
		suffix: [],
		meridiem: [],
		weekStart: 1,
		format: "dd.mm.yyyy"
	};
}(jQuery));
