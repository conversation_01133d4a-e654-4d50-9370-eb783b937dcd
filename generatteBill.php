<?php
session_start();
include("connection.php");
require_once('database.php');
require_once('library.php');
isUser();
$date=$_POST['sdate'];
$date2=$_POST['edate3'];
$custid=$_POST['custname'];
$Cdate=date('Y-m-d',strtotime($date));
$Cdate2=date('Y-m-d',strtotime($date2));

$sql = "SELECT * FROM custreg  WHERE custreg.custid ='$custid'";
$result = mysqli_query($con,$sql);
while($row = mysqli_fetch_array($result)) 
 {
     $custname=$row['custname'];
     $custadd=$row['custadd'];
     $custgst=$row['custgst'];
     //$custname=$row['custname'];
 }
   $query = "SELECT MAX(inv_no) AS invoiceno FROM invoicebill";  
if($result = mysql_query($query))
{
while ($row = mysql_fetch_assoc($result))
{
 $invoiceno = $row['invoiceno'];
 $invoiceno++;
}
}
$bookmode=$_SESSION["booking_mode"];
$_SESSION["cgst"]=$custgst;

?>
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>
	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	
	
<style type = "text/css">
.row{margin-left:0px!important;}
table{border:none!important;}
table tr, table tr td, table tr td table tr, table tr td table tr td{border-collapse: collapse;}
@media print {
.row{margin-left:0px!important;}
.remove {margin-left: 0px;}}

.remove {margin-left: 10px; color: red;}
.remove:hover{cursor: pointer;}
body{margin:0 auto; 
    width:980px;
}
</style>


</head>
    
<body>
 <div style="margin-top:20px;">
   <div>
       <center>
          <form action="print_invoice.php" method="post" name="myform">     
<table style="margin:0 auto; width:100%; border:1px solid #000; border-collapse:collapse;">

        
<!--header--->
<tr><td colspan="3"> <center>Tax Invoice</center></td></tr>    

<tr>
<td width="20%"><center><img src="img/logo.png" width="60%" /></center></td>   
<td width="60%" style="text-align:center;">
     <h5><b><font size="4">Vivanta Logistics Private. Limited</font></b> </h5>
  <b>CIN: U74999PN2017PTC172759</b></br>
  <b>Registered Address</b>: Bungalow No.-7,samata Hsg.Soc,behind MSEB Colony,Bhosale Nagar,</br> Pune-411007
   Customer Care No.-18003131944
</td>
<td width="20%">Invoice <input type='text' name='invoce' id='invoce' value='<?php echo $invoiceno;?>' ></td>
</tr>

<tr><td colspan="3" width="100%"> <center>Billing Details</center></br>
<p>Name:-<?php echo $custname; ?></p>
<p>Address:-<?php echo $custadd; ?></p>
<p>GSTIN:-<?php echo $custgst; ?></p>
<p>Boking Mode:-<?php echo $bookmode;?></p>
</td></tr>



<tr><td colspan="3"> 
<table style="margin:0 auto; width:100%; border-collapse:collapse;"  id="Table1" border="1">
<thead>
   
    <tr>
           <th>Sr. No.</th>
           
           <th>Docket No.</th>
           <th>Date</th>
           <th>Destin.</th>
           <th><span id="Weight" class="remove remove-col">Weight</span></th>
           <th><span id="Rate" class="remove remove-col">Rate</span></th>
           <th><span id="Freight" class="remove remove-col">Freight</span> </th>
           <th><span id="invalue" class=" remove remove-col">Specail Veh.Charges</span></th>
           <th><span id="oda" class="remove remove-col">ODA/Other Charges</span></th>
           <th><span id="othercharges" class=" remove remove-col">Total Frieght</span> </th>
          
          
          
           <th><span id="cgst" class="remove remove-col">SGST</span></th>
                      <th><span id="cgst" class="remove remove-col">CGST</span></th>

           <th><span id="total" class="remove remove-col">Total</span></th>
           
</tr>   
</thead>     
<tbody>
   <?php
  
  $sname=$_POST['custname']; 
  $mode=$_POST['mode'];
   $count=0;    
   if (is_array($_POST['bh']))
{
foreach($_POST['bh'] as $area) {
  $sql = "SELECT * FROM tbl_courier  WHERE tbl_courier.cons_no ='$area'   ORDER BY cons_no DESC ";
$result = mysqli_query($con,$sql);
$row=mysqli_fetch_array($result);
  if($row['r_add']==""){
         $destination="";
     }else{
  $sql1="SELECT city_name,r_add from tbl_city_code inner join tbl_courier on tbl_city_code.Id= tbl_courier.r_add 
  WHERE tbl_courier.r_add = '".$row['r_add']."' " ;

$result1 = mysqli_query($con,$sql1);
$row1 = mysqli_fetch_array($result1);
$destination=$row1['city_name'];
     }
 $sqlrate="select crrate,miniweight from custrate inner join tbl_courier on custrate.crid=tbl_courier.shipper_code  where crsource='".$row['s_add']."' 
and crdesti='".$row['r_add']."' and crid='$row[shipper_code]' and cons_no='$area' ";
$resultrate = mysqli_query($con,$sqlrate);
$rowrate = mysqli_fetch_array($resultrate);
$rate=$rowrate['crrate'];
$weight=$rowrate['miniweight'];






if($row['chweight']<$weight){
    $miniweight=$rowrate['miniweight'];
   }else{
        $miniweight=$row['chweight'];
   }
   
   $freight=$rate*$miniweight;
$sgst=$freight*(9/100);
$cgst=$freight*(9/100);
$cgstround=round($cgst);

$totfreight=$totfreight+$freight;
$total=$freight+$sgst+$cgst;
    $count=$count+1;   
    echo "<tr>
<td>".$count."<input type='hidden' onkeypress='validate(event)' size='20px;' id='cnt".$count."' name='cnt".$count."' value=".$count." ></td>
<td>".$area."<input type='hidden' onkeypress='validate(event)' size='20px;' id='docketno".$count."' name='docketno".$count."' value='".$area."' ></td>
<td>".$row['book1_date']."<input type='hidden' onkeypress='validate(event)' size='20px;' id='bookdate".$count."' name='bookdate".$count."' value='".$row['book1_date']."' ></td>
<td>".$destination."<input type='hidden' onkeypress='validate(event)' size='20px;' id='destination".$count."' name='destination".$count."' value=".$destination." ></td>
<td> ".$miniweight."<input type='hidden' onkeypress='validate(event)' size='20px;' id='chweight".$count."' name='chweight".$count."' value='".$miniweight."' onchange='myfunction()'></td>
<td>".$rate."<input type='hidden' onkeypress='validate(event)' size='20px;' id='rate".$count."' name='rate".$count."' value='".$rate."' onchange='myfunction()'></td>
<td>".$freight."<input type='hidden' onkeypress='validate(event)' size='20px;' id='freight".$count."' name='freight".$count."' value='".round($freight)."' onchange='myfunction()'></td>
<td><input type='text' onkeypress='validate(event)' size='15px;' id='vehiclecharge".$count."' name='vehiclecharge".$count."' value='0' onchange='myfunction()'></td>
<td><input type='text' onkeypress='validate(event)' size='15px;' id='oda".$count."' name='oda".$count."' value='0' onchange='myfunction()'></td>
<td><input type='text' onkeypress='validate(event)' size='15px;' id='totfreght".$count."' name='totfreght".$count."' value='".round($freight)."' onchange='myfunction()'></td>
<td><input type='text' onkeypress='validate(event)' size='15px;' id='sgst".$count."' name='sgst".$count."' value='".round($sgst)."' onchange='myfunction()'></td>
<td><input type='text' onkeypress='validate(event)' size='15px;' id='cgst".$count."' name='cgst".$count."' value='".round($cgstround)."' onchange='myfunction()'></td>

<td><input type='text' onkeypress='validate(event)' size='15px;' id='total".$count."' name='total".$count."' value='".round($total)."' onchange='myfunction()'></td>
</tr>";	

} 
}$_SESSION["cnt"]=$count;
$_SESSION["sname"]=$sname;
$_SESSION["sdate"]=$date2;
$_SESSION["edate"]=$count;
?>	  				

		
<input type='hidden' name='cnt' id='cnt' value='<?php echo $count; ?>' onchange='myfunction()'>		  
<input type='hidden' name='sname' id='sname' value='<?php echo $sname; ?>'>		  		    
 <input type="hidden" name="sdate" id="sdate" value="<?php echo $date;?>">
<input type="hidden" name="edate" id="edate" value="<?php echo $date2;?>">
</tbody>

</table>
</td></tr> 


<tr><td colspan="3"> 
Notes:-</br>
  1.   Please Pay as per due date given in this Logistics Services Invoice.:-</br>
  2.  Please pay by cheque only in favour of "Vivanta Logistics Private Limited"</br>
  3.  Permanent Account Number(PAN):-**********</br>
  4.  GSTN:-27**********1Z5</br>
  5.  Corporate Identity Number:-U74999PN2017PTC172759</br>
  6.  Invoice Queries,please mail <NAME_EMAIL></br>
  7.  Request you to please pay on time to avoid disruption in service/late payment fees.</br>
  8.  All disputes are subject to pune jirisdiction.</br>
  9.  TDS to be deducted as per provision of section 194C</br>
  10.  Please email TDS <NAME_EMAIL></br>
</td></tr>

<tr><td colspan="3"> 
<b>Bank Details:-</b></br>
 <b>Company Name:</b>Vivanta Logistics Private Limited</br>
 <b>Bank Name:</b>AU Small Finance Bank</br>
 <b>Branch:</b>FC Road, Pune -411016</br>
 <b>A/C No:</b>****************</br>
 <b>IFSC Code:</b>AUBL0002354</br>
 <b>A/C Type:</b>OD Account
 
</td></tr>
  <!--end header---> 



</table>


<div class="control-group">
							    <div class="controls">
								
									<input type="submit" class="btn btn-primary" id="submit" name="submit" value="Save">
									<a id="backbutton" href="invoice1.php">&nbsp;
									<input type="button" class="btn btn-primary" id="backbutton" onClick="closeWin();" value="Close"> </a>
							    </div>
							</div><!--end control-group-->

</form>
</center>
</div>
</div>
<link href="./bootstrap/css/bootstrap-datetimepicker.min.css" rel="stylesheet" media="screen">		
<script type="text/javascript" src="./jquery/jquery-1.8.3.min.js" charset="UTF-8"></script>
<script type="text/javascript" src="./bootstrap/js/bootstrap.min.js"></script>
<script type="text/javascript" src="./jquery/bootstrap-datetimepicker.js" charset="UTF-8"></script>
<script type="text/javascript" src="./jquery/locales/bootstrap-datetimepicker.pl.js" charset="UTF-8"></script>
<script type="text/javascript">
   	$('.form_date').datetimepicker({
        language:  'pl',
        weekStart: 1,
        todayBtn:  1,
		autoclose: 1,
		todayHighlight: 1,
		startView: 2,
		minView: 2,
		forceParse: 0
    });
    
  
function checkAll(bx) {
var cbs = document.getElementsByTagName('input');
for(var i=0; i < cbs.length; i++) {
if(cbs[i].type == 'checkbox') {
cbs[i].checked = bx.checked;
}
}
}
	
</script>





<script>




 






// we're binding a lot of different click event-handlers to this element
// there's no point looking it up every time we do so:
var body = $('body');

// binding the click event for the add-row button:
body.on('click', 'button.add-row', function() {
  // getting the relevant <table>:
  var table = $(this).closest('div.table-content'),
    // and the <tbody> and <thead> elements:
    tbody = table.find('tbody'),
    thead = table.find('thead');

  // if the <tbody> has children:
  if (tbody.children().length > 0) {
    // we find the last <tr> child element, clone it, and append
    // it to the <tbody>:
    tbody.find('tr:last-child').clone().appendTo(tbody);
  } else {
    // otherwise, we create the basic/minimum <tr> element:
    var trBasic = $('<tr />', {
        'html': '<td><span class="remove remove-row">x</span></td><td><input type="text" class="form-control" /></td>'
      }),
      // we find the number of columns that should exist, by
      // looking at the last <tr> element of the <thead>,
      // and finding out how many children (<th>) elements it has:
      columns = thead.find('tr:last-child').children().length;

    // a for loop to iterate over the difference between the number
    // of children in the created trBasic element and the current
    // number of child elements of the last <tr> of the <thead>:
    for (var i = 0, stopWhen = columns - trBasic.children.length; i < stopWhen; i++) {
      // creating a <td> element:
      $('<td />', {
        // setting its text:
        'text': 'static element'
          // appending that created <td> to the trBasic:
      }).appendTo(trBasic);
    }
    // appending the trBasic to the <tbody>:
    tbody.append(trBasic);
  }
});

body.on('click', 'span.remove-row', function() {
  $(this).closest('tr').remove();
});

body.on('click', 'span.remove-col', function() {
  // getting the closest <th> ancestor:
  var cell = $(this).closest('th'),
    // getting its index with jQuery's index(), though
    // cell.prop('cellIndex') would also work just as well,
    // and adding 1 (JavaScript is zero-based, CSS is one-based):
    index = cell.index() + 1;
  // finding the closest <table> ancester of the <th> containing the
  // clicked <span>:
  cell.closest('table')
    // finding all <td> and <th> elements:
    .find('th, td')
    // filtering that collection, keeping only those that match
    // the same CSS-based, using :nth-child(), index as the <th>
    // containing the clicked <span>:
    .filter(':nth-child(' + index + ')')
    // removing those cells:
    .remove();
});

body.on('click', 'button.add-col', function() {
  // finding the table (because we're using it to find both
  // the <thead> and <tbody>:
  var table = $(this).closest('div.table-content').find('table'),
    thead = table.find('thead'),
    // finding the last <tr> of the <thead>:
    lastTheadRow = thead.find('tr:last-child'),
    tbody = table.find('tbody');

  // creating a new <th>, setting its innerHTML to the string:
  $('<th>', {
    'html': '<input type="text" class="form-control pull-left" value="Property" /> <span class="pull-left remove remove-col">x</span>'
      // appending that created <th> to the last <tr> of the <thead>:
  }).appendTo(lastTheadRow);
  // creating a <td>:
  $('<td>', {
    // setting its text:
    'text': 'static element'
      // inserting the created <td> after every <td> element
      // that is a :last-child of its parent:
  }).insertAfter('td:last-child');
});
</script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.0/jquery.min.js"></script>
<script type="text/javascript">
    function printpage() {
        //Get the print button and put it into a variable
        var printButton = document.getElementById("printpagebutton");
        var back=document.getElementById("backbutton");
        //var rate2=document.getElementById("rate1").value);
       // rate2.style.visibility = 'hidden';
        //   $("#rate").text(rate2);  
          window.print();
        //Set the print button visibility to 'hidden' 
        printButton.style.visibility = 'hidden';
        back.style.visibility = 'hidden';
     
         printButton.style.visibility = 'visible';
         back.style.visibility = 'visible';
         //rate.style.visibility = 'visible';
    }
</script>
<script type="text/javascript">

function closeWin() {
    back.close();
}
    window.onbeforeunload = function() {
        
    }
</script>
<?php

 
function convert_number_to_words($number) {

    $hyphen      = '-';
    $conjunction = ' and ';
    $separator   = ', ';
    $negative    = 'negative ';
    $decimal     = ' point ';
    $dictionary  = array(
        0                   => 'Zero',
        1                   => 'One',
        2                   => 'Two',
        3                   => 'Three',
        4                   => 'Four',
        5                   => 'Five',
        6                   => 'Six',
        7                   => 'Seven',
        8                   => 'Eight',
        9                   => 'Nine',
        10                  => 'Ten',
        11                  => 'Eleven',
        12                  => 'Twelve',
        13                  => 'Thirteen',
        14                  => 'Fourteen',
        15                  => 'Fifteen',
        16                  => 'Sixteen',
        17                  => 'Seventeen',
        18                  => 'Eighteen',
        19                  => 'Nineteen',
        20                  => 'Twenty',
        30                  => 'Thirty',
        40                  => 'Fourth',
        50                  => 'Fifty',
        60                  => 'Sixty',
        70                  => 'Seventy',
        80                  => 'Eighty',
        90                  => 'Ninety',
        100                 => 'Hundred',
        1000                => 'Thousand',
        1000000             => 'Million',
        1000000000          => 'Billion',
        1000000000000       => 'Trillion',
        1000000000000000    => 'Quadrillion',
        1000000000000000000 => 'Quintillion'
    );

    if (!is_numeric($number)) {
        return false;
    }

    if (($number >= 0 && (int) $number < 0) || (int) $number < 0 - PHP_INT_MAX) {
        // overflow
        trigger_error(
            'convert_number_to_words only accepts numbers between -' . PHP_INT_MAX . ' and ' . PHP_INT_MAX,
            E_USER_WARNING
        );
        return false;
    }

    if ($number < 0) {
        return $negative . convert_number_to_words(abs($number));
    }

    $string = $fraction = null;

    if (strpos($number, '.') !== false) {
        list($number, $fraction) = explode('.', $number);
    }

    switch (true) {
        case $number < 21:
            $string = $dictionary[$number];
            break;
        case $number < 100:
            $tens   = ((int) ($number / 10)) * 10;
            $units  = $number % 10;
            $string = $dictionary[$tens];
            if ($units) {
                $string .= $hyphen . $dictionary[$units];
            }
            break;
        case $number < 1000:
            $hundreds  = $number / 100;
            $remainder = $number % 100;
            $string = $dictionary[$hundreds] . ' ' . $dictionary[100];
            if ($remainder) {
                $string .= $conjunction . convert_number_to_words($remainder);
            }
            break;
        default:
            $baseUnit = pow(1000, floor(log($number, 1000)));
            $numBaseUnits = (int) ($number / $baseUnit);
            $remainder = $number % $baseUnit;
            $string = convert_number_to_words($numBaseUnits) . ' ' . $dictionary[$baseUnit];
            if ($remainder) {
                $string .= $remainder < 100 ? $conjunction : $separator;
                $string .= convert_number_to_words($remainder);
            }
            break;
    }

    if (null !== $fraction && is_numeric($fraction)) {
        $string .= $decimal;
        $words = array();
        foreach (str_split((string) $fraction) as $number) {
            $words[] = $dictionary[$number];
        }
        $string .= implode(' ', $words);
    }

    return $string;
}

?>



<script type="text/javascript">

$(document).ready(function() {
//alert();
$("#remove_column").click( function() {

//Remove all columns except first column

$('#my_table td:not(:nth-child(1))').remove();

//Remove all columns except first column along with Header

$('#my_table th:not(:nth-child(1)), #my_table td:not(:nth-child(1))').remove();

});

});

</script>


<script type="text/javascript" src="http://ajax.googleapis.com/ajax/libs/jquery/1.8.3/jquery.min.js"></script>
<script type="text/javascript">
$(function () {
    //Loop through all Labels with class 'editable'.
    $(".editable").each(function () {
        //Reference the Label.
        
        var label = $(this);
     
       
        //Add a TextBox next to the Label.
       // label.after("<input type = 'text' style = 'display:none' value='' />");
 
        //Reference the TextBox.
        var textbox = $(this).next();
        
        //Set the name attribute of the TextBox.
      //  textbox[0].name = this.id.replace("lbl", "txt");
      // textbox[0].id = this.id.replace("lbl", "txt");
          //textbox[0].value = this.value.replace("lbl", "txt");
        //Assign the value of Label to TextBox.
        textbox.val(label.html());
      
        //When Label is clicked, hide Label and show TextBox.
        label.click(function () {
            $(this).hide();
             //$("rate1").show();
           // $(this).next().show();
            $('.update').show();
          
        });
 
        //When focus is lost from TextBox, hide TextBox and show Label.
        textbox.focusout(function () {
            $(this).hide();
            $(this).prev().html($(this).val());
        
            $(this).prev().show();
        });
    });
});

function myfunction(){
var srn=parseInt(document.getElementById('cnt').value);
//alert(srn);
 for(var i=1;i<=srn;i++)
{ 

var sgst=$('#sgst'+i).val();
//alert(rate);
var cgst=$('#cgst'+i).val();
var oda=$('#oda'+i).val();
//var othercharges=$('#othercharge'+i).val();
var spevehcharge=$('#vehiclecharge'+i).val();
var freght=$('#freight'+i).val();

var totfreight=parseFloat(oda)+parseFloat(spevehcharge)+parseFloat(freght);
var totalfrieght=Math.round(totfreight)
$('#totfreght'+i).val(totalfrieght);
var sgstround=totalfrieght*9/100;
var cgstround=totalfrieght*9/100;

$('#sgst'+i).val(Math.round(sgstround));
$('#cgst'+i).val(Math.round(cgstround));

$('#total'+i).val(totalfrieght+parseFloat(sgst)+parseFloat(cgst));
}
 
//document.getElementById("total").value=parseInt(dispencertot);
///document.getElementById("totaljar").value=parseInt(totjar);
//var total1=parseInt(document.getElementById("totaljar").value);

}

</script>

</body>    
</html>