<?php
// Database connection config for Inventory DB
$dbHost = 'localhost';
$dbUser = 'root';
$dbPass = '';
$dbName = 'rqhmqxnf_Inventory';

$dbConnInventory = mysqli_connect($dbHost, $dbUser, $dbPass, $dbName);

if (!$dbConnInventory) {
    die('MySQL connect failed: ' . mysqli_connect_error());
}

// Utility functions for Inventory DB
function dbInventoryAffectedRows()
{
    global $dbConnInventory;
    return mysqli_affected_rows($dbConnInventory);
}

function dbInventoryFetchArray($result, $resultType = MYSQLI_NUM)
{
    return mysqli_fetch_array($result, $resultType);
}

function dbInventoryFetchRow($result) 
{
    return mysqli_fetch_row($result);
}

function dbInventoryFreeResult($result)
{
    return mysqli_free_result($result);
}

function dbInventoryNumRows($result)
{
    return mysqli_num_rows($result);
}

function dbInventorySelect($dbName)
{
    global $dbConnInventory;
    return mysqli_select_db($dbConnInventory, $dbName);
}

function dbInventoryInsertId()
{
    global $dbConnInventory;
    return mysqli_insert_id($dbConnInventory);
}
?>
