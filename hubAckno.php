<?php
session_start();
require_once('database.php');
require_once('library.php');
require 'connection.php';
isUser();

$a=$_SESSION['username'];
$sql="select * from login where username='$a'";
$result = mysqli_query($con, $sql);
$row = mysqli_fetch_array($result, MYSQLI_BOTH);
 $d=$row['rid'];


 $sql="select * from tbl_courier_officers where username='$d'";
$result = mysqli_query($con, $sql);
$row1 = mysqli_fetch_array($result, MYSQLI_BOTH);
 $uoffice=$row1['office'];  $uaddress=$row1['address'];
 $id=$row1['cid'];
 
$num_rec_per_page=20;
if (isset($_GET["page"])) { $page  = $_GET["page"]; } else { $page=1; }; 
$start_from = ($page-1) * $num_rec_per_page; 


//$sql = "SELECT * FROM currentloc WHERE current_loc = ".$_GET[id]." ORDER BY clid ASC ";
	

 $sql ="SELECT * FROM `currentloc` where userid='".$id."'";
//$result = dbQuery($sql);		
$cnt=0;
$result=mysqli_query($con,$sql);
while($row=mysqli_fetch_array($result))
{
$cnt=$cnt+1;
 $tr=$tr.'<tr><td>'.$cnt.'</td><td> '.$row['current_date'].'</td><td> '.$row['current_time'].'</td><td> '.$row['consig_no'].'</td><td><input type="checkbox" name="bh[]" value="'.$row['consig_no'].'"> </td><td><input type="test" name="remark[]" value=""> </td></tr>';

}

  
 /*
$first = true;
echo "You checked boxes:";
foreach($_POST['bh'] as $cb)
{
    if (!$first)
    echo ",";
 echo " $cb";
   $first = false;
}
exit();*/
 
 if(isset($_GET['id']) && $_GET['del']=='yes')

{ 
 $sql1="select * from `tbl_offices` WHERE `id`=".$_GET[id]." ORDER BY off_name ASC";
 
}
$sql1="select off_name as name , id from `tbl_offices` ORDER BY name ASC";
 $result2=mysqli_query($con,$sql1);
while($row2=mysqli_fetch_array($result2))
{
	if($company==$row2['id'])
	{
	$loc=$loc."<option value='".$row2['id']."' selected>".$row2['name']."</option>";
	}
	else{
	$loc=$loc."<option value='".$row2['id']."' >".$row2['name']."</option>";
	}
}

 
?>

<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>
	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
<script language="JavaScript">
var checkflag = "false";
 //---- Checks consignment is selected or not script start-----
function check(field) {
if (checkflag == "false")
 {
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll")
	{
	field[i].checked=true;	
	}
	}
	checkflag = "true";
}
else
{
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll")
	{
	field[i].checked=false;
	}
	}
	checkflag = "false";
}

} //---- Checks consignment is selected or not script end-----
//---- Delete the consignment script start-----
function confirmDel(field,msg)
{
	count=0;
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll" && field[i].checked==true)
	{
	count++;
	}
	}
	
	if(count == 0)
	{
		alert("Select any one record to delete!");
		return false;
	}
	else
	{
		return confirm(msg);
	}
}
//---- Delete the consignment script end-----
</script>
</head>
<?php include("header.php"); ?>
		<div class="container">
			<div class="row">
               <div class="span11">
					<div class="account-list-outer">

						<div class="titleHeader clearfix">
							<h3> Hub Acknowledge </h3> 
						</div>	
							<!--<div class="control-group">
                            <label>Branch Location :</label>
							<div class="controls">
	                            <select name="curntloca" id="curntloca">
												<option value="">-- Please select --</option>
												<?php echo $loc; ?>
								</select>
							</div>
					    </div>
						</div> end titleHeader-->
					 
                  <form  action="processH.php?action=hub-ack1" method="POST" name="hubtohub">
					<table class="table">
						<thead>
							<tr>
								<th><h5>Sr.No</h5></th>
								<th><h5>Date</h5></th>
								<th><h5>Time</h5></th>
								<th><h5>C/N No</h5></th>
								<th><h5><input type="checkbox" name="check_all" id="check_all" onClick="checkAll(this)">All / Select</h5>
								</th>
								<th><h5>Remark</h5></th>
							</tr>
						</thead>
						
						<tbody><?php echo $tr; ?> 
						   
						</tbody>
						
					</table>
					<table class="table">
						 				
						<tbody>
					<tr> 
					   <td>	
					     <div class="control-group">
                            <div class="controls">
							 <label class="control-label"> <input type="radio" name="huback" id="huback" value="HUB" onChange="filterbytype('HUB_Office');"> HUB Office</label>
<input type="hidden" name="uoffice" value="<?php echo $uoffice;?>">
<input type="hidden" name="uaddress" value="<?php echo $uaddress;?>">
<input type="hidden" name="cid" value="<?php echo $id;?>">
							 </div>
					    </div>
						</td>
					    <td>
					        <label class="control-label"> <input type="radio" name="huback" id="huback" value="Branch" onChange="filterbytype('Branch_Office');"> Branch Office 
								</label>
						</td>
					 <td>
					    <label class="control-label">  <input type="radio" name="huback" id="huback" value="De" onChange="filterbytype('Deli_Vehicle');"> Delivery Vehicle &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
						<input type="radio" name="huback" id="huback" value="#" onChange="filterbytype('Deli_Boy');"> Delivery Boy &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
								</label>
							
					  </td>
					</tr>
					<tr> 
					  <td>
						<div class="control-group">
                            <label>Move to Location :</label>
							<div class="controls">
	                            <select name="mloca" id="mloca" value="">
												<option value="">-- Please select --</option>
												<!--<?php echo $loc; ?>-->
								</select>
							</div>
					    </div>
					  </td>	
			    	 <td> 
					    <div class="control-group">
                            <label>Date :</label>
							    <div class="controls">
									 <div class="controls input-append date form_date" data-date="" data-date-format="dd MM yyyy" data-link-field="dtp_input2" data-link-format="yyyy-mm-dd">
										<input name="curdate" type="text" value="" readonly>
										<span class="add-on"><i class="icon-remove"></i></span>
										<span class="add-on"><i class="icon-th"></i></span>
									 </div>
										<input type="hidden" id="dtp_input2" value="" />
							    </div>
					    </div>
					 </td>
					 <td>
						<div class="control-group">
                            <label>Time :</label>
							    <div class="controls">
									 <div class="controls input-append date form_time" data-date="" data-date-format="hh:ii" data-link-field="dtp_input3" data-link-format="hh:ii">
										<input name="curtime" type="text" value="" readonly>
										<span class="add-on"><i class="icon-remove"></i></span>
										<span class="add-on"><i class="icon-th"></i></span>
									 </div>
										<input type="hidden" id="dtp_input3" value="" />
							    </div>
					    </div>	 
					 </td>
					</tr>
					<tr> 
					  <td>
					  </td>
					 <td>
					  
				     	<div class="control-group">
						    <div class="controls"> 
								<input name="Submit" class="btn btn-primary" type="submit" value="Save" onClick="check(this);" >
							&nbsp;&nbsp;&nbsp;<button type="reset" class="btn ">Clear</button>
					        </div>
					    </div>
						</td>
						
					 <td>
					 </td>
					</tr>
						</tbody>
					</table>
						
						
						</form>
					</div><!--end -->
				</div><!--end span-->
			</div><!--end row-->
		</div><!--end conatiner-->
	
<script>
function checkAll(bx) {

  var cbs = document.getElementsByTagName('input');
  for(var i=0; i < cbs.length; i++) {
    if(cbs[i].type == 'checkbox') {
      cbs[i].checked = bx.checked;
    }
  }
}
</script>	

<link href="./bootstrap/css/bootstrap-datetimepicker.min.css" rel="stylesheet" media="screen">
<script type="text/javascript" src="./jquery/jquery-1.8.3.min.js" charset="UTF-8"></script>
<script type="text/javascript" src="./bootstrap/js/bootstrap.min.js"></script>
<script type="text/javascript" src="./jquery/bootstrap-datetimepicker.js" charset="UTF-8"></script>
<script type="text/javascript" src="./jquery/locales/bootstrap-datetimepicker.pl.js" charset="UTF-8"></script>
<script type="text/javascript">
	$('.form_date').datetimepicker({
        language:  'pl',
        weekStart: 1,
        todayBtn:  1,
		autoclose: 1,
		todayHighlight: 1,
		startView: 2,
		minView: 2,
		forceParse: 0
    });
	$('.form_time').datetimepicker({
        language:  'pl',
        weekStart: 1,
        todayBtn:  1,
		autoclose: 1,
		todayHighlight: 1,
		startView: 1,
		minView: 0,
		maxView: 1,
		forceParse: 0
    });
</script>

<script src="jquery/lib/sweet-alert.min.js"></script>
<script src="jquery/jquery.min.js"></script>
<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
<script  type="text/javascript">
function filterbytype(type)
{
//alert("type="+type);
$('#huback').val(type);

$('#mloca').find('option').remove().end().append('<option value="">--- Select '+type+'---</option>').val('');
	// Ajax post
	jQuery.ajax({
	type: "POST",
	url: "ajaxfiltertype.php?type="+type,
	dataType: 'json',
	data: {
		
	},
	//cache: false,	
	success: function(data) { 
	
	   		if (data)
			{  
				$.each(data, function(index, data) 
				{
				$('#mloca').append( $('<option></option>').val(data.id).html(data.name) );	

			} );
			
			} 
		} 
		
	});


}

$(document).ready(function() {
    $('#selecctall').click(function(event) {  //on click 
        if(this.checked) { // check select status
            $('.checkbox1').each(function() { //loop through each checkbox
                this.checked = true;  //select all checkboxes with class "checkbox1"  
                $("#sel").html("Deselect All");             
            });
        }else{
            $('.checkbox1').each(function() { //loop through each checkbox
                this.checked = false; //deselect all checkboxes with class "checkbox1"  
                 $("#sel").html("Select All");                         
            });         
        }
    });
    
});

</script>		
<?php 
$sql = "SELECT * FROM tbl_courier WHERE status != 'Delivered' ORDER BY cid DESC "; 
$rs_result = mysql_query($sql); //run the query
$total_records = mysql_num_rows($rs_result);  //count number of records
$total_pages = ceil($total_records / $num_rec_per_page); 

echo "<a href='hubAckno.php?page=1'>".'|<'."</a> "; // Goto 1st page  

for ($i=1; $i<=$total_pages; $i++) { 
            echo "<a href='hubAckno.php?page=".$i."'>".$i."</a> "; 
}; 
include("footer.php"); ?>