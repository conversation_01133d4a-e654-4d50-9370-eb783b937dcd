<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<?php
error_reporting(~E_ALL);
session_start();
require_once('library.php');
require 'connection.php';

$a=$_SESSION['username'];
$sql="select * from login where username='$a'";
$result = mysqli_query($con, $sql);
$row1 = mysqli_fetch_array($result);
 $userid=$row1['rid']; 
 $count=$_POST['count'];  
 $sname=$_POST['sname'];
 $sql = "SELECT * FROM custreg  WHERE custreg.custid ='$sname'";
$result = mysqli_query($con,$sql);
while($row = mysqli_fetch_array($result)) 
 {
     $custname=$row['custname'];
     $custadd=$row['custadd'];
     $custgst=$row['custgst'];
     //$custname=$row['custname'];
 }
 
$msg="";

 $date=$_POST['date2'];  $date2=$_POST['date4']; // $bill=$_POST['bilno'];
	
       
if($date2=='')
{
 $Cdate2=date('Y-m-d',time());
}
else
{
 $Cdate2=date('Y-m-d',strtotime($date2));
}
 $Cdate=date('Y-m-d',strtotime($date));


/* echo $sql1="INSERT INTO `invoicebill` (`custid`, `sdate`, `tdate`, `inv_no`, `inv_date`, `inv_duedate`, `tot_consign`, `grandtot`, `userid`, `edate`,`bokkingmode`) 
 VALUES ('".$sname."', '".$Cdate."', '".$Cdate2."', '".$_POST['invoce']."', '".$Cdate."', '".$Cdate2."', '".$_POST['count']."', '".$_POST['grand']."', '".$userid."', now(),'$bookmode')";
if(!mysqli_query($con,$sql1)){
  echo $msg="no";
}else{ echo $msg="yes";}

header ("Location:invoice1.php?msg=".$msg);


 if(isset($_POST['update'])){
	 
	echo $updatesql="UPDATE `invoicebill` SET `inv_no`='".$_POST['invoiceno']."', `inv_date`='".$Cdate."', `inv_duedate`='".$Cdate2."',`userid`='".$userid."', `edate`=now() where id='".$_POST['iid']."'";
	 
	 if(!mysqli_query($con,$updatesql))
	 {
		echo $msg="no1"; 
	 }else{ echo $msg="yes1";}
 
 header ("Location:invoice1.php?msg=".$msg);
 
 }*/



header("Content-Type:   application/vnd.ms-excel; charset=utf-8");
header("Content-type:   application/x-msexcel; charset=utf-8");
header("Content-Disposition: attachment; filename=invoicebill.xls"); 
header("Expires: 0");
header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
header("Cache-Control: private",false);






echo '<tr><td colspan="3"> 
<table style="margin:0 auto; width:100%; border-collapse:collapse;"  id="Table1" border="1">
<thead>
   
    <tr>
           <th>Sr. No.</th>
           
           <th>Docket No.</th>
           <th>Date</th>
           <th>Destin.</th>
           <th><span id="Weight" class="remove remove-col">Weight</span></th>
           <th><span id="Rate" class="remove remove-col">Rate</span></th>
           <th><span id="Freight" class="remove remove-col">Freight</span> </th>
           <th><span id="invalue" class=" remove remove-col">Specail Veh.Charges</span></th>
           <th><span id="oda" class="remove remove-col">ODA/Other</span></th>
           <th><span id="othercharges" class=" remove remove-col">Total Frieght</span> </th>
          
          
          
          <th><span id="cgst" class="remove remove-col">SGST</span></th>
                      <th><span id="cgst" class="remove remove-col">CGST</span></th>
           <th><span id="total" class="remove remove-col">Total</span></th>
           
</tr>   
</thead>     
<tbody>';
 $cnt=0;
   
for($i=1;$i<=$count;$i++) {
$cnt=$cnt+1;
$docketno=$_POST['docketno'.$i];
$bookdate=$_POST['bookdate'.$i];
$destination=$_POST['destination'.$i];
$chweight=$_POST['chweight'.$i];
$rate=$_POST['rate'.$i];
$freight=$_POST['freight'.$i];
$vehiclecharge=$_POST['vehiclecharge'.$i];
$oda=$_POST['oda'.$i];
$totfreght=$_POST['totfreght'.$i];
$sgst=$_POST['sgst'.$i];
$cgst=$_POST['cgst'.$i];
$total=$_POST['total'.$i];

$totalvehiclecharges=$totalvehiclecharges+$vehiclecharge;
$totaloda=$totaloda+$oda;
$totalfrieght=$totalfrieght+$totfreght;
$totalcgst=$totalcgst+$cgst;
$totalsgst=$totalsgst+$sgst;
$sumtotal=$sumtotal+$total;

echo "<tr>
<td>".$i."</td>
<td>".$docketno."</td>
<td>".$bookdate."</td>
<td>".$destination."</td>
<td>".$chweight."</td>
<td>".$rate."</td>
<td>".$freight."</td>
<td>".$vehiclecharge."</td>
<td>".$oda."</td>
<td>".$totfreght."</td>
<td>".$sgst."</td>
<td>".$cgst."</td>

<td>".$total."</td>
</tr>";		

} 				
		

echo "</tbody>
<tr><td colspan='6'>Total Amount</td><td>$totfreight</td><td>$totalvehiclecharges</td><td>$totaloda</td><td> $totalfrieght</td>
<td>$totalsgst</td><td>$totalcgst</td><td>$sumtotal</td></tr>

</table>
</td></tr>"; 






  

mysqli_close($con);

function convert_number_to_words($number) {

    $hyphen      = '-';
    $conjunction = ' and ';
    $separator   = ', ';
    $negative    = 'negative ';
    $decimal     = ' point ';
    $dictionary  = array(
        0                   => 'Zero',
        1                   => 'One',
        2                   => 'Two',
        3                   => 'Three',
        4                   => 'Four',
        5                   => 'Five',
        6                   => 'Six',
        7                   => 'Seven',
        8                   => 'Eight',
        9                   => 'Nine',
        10                  => 'Ten',
        11                  => 'Eleven',
        12                  => 'Twelve',
        13                  => 'Thirteen',
        14                  => 'Fourteen',
        15                  => 'Fifteen',
        16                  => 'Sixteen',
        17                  => 'Seventeen',
        18                  => 'Eighteen',
        19                  => 'Nineteen',
        20                  => 'Twenty',
        30                  => 'Thirty',
        40                  => 'Fourth',
        50                  => 'Fifty',
        60                  => 'Sixty',
        70                  => 'Seventy',
        80                  => 'Eighty',
        90                  => 'Ninety',
        100                 => 'Hundred',
        1000                => 'Thousand',
        1000000             => 'Million',
        1000000000          => 'Billion',
        1000000000000       => 'Trillion',
        1000000000000000    => 'Quadrillion',
        1000000000000000000 => 'Quintillion'
    );

    if (!is_numeric($number)) {
        return false;
    }

    if (($number >= 0 && (int) $number < 0) || (int) $number < 0 - PHP_INT_MAX) {
        // overflow
        trigger_error(
            'convert_number_to_words only accepts numbers between -' . PHP_INT_MAX . ' and ' . PHP_INT_MAX,
            E_USER_WARNING
        );
        return false;
    }

    if ($number < 0) {
        return $negative . convert_number_to_words(abs($number));
    }

    $string = $fraction = null;

    if (strpos($number, '.') !== false) {
        list($number, $fraction) = explode('.', $number);
    }

    switch (true) {
        case $number < 21:
            $string = $dictionary[$number];
            break;
        case $number < 100:
            $tens   = ((int) ($number / 10)) * 10;
            $units  = $number % 10;
            $string = $dictionary[$tens];
            if ($units) {
                $string .= $hyphen . $dictionary[$units];
            }
            break;
        case $number < 1000:
            $hundreds  = $number / 100;
            $remainder = $number % 100;
            $string = $dictionary[$hundreds] . ' ' . $dictionary[100];
            if ($remainder) {
                $string .= $conjunction . convert_number_to_words($remainder);
            }
            break;
        default:
            $baseUnit = pow(1000, floor(log($number, 1000)));
            $numBaseUnits = (int) ($number / $baseUnit);
            $remainder = $number % $baseUnit;
            $string = convert_number_to_words($numBaseUnits) . ' ' . $dictionary[$baseUnit];
            if ($remainder) {
                $string .= $remainder < 100 ? $conjunction : $separator;
                $string .= convert_number_to_words($remainder);
            }
            break;
    }

    if (null !== $fraction && is_numeric($fraction)) {
        $string .= $decimal;
        $words = array();
        foreach (str_split((string) $fraction) as $number) {
            $words[] = $dictionary[$number];
        }
        $string .= implode(' ', $words);
    }

    return $string;
}

?>
</html>