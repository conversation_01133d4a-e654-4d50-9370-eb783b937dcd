<?php
session_start();
require_once('database.php');
require_once('library.php');
require 'connection.php';
isUser();

// Get the state parameter (currently not used since cities don't have state relationship)
$type = isset($_GET['type']) ? $_GET['type'] : '';

// For now, return all cities since there's no state relationship in the database
// In the future, you could add a state_id field to tbl_city_code table to filter by state
$sql = "SELECT city_name, Id FROM tbl_city_code ORDER BY city_name ASC";
$result = mysqli_query($con, $sql);

$data = array();
if($result && mysqli_num_rows($result) > 0) {
    while($row = mysqli_fetch_array($result)) {
        $row_data = array(
            'id' => $row['Id'],
            'name' => $row['city_name']
        );
        array_push($data, $row_data);
    }
}

// Set proper content type header
header('Content-Type: application/json');
echo json_encode($data);
?>