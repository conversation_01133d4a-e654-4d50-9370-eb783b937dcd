<?php 
session_start();
require 'connection.php';
require_once('database.php');
require_once('library.php');
ob_start();
$date = date('Y-m-d');
$date1=$_GET['date1'];
$date2=$_GET['date2'];
$custid=$_GET['id'];
$Cdate=date('Y-m-d',strtotime($date1));
$Cdate1=date('Y-m-d',strtotime($date2));

/*$sql="SELECT cons_no,weight,gtotamt,rev_name,ship_name,noofpackage,gst,partno,freight,book_mode,invi_value,qty,assured_dly_date,book1_date,a.city_name as city 
  ,type,invice_no,chweight,mode,statusname FROM (tbl_courier inner join status on tbl_courier.status=status.statusid)   join  tbl_city_code a on tbl_courier.s_add=a.Id 
  WHERE tbl_courier.shipper_code ='$custid' and tbl_courier.book_date between '$Cdate' and '$Cdate1' ORDER BY cons_no DESC  ";*/
  
  $sql = "SELECT cons_no,weight,gtotamt,rev_name,r_add,ship_name,book_mode,userid,status_date,noofpackage,status,e_waybill,eway_expdate,gst,partno,remark,freight,book_mode,invi_value,qty,
assured_dly_date,book1_date,a.city_name as city ,type,invice_no,chweight,mode,rate,oda_mis,statusname FROM (tbl_courier inner join status on tbl_courier.status=status.statusid)
join  tbl_city_code a on tbl_courier.s_add=a.Id 
where status_date='$date' ORDER BY status_update_datetime DESC ";
$result = mysqli_query($con,$sql); 

$count=0;
while($row=mysqli_fetch_array($result))
{
    
      if($row['status']=='6' || $row['status']=='50' || $row['status']=='57'){
      $statusdate=$row['status_date'];
 }else{
     $statusdate="";
 }
 
  if($row['status']=='5'){
     $delivereddate=$row['status_date'];
 }
 
 if($row['r_add']==""){
         $destination="";
     }else{
  $sql1="SELECT city_name,r_add from tbl_city_code inner join tbl_courier on tbl_city_code.Id= tbl_courier.r_add 
  WHERE tbl_courier.r_add = '".$row['r_add']."' " ;

$result1 = mysqli_query($con,$sql1);
$row1 = mysqli_fetch_array($result1);
$destination=$row1['city_name'];
     }
 
$sql2="SELECT Manager_name,empcode from tbl_courier_officers inner join tbl_courier on tbl_courier_officers.cid=tbl_courier.userid
  WHERE tbl_courier.userid = '".$row['userid']."' " ;

$result2 = mysqli_query($con,$sql2);
$row2 = mysqli_fetch_array($result2);
$Manager_name=$row2['Manager_name'];  
$empcode=$row2['empcode'];  
    
    
	$count++;
	
$tr=$tr."<tr><td>".$count."</td><td>".$Manager_name."</td><td>".$empcode."</td><td>".$row['status_date']."</td><td>".$row['cons_no']."</td><td>".$row['book1_date']."</td><td>".$row['invice_no']."</td>
<td>".$row['rev_name']."</td><td>".$destination."</td><td>".$row['noofpackage']."</td><td>".$row['qty']."</td><td>".$row['partno']."</td><td>".$withoutgstvalue."</td><td>".$row['invi_value']."</td><td>".$statusdate."</td>
<td>".$row['book_mode']."</td><td>".$row['statusname']."</td><td>".$row['remark']."</td><td>".$delivereddate."</td><td>".$row['mode']."</td><td>".$row['weight']."</td><td>".$row['chweight']."</td><td>".$row['e_waybill']."</td><td>".$row['rate']."</td>
<td>".$row['oda_mis']."</td><td>".$row['freight']."</td><td></td><td></td></tr>";
}

header("Content-Type:   application/vnd.ms-excel; charset=utf-8");
header("Content-type:   application/x-msexcel; charset=utf-8");
header("Content-Disposition: attachment; filename=Report.xls"); 
header("Expires: 0");
header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
header("Cache-Control: private",false);

	
	
		echo' <table class="table1"  style="width:100%"  border="1">
						<thead>
						<tr>
							     	
							      <th><h6>Sr No. </h6></th>
							      <th><h6>Employee Name </h6></th>
							      <th><h6>Emp=ID</h6></th>
							       <th><h6>Scanning Date</h6></th>
							  <th><h6>LR No </h6></th>
							     <th><h6>BKG Date</h6></th>
							     <th><h6>Invoice No </h6></th>
							     <th><h6>Customer Name </h6></th>
						         <th><h6>Destinantion</h6></th>
						         	<th><h6>Cases </h6></th>
									<th><h6>Qty </h6></th>
										<th><h6>Part No. </h6></th>
								
								 
								  <th><h6>Without GST Value </h6></th>
								    <th><h6>Invoice Value </h6></th>
								  	 
									
								   <th><h6>Godown Receipt Date </h6></th>
								<th><h6>Type</h6></th>
									<th><h6>Status</h6></th>
								<th><h6>My Remarks</h6></th>
								  <th><h6>Delivery date </h6></th>
								    <th><h6>Mode</h6></th>
							    <th><h6>A/Weight </h6></th>
								<th><h6>C/Weight </h6></th>
									<th><h6>E WayBill No. </h6></th>
									<th><h6>Rate </h6></th>
										<th><h6>ODA </h6></th>
											<th><h6>Fright </h6></th>
											<th><h6>GST </h6></th>
								
								<th><h6>Total </h6></th>
							</tr>
						</thead> ';
						echo '<tbody>';
						 echo $tr; 	
						echo'</tbody>
					</table>';
	
?>
	
	