<?php 
require "conn.php";
$docketno = $_POST['docketno'];
$mysql_qry ="select * from `tbl_courier` inner join `status` on tbl_courier.status=status.statusid where cons_no ='$docketno' ";
$result = mysqli_query($con,$mysql_qry);
$chk = mysqli_fetch_array($result);
if($chk['r_add']==""){
$destination="";
}else{
$sql2="SELECT city_name,r_add from tbl_city_code inner join tbl_courier on tbl_city_code.Id= tbl_courier.r_add 
WHERE tbl_courier.r_add = '".$chk['r_add']."' " ;
$result2 = mysqli_query($con,$sql2);
$row1 = mysqli_fetch_array($result2);
$destination=$row1['city_name'];
     }
if($chk['s_add']==""){
$source="";
}else{
$sql1="SELECT city_name,s_add from tbl_city_code inner join tbl_courier on tbl_city_code.Id= tbl_courier.s_add 
WHERE tbl_courier.s_add = '".$chk['s_add']."' " ;
$result1 = mysqli_query($con,$sql1);
$row = mysqli_fetch_array($result1);
$source=$row['city_name'];
     }
if($chk){
$result1="success~".$chk['statusname']."~".$chk['book1_date']."~".$chk['cons_no']."~".$chk['remark']."~".$destination."~".$source."~".$chk['ship_name']."~".
$chk['rev_name']."~".$chk['type']."~".$chk['qty']."~".$chk['mode']."~".$chk['chweight'];
	  
}else{
		$result1 = false; 
				
	}




echo $result1;

?>