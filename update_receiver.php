<?php 
session_start();
require_once('library.php');
$rand = get_rand_id(8);
require 'connection.php';

//echo $rand;

 $a=$_SESSION['username'];
$sql="select * from login where username='$a'";
$result = mysqli_query($con, $sql);
$row1 = mysqli_fetch_array($result, MYSQLI_BOTH);
 $userid = ($row1 && isset($row1['rid'])) ? $row1['rid'] : 0;

$query = "SELECT MAX(custid) AS custid FROM custreg ";
    if($result = mysqli_query($con,$query))
    {
  while ($row = mysqli_fetch_assoc($result))
  {
        $count = $row['custid'];
        $count = $count+1;

      $code_no = str_pad($count, 7, "0", STR_PAD_LEFT);
  }
	}

	// Initialize POST variables
	$he = $_POST['cstin'] ?? 0;
	$we = $_POST['custstax'] ?? 0;
	$len = $_POST['custpan'] ?? 0;

	// echo $he;
	// echo $we;
	// echo $len;

	 $tot=($len*$we*$he)/6000;

	// Initialize dropdown variables
	$company1 = $_POST['company1'] ?? '';
	$drop2 = $_POST['drop2'] ?? '';

  if($a!="admin") {
	$sql1 = "SELECT * FROM `receiver_reg` where userid='$userid' ORDER BY receiver_code ASC";
}
else{
	$sql1 = "SELECT * FROM `receiver_reg` ORDER BY receiver_code ASC";
}
$result3=mysqli_query($con,$sql1);
while($row3=mysqli_fetch_array($result3))
{
	if($company1==$row3['id'])
	{
	$drop2=$drop2."<option value='".$row3['id']."' selected>".$row3['receiver_code']."-".$row3['receivername']."</option>";
	}
	else{
	 $drop2=$drop2."<option value='".$row3['id']."' >".$row3['receiver_code']."-".$row3['receivername']."</option>";
	}
}

 // Initialize state dropdown variables
 $stname = $_POST['stname'] ?? '';
 $statedrop = $_POST['statedrop'] ?? '';

 $statesql="SELECT * FROM state order by statename ASC";
 $stateresult=mysqli_query($con,$statesql);
 While($staterow=mysqli_fetch_array($stateresult))
{
	if($stname==$staterow['stid'])
	{
	 $statedrop=$statedrop."<option value='".$staterow['stid']."' selected>".$staterow['statename']."</option>";
	}
	else{
	  $statedrop=$statedrop."<option value='".$staterow['stid']."' >".$staterow['statename']."</option>";
	}
}
  
//echo $Bookingmod = $_GET['Bookingmode'];

$query = "SELECT MAX(cid) AS custid FROM tbl_courier";  
    if($result = mysqli_query($con,$query))
    {
  while ($row = mysqli_fetch_assoc($result))
  {
        $count = $row['custid'];
  }
	}

// Initialize city dropdown variables
$cityname = $_POST['cityname'] ?? '';
$citydrop = $_POST['citydrop'] ?? '';

$statesql="SELECT * FROM tbl_city_code  ";
 $cityresult=mysqli_query($con,$statesql);
 while($staterow=mysqli_fetch_array($cityresult))
{
	if($cityname==$staterow['Id'])
	{
	 $citydrop=$citydrop."<option value='".$staterow['Id']."' selected>".$staterow['city_name']."-".$staterow['city_code']."</option>";
	}
	else{
	  $citydrop=$citydrop."<option value='".$staterow['Id']."' >".$staterow['city_name']."-".$staterow['city_code']."</option>";
	}
}
	
?>
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>

	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	<title>Vivanta Logistics</title>
	<meta name="description" content="">
	<meta name="author" content="Ahmed Saeed">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
	
</head>
<?php
include("header.php");
?>



        <div class="container">

			<div class="row">

				<div class="span2">
				
				</div><!--end span8-->
                             
                <div class="span8">
					<div class="register">
						<div class="titleHeader clearfix">
							<h3>Update Receiver </h3>
						</div><!--end titleHeader-->
						<form action="update_receiverIn.php" method="post" class="form-horizontal" onSubmit="return validate()" name="frmShipment">
						   	<legend>&nbsp;&nbsp;&nbsp;&nbsp;Receiver Information :</legend>
	<div class="control-group ">
							     <div class="control-group ">
							     <label class="control-label" for="Receiveraddress">Receiver Code : <span class="text-error">*</span></label>
							    <div class="controls">
							     <select name="rccode" id="rccode" onchange="fun2();">
									     <option  value="">-- Please Select --</option>
												<?php echo $drop2; ?>
								        </select>
	                                </div>
							     <br>	
							     <input type="hidden" name="rid" id="rid" value="">
						</div>
							<div class="control-group ">
							    <label class="control-label">Receiver Name : <span class="text-error" >*</span></label>
							    <div class="controls">
							      <input type="text" name="rcname" id="rcname" placeholder="Receiver Name">
							     <!-- <span class="help-inline"><i class="icon-ok"></i> Avaliable input!</span>-->
							    </div>
							</div><!--end control-group-->
							<input type="hidden" name="cid" id="cid" value="<?php echo $userid;?>">
								<div class="control-group ">
							    <label class="control-label" for="rcaddress"> Address : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="rcaddress" id="rcaddress" placeholder="Address">
							     <!--< <span class="help-inline">-->
							    </div>
							</div>
							
							<div class="control-group ">
							    <label class="control-label" for="Receiveraddress">Receiver City : <span class="text-error">*</span></label>
							    <div class="controls">
							       <select name="Receiveraddress" id="Receiveraddress" class="select2-search"required><option value="" selected="selected">-- Select--</option>
							     <!--< <span class="help-inline">-->	<?php echo $citydrop; ?>
							     </select>
							    </div>
							</div>
								<div class="control-group success">
							    <label class="control-label" for="custzip">Zip Code: <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="rczip" id="rczip" placeholder="Zip Code">
							      <!--<span class="help-inline"><i class="icon-ok"></i> Avaliable input!</span>-->
							    </div>
							</div>
							<div class="control-group">
							    <label class="control-label" for="custphone">Mobile No : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="rcphone" id="rcphone" placeholder="Mobile No">
							    </div>
							</div><!--end control-group-->
							
								<div class="control-group">
							    <label class="control-label" for="custphone">Contact Person  : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="text" name="cphone" id="cphone" placeholder="Contact Person Name">
							    </div>
							</div><!--end control-group-->
							<div class="control-group">
							    <label class="control-label">E-Mail : <span class="text-error">*</span></label>
							    <div class="controls">
							      <input type="email" name="rcemail" id="rcemail" placeholder="<EMAIL>">
							    </div>
							</div><!--end control-group-->
						<!--end control-group   <i class="icon-remove"></i> Invalid input!</span>-->
						<!--end control-group-->
		
						<!--end control-group-->

<!--end control-group-->    

							<div class="control-group">
							    <label class="control-label" for="custstax">GST No: </label>
							    <div class="controls">
							      <input type="text" name="rcgst" id="rcgst" placeholder="GST No">
							    </div>
							</div><!--end control-group-->
							<div class="control-group ">
							    <label class="control-label" for="custpan">PAN No : </label>
							    <div class="controls">
							      <input type="text" name="rcpan" id="rcpan" placeholder="PAN No">
							     <!--< <span class="help-inline">-->
							    </div>
							</div><!--end control-group   <i class="icon-remove"></i> Invalid input!</span>-->



					
							<div class="control-group">
							    <div class="controls">
							
									<input name="Update" class="btn btn-primary" type="submit" value="Update Register" onClick="return validateForm()">
										 <input type="submit" class="btn btn-primary" value="Delete" name="delete" onClick="return validateForm()">
								
							    </div>
							</div><!--end control-group-->
							
																				
						</form><!--end form-->

					</div><!--end register-->
				
				</div><!--end span-->
	
			</div><!--end row-->

        </div><!--end conatiner-->



<script>
function validate()
{
 var name=$("#Name").val();
 if(name=='')
 {
 	alert("Please Select Name Or User  Id Not set");
 	return false;
 }
 var address=$("#Address").val();
 if(address=='')
 {
 	alert("Please Enter Shipping address");
 	$("#Address").focus();
 	return false;
 }	
}
</script> 
		
<script  type="text/javascript">
function validateForm()
{
var x=document.forms["frmShipment"]["rcname"].value;
if (x==null || x=="")
  {
  alert("Receiver Name must be filled out");
  return false;
  }
  
  var phone=document.forms["frmShipment"]["rcphone"].value;
if (phone==null || phone=="")
  {
   alert("Mobile No must be 10 digit ");
  return false;
  
  }
  
 var custadd=document.forms["frmShipment"]["rcemail"].value;
if (custadd==null || custadd=="")
  {
  alert("Email must be filled out");
  return false;
  }
  var add=document.forms["frmShipment"]["rcaddress"].value;
if (add==null || add=="")
  {
  alert("Address must be filled out");
  return false;
  }
  //
  var x=document.forms["frmShipment"]["rczip"].value;
if (x==null || x=="")
  {
  alert("Zip Code must be filled out");
  return false;
  }
  
  var states1=document.forms["frmShipment"]["states"].selectedIndex;
if (states1==null || states1=="")
  {
   alert("State must be select");
  return false;
  
  }
  
 var city1=document.forms["frmShipment"]["city"].selectedIndex;
if (city1==null || city1=="")
  {
  alert("City must be select");
  return false;
  }
/*  var custin1=document.forms["frmShipment"]["cusvatin"].value;
if (custin1==null || custin1=="")
  {
  alert("VAT Tin must be filled out");
  return false;
  }

   var csttin1=document.forms["frmShipment"]["csttin"].value;
if (csttin1==null || csttin1=="")
  {
  alert("CST Tin must be filled out");
  return false;
  }
  
 var custstax1=document.forms["frmShipment"]["custstax"].value;
if (custstax1==null || custstax1=="")
  {
  alert("Service Tax must be filled out");
  return false;
  }
  var custpan1=document.forms["frmShipment"]["custpan"].value;
if (custpan1==null || custpan1=="")
  {
  alert("PAN No. must be filled out");
  return false;
  }
*/
}
</script>

<script  type="text/javascript">
function validateForm1()
{ 
  var x1=document.forms["frmShipment"]["rcphone"].value;
if (!x1>10 || x1=10)
  {
  alert("Mobile No must be 10 digit ");
  return false;
  }
  
 var x2=document.forms["frmShipment"]["rcemail"].value;
 var atposition=x2.indexOf("@");  
var dotposition=x2.lastIndexOf(".");  
if (atposition<1 || dotposition<atposition+2 || dotposition+2>=x.length){  
  alert("Please enter a valid e-mail address \n atpostion:"+atposition+"\n dotposition:"+dotposition);  
  return false;  
  }
  
 var x3=document.forms["frmShipment"]["rcaddress"].value;
if (x3==null || x3=="")
  {
  alert("Address must be filled out");
  return false;
  }
  
 var x4=document.forms["frmShipment"]["rczip"].value;
if (x4==6 || !x4>6)
  {
  alert("Zip code must be 6 Digit only ");
  return false;
  }
  
 var x5=document.forms["frmShipment"]["states"].value;
if (x5==null || x5=="")
  {
  alert("Please Select States");
  return false;
  }
  
   var x6=document.forms["frmShipment"]["city"].value;
if (x6==null || x6=="")
  {
  alert("Please Select City");
  return false;
  }
  
 var x7=document.forms["frmShipment"]["custin"].value;
if (isNaN(x7)){  
  document.getElementById("vati").innerHTML="Enter Numeric value only";  
  return false;  
}else{  
  return true;  
  }  
  
  var x8=document.forms["frmShipment"]["rcgst"].value;
if (isNaN(x8)){  
  document.getElementById("st").innerHTML="Enter Numeric value only";  
  return false;  
}else{  
  return true;  
  }  
}
</script>
    <script src="https://code.jquery.com/jquery-1.9.1.min.js"></script>
<script>
$(document).ready(function(){ 
         $("#add").click(function(){
        
  var rowCount=$('#tb tr').length;

$("#tb").append('<tr><td>'+rowCount+'</td><td><input name="sources'+rowCount+'" id="sources'+rowCount+'" class="input-small"></td><td><input name="destin'+rowCount+'" id="destin'+rowCount+'" class="input-small"></td><td><select name="mode'+rowCount+'" id="mode'+rowCount+'" class="input-small" ><option name="mode'+rowCount+'" id="mode'+rowCount+'" value="" selected>Select</option><option name="mode'+rowCount+'" id="mode'+rowCount+'" value="Air" >Air</option><option name="mode'+rowCount+'" id="mode'+rowCount+'" value="Road" >Road</option></select> </td><td><input name="fuelc'+rowCount+'" id="fuelc'+rowCount+'" class="input-small"></td><td><select name="ftl'+rowCount+'" id="ftl'+rowCount+'" class="input-small"><option name="ftl'+rowCount+'" id="ftl'+rowCount+'" class="input-small"  value="" selected>Select</option><option name="ftl'+rowCount+'" id="ftl'+rowCount+'" value="ftl" class="input-small">FTL</option><option name="ftl'+rowCount+'" id="ftl'+rowCount+'" value="per/kg" class="input-small" >Per/Kg</option></select> </td><td><input name="rate'+rowCount+'" id="rate'+rowCount+'" class="input-small"></td></tr>');



     $("#ct").val(rowCount);
    });
});
</script>
<script> 
  function del1()
{
rowCount=0;
 rowCount=$('#tb tr').length;
 if((rowCount-1)!=1)
 {
  	$('#hid_count1').val(rowCount-2);
 	if(rowCount!=2)
 	{
	var table = document.getElementById("tb");
 	table.deleteRow(rowCount -1);
  
	}
}
}
</script>
<script src="//ajax.googleapis.com/ajax/libs/jquery/1.10.2/jquery.min.js"></script>
 <script>           
            
$(document).ready(function() { 
$('#rccode').on("change",function()
  {
   var rccode=$("#rccode").val();
   //alert(rccode);
   
 $.ajax({                 
 url:'ajax_getCity.php',   //the script to call to get data          
      method:'POST',         //send it through post method
      data: { 
      rcid:rccode,
},                         //for example "id=5&parent=6"
      dataType:'json',                //data format      
      success: function(data)          //on recieve of reply
      {
           $("#city").empty();
          
      $.each(data, function(index, data) {
        var id = data['id'];
         var name = data['name']; 
        // alert(name);
        $('#city').append("<option value='"+id+"'>"+name+"</option>");
     
       });
       }
       });
}); 
        }); 
    </script>
       
<script>
function fun()
{
var a=document.getElementById("sname").value;
//alert(a);
obj=new XMLHttpRequest();
obj.open("GET","ajaxgetCustDetails.php?a="+a,true);
obj.send();
obj.onreadystatechange=funca
}
function fun2()
{
var a=document.getElementById("rccode").value;
//alert(a);
obj=new XMLHttpRequest();
obj.open("GET","ajaxgetrcDetails.php?a="+a,true);
obj.send();
obj.onreadystatechange=funca1
}
function fun()
{
var a=document.getElementById("sname").value;
//alert(a);
obj=new XMLHttpRequest();
obj.open("GET","ajaxgetCustDetails.php?a="+a,true);
obj.send();
obj.onreadystatechange=funca
}
function funca()
{
   if(obj.readyState==4)
     {
	vala=obj.responseText;
	//alert(vala);
	var res = vala.split("*");
		//alert(res[0]);
		document.getElementById("Shippername").value=res[1];
		document.getElementById("Shipperphone").value=res[2];
		document.getElementById("Shipperemail").value=res[3];
		document.getElementById("Shipperaddress").value=res[4];
		document.getElementById("custzip").value=res[5];
	//	document.getElementById("custin").value=res[8];
	//	document.getElementById("cstin").value=res[9];
		document.getElementById("custgst").value=res[10];
		document.getElementById("custpan").value=res[11];
		document.getElementById("uoffice").value=res[4];
		
		}	
}

function funca1()
{
   if(obj.readyState==4)
     {
	vala=obj.responseText;
	//alert(vala);
	var res = vala.split("*");
		//alert(res[0]);
		document.getElementById("rcname").value=res[3];
		document.getElementById("rcaddress").value=res[6];
		document.getElementById("rcphone").value=res[4];
		document.getElementById("rcemail").value=res[5];
		document.getElementById("rczip").value=res[7];
		document.getElementById("rcgst").value=res[10];
		document.getElementById("rcpan").value=res[11];
		document.getElementById("cphone").value=res[12];
		document.getElementById("rid").value=res[0];
	
			

		
		}	
}


  

function cityfun() { 
 // alert("hhi");
   $('#city').find('option').remove().end().append('<option value=""></option>').val('');
    $.ajax({                                      
      url: 'ajax_getCity.php?type='+$('#states').val(),                  //the script to call to get data          
      data: "",                        //you can insert url argumnets here to pass to api.php
                                       //for example "id=5&parent=6"
      dataType: 'json',                //data format      
      success: function(data)          //on recieve of reply
      {
      $.each(data, function(index, data) {
        $('#city').append( $('<option></option>').val(data.id).html(data.name) );
       });
       }
       });
       
    }
	
</script>
<!--<script  type="text/javascript">
function myFunction(q)
{

$.ajax({

  type: "POST",
  url: "rcode.php",
  data: {item:q},
  success:function(data){
 //alert(data);
  document.getElementById("hubunames").innerHTML=data;
  if(data=="Receiver Code Already Exist!!")
  {
  document.getElementById("bute").style.visibility = 'hidden';
  document.getElementById("hubunamea").style.visibility = 'hidden';
  }else
  {
	document.getElementById("hubunamea").style.visibility = 'visible';
	document.getElementById("hubunamea").innerHTML="Receiver Code Available!!";
	document.getElementById("bute").style.visibility = 'visible';  
  }
}
  });
}
</script> -->

<script>



function insur(insu){
	if(insu=="ys")
	{  
	document.getElementById("insurance").style.display="block";
	}
	else{
	document.getElementById("insurance").style.display="none";
	}
}
</script> 
 <?php

								$msg = $_GET['msg'] ?? '';
								if($msg=="yes1")
								{
						echo "<script> alert('Receiver Updated Successfully');</script>";
								}
								else if($msg=="no1"){
						echo "<script> alert('Receiver Not Updated Successfully');</script>";
								}
								?>    <!--   --> 

<?php
include("footer.php");
?>