<?php
require 'connection.php';
session_start();
$q = intval($_GET['q']);



 $sql="SELECT a.CustID,ComName,jarlimit,credit_limit,(SELECT NewBalJar FROM JarDistribution b WHERE ( DistDate = ( SELECT MAX( DistDate ) FROM JarDistribution c WHERE ( CmpId = a.CustID) AND ( CmpId = b.CmpId ) ) ) order by DistId DESC limit 1  ) AS bal  FROM CustReg a WHERE a.CustID ='".$q."'";

$result = mysqli_query($con,$sql);
$date1 = date('Y-m-d',  time());
$sql2="SELECT * FROM JarRate INNER JOIN CustReg on JarRate.cmpid=CustReg.CustID";
 $resultDiesel=mysqli_query($con,$sql2);
 while($row2=mysqli_fetch_array($resultDiesel))
 {
  $jarRate=$row2['rate'];
 
 }

?>


<table border='1' id='tableid'>
<tr>

<th>Product Name</th>
<th>Balance Jar</th>
<th>Delivered Jar</th>
<th>Return Jar</th>
<th>New Balance</th> 

<th>Rate</th> 
<th>Amount</th>

</tr>

  
 
 <td><input type='hidden' name='credit_limt' id='credit_limt' value= '".$creditlimit."'  /></td>";
 
 <td> <input type='text' size='1px;' id='balance".$srn."' name='balance".$srn."' value='".$bal3."' readonly onchange='myfunction()'></td>";
  
<td><input type='text' onkeypress='validate(event)' size='1px;' id='delivered".$srn."' name='delivered".$srn."' value='0' onchange='myfunction()'>  </td>";
<td><input type='text' onkeypress='validate(event)' size='1px;' id='returnj".$srn."' name='returnj".$srn."' value='0' onchange='myfunction()'></td>";

<td><input type='text' size='1px;' id='newbalnce".$srn."' name='newbalnce".$srn."' value='0' readonly onchange='myfunction()'></td>";

  
  <td><input type='text' size='1px;' id='rate".$srn."' name='rate".$srn."' value='".$rate."' onchange='myfunction()' ></td>";

  <td><input type='text' onkeypress='validate(event)' size='1px;' id='amount".$srn."' name='amount".$srn."' value='0' onchange='myfunction()'></td>";
 <input type='hidden' name='delbaljar".$srn."' id='delbaljar".$srn."' value='0' onchange='myfunction()'/>";
  
  
  
 
</tr>
