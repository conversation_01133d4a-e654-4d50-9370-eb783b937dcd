
<?php
// Clean all output buffers
while (ob_get_level()) {
    ob_end_clean();
}

// Start fresh
ob_start();

session_start();

// Set headers early
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');
header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');

// Disable error display for JSON responses
ini_set('display_errors', 0);
error_reporting(0);

// Function to send clean JSON response
function sendJsonResponse($status, $message, $data = null) {
    // Clean any existing output
    while (ob_get_level()) {
        ob_end_clean();
    }

    // Prepare response
    $response = array(
        'status' => $status,
        'message' => $message
    );

    if ($data !== null) {
        $response = array_merge($response, $data);
    }

    // Set headers again to be sure
    header('Content-Type: application/json');
    header('Content-Length: ' . strlen(json_encode($response)));

    // Output and exit
    echo json_encode($response);
    exit;
}

// Include your database connection and other required files
include("connection.php");
require_once('database.php');
require_once('library.php');
isUser();

// Add more variations to this array
$delivered_statuses = [
    '5', 'Delivered', 'delivered', 'DELIVERED',
    '6', 'Complete', 'Completed',
    'Deliv', 'Dlvrd' // Add any variations you find
];
$phpmailer_paths = [
    'vendor/phpmailer/phpmailer/src/',
    'vendor/phpmailer/src/',
    'PHPMailer/src/',
    'phpmailer/src/'
];

$phpmailer_found = false;
foreach ($phpmailer_paths as $path) {
    if (file_exists($path . 'PHPMailer.php')) {
        require_once $path . 'PHPMailer.php';
        require_once $path . 'SMTP.php';
        require_once $path . 'Exception.php';
        $phpmailer_found = true;
        break;
    }
}

if (!$phpmailer_found) {
    error_log("❌ PHPMailer NOT FOUND in any of these paths: " . implode(", ", $phpmailer_paths));
    sendJsonResponse('error', 'PHPMailer not found', ['debug_paths' => $phpmailer_paths]);
}

// Check if ZIP extension is available
if (!class_exists('ZipArchive')) {
    error_log("❌ ZIP extension not available");
    sendJsonResponse('error', 'ZIP extension not available. Please enable ZIP extension in PHP.');
}

// Test basic query
$test_query = "SELECT COUNT(*) as total FROM tbl_courier";
$test_result = mysqli_query($con, $test_query);
if ($test_result) {
    $test_data = mysqli_fetch_assoc($test_result);
    error_log("✅ Database connected. Total records in tbl_courier: " . $test_data['total']);
} else {
    error_log("❌ Test query failed: " . mysqli_error($con));
    echo json_encode(['status' => 'error', 'message' => 'Database test query failed: ' . mysqli_error($con)]);
    exit;
}

// Load PHPMailer classes first
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

// Create pod_downloads table if it doesn't exist
$create_table_sql = "CREATE TABLE IF NOT EXISTS pod_downloads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50),
    month VARCHAR(10),
    month_name VARCHAR(50),
    email VARCHAR(255),
    download_token VARCHAR(255) UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    downloaded TINYINT DEFAULT 0
)";
mysqli_query($con, $create_table_sql);

// Basic auth check
if (!isset($_SESSION['desgn'])) {
    echo json_encode(['status' => 'error', 'message' => 'Please login first']);
    exit;
}

// Validate POST data
if (!isset($_POST['month']) || !isset($_POST['email'])) {
    echo json_encode(['status' => 'error', 'message' => 'Invalid request']);
    exit;
}

$user_id = $_SESSION['desgn'];
$selected_month = $_POST['month'];
$user_email = trim($_POST['email']);
$data_filter = isset($_POST['data_filter']) ? trim($_POST['data_filter']) : 'delivered';

// Validate email
if (empty($user_email) || !filter_var($user_email, FILTER_VALIDATE_EMAIL)) {
    echo json_encode(['status' => 'error', 'message' => 'Please enter a valid email address']);
    exit;
}

// Validate data filter
if (!in_array($data_filter, ['delivered', 'all'])) {
    $data_filter = 'delivered'; // Default to delivered
}

// Parse month (format: YYYY-MM)
$month_parts = explode('-', $selected_month);
if (count($month_parts) != 2) {
    echo json_encode(['status' => 'error', 'message' => 'Invalid month format']);
    exit;
}

$year = $month_parts[0];
$month = $month_parts[1];

// Debug: Log the parsed values
error_log("🔍 Parsed values - Year: $year, Month: $month, Selected Month: $selected_month");

// Month name mapping
$month_names = [
    '01' => 'January', '02' => 'February', '03' => 'March', '04' => 'April',
    '05' => 'May', '06' => 'June', '07' => 'July', '08' => 'August',
    '09' => 'September', '10' => 'October', '11' => 'November', '12' => 'December'
];
$month_name = $month_names[$month] . ' ' . $year;

// Function to generate CSV and ZIP files
function generatePODFiles($con, $year, $month, $month_name, $data_filter, $delivered_statuses) {
    // Create temporary directory if it doesn't exist
    $temp_dir = sys_get_temp_dir() . '/pod_downloads/';
    if (!file_exists($temp_dir)) {
        mkdir($temp_dir, 0755, true);
    }

    // Generate filenames with timestamp
    $timestamp = date('Y-m-d_H-i-s');
    $csv_filename = "pod_{$month_name}_{$timestamp}.csv";
    $zip_filename = "pod_{$month_name}_{$timestamp}.zip";
    $csv_path = $temp_dir . $csv_filename;
    $zip_path = $temp_dir . $zip_filename;

    // Build query based on filter
    if ($data_filter === 'delivered') {
        $query = "SELECT * FROM tbl_courier
                  WHERE YEAR(book_date) = '$year'
                  AND MONTH(book_date) = '$month'
                  AND (status = '5' OR status = 'Delivered' OR status = 'delivered' OR status = 'DELIVERED'
                       OR status = '6' OR status = 'Complete' OR status = 'Completed'
                       OR LOWER(status) LIKE '%deliver%' OR LOWER(status) LIKE '%complete%')";
    } else {
        $query = "SELECT * FROM tbl_courier
                  WHERE YEAR(book_date) = '$year'
                  AND MONTH(book_date) = '$month'";
    }

    $result = mysqli_query($con, $query);
    
    if (!$result || mysqli_num_rows($result) == 0) {
        return ['success' => false, 'message' => 'No data found for the selected criteria'];
    }

    // Create CSV file
    $csv_file = fopen($csv_path, 'w');
    
    // Write headers
    $fields = mysqli_fetch_fields($result);
    $headers = array();
    foreach ($fields as $field) {
        $headers[] = $field->name;
    }
    fputcsv($csv_file, $headers);
    
    // Write data rows
    while ($row = mysqli_fetch_assoc($result)) {
        fputcsv($csv_file, $row);
    }
    fclose($csv_file);

    // Create ZIP archive
    $zip = new ZipArchive();
    if ($zip->open($zip_path, ZipArchive::CREATE) !== TRUE) {
        return ['success' => false, 'message' => 'Failed to create ZIP file'];
    }

    $zip->addFile($csv_path, $csv_filename);
    $zip->close();

    // Return file paths
    return [
        'success' => true,
        'csv_path' => $csv_path,
        'zip_path' => $zip_path,
        'csv_filename' => $csv_filename,
        'zip_filename' => $zip_filename,
        'count' => mysqli_num_rows($result)
    ];
}

// Generate unique token
$download_token = md5(uniqid($user_id . $selected_month . time(), true));

// Generate the POD files
$file_generation = generatePODFiles($con, $year, $month, $month_name, $data_filter, $delivered_statuses);

if (!$file_generation['success']) {
    echo json_encode(['status' => 'error', 'message' => $file_generation['message']]);
    exit;
}

// Insert download record
$expires_at = date('Y-m-d H:i:s', strtotime('+15 days'));
$insert_sql = "INSERT INTO pod_downloads (user_id, email, month, month_name, download_token, created_at, expires_at)
               VALUES ('$user_id', '$user_email', '$selected_month', '$month_name', '$download_token', NOW(), '$expires_at')";

if (!mysqli_query($con, $insert_sql)) {
    // Clean up files if database insert failed
    @unlink($file_generation['csv_path']);
    @unlink($file_generation['zip_path']);
    echo json_encode(['status' => 'error', 'message' => 'Failed to create download record']);
    exit;
}

// Prepare download URL
$download_url = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) . "/download_pod_file.php?token=" . $download_token;

// Format expiry date to DD-MM-YYYY format
$expires_formatted = date('d-m-Y H:i:s', strtotime($expires_at));

// PHPMailer function definition
function sendEmailWithPHPMailer($to_email, $subject, $message) {
    $mail = new PHPMailer(true);

    try {
        // SMTP configuration
        $mail->isSMTP();
        $mail->Host       = 'smtp.gmail.com';
        $mail->SMTPAuth   = true;
        $mail->Username   = '<EMAIL>';
        $mail->Password   = 'osddllvvuhntqsgg';
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port       = 587;

        // Email settings
        $mail->setFrom('<EMAIL>', 'Vivanta Logistics');
        $mail->addAddress($to_email);
        $mail->isHTML(false);
        $mail->Subject = $subject;
        $mail->Body    = $message;

        $mail->send();
        error_log("✅ Email sent successfully to: " . $to_email);
        return true;
    } catch (Exception $e) {
        error_log("❌ PHPMailer Error: " . $mail->ErrorInfo);
        return false;
    }
}

// Compose email
$subject = "POD Download Link - $month_name - Vivanta Logistics";
$message = <<<EOD
Dear User,

Your POD download link for $month_name is ready.

Found {$file_generation['count']} shipments for this month.

Download here:
$download_url

Note:
- This link will expire on: {$expires_formatted}
- The file contains a ZIP archive with POD reports for $month_name

Thank you,
Vivanta Logistics Team
EOD;

// Send email
error_log("📧 Attempting to send POD email to: " . $user_email);
error_log("📧 Download URL: " . $download_url);
error_log("📧 Record count: " . $file_generation['count']);

$mail_sent = sendEmailWithPHPMailer($user_email, $subject, $message);

// Clean any output before sending response
while (ob_get_level()) {
    ob_end_clean();
}

// Send final response
if ($mail_sent) {
    $response = array(
        'status' => 'success',
        'message' => 'Email sent successfully with download link',
        'download_url' => $download_url,
        'count' => $file_generation['count']
    );
} else {
    $response = array(
        'status' => 'warning',
        'message' => 'Download ready but email could not be sent',
        'download_url' => $download_url,
        'count' => $file_generation['count']
    );
}

// Set headers and output
header('Content-Type: application/json');
header('Content-Length: ' . strlen(json_encode($response)));
echo json_encode($response);
exit;
?>
