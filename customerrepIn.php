<?php
session_start();
require 'connection.php';
require_once('database.php');
require_once('library.php');
isUser();

$date=$_POST['date2'];

 $date2=$_POST['date4'];

$custid=$_POST['rep'];
//$statusid="All";

//$prospect=$_GET['prospect'];

if($date2=='')
{
 
$Cdate2=date('Y-m-d',time());
}
else
{
$Cdate2=date('Y-m-d',strtotime($date2));
}



$Cdate=date('Y-m-d',strtotime($date));


 $sql="SELECT cons_no,weight,gtotamt,rev_name,ship_name,noofpackage,gst,partno,freight,invi_value,assured_dly_date,book1_date,a.city_name as city ,tbl_city_code.city_name,type,invice_no,chweight,mode,statusname FROM (tbl_courier inner join status on tbl_courier.status=status.statusid) join tbl_city_code on tbl_city_code.Id= tbl_courier.r_add join tbl_city_code a on tbl_courier.s_add=a.Id    WHERE tbl_courier.shipper_code ='$custid' and tbl_courier.book1_date between '$date' and '$date2'   ";

/*else 
{
echo $sql="SELECT * FROM tbl_courier inner join status on tbl_courier.status=status.statusid WHERE tbl_courier.book_date between '$Cdate' and '$Cdate2' ";
}*/
//echo $sql;
			
echo "<table border='1' align='center'  class='table'>";

//echo "<tr ><td colspan='12' align='center'><b>New Enquiries</b></td></tr>";
 echo "<tr align='center'><td><b>Sr.No.</b></td><td><b>BKG date</b></td><td><b>BKG Time</b></td><td><b>Docket No</b></td><td><b>Consignor name</b></td><td><b>BKG Location</b></td><td><b>Invoice No</b></td><td><b>Invocie Value</b></td><td><b>Consignee name</b></td><td><b>Destination</b></td><td><b>No. Of Pkgs</b></td><td><b>Qty </b></td><td><b>A/Weight</b></td><td><b>C/Weight</b></td><td><b>Part No. </b></td><td><b> Delivery Status </b></td><td><b>Type Of Shipment </b></td><td><b>Transit Mode</b></td><td><b>Delivery Date</b></td><td><b>Total fright </b><td><b> GST(18%) </b></td><td><b> Grand Total </b></td></td></tr>";
$result = mysqli_query($con,$sql);
$cntr=0;
while($row = mysqli_fetch_array($result)) 
 {
$cntr=$cntr+1;  
//echo $cono=$row['cons_no'];

 echo "<tr><td>".$cntr."</td><td>".$row['book1_date']."</td><td>".$row['book1_date']."</td><td><a href='details.php?id=".$row['cons_no']."'>".$row['cons_no']."</a></td><td>".$row['ship_name']."</td><td>".$row['city']."</td><td>".$row['invice_no']."</td><td>".$row['invi_value']."</td><td>".$row['rev_name']."</td><td>".$row['city_name']."</td><td>".$row['noofpackage']."</td><td>".$row['qty']."</td><td>".$row['weight']."</td><td>".$row['chweight']."</td><td>".$row['partno']."</td><td>".$row['statusname']."</td><td>".$row['type']."</td><td>".$row['mode']."</td><td>".$row['assured_dly_date']."</td><td>".$row['fright']."</td><td>".$row['gst']."</td><td>".$row['gtotamt']."</td></tr>";
//echo "<tr><td>".$cntr."</td><td><a href='trackConsi.php?Consi=".$row['cons_no']."'>".$row['cons_no']."</a></td><td>".$row['invice_no']."</td><td>".$row['invi_value']."</td><td>".$row['qty']."</td><td>".$row['weight']."</td><td>".$row['type']."</td><td>".$row['mode']."</td><td>".$row['pick_date']."</td><td>".$row['status']."</td></tr>";
  }
  if($cntr==0)
  {
  echo "<tr><td colspan='10' align='center'><font color='red'> No Record Found...........</font></td></tr>";
  }
 echo "</table>";
 	echo'<div class="control-group ">
							     <div class="controls">
								     <input class="btn btn-primary"  type="button" value="Export" onclick="abc();" >
								     
							     </div>
							</div>';
							
							
 echo "</div>";
mysqli_close($con);


echo "<script> 
function abc()
{
location.href='customerwise_excel.php?id=".$custid."&date1=".$date."&date2=".$date2."';

}</script>"; 


?> 


