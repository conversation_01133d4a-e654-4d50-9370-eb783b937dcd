<?php
session_start();
include("connection.php");
require_once('database.php');
require_once('library.php');
isUser();

$date = $_GET['date'];
$date2 = $_GET['date2'];
$branch = $_GET['branch'];
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
if (!in_array($limit, [10, 20, 50, 100])) {
    $limit = 10;
}
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$start_from = ($page - 1) * $limit;

$Cdate = date('Y-m-d', strtotime($date));
$Cdate1 = date('Y-m-d', strtotime($date2));

// Query to get branch-wise data
$sql = "SELECT tbl_courier.*, status.statusname, tbl_city_code.city_name as destination_city,
        tbl_city_code2.city_name as source_city
        FROM tbl_courier
        INNER JOIN status ON tbl_courier.status = status.statusid
        LEFT JOIN tbl_city_code ON tbl_courier.r_add = tbl_city_code.Id
        LEFT JOIN tbl_city_code tbl_city_code2 ON tbl_courier.s_add = tbl_city_code2.Id
        WHERE status.statusname = '$branch'
        AND tbl_courier.book_date BETWEEN '$Cdate' AND '$Cdate1'
        ORDER BY tbl_courier.cons_no DESC
        LIMIT $start_from, $limit";

$result = mysqli_query($con, $sql);
$count = 0;
$tr = "";

while($row = mysqli_fetch_array($result)) {
    $count++;
    $tr = $tr . "<tr>
        <td>" . $count . "</td>
        <td>" . $row['cons_no'] . "</td>
        <td>" . $row['ship_name'] . "</td>
        <td>" . $row['rev_name'] . "</td>
        <td>" . $row['source_city'] . "</td>
        <td>" . $row['destination_city'] . "</td>
        <td>" . $row['statusname'] . "</td>
        <td>" . date('d-m-Y', strtotime($row['book_date'])) . "</td>
        <td>" . $row['weight'] . "</td>
        <td>" . $row['chweight'] . "</td>
        <td>" . $row['noofpackage'] . "</td>
        <td>" . $row['gtotamt'] . "</td>
        <td>" . $row['mode'] . "</td>
        <td>" . $row['book_mode'] . "</td>
        <td>" . $row['invice_no'] . "</td>
        <td>" . $row['remark'] . "</td>
    </tr>";
}

// Get total count for pagination
$count_sql = "SELECT COUNT(*) as total FROM tbl_courier
              INNER JOIN status ON tbl_courier.status = status.statusid
              WHERE status.statusname = '$branch'
              AND tbl_courier.book_date BETWEEN '$Cdate' AND '$Cdate1'";
$count_result = mysqli_query($con, $count_sql);
$count_row = mysqli_fetch_array($count_result);
$total_records = $count_row['total'];
$total_pages = ceil($total_records / $limit);

// Display the table
echo "<div class='row control-group' style='display: flex; align-items: center; justify-content: space-between; margin-bottom: 15px;'>
    <div class='col-md-4'>
        <form method='get' id='entryForm' class='form-inline'>
            <label for='limit' style='margin-right: 5px;'>Show</label>
            <select name='limit' id='limit' onchange='loadPage(1, this.value);' class='form-control input-sm'>
                <option value='10' " . ($limit == 10 ? 'selected' : '') . ">10</option>
                <option value='20' " . ($limit == 20 ? 'selected' : '') . ">20</option>
                <option value='50' " . ($limit == 50 ? 'selected' : '') . ">50</option>
                <option value='100' " . ($limit == 100 ? 'selected' : '') . ">100</option>
            </select>
            entries
        </form>
    </div>
    <div class='col-md-2 text-right'>
        <span><strong>Total Records: $total_records</strong></span>
    </div>
</div>";

echo "<table class='table table-bordered table-striped' style='width:100%'>
<thead>
    <tr style='background-color: #f16325; color: white;'>
        <th><h6>Sr No.</h6></th>
        <th><h6>Consignment No</h6></th>
        <th><h6>Shipper Name</h6></th>
        <th><h6>Receiver Name</h6></th>
        <th><h6>Source City</h6></th>
        <th><h6>Destination City</h6></th>
        <th><h6>Status</h6></th>
        <th><h6>Book Date</h6></th>
        <th><h6>Actual Weight</h6></th>
        <th><h6>Charged Weight</h6></th>
        <th><h6>Packages</h6></th>
        <th><h6>Amount</h6></th>
        <th><h6>Mode</h6></th>
        <th><h6>Book Mode</h6></th>
        <th><h6>Invoice No</h6></th>
        <th><h6>Remarks</h6></th>
    </tr>
</thead>
<tbody>
    $tr
</tbody>
</table>";

// Pagination
if ($total_pages > 1) {
    echo "<div class='pagination-wrapper'>";
    echo "<div class='pagination'>";
    
    // Previous button
    if ($page > 1) {
        echo "<a href='javascript:void(0)' onclick='loadPage(" . ($page - 1) . ", $limit)'>« Previous</a>";
    }
    
    // Page numbers
    $start_page = max(1, $page - 2);
    $end_page = min($total_pages, $page + 2);
    
    if ($start_page > 1) {
        echo "<a href='javascript:void(0)' onclick='loadPage(1, $limit)'>1</a>";
        if ($start_page > 2) {
            echo "<span>...</span>";
        }
    }
    
    for ($i = $start_page; $i <= $end_page; $i++) {
        if ($i == $page) {
            echo "<a href='javascript:void(0)' class='active'>$i</a>";
        } else {
            echo "<a href='javascript:void(0)' onclick='loadPage($i, $limit)'>$i</a>";
        }
    }
    
    if ($end_page < $total_pages) {
        if ($end_page < $total_pages - 1) {
            echo "<span>...</span>";
        }
        echo "<a href='javascript:void(0)' onclick='loadPage($total_pages, $limit)'>$total_pages</a>";
    }
    
    // Next button
    if ($page < $total_pages) {
        echo "<a href='javascript:void(0)' onclick='loadPage(" . ($page + 1) . ", $limit)'>Next »</a>";
    }
    
    echo "</div>";
    echo "</div>";
}

// Styling
echo "<style>
.pagination-wrapper {
    display: flex;
    justify-content: center;
    margin: 30px 0;
}
.pagination {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}
.pagination a {
    padding: 6px 12px;
    background-color: white;
    border: 1px solid rgb(255, 106, 0);
    color: #f16325;
    text-decoration: none;
    border-radius: 4px;
}
.pagination a.active {
    background-color: #f16325;
    color: white;
    font-weight: bold;
}
.pagination a:hover {
    background-color: #f16325;
    color: white;
}
.pagination span {
    padding: 6px 12px;
    color: #999;
}
</style>";
?>
