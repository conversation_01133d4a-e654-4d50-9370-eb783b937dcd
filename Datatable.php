<?php
session_start();
include("connection.php");
require_once('database.php');
require_once('library.php');
isUser();

$date=$_POST['date2'];

 $date2=$_POST['date4'];

$custid=$_POST['rep'];
//$statusid="All";

//$prospect=$_GET['prospect'];





$Cdate=date('Y-m-d',strtotime($date));


 //$sql="SELECT * FROM tbl_courier inner join status on tbl_courier.status=status.statusid WHERE tbl_courier.shipper_code ='$custid' and tbl_courier.book1_date between '$date' and '$date2'   ";


  $sql = "SELECT cons_no,weight,gtotamt,rev_name,ship_name,noofpackage,gst,partno,freight,invi_value,assured_dly_date,book1_date,a.city_name as city ,tbl_city_code.city_name,type,invice_no,chweight,mode,statusname FROM (tbl_courier inner join status on tbl_courier.status=status.statusid) join tbl_city_code on tbl_city_code.Id= tbl_courier.r_add join tbl_city_code a on tbl_courier.s_add=a.Id WHERE tbl_courier.shipper_code ='$custid' and tbl_courier.book1_date between '$date' and '$date2' ORDER BY cons_no DESC ";
//$sql = "SELECT cons_no, ship_name, rev_name,STATUS , r_add,TYPE , weight, invice_no,MODE , book_mode FROM tbl_courier WHERE STATUS = 5 ORDER BY cons_no DESC LIMIT $start_from, $num_rec_per_page";
$result = mysqli_query($con,$sql);	
$count=0;
while($row=mysqli_fetch_array($result))
{
	$count++;
	
//	$tr=$tr.'<tr><td>'.$count.'</td><td>'.$row['cons_no'].'</td><td>'.$row['ship_name'].'</td><td>'.$row['rev_name'].'</td><td>'.$row['statusname'].'</td><td>'.$row['city_name'].'</td><td>'.$row['type'].'</td><td>'.$row['weight'].'</td><td>'.$row['invice_no'].'</td><td>'.$row['mode'].'</td><td>'.$row['book_mode'].'</td></tr>';
	
	
		$tr=$tr. "<tr><td>".$count."</td><td>".$row['book1_date']."</td><td>".$row['book1_date']."</td><td><a href='details.php?id=".$row['cons_no']."'>".$row['cons_no']."</a></td><td>".$row['ship_name']."</td><td>".$row['city']."</td><td>".$row['invice_no']."</td><td>".$row['invi_value']."</td><td>".$row['rev_name']."</td><td>".$row['city_name']."</td><td>".$row['noofpackage']."</td><td>".$row['qty']."</td><td>".$row['weight']."</td><td>".$row['chweight']."</td><td>".$row['partno']."</td><td>".$row['statusname']."</td><td>".$row['type']."</td><td>".$row['mode']."</td><td>".$row['assured_dly_date']."</td><td>".$row['fright']."</td><td>".$row['gst']."</td><td>".$row['gtotamt']."</td></tr>";
}
	
?>

<!doctype html>
<html lang="en">
 
<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>Data Tables</title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="assets/vendor/bootstrap/css/bootstrap.min.css">
    <link href="../assets/vendor/fonts/circular-std/style.css" rel="stylesheet">
    <link rel="stylesheet" href="assets/libs/css/style.css">
    <link rel="stylesheet" href="assets/vendor/fonts/fontawesome/css/fontawesome-all.css">
    <link rel="stylesheet" type="text/css" href="assets/vendor/datatables/css/dataTables.bootstrap4.css">
    <link rel="stylesheet" type="text/css" href="assets/vendor/datatables/css/buttons.bootstrap4.css">
    <link rel="stylesheet" type="text/css" href="assets/vendor/datatables/css/select.bootstrap4.css">
    <link rel="stylesheet" type="text/css" href="assets/vendor/datatables/css/fixedHeader.bootstrap4.css">
</head>


       
                  
                    
                    <!-- ============================================================== -->
                    <!-- fixed header  -->
                    <!-- ============================================================== -->
                    <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Data Tables - Fixed Header  </h5>
                                <p>This example shows FixedHeader being styled by the Bootstrap 4 CSS framework.</p>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    	<div class="one-third last" style="height:350px;overflow:scroll;width=5000px;">	
                                    <table id="example4" class="table table-striped table-bordered" style="width:100%">
                                        <thead>
                                            <tr>
                                                <th><h6>Sr No. </h6></th>
							     <th><h6>BKG date</h6></th>
							     <th><h6>BKG Time</h6></th>
								<th><h6>Docket No </h6></th>
								<th><h6>Cosignor  Name </h6></th>
								 <th><h6>BKG Location</h6></th>
								 <th><h6>Invoice No </h6></th>
								 <th><h6>Invoice Value </h6></th>
								<th><h6>Consignee_Name </h6></th>
								<th><h6>Destination </h6></th>
								<th><h6>No. Of Packages </h6></th>
								<th><h6>Qty </h6></th>
								<th><h6>A/Weight </h6></th>
								<th><h6>C/Weight </h6></th>
								<th><h6>Part No. </h6></th>
								<th><h6>Delivery Status </h6></th>
								<th><h6>Type of Shipment </h6></th>
								<th><h6>Transit Mode </h6></th>
								<th><h6>Delivery date </h6></th>
								<th><h6>Total frieght</h6></th>
								<th><h6>GST </h6></th>
								<th><h6>Grand Total </h6></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                            <?php echo $tr; ?>
                                            </tbody>
                                            </tr>
                                           
                                    </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- ============================================================== -->
                    <!-- end fixed header  -->
                   
           
   
    <!-- ============================================================== -->
    <!-- end main wrapper -->
    <!-- ============================================================== -->
    <!-- Optional JavaScript -->
    <script src="assets/vendor/jquery/jquery-3.3.1.min.js"></script>
    <script src="assets/vendor/bootstrap/js/bootstrap.bundle.js"></script>
    <script src="assets/vendor/slimscroll/jquery.slimscroll.js"></script>
    <script src="assets/vendor/multi-select/js/jquery.multi-select.js"></script>
    <script src="/assets/libs/js/main-js.js"></script>
    <script src="../../../../../cdn.datatables.net/1.10.19/js/jquery.dataTables.min.js"></script>
    <script src="assets/vendor/datatables/js/dataTables.bootstrap4.min.js"></script>
    <script src="../../../../../cdn.datatables.net/buttons/1.5.2/js/dataTables.buttons.min.js"></script>
    <script src="assets/vendor/datatables/js/buttons.bootstrap4.min.js"></script>
     <script src="assets/vendor/datatables/js/data-table.js"></script>
    <script src="../../../../../cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
    <script src="../../../../../cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/pdfmake.min.js"></script>
    <script src="../../../../../cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/vfs_fonts.js"></script>
    <script src="../../../../../cdn.datatables.net/buttons/1.5.2/js/buttons.html5.min.js"></script>
    <script src="../../../../../cdn.datatables.net/buttons/1.5.2/js/buttons.print.min.js"></script>
    <script src="../../../../../cdn.datatables.net/buttons/1.5.2/js/buttons.colVis.min.js"></script>
    <script src="../../../../../cdn.datatables.net/rowgroup/1.0.4/js/dataTables.rowGroup.min.js"></script>
    <script src="../../../../../cdn.datatables.net/select/1.2.7/js/dataTables.select.min.js"></script>
    <script src="../../../../../cdn.datatables.net/fixedheader/3.1.5/js/dataTables.fixedHeader.min.js"></script>
    
</body>
 
</html>