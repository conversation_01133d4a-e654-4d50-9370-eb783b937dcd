<?php
session_start();
include("connection.php");
require_once('database.php');
require_once('library.php');
isUser();

header('Content-Type: application/json');

// Get month parameter
$month = isset($_GET['month']) ? $_GET['month'] : '';

if (empty($month)) {
    echo json_encode(array('error' => 'Month parameter required'));
    exit;
}

// Parse month (format: YYYY-MM)
$monthParts = explode('-', $month);
if (count($monthParts) !== 2) {
    echo json_encode(array('error' => 'Invalid month format. Use YYYY-MM'));
    exit;
}

$year = intval($monthParts[0]);
$monthNum = intval($monthParts[1]);

try {
    // Debug query to check data availability
    $sql = "SELECT 
                COUNT(*) as total_records,
                COUNT(CASE WHEN status = 'Delivered' THEN 1 END) as delivered_records,
                MIN(book_date) as earliest_date,
                MAX(book_date) as latest_date
            FROM tbl_courier 
            WHERE YEAR(book_date) = ? 
            AND MONTH(book_date) = ?";
    
    $stmt = mysqli_prepare($con, $sql);
    mysqli_stmt_bind_param($stmt, "ii", $year, $monthNum);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $data = mysqli_fetch_assoc($result);
    
    // Also get sample records
    $sql2 = "SELECT ConsignmentNo, book_date, status, PartNo 
             FROM tbl_courier 
             WHERE YEAR(book_date) = ? 
             AND MONTH(book_date) = ?
             LIMIT 5";
    
    $stmt2 = mysqli_prepare($con, $sql2);
    mysqli_stmt_bind_param($stmt2, "ii", $year, $monthNum);
    mysqli_stmt_execute($stmt2);
    $result2 = mysqli_stmt_get_result($stmt2);
    
    $samples = array();
    while ($row = mysqli_fetch_assoc($result2)) {
        $samples[] = $row;
    }
    
    echo json_encode(array(
        'month' => $month,
        'year' => $year,
        'month_num' => $monthNum,
        'statistics' => $data,
        'sample_records' => $samples,
        'query_executed' => true
    ));
    
} catch (Exception $e) {
    echo json_encode(array(
        'error' => 'Database error: ' . $e->getMessage()
    ));
}
?>
