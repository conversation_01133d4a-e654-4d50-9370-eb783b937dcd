<?php
session_start();
require_once('database.php');
require_once('library.php');

isUser();

$cons= $_POST['Consignment'];

$sql = "SELECT *
		FROM tbl_courier
		WHERE cons_no = '$cons'";
	//	echo $sql;
$result = dbQuery($sql);
$no = dbNumRows($result);
if($no == 1){
while($data = dbFetchAssoc($result)) {
extract($data);
?>
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>
	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
	
</head>
<?php
include("header.php");
?>
      
		<div class="container">
			<div class="row">
				<div class="span2">
				</div><!--end span8-->

                <div class="span8">
					<div class="account-list-outer">

						<div class="titleHeader clearfix">
							<h3>Search Result </h3>
						</div><!--end titleHeader-->
	<table  cellpadding="2" cellspacing="2" width="100%" border="1"> 
            <tbody>     <tr> 
							    <td >

        <table border="0" cellpadding="1" cellspacing="1" width="100%">
			<tbody>
				<tr>
					<td >Shipper Name :</td>
					<td ><?php echo $ship_name; ?></td>
				</tr>
				<tr>
					<td>Shipper Phone :</td>
					<td> <?php echo $phone; ?>  </td>
				</tr>
				<tr>
					<td>Shipper Address :</td>
					<td><?php echo $s_add; ?></td>
				</tr>
			</tbody>
		</table>
                             </td >
							 <td >

        <table border="0" cellpadding="1" cellspacing="1" width="100%">
			<tbody>
				<tr>
					<td >Receiver Name :</td>
					<td ><?php echo $rev_name; ?></td>
				</tr>
				<tr>
					<td>Receiver Phone :</td>
					<td> <?php echo $r_phone; ?>  </td>
				</tr>
				<tr>
					<td>Receiver Address :</td>
					<td><?php echo $r_add; ?></td>
				</tr>
			</tbody>
		</table>
                             </td >
									</tr> 
	<tr>
      <td >&nbsp;</td>
      <td >&nbsp;</td>
    </tr>
	<tr> 
      <td align="right">Consignment No  : </td> 
      <td ><?php echo $cons_no; ?>&nbsp;</td> 
    </tr> 
    <tr>
      <td align="right">Ship Type  :</td>
      <td><?php echo $type; ?>&nbsp;</td>
    </tr>
    <tr>
      <td align="right">Weight :</td>
      <td><?php echo $weight; ?>&nbsp;kg</td>
    </tr>
    <tr>
      <td align="right">Invoice no  :</td>
      <td><?php echo $invice_no; ?>&nbsp;</td>
    </tr>
    <tr>
      <td align="right">Booking Mode :</td>
      <td><?php echo $book_mode; ?>&nbsp;</td>
    </tr>
    <tr>
      <td align="right">Total freight : </td>
      <td><?php echo $freight; ?>&nbsp;Rs.</td>
    </tr>
    <tr>
      <td align="right">Mode : </td>
      <td><?php echo $mode; ?></td>
    </tr> 
    <tr> 
      <td align="right">Pickup Date:</td> 
      <td><?php echo $pick_date; ?></td> 
    </tr> 
    <tr> 
      <td align="right">Status :</td> 
      <td>&nbsp;<?php echo $status; ?></td> 
    </tr> 
    
			</tbody>
		</table> 						 
	
</div><!--end -->
				</div><!--end span6-->
			</div><!--end row-->
		</div><!--end conatiner-->
		
<?php }
include("footer.php");
}
else {
//echo 'In else....';
?>
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>
	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
	
</head>
<?php
include("header.php");
?>
      
		<div class="container">
			<div class="row">
				<div class="span1">
				</div><!--end span8-->

                <div class="span9">
					<div class="account-list-outer">

						<div class="titleHeader clearfix">
							<h3>Search Result </h3>
						</div><!--end titleHeader-->
					 <center> <h2>Consignment Number <font color="#FF0000"><?php echo $cons; ?></font> not found. Please verify the Number.<br>
                          <a href="searchEdit.php">Go Back</a> to Search Again.</h2> </center>	 
	
                    </div><!--end -->
				</div><!--end span6-->
			</div><!--end row-->
		</div><!--end conatiner-->
		
<?php
include("footer.php");
}
?>