<?php 
session_start();
require 'connection.php';
require_once('database.php');
require_once('library.php');
ob_start();

 $date1=$_GET['date1'];
 $date2=$_GET['date2'];

 $custid=$_GET['id'];
 $Cdate=date('Y-m-d',strtotime($date1));
 $Cdate1=date('Y-m-d',strtotime($date2));


$sql="SELECT cons_no,weight,gtotamt,rev_name,ship_name,book_mode,noofpackage,gst,partno,freight,qty,invi_value,assured_dly_date,book1_date,a.city_name as city ,tbl_city_code.city_name,type,invice_no,chweight,mode,statusname FROM (tbl_courier inner join status on tbl_courier.status=status.statusid) join tbl_city_code on tbl_city_code.Id= tbl_courier.r_add join tbl_city_code a on tbl_courier.s_add=a.Id WHERE tbl_courier.shipper_code ='$custid' and tbl_courier.book_date between '$Cdate' and '$Cdate1' ORDER BY cons_no DESC ";

$result1 = mysqli_query($con,$sql); 

include_once("excel.php");
$title = "Sheet";
$colors = array("Sr.No.","BKG date","BKG Time","Docket No.","Cosignor_Name","BKG Location","Invoice No","Invoice Value","Consignee_Name","Destination","No. Of Packages","Qty","A/Weight","C/Weight","Part No.","Delivery Staus","Type of Shipment","Payment Mode","Transit Mode","Delivery date","Total frieght","GST","Grand Total");
$xls = new Excel($title);
$xls->top();
$xls->home();
foreach ($colors as $color)
{
$xls->label($color);
$xls->right();
//$xls->down();
};
$xls->down();
$xls->home();


	$cnt=0;
	while($row2 = mysqli_fetch_array($result1))
  {
 	 $cnt=$cnt+1;
 	$xls->label($cnt);$xls->right();
 	$xls->label($row2['book1_date']);$xls->right();
 		$xls->label($row2['book1_date']);$xls->right();
 	$xls->label($row2['cons_no']);$xls->right();
 		$xls->label($row2['ship_name']);$xls->right();
 			$xls->label($row2['city']);$xls->right();
 $xls->label($row2['invice_no']);$xls->right();
  $xls->label($row2['invi_value']);$xls->right();
  	$xls->label($row2['rev_name']);$xls->right();
  		$xls->label($row2['city_name']);$xls->right();
  			$xls->label($row2['noofpackage']);$xls->right();
 
 	
 	 $xls->label($row2['qty']);$xls->right();
 	
	 	$xls->label($row2['weight']);$xls->right();
	 	$xls->label($row2['chweight']);$xls->right();
	 		$xls->label($row2['partno']);$xls->right();
	 			$xls->label($row2['statusname']);$xls->right();
	 			
	 	$xls->label($row2['type']);$xls->right();
	 	$xls->label($row2['book_mode']);$xls->right();
	$xls->label($row2['mode']);$xls->right();
	 	$xls->label($row2['assured_dly_date']);$xls->right();
	
	 	 $xls->label($row2['freight']);$xls->right();
 	
	 	$xls->label($row2['gst']);$xls->right();
	 
	$xls->label($row2['gtotamt']);$xls->right();
	 
	
	
	
	 	
	
	$xls->home();
 	$xls->down();
	
	}
		
		

	$xls->home();
	$xls->down();
	$xls->home();
	$xls->down();
	$xls->home();
	
	$data = ob_get_clean();
	file_put_contents('report.xls',$data);
$xls->send();
	
	
	 ?>
	
	
	
	
	
	
	
	
	
	