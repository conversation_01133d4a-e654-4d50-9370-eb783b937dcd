<?php
session_start();
include("connection.php");
require_once('database.php');
require_once('library.php');
isUser();

$a = isset($_SESSION['username']) ? $_SESSION['username'] : '';
//echo $a;
$sql="select * from login where username='$a'";
$result = mysqli_query($con, $sql);
$row1 = mysqli_fetch_array($result, MYSQLI_BOTH);
$userid = ($row1 && isset($row1['rid'])) ? $row1['rid'] : 0;

// Get date parameter from URL
$selected_date = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');

// Pagination setup (following reports_pod.php pattern)
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
if (!in_array($limit, [10, 20, 50, 100])) {
    $limit = 20;
}
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$start_from = ($page - 1) * $limit;

// Add date filtering to the query
$sql = "SELECT cons_no,weight,gtotamt,rev_name,r_add,ship_name,book_mode,userid,status_date,noofpackage,status,e_waybill,eway_expdate,gst,partno,remark,freight,book_mode,invi_value,qty,
assured_dly_date,book1_date,a.city_name as city ,type,invice_no,rate,oda_mis,chweight,mode,statusname FROM (tbl_courier inner join status on tbl_courier.status=status.statusid)
join  tbl_city_code a on tbl_courier.s_add=a.Id
WHERE status = '1' AND DATE(book_date) = '$selected_date' ORDER BY cons_no DESC LIMIT $start_from, $limit";

//$sql = "SELECT cons_no, ship_name, rev_name,STATUS , r_add,TYPE , weight, invice_no,MODE , book_mode FROM tbl_courier WHERE status = 'Booking' ORDER BY cons_no DESC LIMIT $start_from, $num_rec_per_page";
$result = mysqli_query($con,$sql);
$count=0;
$tr = ""; // Initialize table rows variable
while($row=mysqli_fetch_array($result))
{
    // Initialize variables to prevent undefined variable warnings
    $statusdate = "";
    $delivereddate = "";
    $destination = "";
    $Manager_name = "";
    $empcode = "";

    if($row['status']=='6' || $row['status']=='50' || $row['status']=='57'){
     $statusdate=$row['status_date'];
 }

  if($row['status']=='5'){
     $delivereddate=$row['status_date'];
 }
 
 if($row['r_add']==""){
         $destination="";
     }else{
  $sql1="SELECT city_name,r_add from tbl_city_code inner join tbl_courier on tbl_city_code.Id= tbl_courier.r_add 
  WHERE tbl_courier.r_add = '".$row['r_add']."' " ;

$result1 = mysqli_query($con,$sql1);
$row1 = mysqli_fetch_array($result1);
$destination = ($row1 && isset($row1['city_name'])) ? $row1['city_name'] : "";
     }
      $sql2="SELECT Manager_name,empcode from tbl_courier_officers inner join tbl_courier on tbl_courier_officers.cid=tbl_courier.userid
  WHERE tbl_courier.userid = '".$row['userid']."' " ;

$result2 = mysqli_query($con,$sql2);
$row2 = mysqli_fetch_array($result2);
$Manager_name = ($row2 && isset($row2['Manager_name'])) ? $row2['Manager_name'] : "";
$empcode = ($row2 && isset($row2['empcode'])) ? $row2['empcode'] : "";
 $invoicevalue=$row['invi_value'];
 $withoutgstvalue=$invoicevalue;
    
	$count++;
	
$tr=$tr."<tr><td>".$count."</td><td>".$Manager_name."</td><td>".$empcode."</td><td>".$row['cons_no']."</td><td>".$row['book1_date']."</td><td>".$row['invice_no']."</td>
<td>".$row['rev_name']."</td><td>".$destination."</td><td>".$row['noofpackage']."</td><td>".$row['qty']."</td><td>".$row['partno']."</td><td>".$withoutgstvalue."</td><td>".$row['invi_value']."</td><td>".$statusdate."</td>
<td>".$row['mode']."</td><td>".$row['statusname']."</td><td>".$row['remark']."</td><td>".$delivereddate."</td><td>".$row['book_mode']."</td><td>".$row['weight']."</td><td>".$row['chweight']."</td><td>".$row['e_waybill']."</td><td>".$row['rate']."</td>
<td>".$row['oda_mis']."</td><td>".$row['freight']."</td><td></td><td></td></tr>";
}
	
?>
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>

	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->

<script language="JavaScript">
var checkflag = "false";

function check(field) {
if (checkflag == "false")
 {
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll")
	{
	field[i].checked=true;	
	}
	}
	checkflag = "true";
}
else
{
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll")
	{
	field[i].checked=false;
	}
	}
	checkflag = "false";
}

}
function confirmDel(field,msg)
{
	count=0;
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll" && field[i].checked==true)
	{
	count++;
	}
	}
	
	if(count == 0)
	{
		alert("Select any one record to delete!");
		return false;
	}
	else
	{
		return confirm(msg);
	}
}
</script>
	
</head> 

<?php include("header.php"); ?>
		<div class="container">

			<div class="row">

                <div class="span12">
					<div class="account-list-outer">

						<div class="titleHeader clearfix">
							<h3> Booked Report</h3>
						</div><!--end titleHeader-->

						<!-- Pagination Controls -->
						<div class="row control-group" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 15px;">
							<div class="col-md-4">
								<form method="get" id="entryForm" class="form-inline">
									<label for="limit" style="margin-right: 5px;">Show</label>
									<select name="limit" id="limit" onchange="document.getElementById('entryForm').submit();" class="form-control input-sm">
										<option value="10" <?php if ($limit == 10) echo 'selected'; ?>>10</option>
										<option value="20" <?php if ($limit == 20) echo 'selected'; ?>>20</option>
										<option value="50" <?php if ($limit == 50) echo 'selected'; ?>>50</option>
										<option value="100" <?php if ($limit == 100) echo 'selected'; ?>>100</option>
									</select>
									entries
									<?php if(isset($_GET['date'])): ?>
										<input type="hidden" name="date" value="<?php echo $_GET['date']; ?>">
									<?php endif; ?>
									<input type="hidden" name="page" value="<?php echo $page; ?>">
								</form>
							</div>
							<div class="col-md-2 text-right">
								<input class="btn btn-primary" type="button" value="Export" onclick="abc();">
							</div>
						</div>
	<div class="one-third last" style="height:450px;overflow:scroll;width=8000px;">
						<table class="table1"  style="width:100%">
						<thead>
					<th><h6>Sr No. </h6></th>
					 <th><h6>Employee Name </h6></th>
							     <th><h6>Employee Id</h6></th>
							     <th><h6>LR No </h6></th>
							     <th><h6>BKG Date</h6></th>
							     <th><h6>Invoice No </h6></th>
							     <th><h6>Customer Name </h6></th>
						         <th><h6>Destinantion</h6></th>
						         	<th><h6>Cases </h6></th>
									<th><h6>Qty </h6></th>
										<th><h6>Part No. </h6></th>
								
								 
								  <th><h6>Without GST Value </h6></th>
								    <th><h6>Invoice Value </h6></th>
								  	 
									
								   <th><h6>Godown Receipt Date </h6></th>
								<th><h6>Type</h6></th>
								<th><h6>Remarks</h6></th>
								<th><h6>My Remarks</h6></th>
								  <th><h6>Delivery date </h6></th>
								    <th><h6>Mode</h6></th>
							    <th><h6>A/Weight </h6></th>
								<th><h6>C/Weight </h6></th>
									<th><h6>E WayBill No. </h6></th>
									<th><h6>Rate </h6></th>
										<th><h6>ODA </h6></th>
											<th><h6>Fright </h6></th>
											<th><h6>GST </h6></th>
								
								<th><h6>Total </h6></th>
							  
							</tr>
						</thead> 
						<tbody>
							<tr><?php echo $tr; ?>	</tr>
						</tbody>
					</table></div>

					<?php
					// Count total records for pagination with date filtering
					$count_sql = "SELECT COUNT(*) as total FROM (tbl_courier inner join status on tbl_courier.status=status.statusid) join tbl_city_code a on tbl_courier.s_add=a.Id WHERE status = '1' AND DATE(book_date) = '$selected_date'";
					$count_result = mysqli_query($con, $count_sql);
					$count_row = mysqli_fetch_assoc($count_result);
					$total_records = $count_row['total'];
					$total_pages = ceil($total_records / $limit);

					// Build URL parameters
					$url_params = "?";
					if(isset($_GET['date'])) {
						$url_params .= "date=" . urlencode($_GET['date']) . "&";
					}
					$url_params .= "limit=$limit&";

					$adjacents = 2;
					$start_loop = ($page > $adjacents) ? $page - $adjacents : 1;
					$end_loop = ($page < ($total_pages - $adjacents)) ? $page + $adjacents : $total_pages;

					// Styling
					echo "<style>
					.pagination-wrapper {
						display: flex;
						justify-content: center;
						margin: 30px 0;
					}
					.pagination {
						display: flex;
						flex-wrap: wrap;
						gap: 5px;
					}
					.pagination a {
						padding: 6px 12px;
						background-color: white;
						border: 1px solid rgb(255, 106, 0);
						color: #f16325;
						text-decoration: none;
						border-radius: 4px;
					}
					.pagination a.active {
						background-color: #f16325;
						color: white;
						font-weight: bold;
					}
					.pagination a:hover {
						background-color: #f16325;
						color: white;
					}
					</style>";

					// Output pagination
					if($total_pages > 1) {
						echo "<div class='pagination-wrapper'>";
						echo "<div class='pagination'>";

						// First/Previous
						if ($page > 1) {
							echo "<a href='{$url_params}page=1'>&laquo;</a>";
							echo "<a href='{$url_params}page=" . ($page - 1) . "'>Previous</a>";
						}

						// Page numbers
						for ($i = $start_loop; $i <= $end_loop; $i++) {
							if ($i == $page)
								echo "<a class='active' href='{$url_params}page=$i'>$i</a>";
							else
								echo "<a href='{$url_params}page=$i'>$i</a>";
						}

						// Next/Last
						if ($page < $total_pages) {
							echo "<a href='{$url_params}page=" . ($page + 1) . "'>Next</a>";
							echo "<a href='{$url_params}page=$total_pages'>&raquo;</a>";
						}

						echo "</div></div>";
					}
					?>

					</div><!--end -->
					<!--<input type="button" class='btn btn-primary' id="printpagebutton" onclick="printpage();" value="Print">&nbsp;&nbsp;<a id="backbutton" href="inTransitReport.php"><input type="button" id="backbutton" class='btn' onclick="closeWin();" value="Close"> </a></p>-->
					
				</div><!--end span-->
		
			</div><!--end row-->

		</div><!--end conatiner-->
		

<script type="text/javascript">
    function printpage() {
        //Get the print button and put it into a variable
        var printButton = document.getElementById("printpagebutton");
        var back=document.getElementById("backbutton");
        //Set the print button visibility to 'hidden' 
        printButton.style.visibility = 'hidden';
        back.style.visibility = 'hidden';
        //Print the page content
        window.print();
        //Set the print button to 'visible' again 
        //[Delete this line if you want it to stay hidden after printing]
        printButton.style.visibility = 'visible';
         back.style.visibility = 'visible';
    }
</script>
<script type="text/javascript">

function closeWin() {
    back.close();
}
    window.onbeforeunload = function() {
        
    }
</script>		
		
<?php
echo "<script>
function abc()
{

location.href='inTransitRepor_excel.php?date=$selected_date';

}</script>";
?>

<?php include("footer.php"); ?>