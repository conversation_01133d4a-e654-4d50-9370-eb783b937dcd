@RestController
@RequestMapping("/api/pod")
public class PodApiController {
    
    @Autowired
    private PodDownloadService podDownloadService;
    
    @PostMapping("/generate")
    public ResponseEntity<Map<String, String>> generateDownloadLink(
            @RequestParam String month,
            @RequestParam String email) {
        
        try {
            String downloadToken = podDownloadService.generateDownloadToken(month, email);
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "token", downloadToken,
                "download_url", "/downloadpod.php?token=" + downloadToken
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "status", "error",
                "message", e.getMessage()
            ));
        }
    }
    
    @GetMapping("/zip/{token}")
    public ResponseEntity<Resource> getZipFile(
            @PathVariable String token,
            HttpServletResponse response) throws IOException {
        
        return podDownloadService.getZipFileByToken(token, response);
    }
}