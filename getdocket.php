<?php
session_start();
require 'connection.php';
require_once('database.php');
require_once('library.php');
isUser();

$num_rec_per_page = 20; // Define records per page for pagination

 $sdate=$_POST['date2'];
 $edate2=$_POST['date4'];
$custid=$_POST['cname'];
$Cdate=date('Y-m-d',strtotime($sdate));
$Cdate2=date('Y-m-d',strtotime($edate2));
  $mode=$_POST['mode'];
$_SESSION["booking_mode"] = $mode;

  $sql = "SELECT * FROM tbl_courier  WHERE tbl_courier.shipper_code ='$custid' and mode='$mode' and tbl_courier.book_date between '$Cdate' and '$Cdate2' ORDER BY cons_no DESC ";
$result = mysqli_query($con,$sql);
$cntr=0;
$cnt=0;
$tr='';
$doc=array();
while($row = mysqli_fetch_array($result)) 
 {
     
     
     if($row['r_add']==""){
         $destination="";
     }else{
  $sql1="SELECT city_name,r_add from tbl_city_code inner join tbl_courier on tbl_city_code.Id= tbl_courier.r_add 
  WHERE tbl_courier.r_add = '".$row['r_add']."' " ;

$result1 = mysqli_query($con,$sql1);
$row1 = mysqli_fetch_array($result1);
$destination=$row1['city_name'];
     }
     
     $cntr=$cntr+1;  
$cnt=$cnt+1;
 $tr=$tr.'<tr class="table"><td>'.$doc[]=$cntr.'</td><td> '.$row['cons_no'].'</td><td> '.$row['book1_date'].'</td><td> '.$destination.'</td><td><input type="checkbox" name="bh[]" value="'.$row['cons_no'].'"> </td></tr>';


  }
?>

<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>
	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons
	================================================== -->
<script language="JavaScript">
var checkflag = "false";
 //---- Checks consignment is selected or not script start-----
function check(field) {
if (checkflag == "false")
 {
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll")
	{
	field[i].checked=true;	
	}
	}
	checkflag = "true";
}
else
{
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll")
	{
	field[i].checked=false;
	}
	}
	checkflag = "false";
}

} //---- Checks consignment is selected or not script end-----
//---- Delete the consignment script start-----
function confirmDel(field,msg)
{
	count=0;
	for (i = 0; i < field.length; i++) {
	if(field[i].type=="checkbox" && field[i].name!="chkAll" && field[i].checked==true)
	{
	count++;
	}
	}
	
	if(count == 0)
	{
		alert("Select any one record to delete!");
		return false;
	}
	else
	{
		return confirm(msg);
	}
}
//---- Delete the consignment script end-----
</script>
</head>
<?php include("header.php"); ?>
		<div class="container">
			<div class="row">
               <div class="span11">
					<div class="account-list-outer">

						<div class="titleHeader clearfix">
							<h3>   </h3>
						
						</div>
						
                  <form  action="generatteBill.php" method="post" >
                     
					<table class="table">
						<thead>
							<tr>
								<th><h6>Sr No. </h6></th>
							     <th><h6>LR No </h6></th>
							     <th><h6>BKG Date</h6></th>
							   
						         <th><h6>Destinantion</h6></th>
								<th><h5><input type="checkbox" name="check_all" id="check_all" onClick="checkAll(this)"> All / Select</h5></th>
								
							</tr>
						</thead>
						<input type="hidden" name="custname" id="custname" value="<?php echo $custid;?>">
							<input type="hidden" name="sdate" id="sdate" value="<?php echo $sdate;?>">
								<input type="hidden" name="edate3" id="edate3" value="<?php echo $edate2;?>">
						<tbody><?php echo $tr; ?> 
						   
						</tbody>
						
					</table>
			
			<div class="row" align="right">	
			    <div class="span8">
				    <div class="control-group">
						<div class="controls"> 
							<input name="Submit" class="btn btn-primary" type="submit" value="Submit" onClick="return valid()" >
					    </div>
					</div>
				</div>
			</div>			
						
						
						
						
						
						</form>
					</div><!--end -->
				</div><!--end span-->
			</div><!--end row-->
		</div><!--end conatiner-->

<script type="text/javascript">
    function printpage() {
        //Get the print button and put it into a variable
        var printButton = document.getElementById("printpagebutton");
        var back=document.getElementById("backbutton");
        //Set the print button visibility to 'hidden' 
        printButton.style.visibility = 'hidden';
        back.style.visibility = 'hidden';
        //Print the page content
        window.print();
        //Set the print button to 'visible' again 
        //[Delete this line if you want it to stay hidden after printing]
        printButton.style.visibility = 'visible';
         back.style.visibility = 'visible';
    }
</script>
<script type="text/javascript">

function closeWin() {
    back.close();
}
    window.onbeforeunload = function() {
        
    }
</script>



		
<script>
function checkAll(bx) {

  var cbs = document.getElementsByTagName('input');
  for(var i=0; i < cbs.length; i++) {
    if(cbs[i].type == 'checkbox') {
      cbs[i].checked = bx.checked;
    }
  }
}
</script>	

<link href="./bootstrap/css/bootstrap-datetimepicker.min.css" rel="stylesheet" media="screen">
<script type="text/javascript" src="./jquery/jquery-1.8.3.min.js" charset="UTF-8"></script>
<script type="text/javascript" src="./bootstrap/js/bootstrap.min.js"></script>
<script type="text/javascript" src="./jquery/bootstrap-datetimepicker.js" charset="UTF-8"></script>
<script type="text/javascript" src="./jquery/locales/bootstrap-datetimepicker.pl.js" charset="UTF-8"></script>
<script type="text/javascript">
	$('.form_date').datetimepicker({
        language:  'pl',
        weekStart: 1,
        todayBtn:  1,
		autoclose: 1,
		todayHighlight: 1,
		startView: 2,
		minView: 2,
		forceParse: 0
    });
	$('.form_time').datetimepicker({
        language:  'pl',
        weekStart: 1,
        todayBtn:  1,
		autoclose: 1,
		todayHighlight: 1,
		startView: 1,
		minView: 0,
		maxView: 1,
		forceParse: 0
    });
</script>
	<script  type="text/javascript">
function valid()
{
var curntloca1=document.forms["buktohub"]["mloca"].value;
if (curntloca1==null || curntloca1=="")
  {
  alert("Please Select HUB Location");
  return false;
  }
  
  var curdate1=document.forms["buktohub"]["curdate"].value;
if (curdate1==null || curdate1=="")
  {
   alert("Please Select Date ");
  return false;
  
  }
  
  var curtime1=document.forms["buktohub"]["curtime"].value;
if (curtime1==null || curtime1=="")
  {
  alert("Please Select Time ");
  return false;
  }
  
}
</script>

<script  type="text/javascript">
function filterbytype(type)
{
//alert("type="+type);
$('#huback').val(type);

$('#mloca').find('option').remove().end().append('<option value="">--- Select '+type+'---</option>').val('');
	// Ajax post
	jQuery.ajax({
	type: "POST",
	url: "ajaxfiltertype.php?type="+type,
	dataType: 'json',
	data: {
		
	},
	//cache: false,	
	success: function(data) { 
	
	   		if (data)
			{  
				$.each(data, function(index, data) 
				{
				$('#mloca').append( $('<option></option>').val(data.id).html(data.name) );	

			} );
			
			} 
		} 
		
	});
$('#status').find('option').remove().end().append('<option value="">--- Select Status---</option>').val('');
	// Ajax post
	jQuery.ajax({
	type: "POST",
	url: "ajax_getstatus.php?type="+type,
	dataType: 'json',
	data: {
		
	},
	//cache: false,	
	success: function(data) { 
	
	   		if (data)
			{  
				$.each(data, function(index, data) 
				{
				$('#status').append( $('<option></option>').val(data.id).html(data.name) );	

			} );
			
			} 
		} 
		
	});
	

}

$(document).ready(function() {
    $('#selecctall').click(function(event) {  //on click 
        if(this.checked) { // check select status
            $('.checkbox1').each(function() { //loop through each checkbox
                this.checked = true;  //select all checkboxes with class "checkbox1"  
                $("#sel").html("Deselect All");             
            });
        }else{
            $('.checkbox1').each(function() { //loop through each checkbox
                this.checked = false; //deselect all checkboxes with class "checkbox1"  
                 $("#sel").html("Select All");                         
            });         
        }
    });
    
});

</script>		

<?php 
$sql = "SELECT * FROM tbl_courier WHERE status = '1' ORDER BY cid DESC "; 
$rs_result = mysqli_query($con,$sql); //run the query
$total_records = mysqli_num_rows($rs_result);  //count number of records
$total_pages = ceil($total_records / $num_rec_per_page); 

echo "<a href='bukhub.php?page=1'>".'|<'."</a> "; // Goto 1st page  

for ($i=1; $i<=$total_pages; $i++) { 
            echo "<a href='bukhub.php?page=".$i."'>".$i."</a> "; 
}; 
include("footer.php"); ?>