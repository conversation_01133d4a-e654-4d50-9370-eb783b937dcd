<!DOCTYPE html>
<html>
<head>
    <title>Search Debug Test</title>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <link rel="stylesheet" href="css/select2.min.css" />
    <script src="select2.min.js"></script>
</head>
<body>
    <h2>Search Debug Test</h2>
    
    <div class="control-group">
        <label class="control-label">Test Shipper Code:</label>
        <div class="controls">
            <select name="sname" id="sname" class="select2-search" required>
                <option value="" selected>-- Please Select --</option>
                <option value="1">ABC-Company Name-Mumbai</option>
                <option value="2">XYZ-Another Company-Delhi</option>
                <option value="3">DEF-Third Company-Bangalore</option>
                <option value="4">GHI-Fourth Company-Chennai</option>
                <option value="5">JKL-Fifth Company-Kolkata</option>
            </select>
        </div>
    </div>

    <script>
    $(document).ready(function() {
        console.log('=== Debug Test Page Loaded ===');
        
        // Enhanced search functionality for shipper and receiver codes
        function initializeEnhancedSearch() {
            console.log('=== Initializing Enhanced Search ===');
            
            // Check if elements exist
            console.log('Shipper select exists:', $('#sname').length > 0);
            
            // Initialize shipper code search
            initializeSearchForSelect('#sname', 'Shipper Code');
            
            console.log('=== Enhanced Search Initialization Complete ===');
        }

        function initializeSearchForSelect(selectId, selectName) {
            var $select = $(selectId);
            
            if ($select.length === 0) {
                console.log('Select element not found:', selectId);
                return;
            }

            console.log('Initializing search for:', selectName, 'Element:', $select);

            // Store original options (excluding placeholder)
            var originalOptions = [];
            $select.find('option').each(function(index) {
                if (index > 0 && $(this).val()) { // Skip the first placeholder option and empty values
                    originalOptions.push({
                        value: $(this).val(),
                        text: $(this).text(),
                        selected: $(this).is(':selected')
                    });
                }
            });
            $select.data('original-options', originalOptions);

            console.log('Original options stored:', originalOptions);

            // Add "Search" option as the second option
            var firstOption = $select.find('option:first');
            firstOption.after('<option value="__SEARCH__">🔍 Search...</option>');

            console.log('Search option added');

            // Initialize Select2
            $select.select2({
                placeholder: "-- Please Select --",
                allowClear: true,
                width: '100%'
            });

            console.log('Select2 initialized');

            // Handle selection change
            $select.on('change', function() {
                var selectedValue = $(this).val();

                console.log('Selected value for ' + selectName + ':', selectedValue);

                if (selectedValue === '__SEARCH__') {
                    console.log('Opening search input for ' + selectName + '...');
                    // Show inline search input
                    showSearchInput($select, originalOptions, selectName);

                    // Reset selection to placeholder
                    setTimeout(function() {
                        $select.val('').trigger('change');
                    }, 100);
                }
            });

            console.log('Enhanced search initialized for ' + selectName + ' with ' + originalOptions.length + ' options');
        }

        // Function to show inline search input (similar to test_select2_search)
        function showSearchInput($select, originalOptions, selectName) {
            console.log('showSearchInput called for ' + selectName + ' with:', originalOptions);
            console.log('Select element:', $select);
            console.log('Select parent:', $select.parent());

            // Hide the select dropdown
            $select.select2('close');

            // Remove any existing search container
            $('#searchContainer_' + $select.attr('id')).remove();

            // Create search input container
            var searchContainer = $('<div id="searchContainer_' + $select.attr('id') + '" style="position: relative; display: inline-block; width: 100%; margin-top: 10px; margin-bottom: 10px; border: 2px solid red;"></div>');

            // Create search input
            var searchInput = $('<input type="text" placeholder="🔍 Type to search and press Enter..." style="width: 100%; padding: 8px 12px; border: 2px solid #007bff; border-radius: 4px; font-size: 14px; box-sizing: border-box; outline: none; background-color: yellow;">');

            // Create results dropdown
            var resultsDropdown = $('<div style="position: absolute; top: 100%; left: 0; right: 0; background: white; border: 1px solid #ddd; border-top: none; border-radius: 0 0 4px 4px; max-height: 200px; overflow-y: auto; z-index: 1000; display: none; box-shadow: 0 2px 8px rgba(0,0,0,0.1);"></div>');

            // Add elements to container
            searchContainer.append(searchInput);
            searchContainer.append(resultsDropdown);

            // Insert after the controls div (which contains the select)
            $select.closest('.controls').after(searchContainer);
            
            console.log('Search container inserted:', searchContainer);
            console.log('Container is visible:', searchContainer.is(':visible'));

            // Focus on input
            searchInput.focus();
            
            console.log('Input focused');

            // Handle input events
            searchInput.on('input', function() {
                var searchTerm = $(this).val().toLowerCase();
                console.log('Search term:', searchTerm);

                if (searchTerm.length === 0) {
                    resultsDropdown.hide();
                    return;
                }

                var resultsHtml = '';
                var matchCount = 0;

                for (var i = 0; i < originalOptions.length; i++) {
                    var option = originalOptions[i];
                    if (option.value && option.text && option.text.toLowerCase().indexOf(searchTerm) > -1) {
                        resultsHtml += '<div class="search-result-item" data-value="' + option.value + '" style="padding: 8px 12px; cursor: pointer; border-bottom: 1px solid #eee; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor=\'#f8f9fa\'" onmouseout="this.style.backgroundColor=\'white\'">' + option.text + '</div>';
                        matchCount++;
                    }
                }

                if (matchCount === 0) {
                    resultsHtml = '<div style="padding: 8px 12px; color: #666;">No matches found</div>';
                }

                resultsDropdown.html(resultsHtml);
                resultsDropdown.show();

                // Handle result item clicks
                $('.search-result-item').off('click').on('click', function() {
                    var selectedValue = $(this).data('value');
                    var selectedText = $(this).text();

                    // Set the selected value in the original select
                    $select.val(selectedValue).trigger('change');

                    console.log('Selected:', selectedText, 'with value:', selectedValue);

                    // Remove search container
                    searchContainer.remove();
                });
            });

            // Handle Enter key
            searchInput.on('keydown', function(e) {
                if (e.key === 'Enter') {
                    var firstResult = resultsDropdown.find('.search-result-item').first();
                    if (firstResult.length > 0) {
                        firstResult.click();
                    }
                } else if (e.key === 'Escape') {
                    searchContainer.remove();
                }
            });

            // Handle click outside to close
            $(document).on('click.searchInput_' + $select.attr('id'), function(e) {
                if (!searchContainer.is(e.target) && searchContainer.has(e.target).length === 0) {
                    searchContainer.remove();
                    $(document).off('click.searchInput_' + $select.attr('id'));
                }
            });
        }

        // Initialize enhanced search functionality
        initializeEnhancedSearch();
    });
    </script>
</body>
</html>
