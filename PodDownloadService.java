@Service
public class PodDownloadService {
    
    // ... [previous configuration and dependencies]
    
    public String generateDownloadToken(String month, String email) {
        validateMonthFormat(month);
        validateEmail(email);
        
        String token = UUID.randomUUID().toString();
        long expiryTime = System.currentTimeMillis() + TimeUnit.DAYS.toMillis(zipExpiryDays);
        
        PodDownload download = new PodDownload();
        download.setEmail(email);
        download.setMonth(month);
        download.setToken(token);
        download.setExpiryTime(expiryTime);
        podDownloadRepository.save(download);
        
        return token;
    }
    
    public ResponseEntity<Resource> getZipFileByToken(String token, HttpServletResponse response) 
            throws IOException {
        
        PodDownload download = podDownloadRepository.findByToken(token)
            .orElseThrow(() -> new RuntimeException("Invalid download token"));
        
        if (download.getExpiryTime() < System.currentTimeMillis()) {
            throw new RuntimeException("Download link has expired");
        }
        
        String month = download.getMonth();
        List<Courier> courierRecords = getCourierRecordsForMonth(month);
        List<File> filesToZip = getValidFiles(courierRecords);
        
        if (filesToZip.isEmpty()) {
            throw new RuntimeException("No POD files found for the selected month");
        }
        
        File zipFile = createZipFile(month, filesToZip);
        
        // Mark as downloaded
        download.setDownloadedAt(LocalDateTime.now());
        podDownloadRepository.save(download);
        
        Path path = Paths.get(zipFile.getAbsolutePath());
        ByteArrayResource resource = new ByteArrayResource(Files.readAllBytes(path));
        
        return ResponseEntity.ok()
            .header(HttpHeaders.CONTENT_DISPOSITION, 
                "attachment; filename=\"" + zipFile.getName() + "\"")
            .contentLength(zipFile.length())
            .contentType(MediaType.APPLICATION_OCTET_STREAM)
            .body(resource);
    }
    
    // ... [rest of the methods from previous implementation]
}