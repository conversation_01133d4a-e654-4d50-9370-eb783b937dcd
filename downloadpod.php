
<?php
session_start();
include("connection.php");
require_once('database.php');
require_once('library.php');
isUser();

// Generate CSRF token for security
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

$sql = "SELECT COUNT(status) FROM tbl_courier
        WHERE status = 'Delivered'";
$result=mysqli_query($con,$sql) or die(mysqli_error($con));
while($row = mysqli_fetch_array($result)){
    $deliver=$row['COUNT(status)'];
}

$sql1 = "SELECT COUNT(status) FROM tbl_courier
        WHERE status = 'Onhold'";
$result1=mysqli_query($con,$sql1) or die(mysqli_error($con));
while($row1 = mysqli_fetch_array($result1)){
    $onhold=$row1['COUNT(status)'];
}

$sql2 = "SELECT COUNT(status) FROM tbl_courier
        WHERE status = 'In Transit'";
$result2=mysqli_query($con,$sql2) or die(mysqli_error($con));
while($row2 = mysqli_fetch_array($result2)){
    $transit=$row2['COUNT(status)'];
}

$sql3 = "SELECT COUNT(status) FROM tbl_courier
        WHERE status = 'Landed'";
$result3=mysqli_query($con,$sql3) or die(mysqli_error($con));
while($row3 = mysqli_fetch_array($result3)){
    $land=$row3['COUNT(status)'];
}

// Get available months with data from tbl_courier
$available_months = array();
$month_names = array(
    '01' => 'January', '02' => 'February', '03' => 'March', '04' => 'April',
    '05' => 'May', '06' => 'June', '07' => 'July', '08' => 'August',
    '09' => 'September', '10' => 'October', '11' => 'November', '12' => 'December'
);

// Query to get distinct months and years from book_date where data exists
$sql_months = "SELECT DISTINCT
                YEAR(book_date) as year,
                MONTH(book_date) as month
               FROM tbl_courier
               WHERE book_date IS NOT NULL
               ORDER BY year DESC, month DESC";

$result_months = mysqli_query($con, $sql_months);
if($result_months) {
    while($row_month = mysqli_fetch_array($result_months)) {
        $month_key = sprintf('%02d', $row_month['month']);
        $year = $row_month['year'];
        $year_month = $year . '-' . $month_key;

        $available_months[] = array(
            'value' => $year_month,
            'year' => $year,
            'month_num' => $month_key,
            'display' => $month_names[$month_key] . ' ' . $year
        );
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Vivanta Logistics</title>
    <meta name="description" content="">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
    <link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
    <link rel="stylesheet" href="css/customize.css">
    <link rel="stylesheet" href="css/font-awesome.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/flexslider.css">
    <link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
    <!--[if lt IE 9]>
        <script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
        <script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
        <link rel="stylesheet" href="css/font-awesome-ie7.css">
    <![endif]-->
</head>
<body>
<?php include("header.php"); ?>

<div class="container">
    <div class="row">
        <div class="span2"></div>
        <div class="span8" align="center">
            <h2 style="color: black;">Download POD Month-Wise</h2>  
        </div>
    </div>
</div>

<div class="container" style="display: flex; justify-content: center; align-items: center; min-height: 300px; margin-top: 30px;">
    <div class="span6" style="border: 1px solid #ccc; border-radius: 10px; padding: 30px; box-shadow: 0px 0px 10px #aaa; background-color: #f9f9f9; text-align: center; width: 100%; max-width: 500px;">
        <!-- First Form -->
        <form id="podForm" onsubmit="handleSubmit(event)">
            <h4 style="margin-bottom: 20px; color: black;">Select Month to Download POD</h4>
            <div style="text-align: center;">
                <select name="month" id="month" style="width: 80%; padding: 3px; font-size: 16px; border-radius: 5px; margin-bottom: 15px; text-align-last: center;" required>
                    <option value="">-- Select Month --</option>
                    <?php
                    if(!empty($available_months)) {
                        foreach($available_months as $month_data) {
                            echo '<option value="' . $month_data['value'] . '">' . $month_data['display'] . '</option>';
                        }
                    } else {
                        echo '<option value="" disabled>No data available</option>';
                    }
                    ?>
                </select>

                <div style="margin-bottom: 15px;">
                    <label style="font-weight: bold; margin-bottom: 10px; display: block;">Data Filter:</label>
                    <div style="display: flex; justify-content: center; gap: 20px; flex-wrap: wrap;">
                        <label style="display: flex; align-items: center; gap: 5px;">
                            <input type="radio" name="dataFilter" value="delivered" checked>
                            <span>Delivered Only</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 5px;">
                            <input type="radio" name="dataFilter" value="all">
                            <span>All Records</span>
                        </label>
                    </div>
                    <br>
                </div>
            </div>
            <button type="submit" class="orange-btn">Submit</button>
        </form>

        <!-- Download POD button section -->
        <div id="downloadSection" style="display: none; margin-top: 20px;">
            <p id="noteText" style="margin-bottom: 15px; font-size: 14px; color: #555;"></p>
            <form id="downloadForm" onsubmit="processDownloadRequest(event)">
                <input type="hidden" name="month" id="downloadMonth">
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                
                <div style="margin-bottom: 15px;">
                    <input type="email" name="email" id="email" required placeholder="Enter your email address" style="width: 100%; max-width: 400px; padding: 10px; border: 1px solid #ccc; border-radius: 5px; font-size: 14px;">
                </div>
                <button type="submit" class="orange-btn" id="downloadBtn">Download POD</button>
                <button type="button" class="orange-btn" id="showTableBtn" onclick="showMonthData()" style="margin-left: 10px;">Show Data Table</button>
            </form>
            <div id="loadingSpinner" style="display: none; margin-top: 15px;">
                <i class="fa fa-spinner fa-spin fa-2x"></i>
                <p>Processing your request...</p>
            </div>
            <div id="responseMessage" style="margin-top: 15px;"></div>
        </div>
    </div>
</div>

<!-- Data Table Section -->
<div id="dataTableSection" style="display: none; margin-top: 30px;">
    <div class="container-fluid">
        <div class="row-fluid">
            <div class="span12">
                <div class="widget-box">
                    <div class="widget-title">
                        <span class="icon"><i class="fa fa-table"></i></span>
                        <h5 id="tableTitle">Monthly POD Data</h5>
                        <div class="buttons">
                            <button class="btn btn-mini btn-danger" onclick="hideDataTable()">
                                <i class="fa fa-times"></i> Close
                            </button>
                        </div>
                    </div>
                    <div class="widget-content nopadding">
                        <div id="tableStats" style="padding: 10px; background: #f5f5f5; border-bottom: 1px solid #ddd;">
                            <!-- Statistics will be shown here -->
                        </div>
                        <div id="tableContainer" style="max-height: 600px; overflow-y: auto;">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr style="background: #f16325; color: white;">
                                        <th>S.No</th>
                                        <th>Consignment No</th>
                                        <th>Book Date</th>
                                        <th>Part No</th>
                                        <th>Sender Name</th>
                                        <th>Receiver Name</th>
                                        <th>Weight (KG)</th>
                                        <th>Status</th>
                                        <th>Delivery Date</th>
                                        <th>Vehicle No</th>
                                        <th>Staff Name</th>
                                    </tr>
                                </thead>
                                <tbody id="dataTableBody">
                                    <!-- Data will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                        <div id="tableLoading" style="display: none; text-align: center; padding: 40px;">
                            <i class="fa fa-spinner fa-spin fa-3x" style="color: #f16325;"></i>
                            <p style="margin-top: 15px; font-size: 16px;">Loading data...</p>
                        </div>
                        <div id="tableError" style="display: none; text-align: center; padding: 40px; color: #d9534f;">
                            <!-- Error message will be shown here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .orange-btn {
        background-color: #f16325;
        color: white;
        border: none;
        padding: 12px 24px;
        font-size: 16px;
        border-radius: 5px;
        cursor: pointer;
        transition: 0.3s ease;
    }
    .orange-btn:hover {
        background-color: #e85c1f;
    }
    .orange-btn:disabled {
        background-color: #cccccc;
        cursor: not-allowed;
    }
    #responseMessage.success {
        color: green;
    }
    #responseMessage.error {
        color: red;
    }

    /* Data Table Styles */
    #dataTableSection {
        animation: fadeIn 0.5s ease-in;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    #dataTableSection .table {
        margin-bottom: 0;
    }

    #dataTableSection .table th {
        background: #f16325 !important;
        color: white !important;
        border-color: #d4541f !important;
        font-weight: bold;
        text-align: center;
        vertical-align: middle;
    }

    #dataTableSection .table td {
        vertical-align: middle;
        border-color: #ddd;
    }

    #dataTableSection .table tbody tr:hover {
        background-color: #f5f5f5;
    }

    #tableStats {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 2px solid #f16325;
        font-size: 14px;
    }

    #tableStats .span3 {
        padding: 5px 10px;
        border-right: 1px solid #ddd;
    }

    #tableStats .span3:last-child {
        border-right: none;
    }

    .badge-success {
        background-color: #5cb85c;
        color: white;
        padding: 3px 8px;
        border-radius: 3px;
        font-size: 11px;
    }

    .badge-warning {
        background-color: #f0ad4e;
        color: white;
        padding: 3px 8px;
        border-radius: 3px;
        font-size: 11px;
    }

    .badge-info {
        background-color: #5bc0de;
        color: white;
        padding: 3px 8px;
        border-radius: 3px;
        font-size: 11px;
    }

    .badge-inverse {
        background-color: #333;
        color: white;
        padding: 3px 8px;
        border-radius: 3px;
        font-size: 11px;
    }

    .orange-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(241, 99, 37, 0.3);
    }
</style>

<script>


    
    function sendEmailWithDownloadLink(email, month, downloadUrl) {
    const formData = new FormData();
    formData.append('email', email);
    formData.append('month', month);
    formData.append('download_url', downloadUrl);
    formData.append('csrf_token', '<?php echo $_SESSION['csrf_token']; ?>');

    return fetch('send_pod_email.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            return true;
        } else {
            throw new Error(data.message || 'Email sending failed');
        }
    });
}
    // Updated PHP backend URL
    const phpBackendUrl = 'pod_download_processor.php';

    function handleSubmit(event) {
        event.preventDefault();
        const monthValue = document.getElementById('month').value;

        if (monthValue === "") {
            alert("Please select a month.");
            return;
        }

        const selectedOption = document.getElementById('month').options[document.getElementById('month').selectedIndex];
        const monthText = selectedOption.text;

        document.getElementById('downloadMonth').value = monthValue;
        document.getElementById('noteText').textContent =
            `Enter your email address to receive the POD download link for ${monthText}.`;

        document.getElementById('downloadSection').style.display = 'block';
    }

    // Inside the <script> tag, replace the existing processDownloadRequest function:
function processDownloadRequest(event) {
    event.preventDefault();
    
    const month = document.getElementById('downloadMonth').value;
    const email = document.getElementById('email').value.trim();
    const csrfToken = document.querySelector('input[name="csrf_token"]').value;
    const downloadBtn = document.getElementById('downloadBtn');
    const loadingSpinner = document.getElementById('loadingSpinner');
    const responseMessage = document.getElementById('responseMessage');

    // Validate email
    if (email === "") {
        showResponseMessage('Please enter your email address.', 'error');
        return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        showResponseMessage('Please enter a valid email address.', 'error');
        return;
    }

    // Show loading state
    downloadBtn.disabled = true;
    loadingSpinner.style.display = 'block';
    responseMessage.style.display = 'none';

    // Get selected filter option
    const dataFilter = document.querySelector('input[name="dataFilter"]:checked').value;

    // Prepare form data
    const formData = new FormData();
    formData.append('month', month);
    formData.append('email', email);
    formData.append('data_filter', dataFilter);
    formData.append('csrf_token', csrfToken);

    // ==============================================
    // PASTE THE ENHANCED FETCH CODE HERE (REPLACING THE CURRENT FETCH CALL):
    // ==============================================
    fetch(phpBackendUrl, {
        method: 'POST',
        body: formData
    })
    .then(response => {
        console.log('Raw response:', response);
        return response.text(); // First get as text to debug
    })
    .then(text => {
        console.log('Response text:', text);
        try {
            const data = JSON.parse(text);
            
            if (data.status === 'success') {
                let message = data.message || 'Download link has been sent to your email. Please check your inbox (and spam folder).';
                
                showResponseMessage(message, 'success');
            } else {
                throw new Error(data.message || 'Unknown error occurred');
            }
        } catch (e) {
            console.error('JSON parse error:', e);
            showResponseMessage('Invalid server response: ' + text.substring(0, 100), 'error');
        }
    })
    .catch(error => {
        console.error('Fetch error:', error);
        showResponseMessage('Network error: ' + error.message, 'error');
    })
    .finally(() => {
        downloadBtn.disabled = false;
        loadingSpinner.style.display = 'none';
    });
    // ==============================================
}

function showResponseMessage(message, type) {
    const responseMessage = document.getElementById('responseMessage');
    responseMessage.innerHTML = message;
    responseMessage.className = type;
    responseMessage.style.display = 'block';
}
    // Function to show month data in table format
    function showMonthData() {
        const monthSelect = document.getElementById('month');
        const selectedMonth = monthSelect.value;

        if (!selectedMonth) {
            alert('Please select a month first');
            return;
        }

        // Show the data table section
        document.getElementById('dataTableSection').style.display = 'block';
        document.getElementById('tableLoading').style.display = 'block';
        document.getElementById('tableContainer').style.display = 'none';
        document.getElementById('tableError').style.display = 'none';

        // Update table title
        const monthNames = {
            '01': 'January', '02': 'February', '03': 'March', '04': 'April',
            '05': 'May', '06': 'June', '07': 'July', '08': 'August',
            '09': 'September', '10': 'October', '11': 'November', '12': 'December'
        };

        const [year, month] = selectedMonth.split('-');
        const monthName = monthNames[month] + ' ' + year;
        document.getElementById('tableTitle').textContent = 'POD Data - ' + monthName;

        // Scroll to table
        document.getElementById('dataTableSection').scrollIntoView({ behavior: 'smooth' });

        // Fetch data from server
        fetchMonthData(selectedMonth);
    }

    // Function to fetch month data
    function fetchMonthData(month) {
        // Get selected filter option
        const dataFilter = document.querySelector('input[name="dataFilter"]:checked').value;

        const formData = new FormData();
        formData.append('action', 'fetch_month_data');
        formData.append('month', month);
        formData.append('data_filter', dataFilter);
        formData.append('csrf_token', '<?php echo $_SESSION['csrf_token']; ?>');

        fetch('fetch_pod_data.php', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            // Check if response is ok
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // Get response text first to debug
            return response.text();
        })
        .then(text => {
            document.getElementById('tableLoading').style.display = 'none';

            try {
                // Try to parse JSON
                const data = JSON.parse(text);

                if (data.status === 'success') {
                    displayTableData(data.data, data.stats);
                    document.getElementById('tableContainer').style.display = 'block';
                } else {
                    document.getElementById('tableError').innerHTML = '<i class="fa fa-exclamation-triangle"></i> ' + data.message;
                    document.getElementById('tableError').style.display = 'block';
                }
            } catch (jsonError) {
                // Show the actual response text for debugging
                console.error('JSON Parse Error:', jsonError);
                console.error('Response Text:', text);
                document.getElementById('tableError').innerHTML =
                    '<i class="fa fa-exclamation-triangle"></i> JSON Parse Error. Check console for details.<br>' +
                    '<small>Response preview: ' + text.substring(0, 200) + '...</small>';
                document.getElementById('tableError').style.display = 'block';
            }
        })
        .catch(error => {
            document.getElementById('tableLoading').style.display = 'none';
            console.error('Fetch Error:', error);
            document.getElementById('tableError').innerHTML = '<i class="fa fa-exclamation-triangle"></i> Error loading data: ' + error.message;
            document.getElementById('tableError').style.display = 'block';
        });
    }

    // Function to display data in table
    function displayTableData(data, stats) {
        const tbody = document.getElementById('dataTableBody');
        const statsDiv = document.getElementById('tableStats');

        // Display statistics
        statsDiv.innerHTML = `
            <div class="row-fluid">
                <div class="span3"><strong>Total Records:</strong> ${stats.total_records}</div>
                <div class="span3"><strong>Delivered:</strong> ${stats.delivered_records}</div>
                <div class="span3"><strong>Total Weight:</strong> ${stats.total_weight} KG</div>
                <div class="span3"><strong>Date Range:</strong> ${stats.date_range}</div>
            </div>
        `;

        // Clear existing data
        tbody.innerHTML = '';

        if (data.length === 0) {
            tbody.innerHTML = '<tr><td colspan="11" style="text-align: center; padding: 20px;">No data found for selected month</td></tr>';
            return;
        }

        // Populate table with data
        data.forEach((row, index) => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>${index + 1}</td>
                <td><strong>${row.ConsignmentNo || ''}</strong></td>
                <td>${row.book_date || ''}</td>
                <td>${row.PartNo || ''}</td>
                <td>${row.sender_name || ''}</td>
                <td>${row.receiver_name || ''}</td>
                <td>${row.Weight || ''}</td>
                <td><span class="badge ${getStatusBadgeClass(row.status)}">${row.status || ''}</span></td>
                <td>${row.delivery_date || ''}</td>
                <td>${row.vehicle || ''}</td>
                <td>${row.staff_name || ''}</td>
            `;
            tbody.appendChild(tr);
        });
    }

    // Function to get status badge class
    function getStatusBadgeClass(status) {
        switch(status) {
            case 'Delivered': return 'badge-success';
            case 'In Transit': return 'badge-warning';
            case 'Booked': return 'badge-info';
            default: return 'badge-inverse';
        }
    }

    // Function to hide data table
    function hideDataTable() {
        document.getElementById('dataTableSection').style.display = 'none';
    }

    // Debug function to test endpoint
    function testDebugEndpoint() {
        console.log('Testing debug endpoint...');

        const formData = new FormData();
        formData.append('month', '2025-07');
        formData.append('data_filter', 'all');

        // Test the debug endpoint
        fetch('fetch_pod_data_debug.php', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);
            return response.text();
        })
        .then(text => {
            console.log('Raw response:', text);

            try {
                const data = JSON.parse(text);
                console.log('Parsed JSON:', data);
                alert('Debug test successful! Check console for details.');
            } catch (error) {
                console.error('JSON parse error:', error);
                console.error('Response text:', text);
                alert('Debug test failed. Check console for details.');
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
            alert('Network error. Check console for details.');
        });
    }
</script>


<?php include("footer.php"); ?>
</body>
</html>
