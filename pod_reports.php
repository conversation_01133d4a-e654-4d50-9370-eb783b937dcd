<?php
session_start();
require 'connection.php';
require_once('database.php');
require_once('library.php');
isUser();   

 ?>
<!DOCTYPE html>
<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->
<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->
<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->
<head>

	<!-- Basic Page Needs
  ================================================== -->
	<meta charset="utf-8">
	
	<!-- Mobile Specific Metas
  ================================================== -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<!-- CSS
  ================================================== -->
	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">
	<!-- jquery ui css -->
	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">
	<link rel="stylesheet" href="css/customize.css">
	<link rel="stylesheet" href="css/font-awesome.css">
	<link rel="stylesheet" href="css/style.css">
	<!-- flexslider css-->
	<link rel="stylesheet" href="css/flexslider.css">
	<!-- fancybox -->
	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">
	<!--[if lt IE 9]>
		<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
		<script src="http://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>
		<link rel="stylesheet" href="css/font-awesome-ie7.css">
	<![endif]-->
	<!-- Favicons 
	================================================== -->
<script src="js/jquery-1.7.2.min.js"></script>
   
</head>
<?php include("header.php"); ?>

   <div class="container">
			<div class="row">
     			<div class="span2">
				<div class="control-group ">
				
				</div>
				</div><!--end span3-->
                	 <form method="post" action="reports_pod.php" >
                <div class="span8">
					<div class="account-list-outer">
						<div class="control-group ">
						 Select Date 
						</div>
						<div class="control-group ">
						   <div class="controls">
							<label for="rep"> Start Date : <span class="text-error">*</span></label>
								<div class="controls input-append date form_date" data-date="" data-date-format="dd MM yyyy" data-link-field="dtp_input2" data-link-format="yyyy-mm-dd">
									 <input  type="text" value=""id="date2" name="date2" readonly>	
										<span class="add-on"><i class="icon-remove"></i></span>
										 <span class="add-on"><i class="icon-th"></i></span>
								</div>
									 <input type="hidden" id="dtp_input2" value="" />
						    </div>
						</div>
						<div class="control-group ">
						   <div class="controls">
							<label class="control-label" for="rep"> End Date : <span class="text-error">*</span></label>
								<div  class="controls input-append date form_date" data-date="" data-date-format="dd MM yyyy" data-link-field="dtp_input2" data-link-format="yyyy-mm-dd">
									<input  type="text" value="" id="date4" name="date4" readonly>	
										<span class="add-on"><i class="icon-remove"></i></span>
										 <span class="add-on"><i class="icon-th"></i></span>
								</div>
									 <input type="hidden" id="dtp_input2" value="" />
						   </div>
						</div> 

					<div class="control-group">
 							 <label for="rep">Select Report Type: <span class="text-error">*</span></label>
  								<div class="controls">
    							<select name="rep" id="rep" class="form-control">
      							<option value="">-- Select Report --</option>
      							<option value="pending">Pending</option>
      							<option value="uploaded">Uploaded</option>
    						</select>
  								</div>
									</div>
									
  <div class="control-group">
  <div class="controls">
    <input class="btn btn-primary" type="submit" value="Submit">
    <input class="btn btn-warning" type="reset" value="Reset">
</div>
</div>
							</form>
<style>
.btn-warning{
  border:1px solid #f16325;
  background: #f16325;
  background: -moz-linear-gradient(top, #f4885a 0%, #f16325 100%);
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#f4885a), color-stop(100%,#f16325));
  background: -webkit-linear-gradient(top, #f4885a 0%,#f16325 100%);
  background: -o-linear-gradient(top, #f4885a 0%,#f16325 100%);
  background: -ms-linear-gradient(top, #f4885a 0%,#f16325 100%);
  background: linear-gradient(to bottom, #f4885a 0%,#f16325 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}
</style>
							
						
							
				
							
							<div id="cmp"></div>
					</div><!--end -->
				</div><!--end span6-->
			</div><!--end row-->
		</div><!--end conatiner-->
<!-- <link href="./bootstrap/css/bootstrap.min.css" rel="stylesheet" media="screen">-->
    <link href="./bootstrap/css/bootstrap-datetimepicker.min.css" rel="stylesheet" media="screen">		
<script type="text/javascript" src="./jquery/jquery-1.8.3.min.js" charset="UTF-8"></script>
<script type="text/javascript" src="./bootstrap/js/bootstrap.min.js"></script>
<script type="text/javascript" src="./jquery/bootstrap-datetimepicker.js" charset="UTF-8"></script>
<!--<script type="text/javascript" src="./jquery/locales/bootstrap-datetimepicker.pl.js" charset="UTF-8"></script>-->
<script type="text/javascript">
   	$('.form_date').datetimepicker({
        language:  'pl',
        weekStart: 1,
        todayBtn:  1,
		autoclose: 1,
		todayHighlight: 1,
		startView: 2,
		minView: 2,
		forceParse: 0
    });
	
</script>

 
<?php
echo "<script> 
function abc()
{

var rep=document.getElementById('rep').value;

var date=document.getElementById('date2').value;
var date2=document.getElementById('date4').value;
if(rep=='')
{
alert('Please Select Status Report Name');
}
else if(date=='')
{
alert('Please Fill Start Date');
}
else if(date2=='')
{
alert('Please Fill End Date');
}
}
</script>";
?>

<script>
function getRep()
{
	//alert("hello");
var date=document.getElementById("date2").value;
var date2=document.getElementById("date4").value;
var rep=document.getElementById("rep").value;
//alert(rep);
if (window.XMLHttpRequest)
  {// code for IE7+, Firefox, Chrome, Opera, Safari
  xmlhttp=new XMLHttpRequest();
  }
else
  {// code for IE6, IE5
  xmlhttp=new 
   
  veXObject("Microsoft.XMLHTTP");
  }
xmlhttp.onreadystatechange=function()
  {
  if (xmlhttp.readyState==4 && xmlhttp.status==200)
    {
   // alert (xmlhttp.responseText);
   document.getElementById("cmp").innerHTML=xmlhttp.responseText;
    }
  }
  xmlhttp.open("GET","getdetails2.php?date="+date+"&date2="+date2+"&rep="+rep);
xmlhttp.send();

}




</script>
<!--<script>
function printDiv(divID) {
       
            //Get the HTML of div
            var divElements = document.getElementById(divID).innerHTML;
            //Get the HTML of whole page
            var oldPage = document.body.innerHTML;

            //Reset the page's HTML with div's HTML only
            document.body.innerHTML = 
             echo "<html><head><title></title></head><body>" + 
              divElements + "</body>";

            //Print Page
            window.print();

            //Restore orignal HTML
            document.body.innerHTML = oldPage;

          
        }

</script>-->
		
		
<?php 




include("footer.php"); ?>